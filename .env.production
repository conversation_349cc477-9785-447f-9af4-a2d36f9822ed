# Production Environment Configuration for SANSA Flood Mapping System
# Update these values for your UAT/Production environment

# Database Configuration
POSTGRES_USER=sansa_user
POSTGRES_PASSWORD=secure_password_123
POSTGRES_DB=sansa_flood_db

# Backend (UIEngine) Configuration
# Production environment variables for SANSA Flood Mapping
# Linux Server Deployment: *************
# No database setup - UIEngine and Frontend only

# Environment
NODE_ENV=production

# GeoServer Configuration (external)
GEOSERVER_URL=https://*************/geoserver

# CORS Configuration - Allow access from the server IP
CORS_ORIGIN=http://*************,http://*************:80,http://*************:3000,http://*************:8080,*

# Cache Configuration (for UIEngine)
CACHE_TTL=3600
CACHE_REFRESH=0 0 * * *

# Frontend API Configuration
REACT_APP_API_BASE=/api
CORS_ORIGIN=*
JWT_SECRET=your_jwt_secret_here_change_in_production
LOG_LEVEL=info
NODE_ENV=production

# Cache Configuration
CACHE_TTL=3600
CACHE_REFRESH=0 0 * * *

# Networking
# If deploying on a specific IP, you can set these:
# API_HOST=*************
# APP_PORT=3000

# Optional: SSL Configuration (uncomment and configure if using HTTPS)
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/private.key
