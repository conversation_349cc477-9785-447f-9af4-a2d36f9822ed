# AOI Clipping Implementation - Debug Guide

## 🎯 What Was Implemented

### 1. Enhanced AOI Data Flow
- **MapComponent** now receives and processes `aoiData` prop with geometry and bounds
- **Sidebar** generates precise geometry using `getSelectedBoundaryGeometry()`
- **Real-time debugging** shows AOI data structure and clipping capabilities

### 2. Layer Clipping Methods
- **Method 1 (Preferred)**: CQL_FILTER with precise geometry (server-side clipping)
- **Method 2 (Fallback)**: BBOX parameter with bounds (server-side bounds clipping)
- **Method 3 (Visual)**: Leaflet polygon overlay (client-side visual mask)

### 3. Temporal Integration
- **Single Date Range**: Global date range applies to all temporal layers
- **Automatic Parameter Generation**: Converts sidebar date range to WMS TIME parameters
- **Enhanced Debugging**: Shows temporal parameter usage for each layer

## 🔍 How to Test

### Step 1: Select AOI
1. Open the application
2. In sidebar, select **Administrative Boundaries**:
   - Choose Province (e.g., "Western Cape")
   - Choose District (e.g., "City of Cape Town")
   - Optionally choose Municipality/Ward
3. Set **Date Range** (e.g., 2024-01-01 to 2024-12-31)

### Step 2: Toggle Layers
1. In **Data Layers** section, toggle any layer ON
2. **Expected Result**: Layer should only show within the selected AOI bounds
3. **Check Console**: Look for these debug messages:

```
🔍 AOI Data received in MapComponent: { type: "administrative", hasGeometry: true, ... }
🎯 AOI Clipping Summary: { canUseCQLFilter: true, canUseBBOXFilter: true, ... }
🎯 Applying AOI clipping to layer "layername": { hasGeometry: true, ... }
✂️ Adding CQL clipping filter for layer "layername": INTERSECTS(the_geom, POLYGON(...))
✅ Layer "layername" loaded successfully with AOI clipping
```

### Step 3: Debug Console Commands
Open browser console and run:
```javascript
// Get complete debug report
debugAOIClipping()
```

## 🐛 Troubleshooting

### Issue: Layers show full extent (not clipped)

**Check Console For:**
1. `❌ No AOI data received in MapComponent` → AOI not being passed from Sidebar
2. `⚠️ No geometry retrieved - AOI clipping will use bounds only` → Geometry fetch failed
3. `❌ Failed to create CQL filter` → Geometry conversion failed, using BBOX fallback

**Solutions:**
1. **No AOI Data**: Check if `isLayerSelectionComplete()` returns true in Sidebar
2. **No Geometry**: Check `getSelectedBoundaryGeometry()` API call in Network tab
3. **CQL Filter Failed**: Check if layer supports CQL filtering, fallback to BBOX

### Issue: Temporal layers not using date range

**Check Console For:**
1. `🕐 Temporal layer "layername": { hasTimeParam: false, ... }` → No time parameter generated
2. `⚠️ Non-temporal layer "layername" has time parameter` → Layer incorrectly marked as temporal

**Solutions:**
1. Verify `dateRange.startDate` and `dateRange.endDate` are set
2. Check if layer has `temporal` property in layer discovery

## 🔧 Implementation Details

### AOI Data Structure
```typescript
aoiData: {
  type: 'administrative' | 'drawn' | 'pin';
  bounds: { north, south, east, west };
  geometry: GeoJSON.Geometry;  // For precise clipping
  feature: GeoJSON.Feature;    // Complete feature data
}
```

### Layer URL Parameters Added
```
CQL_FILTER=INTERSECTS(the_geom, POLYGON(...))  // Precise geometry clipping
BBOX=west,south,east,north&SRS=EPSG:4326       // Bounds clipping fallback
time=2024-01-01T00:00:00Z/2024-12-31T00:00:00Z // Temporal filtering
```

### Key Files Modified
- `frontend/src/components/Map/MapComponent.tsx` - Enhanced layer rendering with AOI clipping
- `frontend/src/components/Sidebar/Sidebar.tsx` - Enhanced geometry fetching with debugging
- `frontend/src/services/unifiedBoundaryService.ts` - Enhanced boundary geometry service

## ✅ Expected Behavior

1. **Without AOI**: Layers render normally across full extent
2. **With AOI (Geometry)**: Layers clipped precisely to administrative boundary shape
3. **With AOI (Bounds only)**: Layers clipped to rectangular bounds
4. **With Date Range**: Temporal layers show data only for selected time period
5. **Visual Feedback**: AOI boundary shown with dashed blue outline and semi-transparent mask
