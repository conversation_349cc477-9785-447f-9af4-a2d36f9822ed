# AOI Clipping Testing Guide

## Overview

This guide will help you identify and verify the AOI (Area of Interest) clipping functionality after implementing the solution strategy to fix infinite loops.

## ✅ What We Fixed

1. **Infinite Loop Issues:**
   - ✅ Removed circular dependencies in `useEffect` hooks
   - ✅ Memoized the `triggerBoundaryUpdates` function with `useCallback`
   - ✅ Fixed `wmsLayers` initialization issues in MapComponent
   - ✅ Stabilized component rendering

2. **Added Debugging Tools:**
   - ✅ Comprehensive AOI clipping debugger
   - ✅ Global debugging functions available in browser console
   - ✅ Real-time AOI data monitoring

## 🔧 How to Test

### Step 1: Start the Application

1. Open your browser and navigate to `http://localhost:5173`
2. Open browser console (F12)
3. Look for this message: "🛠️ AOI Debugging tools available:"

### Step 2: Test Basic Functionality

1. **Enable Region of Interest toggle** in the sidebar
2. **Select a Province** (e.g., "Western Cape")
3. **Select a District** (e.g., "City of Cape Town")
4. **Select Date Range** in the date section
5. **Enable a layer** to view

Watch the console for log messages about AOI data generation.

### Step 3: Run Comprehensive Tests

In the browser console, run these debugging commands:

```javascript
// Run full comprehensive test
window.aoiDebugger.runFullTest()

// Test current AOI state (after selecting regions)
window.aoiDebugger.testCurrentAOI()

// Test WMS layer accessibility
window.testWmsUrl('geonode:south_africa_municipal_boundaries')

// Debug layer matching
window.debugLayerMatching()
```

## 🔍 What to Look For

### ❓ Test 1: getSelectedBoundaryGeometry() Returns Proper Geometry Data

**Expected Results:**
```
✅ Geometry test results: {
  success: true,
  hasGeometry: true,
  hasFeature: true,
  hasBounds: true,
  geometryType: "Polygon" or "MultiPolygon",
  coordinateCount: > 0
}
```

**Problem Indicators:**
- ❌ `hasGeometry: false` - No geometry returned from service
- ❌ `hasFeature: false` - Feature data missing
- ❌ `hasBounds: false` - No boundary bounds available
- ❌ `success: false` - API call failed

**Troubleshooting:**
1. Check `unifiedBoundaryService.ts` for correct WFS parameters
2. Verify the boundary service endpoint is accessible
3. Check if the selected region exists in the database

### ❓ Test 2: Server Accepts CQL_FILTER Parameter Format

**Expected Results:**
```
✅ CQL filter accepted by server
📊 CQL test results: {
  success: true,
  wktGenerated: true,
  cqlFilterCreated: true,
  responseStatus: 200
}
```

**Problem Indicators:**
- ❌ `HTTP 400` - Bad request, CQL syntax error
- ❌ `HTTP 500` - Server error processing CQL filter
- ❌ `wktGenerated: false` - WKT conversion failed

**Troubleshooting:**
1. Check if GeoServer supports CQL filtering for the layer
2. Verify the geometry column name (`the_geom` vs `geom` vs `geometry`)
3. Test with simpler geometry (smaller coordinate count)

### ❓ Test 3: WKT Conversion Edge Cases

**Expected Results:**
```
✅ WKT conversion successful: {
  success: true,
  wktString: "POLYGON((lng lat, lng lat, ...))",
  geometryType: "Polygon",
  coordinateCount: > 3
}
```

**Problem Indicators:**
- ❌ Empty coordinates properly rejected
- ❌ Invalid coordinates (NaN) properly rejected
- ❌ Large geometry stress test fails

**Troubleshooting:**
1. Check coordinate format (should be [lng, lat] not [lat, lng])
2. Verify polygon closure (first point = last point)
3. Check for invalid coordinate values

## 🧪 Advanced Testing

### Test Different Geometry Types

```javascript
// Test with different administrative levels
window.aoiDebugger.testBoundaryGeometry({
  province: 'Western Cape'  // Province only
})

window.aoiDebugger.testBoundaryGeometry({
  province: 'Western Cape',
  district: 'City of Cape Town'  // District level
})

window.aoiDebugger.testBoundaryGeometry({
  province: 'Western Cape',
  district: 'City of Cape Town',
  municipality: 'Cape Town'  // Municipality level
})
```

### Monitor Network Requests

1. Open **Network tab** in DevTools
2. Filter by **XHR/Fetch**
3. Look for:
   - Boundary geometry requests to WFS service
   - WMS requests with `CQL_FILTER` parameters
   - Error responses (red entries)

### Visual Verification

1. **Layer clipping should be visible** on the map
2. **Blue dashed boundary outline** should appear around selected region
3. **Dark overlay** should mask areas outside the AOI
4. **Layer data** should only appear inside the boundary

## 🐛 Common Issues and Solutions

### Issue 1: No Geometry Retrieved

**Symptoms:** `hasGeometry: false` in test results

**Solutions:**
1. Check if WFS service is running
2. Verify layer name in `unifiedBoundaryService.ts`
3. Test WFS manually: `/geoserver/ows/wfs?service=WFS&version=1.0.0&request=GetFeature&typeName=LAYER_NAME`

### Issue 2: CQL Filter Rejected

**Symptoms:** HTTP 400/500 errors in CQL test

**Solutions:**
1. Check geometry column name in GeoServer layer
2. Test simpler CQL: `BBOX(the_geom, minx, miny, maxx, maxy, 'EPSG:4326')`
3. Verify CQL is enabled in GeoServer

### Issue 3: WKT Too Large

**Symptoms:** Very long WKT strings, server timeouts

**Solutions:**
1. Implement geometry simplification
2. Use BBOX fallback for complex geometries
3. Consider server-side geometry simplification

### Issue 4: Layers Not Clipping

**Symptoms:** Layers show full extent instead of clipped

**Solutions:**
1. Check `aoiData` is being passed to MapComponent
2. Verify CQL_FILTER is added to WMS URLs
3. Test with `window.currentAOIData` in console

## 📊 Success Criteria

✅ **All tests pass** in `window.aoiDebugger.runFullTest()`
✅ **Geometry data is retrieved** from boundary service
✅ **WKT conversion works** for different geometry types
✅ **CQL filters are accepted** by the server
✅ **Layers are visually clipped** on the map
✅ **No infinite loops** or console spam
✅ **Application is stable** and responsive

## 🆘 If Tests Fail

1. **Check console** for specific error messages
2. **Run individual tests** to isolate the problem
3. **Verify services** are running (WFS, WMS)
4. **Test with simple geometries** first
5. **Check network connectivity** to GeoServer

Remember: The solution strategy fixed the **blocking issues** (infinite loops) that prevented testing. The clipping logic itself appears sound, but may need configuration adjustments based on your specific GeoServer setup.
