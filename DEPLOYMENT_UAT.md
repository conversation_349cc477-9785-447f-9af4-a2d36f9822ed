# 🚀 UAT Deployment Guide for SANSA Flood Mapping System

## Overview

This guide provides instructions for deploying the SANSA Flood Mapping System to a UAT (User Acceptance Testing) environment using IP addresses instead of localhost.

## Prerequisites

- Docker and Docker Compose installed on the target server
- Access to the target server with appropriate permissions
- Network access to GeoServer at `https://*************/geoserver`

## Architecture

The application consists of three main components:

1. **Frontend**: React application served by Nginx
2. **Backend (UIEngine)**: Node.js Express API server  
3. **Nginx Proxy**: Reverse proxy for routing requests

### Network Flow

```
User → Nginx Proxy (:80) → Frontend (React)
                        → Backend API (:3001) → GeoServer
```

## Deployment Options

### Option 1: Standard Deployment (Recommended)

Uses the main docker-compose.yml with Nginx reverse proxy:

```bash
# Linux/macOS
./deploy-uat.sh

# Windows PowerShell
.\deploy-uat.ps1
```

**Access**: `http://YOUR_SERVER_IP:80`

### Option 2: Manual Deployment

1. **Clone the repository** to your UAT server:
   ```bash
   git clone [repository-url]
   cd sansabig
   ```

2. **Configure environment variables**:
   ```bash
   cp .env.production.template .env.production
   # Edit .env.production with your specific settings
   ```

3. **Deploy with Docker Compose**:
   ```bash
   # Standard deployment
   docker-compose up -d
   
   # Or production deployment with database
   docker-compose -f deployment/docker/docker-compose.main.yml up -d
   ```

## Configuration

### Environment Variables

The application is now environment-aware and will automatically:

- **Development**: Use `http://localhost:3001/api` for direct backend access
- **Production**: Use relative `/api` paths that get proxied by Nginx

### Key Configuration Files

1. **Frontend Configuration** (`frontend/src/config.ts`):
   - Automatically detects production vs development mode
   - Uses environment variables for API endpoints
   - Supports custom GeoServer URLs

2. **Environment Files**:
   - `.env.production`: Main production configuration
   - `frontend/.env.production`: Frontend-specific variables
   - `frontend/.env.development`: Development overrides

### Custom Configuration

To customize for your environment, update these variables:

```bash
# In .env.production
GEOSERVER_URL=https://YOUR_GEOSERVER_URL/geoserver
POSTGRES_USER=your_db_user
POSTGRES_PASSWORD=your_db_password

# In frontend/.env.production  
VITE_GEOSERVER_URL=https://YOUR_GEOSERVER_URL/geoserver
```

## Network Configuration

### Port Mapping

| Service | Internal Port | External Port | Purpose |
|---------|---------------|---------------|---------|
| Nginx Proxy | 80 | 80 | Main application access |
| Frontend | 80 | - | Static files (internal) |
| Backend | 3001 | 3001* | API services |

*Backend port 3001 may be exposed for direct access in development mode

### Firewall Requirements

Ensure these ports are accessible:
- **Port 80**: Main application access
- **Port 443**: HTTPS (if SSL is configured)
- **Outbound HTTPS**: Access to GeoServer at `*************`

## Health Checks

The application includes built-in health checks:

```bash
# Check main application
curl http://YOUR_SERVER_IP/

# Check API health (if exposed)
curl http://YOUR_SERVER_IP:3001/api/health

# Check Docker services
docker-compose ps
```

## Troubleshooting

### Common Issues

1. **"Legend could not be loaded"**:
   - Verify GeoServer connectivity: `curl https://*************/geoserver`
   - Check workspace detection in backend logs

2. **Frontend can't connect to backend**:
   - Verify Nginx proxy configuration
   - Check if backend service is running: `docker-compose logs uiengine`

3. **Docker services not starting**:
   - Check logs: `docker-compose logs`
   - Verify port availability: `netstat -tlnp | grep :80`

### Log Access

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f frontend
docker-compose logs -f uiengine
docker-compose logs -f nginx
```

## Maintenance

### Updating the Application

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Backup and Restore

```bash
# Backup volumes
docker-compose exec uiengine tar czf /tmp/backup.tar.gz /app/data /app/logs

# Stop services for maintenance
docker-compose down

# Start services
docker-compose up -d
```

## Security Considerations

1. **Change default passwords** in `.env.production`
2. **Configure SSL/TLS** for production use
3. **Set up proper firewall rules**
4. **Use environment-specific secrets**

## Support

For issues or questions:
1. Check the application logs
2. Review this documentation
3. Contact the development team

---

## Quick Reference

**Start services**: `docker-compose up -d`
**Stop services**: `docker-compose down`  
**View logs**: `docker-compose logs -f`
**Restart service**: `docker-compose restart [service-name]`
**Access application**: `http://YOUR_SERVER_IP`
