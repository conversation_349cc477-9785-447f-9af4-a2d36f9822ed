# 🐧 Linux Server Deployment Guide - SANSA Flood Mapping

## 🎯 Target Configuration
- **Server IP**: `*************`
- **Services**: UIEngine + Frontend + Nginx (No Database)
- **GeoServer**: `https://*************/geoserver` (External)

## 🚀 Quick Deployment

### Option 1: Automated Deployment (Linux)
```bash
# On the Linux server
./deploy-linux.sh
```

### Option 2: Manual Deployment
```bash
# Stop any existing containers
docker-compose down

# Build and start services
docker-compose up -d --build
```

## 🌐 Access Points

After deployment, access your application at:

- **Primary**: `http://*************` (Port 80)
- **Secondary**: `http://*************:8080` (Port 8080 - if port 80 is busy)

## 🏗️ Architecture Overview

```
User Browser → http://************* → Nginx (Port 80/8080)
                                    ├── Frontend (React App)
                                    └── /api → UIEngine (Port 3001)
                                              └── GeoServer (*************)
```

## 📁 Deployment Structure

```
/path/to/sansabig/
├── docker-compose.yml         ← Main deployment configuration
├── .env.production           ← Environment variables
├── deploy-linux.sh          ← Linux deployment script
├── deploy-uat.ps1           ← Windows deployment script
├── frontend/                ← React application
│   ├── Dockerfile
│   └── .env.production
├── uiengine/               ← Backend API
│   └── Dockerfile
└── nginx/                  ← Reverse proxy
    └── Dockerfile
```

## ⚙️ Configuration Files

### Main Environment (`.env.production`)
```bash
NODE_ENV=production
GEOSERVER_URL=https://*************/geoserver
CORS_ORIGIN=http://*************,http://*************:80,http://*************:3000,http://*************:8080,*
CACHE_TTL=3600
REACT_APP_API_BASE=/api
```

### Frontend Environment (`frontend/.env.production`)
```bash
VITE_API_BASE_URL=/api
VITE_GEOSERVER_URL=https://*************/geoserver
VITE_SERVER_IP=*************
```

## 🔧 Management Commands

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f nginx
docker-compose logs -f uiengine
docker-compose logs -f frontend

# Check service status
docker-compose ps

# Stop all services
docker-compose down

# Restart services
docker-compose restart

# Rebuild and restart
docker-compose up -d --build
```

## 🔍 Health Checks

### Quick Status Check
```bash
# Check if main application is accessible
curl http://*************

# Check backend API health
curl http://*************/api/health

# Check alternative port
curl http://*************:8080
```

### Service Verification
```bash
# Check if services are running
docker-compose ps

# Should show:
# - nginx (port 80:80, 8080:80)
# - uiengine (port 3001:3001)
# - frontend (no external ports)
```

## 🔒 Firewall Configuration

Ensure these ports are open on the Linux server:

```bash
# Required ports
sudo ufw allow 80/tcp    # Main application access
sudo ufw allow 8080/tcp  # Alternative application access

# Optional (for direct backend access during troubleshooting)
sudo ufw allow 3001/tcp  # UIEngine API (can be restricted)
```

## 🚨 Troubleshooting

### Port 80 Already in Use
```bash
# Check what's using port 80
sudo netstat -tlnp | grep :80

# Kill the process if needed
sudo systemctl stop apache2  # or nginx, httpd, etc.
```

### Service Won't Start
```bash
# Check Docker service
sudo systemctl status docker

# Check logs for errors
docker-compose logs

# Rebuild without cache
docker-compose build --no-cache
docker-compose up -d
```

### Can't Access from External IP
```bash
# Check if services are binding to correct interfaces
netstat -tlnp | grep -E ':(80|8080|3001)'

# Should show:
# 0.0.0.0:80 (not 127.0.0.1:80)
# 0.0.0.0:8080
# 0.0.0.0:3001
```

## 📊 Expected Results

After successful deployment:

### ✅ Working
- Application loads at `http://*************`
- Map displays correctly
- Layers load from GeoServer
- Legend functionality works
- No console errors

### 🔧 Ports in Use
- **80**: Nginx (main access)
- **8080**: Nginx (alternative access)
- **3001**: UIEngine API (internal/debugging)

### 📈 Performance
- Fast initial load
- Responsive map interactions
- Efficient GeoServer communication

## 🎯 Final Checklist

- [ ] Services deploy without errors
- [ ] Application accessible via `http://*************`
- [ ] Map loads and displays correctly
- [ ] Layers are discoverable from GeoServer
- [ ] Legend images load properly
- [ ] No console errors in browser
- [ ] API endpoints respond correctly

---

**Ready to deploy!** Use `./deploy-linux.sh` on your Linux server at `*************`.
