// Initialize the map
const map = L.map('map').setView([-28.4796, 24.6987], 6); // Centered on South Africa

// Add base layer
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
}).addTo(map);

// Sample layers (in a real app, these would come from a server)
const layers = {
    "Satellite Imagery": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
        attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
    }),
    "Topographic Map": <PERSON><PERSON>tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
        attribution: 'Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="http://viewfinderpanoramas.org">SRTM</a> | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a> (<a href="https://creativecommons.org/licenses/by-sa/3.0/">CC-BY-SA</a>)'
    }),
    "Administrative Boundaries": L.layerGroup(),
    "Road Network": L.layerGroup()
};

// Add layer control to the sidebar
const layerList = document.querySelector('.layer-list');
Object.keys(layers).forEach(layerName => {
    const layerItem = document.createElement('div');
    layerItem.className = 'layer-item';
    
    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.id = `layer-${layerName.replace(/\s+/g, '-').toLowerCase()}`;
    checkbox.addEventListener('change', function() {
        if (this.checked) {
            map.addLayer(layers[layerName]);
        } else {
            map.removeLayer(layers[layerName]);
        }
    });
    
    const label = document.createElement('label');
    label.htmlFor = checkbox.id;
    label.textContent = layerName;
    
    layerItem.appendChild(checkbox);
    layerItem.appendChild(label);
    layerList.appendChild(layerItem);
});

// Modal functionality
const loginModal = document.getElementById('login-modal');
const loginBtn = document.getElementById('login-btn');
const closeBtn = document.querySelector('.close');

loginBtn.onclick = function() {
    loginModal.style.display = 'block';
}

closeBtn.onclick = function() {
    loginModal.style.display = 'none';
}

window.onclick = function(event) {
    if (event.target == loginModal) {
        loginModal.style.display = 'none';
    }
}

// Basic measure tool
let measureTool = null;
document.getElementById('measure-btn').addEventListener('click', function() {
    if (measureTool) {
        measureTool.remove();
        measureTool = null;
        this.style.backgroundColor = '';
    } else {
        measureTool = new L.Draw.Polyline(map, {
            shapeOptions: {
                color: '#3498db',
                weight: 3
            },
            showLength: true,
            metric: true
        });
        measureTool.enable();
        this.style.backgroundColor = '#27ae60';
    }
});

// Simulate some administrative boundaries (in a real app, this would be from a GeoJSON API)
fetch('https://raw.githubusercontent.com/codeforafrica/data/master/south-africa/geojson/south-africa-provinces.geojson')
    .then(response => response.json())
    .then(data => {
        L.geoJSON(data, {
            style: {
                color: '#e74c3c',
                weight: 2,
                fillOpacity: 0.1
            }
        }).addTo(layers['Administrative Boundaries']);
    });

// Print functionality
document.getElementById('print-btn').addEventListener('click', function() {
    window.print();
});

// Share functionality
document.getElementById('share-btn').addEventListener('click', function() {
    const center = map.getCenter();
    const zoom = map.getZoom();
    const url = `${window.location.href.split('?')[0]}?lat=${center.lat}&lng=${center.lng}&zoom=${zoom}`;
    
    if (navigator.share) {
        navigator.share({
            title: 'Check out this map location',
            text: 'View this location on the Geospatial Portal',
            url: url
        }).catch(err => {
            console.log('Error sharing:', err);
            prompt('Copy this link to share:', url);
        });
    } else {
        prompt('Copy this link to share:', url);
    }
});

// Help button
document.getElementById('help-btn').addEventListener('click', function() {
    alert('This is a geospatial information portal. Use the search box to find locations, toggle layers to view different data, and use tools for measurements and sharing.');
});