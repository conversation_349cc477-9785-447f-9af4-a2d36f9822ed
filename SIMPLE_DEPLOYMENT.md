# 🚀 Simple UAT Deployment Guide

## Quick Start (2 Steps)

### Step 1: Navigate to the Project Root
```powershell
cd "c:\src\saruwe\ESRI\SANSA FLOOD MAPPING\src\sansabig"
```

### Step 2A: Automated Deployment (Recommended)
```powershell
.\deploy-uat.ps1
```

### Step 2B: Manual Deployment
```powershell
docker-compose up -d --build
```

## What Gets Deployed

Your setup includes **3 services** (no database):

1. **UIEngine** (Backend API) - Port 3001
2. **Frontend** (React App) - Served by Nginx
3. **Nginx** (Reverse Proxy) - Port 80

## Architecture Flow
```
User → http://localhost → Nginx → Frontend (React)
                               → /api → UIEngine (Backend)
                                      → GeoServer (External)
```

## Access Points

- **Main Application**: http://localhost
- **Direct Backend** (for testing): http://localhost:3001

## Management Commands

```powershell
# View logs
docker-compose logs -f

# Stop everything
docker-compose down

# Restart services
docker-compose restart

# Rebuild and restart
docker-compose up -d --build
```

## File Structure

```
c:\src\saruwe\ESRI\SANSA FLOOD MAPPING\src\sansabig\
├── docker-compose.yml          ← Main deployment file
├── .env.production             ← Environment settings
├── deploy-uat.ps1              ← Automated deployment script
├── frontend/                   ← React application
├── uiengine/                   ← Backend API
├── nginx/                      ← Reverse proxy
└── [other files...]
```

## Troubleshooting

### Port 80 Busy?
If port 80 is already in use, edit `docker-compose.yml`:
```yaml
ports:
  - "8080:80"  # Change 80 to 8080
```
Then access via: http://localhost:8080

### Check Service Status
```powershell
docker-compose ps
```

### View Individual Service Logs
```powershell
docker-compose logs uiengine
docker-compose logs frontend
docker-compose logs nginx
```

## That's It!

Just run from the root folder:
```powershell
cd "c:\src\saruwe\ESRI\SANSA FLOOD MAPPING\src\sansabig"
.\deploy-uat.ps1
```
