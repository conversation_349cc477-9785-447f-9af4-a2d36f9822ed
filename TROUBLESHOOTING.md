# 🔧 Deployment Troubleshooting Guide

## 🚨 Issue: No Console Output from deploy-linux.sh

### Possible Causes & Solutions

### 1. **Script Permissions**
```bash
# Make sure script is executable
chmod +x deploy-linux.sh

# Check permissions
ls -la deploy-linux.sh
# Should show: -rwxr-xr-x (executable)
```

### 2. **Line Endings (Windows/Linux)**
```bash
# Convert Windows line endings to Linux
dos2unix deploy-linux.sh

# Or use sed
sed -i 's/\r$//' deploy-linux.sh
```

### 3. **Run Script with Verbose Output**
```bash
# Run with bash explicitly
bash -x deploy-linux.sh

# Or with more verbose output
bash -v deploy-linux.sh
```

### 4. **Check Script Syntax**
```bash
# Check for syntax errors
bash -n deploy-linux.sh

# Should return nothing if syntax is OK
```

### 5. **Simple Test First**
```bash
# Test basic script execution
chmod +x test-deployment.sh
./test-deployment.sh
```

## 🧪 Alternative Testing Methods

### Test 1: Direct Commands
```bash
# Test each command individually
echo "Testing Docker..."
docker --version

echo "Testing Docker Compose..."
docker-compose --version

echo "Testing configuration..."
docker-compose config
```

### Test 2: Manual Deployment
```bash
# Skip the script, deploy manually
docker-compose down
docker-compose up -d --build
docker-compose ps
```

### Test 3: Windows Testing (Before Linux)
```powershell
# Run the test script first
.\test-deployment.ps1

# If that works, try manual deployment
docker-compose up -d --build
```

## 🔍 Debug Steps

### Step 1: Basic Environment Check
```bash
# Check current shell
echo $SHELL

# Check if in correct directory
pwd
ls -la docker-compose.yml

# Check if files have correct line endings
file deploy-linux.sh
```

### Step 2: Docker Environment Check
```bash
# Check Docker daemon
sudo systemctl status docker

# Check Docker permissions
docker ps

# Check Docker Compose
docker-compose --version
```

### Step 3: Script Debugging
```bash
# Add debug output to script
#!/bin/bash
set -x  # Enable debug mode
set -e  # Exit on any error

# Then run normally
./deploy-linux.sh
```

## 🎯 Quick Fix Commands

### Make Script Work
```bash
# All-in-one fix
dos2unix deploy-linux.sh
chmod +x deploy-linux.sh
bash -x deploy-linux.sh
```

### Emergency Manual Deployment
```bash
# If script fails, deploy manually
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# Check status
docker-compose ps
docker-compose logs -f
```

## 🌐 Access Points After Deployment

Once deployment succeeds:
- **Primary**: http://*************
- **Secondary**: http://*************:8080
- **API Health**: http://*************/api/health

## 📋 Success Indicators

You should see:
```
✅ Docker and Docker Compose are available
🛑 Stopping existing containers...
🔨 Building and starting services...
⏳ Waiting for services to start...
🔍 Performing health checks...
✅ Nginx is running on port 80
🎉 Deployment Complete!
```

If you don't see these messages, use the debug steps above!
