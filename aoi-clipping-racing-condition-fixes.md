# AOI Clipping Racing Condition Fixes

## Problem Analysis

The user reported a racing condition causing rapid loops when toggling map layers after selecting an AOI. The console showed three main issues:

### 1. **CQL Clipping Failure Loop**
```
❌ CQL clipping failed for "geonode:2024-12-30_sa_mosaic_final2": Raster layer detected via name pattern - CQL likely not supported
📦 BBOX clipping used for "geonode:2024-12-30_sa_mosaic_final2": CQL clipping failed, using BBOX fallback
```

### 2. **React State Update During Render**
```
Warning: Cannot update a component (`ToastProvider`) while rendering a different component (`MapComponent`). 
To locate the bad setState() call inside `MapComponent`, follow the stack trace...
```

### 3. **Maximum Update Depth Exceeded**
```
Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, 
but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
```

## Root Causes

### **Cause 1: Toast Notifications During Render Cycle**
The `showClippingFallback()` function was being called directly during the render cycle when layers were being processed, causing React state updates during rendering.

### **Cause 2: Repeated Capability Fetches**
Layer capabilities were being fetched on every render cycle without caching, causing:
- Network spam
- Repeated state updates
- Infinite re-render loops

### **Cause 3: Duplicate Toast Notifications**
No deduplication mechanism existed, so rapid layer toggles created multiple identical toast notifications.

## Solutions Implemented

### ✅ **Fix 1: Defer Toast Notifications**

**File**: `frontend/src/components/Map/MapComponent.tsx`

**Before**:
```typescript
// Show notification if BBOX fallback was used
if (clippingResult.method === 'BBOX' && clippingResult.fallbackReason) {
  console.warn(`📦 BBOX clipping used for "${layer.name}": ${clippingResult.fallbackReason}`);
  showClippingFallback(layer.name, clippingResult.fallbackReason, 'BBOX'); // ❌ setState during render
}
```

**After**:
```typescript
// Show notification if BBOX fallback was used (defer to avoid render cycle issues)
if (clippingResult.method === 'BBOX' && clippingResult.fallbackReason) {
  console.warn(`📦 BBOX clipping used for "${layer.name}": ${clippingResult.fallbackReason}`);
  // Defer toast notification to avoid setState during render
  setTimeout(() => {
    showClippingFallback(layer.name, clippingResult.fallbackReason!, 'BBOX');
  }, 0);
}
```

**Impact**: Prevents React state updates during render cycle by deferring toast notifications to the next tick.

### ✅ **Fix 2: Add Capability Caching**

**File**: `frontend/src/components/Map/MapComponent.tsx`

**Added State**:
```typescript
// Cache for layer capabilities to prevent repeated fetches
const [layerCapabilities, setLayerCapabilities] = useState<{ [key: string]: { supportsCQL: boolean } | null }>({});
```

**Before**:
```typescript
// Fetch capabilities on every render (❌ causes loops)
getLayerCapabilitiesService(layer.name)
  .then(capabilities => {
    console.log(`🔍 Layer capabilities for "${layer.name}":`, capabilities);
  })
  .catch(capError => {
    console.warn(`⚠️ Failed to fetch capabilities for "${layer.name}":`, capError);
  });

supportsCQL = undefined; // Always undefined
```

**After**:
```typescript
// Check cache first to avoid repeated fetches
if (layerCapabilities[layer.name] !== undefined) {
  supportsCQL = layerCapabilities[layer.name]?.supportsCQL;
  console.log(`🔍 Using cached capabilities for "${layer.name}": supportsCQL=${supportsCQL}`);
} else {
  // Fetch capabilities if not cached (async, won't block rendering)
  const layerCapabilitiesService = getLayerCapabilitiesService();
  layerCapabilitiesService.getLayerCapabilities(layer.name)
    .then(capabilities => {
      if (capabilities) {
        // Cache the result
        setLayerCapabilities(prev => ({
          ...prev,
          [layer.name]: { supportsCQL: capabilities.supports.cql }
        }));
      }
    })
    .catch(capError => {
      // Cache the failure to prevent repeated attempts
      setLayerCapabilities(prev => ({
        ...prev,
        [layer.name]: null
      }));
    });
  
  // Use undefined to trigger heuristic fallback on first render
  supportsCQL = undefined;
}
```

**Impact**: 
- Prevents repeated capability fetches
- Caches both successful and failed capability requests
- Provides immediate capability access after first fetch

### ✅ **Fix 3: Improve Toast Context**

**File**: `frontend/src/contexts/ToastContext.tsx`

**Duplicate Prevention**:
```typescript
setToasts(prev => {
  // Prevent duplicate toasts with same title and message
  const isDuplicate = prev.some(existingToast => 
    existingToast.title === newToast.title && 
    existingToast.message === newToast.message
  );
  
  if (isDuplicate) {
    return prev; // Don't add duplicate
  }
  
  return [...prev, newToast];
});
```

**Improved Auto-Removal**:
```typescript
// Auto-remove non-persistent toasts
if (!newToast.persistent) {
  setTimeout(() => {
    setToasts(currentToasts => 
      currentToasts.filter(t => t.id !== newToast.id)
    );
  }, newToast.duration || 5000);
}
```

**Debounced Clipping Notifications**:
```typescript
// Specific method for clipping notifications with debouncing
const showClippingFallback = useCallback((layerName: string, reason: string, method: string) => {
  const title = `${method} Clipping Used`;
  const message = `Layer "${layerName}": ${reason}`;
  
  // Debounce rapid notifications for the same layer
  const debounceKey = `clipping-${layerName}`;
  
  // Clear any existing timeout for this layer
  if ((window as any)[debounceKey]) {
    clearTimeout((window as any)[debounceKey]);
  }
  
  // Set new timeout
  (window as any)[debounceKey] = setTimeout(() => {
    addToast({
      type: 'warning',
      title,
      message,
      duration: 7000, // Longer duration for important clipping info
    });
    delete (window as any)[debounceKey];
  }, 100); // 100ms debounce
}, [addToast]);
```

**Impact**:
- Prevents duplicate toast notifications
- Debounces rapid notifications for the same layer
- Improves auto-removal mechanism to prevent memory leaks

## How the `supportsCQL` Flag Works

### **Three-Tier Decision Process**

```mermaid
graph TD
    A[Layer Toggle] --> B[Check Cache]
    B --> C{Cache Hit?}
    C -->|Yes| D[Use Cached supportsCQL]
    C -->|No| E[Fetch Capabilities Async]
    E --> F[Cache Result]
    E --> G[Use undefined for Heuristic]
    
    D --> H{supportsCQL Value?}
    G --> H
    
    H -->|true| I[Try CQL_FILTER]
    H -->|false| J[Skip to BBOX]
    H -->|undefined| K[Use Heuristic]
    
    K --> L{Layer Name Pattern?}
    L -->|Raster Pattern| M[Use BBOX]
    L -->|Vector Pattern| N[Try CQL_FILTER]
    
    I --> O{CQL Success?}
    N --> O
    O -->|Yes| P[Precise Clipping]
    O -->|No| Q[BBOX Fallback + Toast]
    
    J --> R[BBOX Clipping]
    M --> R
    Q --> S[Deferred Toast Notification]
```

### **Flag Values and Behavior**

| `supportsCQL` Value | Source | Behavior |
|-------------------|--------|----------|
| `true` | Layer capabilities API | ✅ Use CQL_FILTER for precise clipping |
| `false` | Layer capabilities API | 📦 Skip directly to BBOX clipping |
| `undefined` | Cache miss or capability fetch failure | 🔍 Use heuristic (name pattern matching) |

### **Heuristic Patterns**

**Raster Layer Patterns** (→ BBOX):
```typescript
['mosaic', 'imagery', 'satellite', 'tiff', 'raster', 'rgb', 'infrared', 
 'coverage', 'landsat', 'sentinel', 'modis', 'dem', 'elevation']
```

**Vector Layer Patterns** (→ CQL_FILTER):
- All other layer names not matching raster patterns

## Testing the Fixes

### **Test 1: No More Racing Conditions**
1. Select an AOI (administrative boundary or drawn polygon)
2. Rapidly toggle multiple layers on/off
3. **Expected**: No React warnings in console
4. **Expected**: Toast notifications appear without causing loops

### **Test 2: Capability Caching**
1. Toggle a layer on/off multiple times
2. **Expected**: First toggle fetches capabilities, subsequent toggles use cache
3. **Expected**: Console shows "Using cached capabilities" messages

### **Test 3: Debounced Notifications**
1. Rapidly toggle the same raster layer multiple times
2. **Expected**: Only one toast notification appears (debounced)
3. **Expected**: No duplicate notifications

### **Test 4: Proper Fallback Behavior**
1. Toggle vector layer: Should attempt CQL_FILTER
2. Toggle raster layer: Should use BBOX with toast notification
3. **Expected**: Appropriate clipping method based on layer type

## Performance Improvements

- ✅ **Eliminated repeated capability fetches** (network optimization)
- ✅ **Prevented render loops** (CPU optimization)
- ✅ **Reduced duplicate notifications** (UX optimization)
- ✅ **Added intelligent caching** (memory optimization)
- ✅ **Implemented debouncing** (prevents notification spam)

The racing condition has been resolved while maintaining all the AOI clipping functionality and user notification features.

## 🔧 **Additional Fixes Applied**

### **Fix 4: Moved Capability Fetching to useEffect**

**Problem**: Capability fetching was happening during render cycle, causing loops.

**Solution**: Moved capability fetching to a dedicated useEffect that runs when selectedLayerNames change.

```typescript
// Fetch capabilities for selected layers when they change
useEffect(() => {
  const layerCapabilitiesService = getLayerCapabilitiesService();

  selectedLayerNames.forEach(layerName => {
    // Skip if already cached or being fetched
    if (layerCapabilities[layerName] !== undefined) {
      return;
    }

    // Fetch capabilities and cache result
    layerCapabilitiesService.getLayerCapabilities(layerName)
      .then(capabilities => {
        setLayerCapabilities(prev => ({
          ...prev,
          [layerName]: { supportsCQL: capabilities.supports.cql }
        }));
      })
      .catch(capError => {
        // Cache the failure to prevent repeated attempts
        setLayerCapabilities(prev => ({
          ...prev,
          [layerName]: null
        }));
      });
  });
}, [selectedLayerNames.join(',')]);
```

### **Fix 5: Notification Deduplication with Ref Tracking**

**Problem**: Same notification appearing repeatedly during layer toggles.

**Solution**: Added ref-based tracking to show each notification only once per layer/reason combination.

```typescript
// Track which layers have had notifications shown to prevent spam
const notificationShownRef = useRef<Set<string>>(new Set());

// Only show notification once per layer to prevent spam
const notificationKey = `${layer.name}-${clippingResult.fallbackReason}`;
if (!notificationShownRef.current.has(notificationKey)) {
  notificationShownRef.current.add(notificationKey);

  // Defer toast notification to avoid setState during render
  setTimeout(() => {
    showClippingFallback(layer.name, clippingResult.fallbackReason!, 'BBOX');
  }, 0);
}
```

### **Fix 6: Improved Fallback Reason Messages**

**Problem**: Generic fallback reasons didn't explain why BBOX was used.

**Solution**: Enhanced aoiClippingService to provide specific, user-friendly reasons.

**Before**:
```
"CQL clipping failed, using BBOX fallback"
```

**After**:
```
"Raster layer - precise clipping not yet implemented, using BBOX"
"Layer capabilities indicate CQL not supported"
"WKT too long: 8500 characters exceeds 6000 limit"
```

### **Fix 7: Clear Notification Tracking on State Changes**

**Problem**: Notifications wouldn't reappear when AOI or layers changed.

**Solution**: Clear notification tracking when relevant state changes.

```typescript
// Clear notification tracking when layers or AOI changes
useEffect(() => {
  notificationShownRef.current.clear();
}, [selectedLayerNames.join(','), aoiData?.feature?.id, aoiData?.bounds]);
```

## 🎯 **Final Result**

After these fixes:

1. ✅ **No more racing conditions** - Capability fetching moved to useEffect
2. ✅ **No duplicate notifications** - Ref-based deduplication prevents spam
3. ✅ **Clear, specific reasons** - Users see exactly why BBOX was used
4. ✅ **Proper state management** - Notifications reset when appropriate
5. ✅ **Performance optimized** - Cached capabilities prevent repeated fetches

**Expected Toast Messages**:
- "Layer 'geonode:2024-12-30_sa_mosaic_final2': Raster layer - precise clipping not yet implemented, using BBOX"
- "Layer 'vector_layer': Layer capabilities indicate CQL not supported"
- "Layer 'large_polygon': WKT too long: 8500 characters exceeds 6000 limit"

The system now provides clear, actionable feedback to users while maintaining stable performance.
