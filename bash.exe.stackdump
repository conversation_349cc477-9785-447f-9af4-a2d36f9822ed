Stack trace:
Frame         Function      Args
0007FFFFABB0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF9AB0) msys-2.0.dll+0x1FEBA
0007FFFFABB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE88) msys-2.0.dll+0x67F9
0007FFFFABB0  000210046832 (000210285FF9, 0007FFFFAA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFABB0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFABB0  0002100690B4 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFAE90  00021006A49D (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8C8F50000 ntdll.dll
7FF8C7E00000 KERNEL32.DLL
7FF8C6510000 KERNELBASE.dll
7FF8C7AA0000 USER32.dll
7FF8C6370000 win32u.dll
7FF8C81B0000 GDI32.dll
7FF8C6AB0000 gdi32full.dll
7FF8C6100000 msvcp_win.dll
7FF8C61D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8C8590000 advapi32.dll
7FF8C81F0000 msvcrt.dll
7FF8C6E60000 sechost.dll
7FF8C61A0000 bcrypt.dll
7FF8C7C60000 RPCRT4.dll
7FF8C57D0000 CRYPTBASE.DLL
7FF8C6A30000 bcryptPrimitives.dll
7FF8C6F50000 IMM32.DLL
