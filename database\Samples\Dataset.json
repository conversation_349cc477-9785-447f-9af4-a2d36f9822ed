{"advertised": true, "distributor": [], "charset": "UTF-8", "metadata_only": false, "group": null, "alternate": "geonode:national_ecds", "share_count": "0", "raw_abstract": "", "processor": [], "title": "national_ecds", "spatial_representation_type": null, "raw_supplemental_information": "No information provided", "ll_bbox_polygon": {"type": "Polygon", "coordinates": [[[16.48684, -34.74788], [16.48684, -22.33098], [32.8322, -22.33098], [32.8322, -34.74788], [16.48684, -34.74788]]]}, "popular_count": "0", "is_published": true, "embed_url": "https://*************/datasets/geonode:national_ecds/embed", "workspace": "geon<PERSON>", "date_type": "publication", "raw_constraints_other": "None", "has_elevation": false, "has_time": false, "owner": {"pk": 1000, "username": "admin", "first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "avatar": "https://*************/static/geonode/img/avatar.png", "is_superuser": true, "is_staff": true, "email": "<EMAIL>", "link": "https://*************/api/v2/users/1000"}, "last_updated": "2025-06-12T12:08:48.254848Z", "sourcetype": "LOCAL", "extent": {"coords": [16.48684, -34.74788, 32.8322, -22.33098], "srid": "EPSG:4326"}, "links": [{"extension": "xml", "link_type": "metadata", "name": "Atom", "mime": "text/xml", "url": "https://*************/catalogue/csw?request=GetRecordById&service=CSW&version=2.0.2&id=9563bea8-f942-490d-82cd-9ab83bc44e30&outputschema=http%3A%2F%2Fwww.w3.org%2F2005%2FAtom&elementsetname=full"}, {"extension": "xml", "link_type": "metadata", "name": "DIF", "mime": "text/xml", "url": "https://*************/catalogue/csw?request=GetRecordById&service=CSW&version=2.0.2&id=9563bea8-f942-490d-82cd-9ab83bc44e30&outputschema=http%3A%2F%2Fgcmd.gsfc.nasa.gov%2FAboutus%2Fxml%2Fdif%2F&elementsetname=full"}, {"extension": "xml", "link_type": "metadata", "name": "Dublin Core", "mime": "text/xml", "url": "https://*************/catalogue/csw?request=GetRecordById&service=CSW&version=2.0.2&id=9563bea8-f942-490d-82cd-9ab83bc44e30&outputschema=http%3A%2F%2Fwww.opengis.net%2Fcat%2Fcsw%2F2.0.2&elementsetname=full"}, {"extension": "xml", "link_type": "metadata", "name": "ebRIM", "mime": "text/xml", "url": "https://*************/catalogue/csw?request=GetRecordById&service=CSW&version=2.0.2&id=9563bea8-f942-490d-82cd-9ab83bc44e30&outputschema=urn%3Aoasis%3Anames%3Atc%3Aebxml-regrep%3Axsd%3Arim%3A3.0&elementsetname=full"}, {"extension": "xml", "link_type": "metadata", "name": "FGDC", "mime": "text/xml", "url": "https://*************/catalogue/csw?request=GetRecordById&service=CSW&version=2.0.2&id=9563bea8-f942-490d-82cd-9ab83bc44e30&outputschema=http%3A%2F%2Fwww.opengis.net%2Fcat%2Fcsw%2Fcsdgm&elementsetname=full"}, {"extension": "xml", "link_type": "metadata", "name": "ISO", "mime": "text/xml", "url": "https://*************/catalogue/csw?request=GetRecordById&service=CSW&version=2.0.2&id=9563bea8-f942-490d-82cd-9ab83bc44e30&outputschema=http%3A%2F%2Fwww.isotc211.org%2F2005%2Fgmd&elementsetname=full"}, {"extension": "xml", "link_type": "metadata", "name": "ISO with XSL", "mime": "text/xml", "url": "https://*************/showmetadata/xsl/49"}, {"extension": "shx", "link_type": "data", "name": "national_ecds", "mime": "", "url": "https://*************/api/v2/assets/32/link", "extras": {"type": "asset", "content": {"title": "Original", "description": null, "type": "shp", "download_url": "https://*************/api/v2/assets/32/download"}}}, {"extension": "jpg", "link_type": "image", "name": "JPEG", "mime": "image/jpeg", "url": "https://*************/geoserver/ows?service=WMS&request=GetMap&layers=geonode%3Anational_ecds&format=image%2Fjpeg&height=550&width=550&srs=EPSG%3A4326&bbox=16.4868400%2C-34.7478800%2C32.8322000%2C-22.3309800"}, {"extension": "csv", "link_type": "data", "name": "CSV", "mime": "csv", "url": "https://*************/geoserver/ows?service=WFS&version=1.0.0&request=GetFeature&typename=geonode%3Anational_ecds&outputFormat=csv&srs=EPSG%3A4326"}, {"extension": "excel", "link_type": "data", "name": "Excel", "mime": "excel", "url": "https://*************/geoserver/ows?service=WFS&version=1.0.0&request=GetFeature&typename=geonode%3Anational_ecds&outputFormat=excel&srs=EPSG%3A4326"}, {"extension": "json", "link_type": "data", "name": "GeoJSON", "mime": "json", "url": "https://*************/geoserver/ows?service=WFS&version=1.0.0&request=GetFeature&typename=geonode%3Anational_ecds&outputFormat=json&srs=EPSG%3A4326&srsName=EPSG%3A4326"}, {"extension": "html", "link_type": "html", "name": "geonode:national_ecds", "mime": "text/html", "url": "https://*************/catalogue/#/dataset/49"}, {"extension": "png", "link_type": "image", "name": "Legend", "mime": "image/png", "url": "https://*************/geoserver/ows?service=WMS&request=GetLegendGraphic&format=image/png&WIDTH=20&HEIGHT=20&LAYER=geonode:national_ecds&STYLE=national_ecds&version=1.3.0&sld_version=1.1.0&legend_options=fontAntiAliasing:true;fontSize:12;forceLabels:on"}, {"extension": "html", "link_type": "OGC:WMS", "name": "OGC WMS: geonode Service", "mime": "text/html", "url": "https://*************/geoserver/ows"}, {"extension": "html", "link_type": "OGC:WFS", "name": "OGC WFS: geonode Service", "mime": "text/html", "url": "https://*************/geoserver/ows"}, {"extension": "pdf", "link_type": "image", "name": "PDF", "mime": "application/pdf", "url": "https://*************/geoserver/ows?service=WMS&request=GetMap&layers=geonode%3Anational_ecds&format=application%2Fpdf&height=550&width=550&srs=EPSG%3A4326&bbox=16.4868400%2C-34.7478800%2C32.8322000%2C-22.3309800"}, {"extension": "png", "link_type": "image", "name": "PNG", "mime": "image/png", "url": "https://*************/geoserver/ows?service=WMS&request=GetMap&layers=geonode%3Anational_ecds&format=image%2Fpng&height=550&width=550&srs=EPSG%3A4326&bbox=16.4868400%2C-34.7478800%2C32.8322000%2C-22.3309800"}, {"extension": "zip", "link_type": "data", "name": "Zipped Shapefile", "mime": "SHAPE-ZIP", "url": "https://*************/geoserver/ows?service=WFS&version=1.0.0&request=GetFeature&typename=geonode%3Anational_ecds&outputFormat=SHAPE-ZIP&srs=EPSG%3A4326&format_options=charset%3AUTF-8"}, {"extension": "gml", "link_type": "data", "name": "GML 2.0", "mime": "gml2", "url": "https://*************/geoserver/ows?service=WFS&version=1.0.0&request=GetFeature&typename=geonode%3Anational_ecds&outputFormat=gml2&srs=EPSG%3A4326"}, {"extension": "gml", "link_type": "data", "name": "GML 3.1.1", "mime": "text/xml; subtype=gml/3.1.1", "url": "https://*************/geoserver/ows?service=WFS&version=1.0.0&request=GetFeature&typename=geonode%3Anational_ecds&outputFormat=text%2Fxml%3B+subtype%3Dgml%2F3.1.1&srs=EPSG%3A4326"}, {"extension": "png", "link_type": "image", "name": "<PERSON><PERSON><PERSON><PERSON>", "mime": "image/png", "url": "https://*************/uploaded/thumbs/dataset-9563bea8-f942-490d-82cd-9ab83bc44e30-thumb-da4f2684-2781-433f-972e-ae5473c73345.jpg"}], "elevation_regex": null, "date": "2025-06-12T12:08:44.348906Z", "rating": "0", "link": "https://*************/api/v2/datasets/49", "originator": [], "default_style": {"pk": 32, "name": "national_ecds", "workspace": "geon<PERSON>", "sld_title": "Red Square Point", "sld_url": "https://*************/geoserver/rest/workspaces/geonode/styles/national_ecds.sld"}, "state": "PROCESSED", "is_mosaic": false, "maintenance_frequency": null, "regions": [], "temporal_extent_start": null, "category": null, "data_quality_statement": null, "temporal_extent_end": null, "metadata_uploaded_preserve": false, "resource_type": "dataset", "favorite": false, "tkeywords": [], "perms": [], "created": "2025-06-12T12:08:44.368740Z", "bbox_polygon": {"type": "Polygon", "coordinates": [[[16.48684, -34.74788], [16.48684, -22.33098], [32.8322, -22.33098], [32.8322, -34.74788], [16.48684, -34.74788]]]}, "uuid": "9563bea8-f942-490d-82cd-9ab83bc44e30", "metadata_author": [{"pk": 1000, "username": "admin", "first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "avatar": "https://*************/static/geonode/img/avatar.png", "is_superuser": true, "is_staff": true, "email": "<EMAIL>", "link": "https://*************/api/v2/users/1000"}], "processed": true, "is_copyable": true, "name": "national_ecds", "polymorphic_ctype_id": "62", "download_url": "https://*************/datasets/geonode:national_ecds/dataset_download", "featureinfo_custom_template": "<div style=\"overflow-x:hidden\"><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">ogc_fid:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['ogc_fid']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">datayear:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['datayear']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">province:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['province']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">provincecd:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['provincecd']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">ecd_name:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['ecd_name']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">status:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['status']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">dsd_status:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['dsd_status']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">ownerland:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['ownerland']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">ownerbuild:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['ownerbuild']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">gis_long:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['gis_long']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">gis_lat:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['gis_lat']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">dmunname:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['dmunname']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">lmunname:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['lmunname']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">ward_id:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['ward_id']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">addressee:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['addressee']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">township_v:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['township_v']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">surburb:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['surburb']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">town_city:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['town_city']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">streetaddr:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['streetaddr']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">postaladdr:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['postaladdr']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">telephone:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['telephone']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">registrati:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['registrati']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">ecd_learne:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['ecd_learne']}</div></div><div class=\"row\"><div class=\"col-xs-6\" style=\"font-weight: bold; word-wrap: break-word;\">ecd_staff2:</div>                             <div class=\"col-xs-6\" style=\"word-wrap: break-word;\">${properties['ecd_staff2']}</div></div></div>", "keywords": [], "license": {"identifier": "not_specified"}, "poc": [{"pk": 1000, "username": "admin", "first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "avatar": "https://*************/static/geonode/img/avatar.png", "is_superuser": true, "is_staff": true, "email": "<EMAIL>", "link": "https://*************/api/v2/users/1000"}], "raw_purpose": "None", "attribution": null, "featured": false, "store": "sansa_flood_risk_data", "publisher": [], "styles": [{"pk": 32, "name": "national_ecds", "workspace": "geon<PERSON>", "sld_title": "Red Square Point", "sld_url": "https://*************/geoserver/rest/workspaces/geonode/styles/national_ecds.sld"}], "constraints_other": null, "thumbnail_url": "https://*************/uploaded/thumbs/dataset-9563bea8-f942-490d-82cd-9ab83bc44e30-thumb-da4f2684-2781-433f-972e-ae5473c73345.jpg", "resource_provider": [], "download_urls": [{"url": "https://*************/api/v2/assets/32/download", "ajax_safe": true, "default": true}], "resource_user": [], "raw_data_quality_statement": "None", "restriction_code_type": null, "edition": null, "pk": "49", "srid": "EPSG:4326", "detail_url": "https://*************/catalogue/#/dataset/49", "principal_investigator": [], "language": "eng", "abstract": "", "custodian": [], "ptype": "gxp_wmscsource", "time_regex": null, "supplemental_information": "No information provided", "is_approved": true, "doi": null, "subtype": "vector", "purpose": null}