-- Initialize <PERSON>GIS extension and create spatial tables for SANSA Flood Mapping

-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;
CREATE EXTENSION IF NOT EXISTS postgis_raster;

-- <PERSON><PERSON> enum types
CREATE TYPE roi_status AS ENUM ('active', 'archived', 'processing');
CREATE TYPE analysis_type AS ENUM ('flood_extent', 'water_level', 'damage_assessment', 'temporal_change');

-- Create ROI (Region of Interest) table for storing user-drawn polygons
CREATE TABLE regions_of_interest (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    geometry GEOMETRY(POLYGON, 4326) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by <PERSON><PERSON><PERSON><PERSON>(100) DEFAULT 'system',
    status roi_status DEFAULT 'active',
    metadata JSONB DEFAULT '{}'::jsonb,
    area_sqkm DECIMAL GENERATED ALWAYS AS (ST_Area(geometry::geography) / 1000000) STORED
);

-- Create spatial analysis results table
CREATE TABLE spatial_analyses (
    id SERIAL PRIMARY KEY,
    roi_id INTEGER REFERENCES regions_of_interest(id) ON DELETE CASCADE,
    analysis_type analysis_type NOT NULL,
    analysis_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    start_date DATE,
    end_date DATE,
    results JSONB NOT NULL DEFAULT '{}'::jsonb,
    geometry GEOMETRY(MULTIPOLYGON, 4326),
    metadata JSONB DEFAULT '{}'::jsonb,
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create flood events table for storing flood occurrences
CREATE TABLE flood_events (
    id SERIAL PRIMARY KEY,
    event_name VARCHAR(255) NOT NULL,
    event_date DATE NOT NULL,
    severity VARCHAR(50),
    affected_geometry GEOMETRY(MULTIPOLYGON, 4326),
    water_level_m DECIMAL(10,3),
    damage_estimate_usd DECIMAL(15,2),
    population_affected INTEGER,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create water level measurements table
CREATE TABLE water_measurements (
    id SERIAL PRIMARY KEY,
    measurement_point GEOMETRY(POINT, 4326) NOT NULL,
    measurement_date TIMESTAMP WITH TIME ZONE NOT NULL,
    water_level_m DECIMAL(10,3) NOT NULL,
    flow_rate_cms DECIMAL(10,3),
    temperature_c DECIMAL(5,2),
    quality_indicators JSONB DEFAULT '{}'::jsonb,
    source VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Authentication tables for Phase 2/3 integration
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'viewer',
    notification_preferences JSONB DEFAULT '{"email": true, "websocket": true, "sms": false}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Alert configuration tables
CREATE TABLE alert_rules (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    dataset_id VARCHAR(100) NOT NULL, -- References dataset from catalog API
    threshold_value DECIMAL(10,4) NOT NULL,
    threshold_operator VARCHAR(10) NOT NULL CHECK (threshold_operator IN ('>', '<', '>=', '<=', '=', '!=')),
    condition_field VARCHAR(100) DEFAULT 'value', -- Field name to monitor in dataset
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    roi_id INTEGER REFERENCES regions_of_interest(id) ON DELETE SET NULL,
    notification_channels TEXT[] DEFAULT ARRAY['websocket', 'email'],
    is_active BOOLEAN DEFAULT true,
    polling_interval_minutes INTEGER DEFAULT 5,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Alert history and events
CREATE TABLE alert_events (
    id SERIAL PRIMARY KEY,
    alert_rule_id INTEGER REFERENCES alert_rules(id) ON DELETE CASCADE,
    triggered_value DECIMAL(10,4) NOT NULL,
    threshold_value DECIMAL(10,4) NOT NULL,
    operator_used VARCHAR(10) NOT NULL,
    dataset_snapshot JSONB, -- Store relevant dataset info at trigger time
    triggered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    acknowledged_by INTEGER REFERENCES users(id),
    notification_status JSONB DEFAULT '{"email": "pending", "websocket": "pending", "sms": "pending"}'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Alert notification log for delivery tracking
CREATE TABLE alert_notifications (
    id SERIAL PRIMARY KEY,
    alert_event_id INTEGER REFERENCES alert_events(id) ON DELETE CASCADE,
    notification_type VARCHAR(20) NOT NULL CHECK (notification_type IN ('email', 'websocket', 'sms')),
    recipient VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed')),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create spatial indexes for better performance
CREATE INDEX idx_roi_geometry ON regions_of_interest USING GIST (geometry);
CREATE INDEX idx_roi_status ON regions_of_interest (status);
CREATE INDEX idx_roi_created_at ON regions_of_interest (created_at);

CREATE INDEX idx_analysis_roi_id ON spatial_analyses (roi_id);
CREATE INDEX idx_analysis_date ON spatial_analyses (analysis_date);
CREATE INDEX idx_analysis_type ON spatial_analyses (analysis_type);
CREATE INDEX idx_analysis_geometry ON spatial_analyses USING GIST (geometry);

CREATE INDEX idx_flood_events_date ON flood_events (event_date);
CREATE INDEX idx_flood_events_geometry ON flood_events USING GIST (affected_geometry);

CREATE INDEX idx_water_measurements_point ON water_measurements USING GIST (measurement_point);
CREATE INDEX idx_water_measurements_date ON water_measurements (measurement_date);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_roi_updated_at BEFORE UPDATE ON regions_of_interest
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for demonstration
INSERT INTO regions_of_interest (name, description, geometry, created_by) VALUES
('Vaal River Basin', 'Critical flood monitoring area around Vaal River', 
    ST_GeomFromText('POLYGON((27.5 -26.5, 28.5 -26.5, 28.5 -27.5, 27.5 -27.5, 27.5 -26.5))', 4326), 
    'system'),
('Johannesburg Urban Area', 'Urban flood risk assessment zone for Johannesburg',
    ST_GeomFromText('POLYGON((27.8 -26.0, 28.3 -26.0, 28.3 -26.4, 27.8 -26.4, 27.8 -26.0))', 4326),
    'system'),
('Orange River Delta', 'Coastal flood monitoring region',
    ST_GeomFromText('POLYGON((16.5 -28.5, 17.0 -28.5, 17.0 -29.0, 16.5 -29.0, 16.5 -28.5))', 4326),
    'system');

-- Insert sample flood events
INSERT INTO flood_events (event_name, event_date, severity, affected_geometry, water_level_m, population_affected) VALUES
('Vaal River Flood 2024-01', '2024-01-15', 'Major', 
    ST_GeomFromText('MULTIPOLYGON(((27.6 -26.6, 28.4 -26.6, 28.4 -27.4, 27.6 -27.4, 27.6 -26.6)))', 4326),
    4.2, 15000),
('Johannesburg Flash Flood', '2024-02-20', 'Moderate',
    ST_GeomFromText('MULTIPOLYGON(((27.9 -26.1, 28.2 -26.1, 28.2 -26.3, 27.9 -26.3, 27.9 -26.1)))', 4326),
    2.1, 8500);

-- Create view for easy ROI queries with statistics
CREATE VIEW roi_with_stats AS
SELECT 
    r.*,
    COUNT(sa.id) as analysis_count,
    MAX(sa.analysis_date) as last_analysis_date,
    COUNT(CASE WHEN sa.analysis_type = 'flood_extent' THEN 1 END) as flood_analyses,
    COUNT(CASE WHEN sa.analysis_type = 'water_level' THEN 1 END) as water_level_analyses
FROM regions_of_interest r
LEFT JOIN spatial_analyses sa ON r.id = sa.roi_id
WHERE r.status = 'active'
GROUP BY r.id, r.name, r.description, r.geometry, r.created_at, r.updated_at, r.created_by, r.status, r.metadata, r.area_sqkm;

-- Grant permissions (adjust as needed for your setup)
GRANT ALL ON ALL TABLES IN SCHEMA public TO sansa_user;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO sansa_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO sansa_user;
