// Debug script for boundary filter requests
const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api/ows'; // Change this if your API runs on a different URL

async function testWardLayer() {
  console.log('Testing wards_2020 layer...');
  
  try {
    // First, try to query the layer itself without filters to see if it exists
    const response = await axios.get(API_BASE_URL, {
      params: {
        service: 'WFS',
        version: '1.0.0',
        request: 'GetFeature',
        typeName: 'geonode:wards_2020',
        outputFormat: 'application/json',
        maxFeatures: 1
      },
      timeout: 30000
    });
    
    console.log('Ward layer test response:', {
      status: response.status,
      hasData: !!response.data,
      hasFeatures: Array.isArray(response.data?.features),
      featureCount: response.data?.features?.length || 0
    });
    
    // If we have a feature, log its properties to understand the structure
    if (response.data?.features?.length > 0) {
      console.log('Sample ward feature properties:', response.data.features[0].properties);
    }
    
    return response.data;
  } catch (error) {
    console.error('Ward layer test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}

// Test the municipal boundaries layer
async function testMunicipalBoundariesLayer() {
  console.log('Testing municipal boundaries layer...');
  
  try {
    const response = await axios.get(API_BASE_URL, {
      params: {
        service: 'WFS',
        version: '1.0.0',
        request: 'GetFeature',
        typeName: 'geonode:south_africa_municipal_boundaries',
        outputFormat: 'application/json',
        maxFeatures: 1
      },
      timeout: 30000
    });
    
    console.log('Municipal layer test response:', {
      status: response.status,
      hasData: !!response.data,
      hasFeatures: Array.isArray(response.data?.features),
      featureCount: response.data?.features?.length || 0
    });
    
    // If we have a feature, log its properties to understand the structure
    if (response.data?.features?.length > 0) {
      console.log('Sample municipal feature properties:', response.data.features[0].properties);
    }
    
    return response.data;
  } catch (error) {
    console.error('Municipal layer test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}

// Execute tests
async function runTests() {
  console.log('Starting boundary filter tests...');
  
  const wardData = await testWardLayer();
  const municipalData = await testMunicipalBoundariesLayer();
  
  console.log('Tests completed');
  
  if (wardData) {
    console.log('Ward layer is available');
  } else {
    console.log('Ward layer is NOT available or returning errors');
  }
  
  if (municipalData) {
    console.log('Municipal layer is available');
  } else {
    console.log('Municipal layer is NOT available or returning errors');
  }
}

runTests();
