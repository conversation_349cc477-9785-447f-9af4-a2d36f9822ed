#!/bin/bash

# SANSA Flood Mapping System - Linux Server Deployment
# Target Server: *************
# Services: UIEngine + Frontend + <PERSON>inx (No Database)

echo "🚀 SANSA Flood Mapping System - Linux Server Deployment"
echo "======================================================="
echo "🖥️  Target Server: *************"
echo "📦 Services: UIEngine + Frontend + Nginx"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed. Please install Docker first.${NC}"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose is not installed. Please install Docker Compose first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker and Docker Compose are available${NC}"

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    echo -e "${YELLOW}⚠️  .env.production not found, using defaults...${NC}"
fi

# Stop any existing containers
echo -e "${YELLOW}🛑 Stopping existing containers...${NC}"
docker-compose down

# Build and start services
echo -e "${GREEN}🔨 Building and starting services...${NC}"
docker-compose up -d --build

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to start services${NC}"
    exit 1
fi

# Wait for services to start
echo -e "${YELLOW}⏳ Waiting for services to start (30 seconds)...${NC}"
sleep 30

# Health checks
echo -e "${CYAN}🔍 Performing health checks...${NC}"

# Check if Nginx is running on port 80
if curl -f http://localhost:80 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Nginx is running on port 80${NC}"
    PORT_80_STATUS="✅ Available"
else
    echo -e "${YELLOW}⚠️  Port 80 check failed${NC}"
    PORT_80_STATUS="❌ Not available"
fi

# Check if Nginx is running on port 8080 (alternative)
if curl -f http://localhost:8080 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Nginx is running on port 8080${NC}"
    PORT_8080_STATUS="✅ Available"
else
    echo -e "${YELLOW}⚠️  Port 8080 check failed${NC}"
    PORT_8080_STATUS="❌ Not available"
fi

# Check UIEngine backend
if curl -f http://localhost:3001/api/health >/dev/null 2>&1; then
    echo -e "${GREEN}✅ UIEngine (Backend API) is running${NC}"
    BACKEND_STATUS="✅ Running"
else
    echo -e "${YELLOW}⚠️  UIEngine direct check failed (may be normal)${NC}"
    BACKEND_STATUS="⚠️  Check through proxy"
fi

# Show container status
echo -e "${CYAN}📊 Container Status:${NC}"
docker-compose ps

echo ""
echo -e "${GREEN}🎉 Deployment Complete!${NC}"
echo "=============================="
echo ""
echo -e "${CYAN}🌐 Access Points:${NC}"
echo -e "  • Primary:   ${GREEN}http://*************${NC} $PORT_80_STATUS"
echo -e "  • Secondary: ${GREEN}http://*************:8080${NC} $PORT_8080_STATUS"
echo ""
echo -e "${CYAN}🔧 Service Status:${NC}"
echo -e "  • Frontend + Nginx: $PORT_80_STATUS"
echo -e "  • UIEngine Backend: $BACKEND_STATUS"
echo -e "  • GeoServer: https://*************/geoserver (external)"
echo ""
echo -e "${CYAN}🛠️  Management Commands:${NC}"
echo "  • View logs:           docker-compose logs -f"
echo "  • Stop services:       docker-compose down"
echo "  • Restart services:    docker-compose restart"
echo "  • Rebuild & restart:   docker-compose up -d --build"
echo ""
echo -e "${YELLOW}📝 Note: Access the application from any browser using the IP addresses shown above${NC}"
