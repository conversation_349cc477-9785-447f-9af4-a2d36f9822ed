# SANSA Flood# SANSA Flood Mapping System - Linux Server Deployment (PowerShell)
# Target Server: *************
# Services: UIEngine + Frontend + Nginx (No Database)

Write-Host "🚀 SANSA Flood Mapping System - Linux Server Deployment" -ForegroundColor Green
Write-Host "=========================================================" -ForegroundColor Green
Write-Host "🖥️  Target Server: *************" -ForegroundColor Cyan
Write-Host "📦 Services: UIEngine + Frontend + Nginx" -ForegroundColor Cyan
Write-Host ""

# Check if .env.production exists
if (-not (Test-Path ".env.production")) {
    Write-Host "⚠️  .env.production not found, using defaults..." -ForegroundColor Yellow
}

# Stop any existing containers
Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
docker-compose down

# Deploy using the main docker-compose.yml
Write-Host "🔨 Building and starting services..." -ForegroundColor Green
docker-compose up -d --build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to start services" -ForegroundColor Red
    exit 1
}

# Wait for services to start
Write-Host "⏳ Waiting for services to start (30 seconds)..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Health checks
Write-Host "🔍 Performing health checks..." -ForegroundColor Cyan

# Check if Nginx is running on port 80
try {
    $response = Invoke-WebRequest -Uri "http://localhost:80" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ Nginx is running on port 80" -ForegroundColor Green
    $port80Status = "✅ Available"
} catch {
    Write-Host "⚠️  Port 80 check failed" -ForegroundColor Yellow
    $port80Status = "❌ Not available"
}

# Check if Nginx is running on port 8080 (alternative)
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ Nginx is running on port 8080" -ForegroundColor Green
    $port8080Status = "✅ Available"
} catch {
    Write-Host "⚠️  Port 8080 check failed" -ForegroundColor Yellow
    $port8080Status = "❌ Not available"
}

# Check UIEngine backend
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ UIEngine (Backend API) is running" -ForegroundColor Green
    $backendStatus = "✅ Running"
} catch {
    Write-Host "⚠️  UIEngine direct check failed (may be normal)" -ForegroundColor Yellow
    $backendStatus = "⚠️  Check through proxy"
}

# Show container status
Write-Host "📊 Container Status:" -ForegroundColor Cyan
docker-compose ps

Write-Host ""
Write-Host "🎉 Deployment Complete!" -ForegroundColor Green
Write-Host "=============================="
Write-Host ""
Write-Host "🌐 Access Points:" -ForegroundColor Cyan
Write-Host "  • Primary:   http://************* $port80Status" -ForegroundColor White
Write-Host "  • Secondary: http://*************:8080 $port8080Status" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Service Status:" -ForegroundColor Cyan
Write-Host "  • Frontend + Nginx: $port80Status" -ForegroundColor White
Write-Host "  • UIEngine Backend: $backendStatus" -ForegroundColor White
Write-Host "  • GeoServer: https://*************/geoserver (external)" -ForegroundColor White
Write-Host ""
Write-Host "🛠️  Management Commands:" -ForegroundColor Cyan
Write-Host "  • View logs:           docker-compose logs -f" -ForegroundColor White
Write-Host "  • Stop services:       docker-compose down" -ForegroundColor White
Write-Host "  • Restart services:    docker-compose restart" -ForegroundColor White
Write-Host "  • Rebuild & restart:   docker-compose up -d --build" -ForegroundColor White
Write-Host ""
Write-Host "📝 Note: Access the application from any browser using the IP addresses shown above" -ForegroundColor YellowMapping System - UAT Deployment Script (PowerShell)
# Simplified deployment: UIEngine + Frontend + Nginx (No Database)

Write-Host "🚀 SANSA Flood Mapping System - UAT Deployment" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Green
Write-Host "📦 Deploying UIEngine + Frontend + Nginx..." -ForegroundColor Green

# Check if .env.production exists
if (-not (Test-Path ".env.production")) {
    Write-Host "⚠️  .env.production not found, using defaults..." -ForegroundColor Yellow
}

# Deploy using the main docker-compose.yml (no database)
Write-Host "� Building and starting services..." -ForegroundColor Yellow
docker-compose up -d --build

# Wait for services to start
Write-Host "⏳ Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Health checks
Write-Host "🔍 Performing health checks..." -ForegroundColor Green

# Check if Nginx is running (main access point)
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ Nginx proxy is running - Main application accessible" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Nginx on port 80 failed, trying port 8080..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080" -UseBasicParsing -TimeoutSec 5
        Write-Host "✅ Application is running on port 8080" -ForegroundColor Green
    } catch {
        Write-Host "❌ Main application health check failed" -ForegroundColor Red
    }
}

# Check backend API directly
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ UIEngine (Backend API) is running" -ForegroundColor Green
} catch {
    Write-Host "⚠️  UIEngine direct access check failed (may be normal if only accessible through proxy)" -ForegroundColor Yellow
}

Write-Host "🎉 Deployment complete!" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Access your application:" -ForegroundColor Cyan
Write-Host "  • Main application: http://localhost" -ForegroundColor White
Write-Host "  • If port 80 is busy: http://localhost:8080" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Management commands:" -ForegroundColor Cyan
Write-Host "  • View logs: docker-compose logs -f" -ForegroundColor White
Write-Host "  • Stop application: docker-compose down" -ForegroundColor White
Write-Host "  • Restart: docker-compose restart" -ForegroundColor White
