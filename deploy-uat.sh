#!/bin/bash

# SANSA Flood Mapping System - UAT Deployment Script
# This script prepares and deploys the application for UAT environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 SANSA Flood Mapping System - UAT Deployment${NC}"
echo "=================================================="

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    echo -e "${YELLOW}⚠️  Creating default .env.production file...${NC}"
    cp .env.production.template .env.production 2>/dev/null || echo -e "${RED}❌ Please create .env.production file manually${NC}"
fi

# Get deployment mode
echo -e "${YELLOW}Select deployment mode:${NC}"
echo "1. Standard deployment (with Nginx reverse proxy)"
echo "2. Simple deployment (direct access to services)"
echo "3. Production deployment (with database)"
read -p "Enter choice (1-3): " DEPLOY_MODE

case $DEPLOY_MODE in
    1)
        echo -e "${GREEN}📦 Deploying standard configuration...${NC}"
        docker-compose -f docker-compose.yml up -d
        ;;
    2)
        echo -e "${GREEN}📦 Deploying minimal configuration...${NC}"
        docker-compose -f deployment/docker/docker-compose.minimal.yml up -d
        ;;
    3)
        echo -e "${GREEN}📦 Deploying full production configuration...${NC}"
        docker-compose -f deployment/docker/docker-compose.main.yml up -d
        ;;
    *)
        echo -e "${RED}❌ Invalid choice${NC}"
        exit 1
        ;;
esac

# Wait for services to start
echo -e "${YELLOW}⏳ Waiting for services to start...${NC}"
sleep 30

# Health checks
echo -e "${GREEN}🔍 Performing health checks...${NC}"

# Check if the main application is running
if curl -f http://localhost:3000/health >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Main application is running${NC}"
else
    echo -e "${YELLOW}⚠️  Main application health check failed, trying alternative port...${NC}"
    if curl -f http://localhost:80 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Application is running on port 80${NC}"
    else
        echo -e "${RED}❌ Application health check failed${NC}"
    fi
fi

# Check backend API
if curl -f http://localhost:3001/api/health >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Backend API is running${NC}"
else
    echo -e "${YELLOW}⚠️  Backend API direct access failed (this is normal in production mode)${NC}"
fi

echo -e "${GREEN}🎉 Deployment complete!${NC}"
echo ""
echo "Access your application:"
echo "  • Standard/Minimal: http://YOUR_SERVER_IP"
echo "  • With Nginx proxy: http://YOUR_SERVER_IP:3000"
echo ""
echo "To check logs:"
echo "  docker-compose logs -f"
echo ""
echo "To stop the application:"
echo "  docker-compose down"
