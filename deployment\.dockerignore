# Node.js dependencies and build artifacts
frontend/node_modules
frontend/dist
uiengine/node_modules
uiengine/dist

# Git and version control
.git
.gitignore

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
uiengine/logs
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and editor files
.vscode
.idea
*.swp
*.swo

# Documentation (already in documentation folder)
documentation/

# Test files (not needed in production builds)
tests/

# Development tools
.bolt

# Database files (handled separately)
database/backups/
database/data/

# Temporary files
tmp/
temp/
