# 🚀 SANSA Flood Monitoring System - Linux Docker Deployment Guide

## 📋 Overview

This guide provides comprehensive instructions for deploying the SANSA Flood Monitoring System on Linux using Docker with separate containers for each service, without database dependency.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │    Frontend     │    │   UIEngine API  │
│   (Port 80/443) │◄──►│   (Port 3000)   │◄──►│   (Port 3001)   │
│                 │    │                 │    │                 │
│ - Load Balancer │    │ - React SPA     │    │ - Node.js API   │
│ - SSL Termination│    │ - Static Assets │    │ - OWS Proxy     │
│ - Rate Limiting │    │ - UI Components │    │ - Alert Engine  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+, CentOS 8+, or similar)
- **CPU**: 2+ cores
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 10GB free space
- **Network**: Internet access for Docker images and GeoServer connectivity

### Software Requirements
```bash
# Docker Engine 20.10+
sudo apt update
sudo apt install -y docker.io docker-compose

# Or using <PERSON><PERSON>'s official repository
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker

# Verify installation
docker --version
docker-compose --version
```

### Network Requirements
- **Port 80**: HTTP traffic (Nginx)
- **Port 443**: HTTPS traffic (Nginx) - Optional
- **Outbound**: Access to `*************` (GeoServer)

## 📦 Service Components

| Service | Container | Port | Purpose | Dependencies |
|---------|-----------|------|---------|--------------|
| **Nginx** | `sansa-nginx` | 80, 443 | Reverse proxy, load balancer | Frontend, UIEngine |
| **Frontend** | `sansa-frontend` | 3000 | React SPA, user interface | UIEngine |
| **UIEngine** | `sansa-uiengine` | 3001 | API server, business logic | None (standalone) |

## 🚀 Quick Deployment

### 1. Clone Repository
```bash
git clone <repository-url>
cd sansabig
```

### 2. Run Deployment Script
```bash
# Make script executable
chmod +x deployment/scripts/deploy-linux.sh

# Deploy all services
./deployment/scripts/deploy-linux.sh
```

### 3. Verify Deployment
```bash
# Check service status
./deployment/scripts/deploy-linux.sh status

# Run endpoint tests
./deployment/scripts/test-endpoints-linux.sh
```

## 🔧 Manual Deployment

### 1. Prepare Environment
```bash
# Navigate to deployment directory
cd deployment/docker

# Create environment file
cp .env.example .env

# Edit configuration
nano .env
```

### 2. Configure Environment Variables
```env
# Core Configuration
NODE_ENV=production
LOG_LEVEL=info

# Ports
HTTP_PORT=80
HTTPS_PORT=443

# GeoServer Configuration
GEOSERVER_URL=https://*************/geoserver
GEOSERVER_BASE_URL=http://*************:8080/geoserver
GEOSERVER_USERNAME=admin
GEOSERVER_PASSWORD=geoserver

# Security (CHANGE IN PRODUCTION!)
CORS_ORIGIN=*
JWT_SECRET=your_secure_jwt_secret_here
JWT_EXPIRY=24h

# Alert System
ALERT_ENGINE_INTERVAL_MINUTES=5
ALERT_ENGINE_MAX_CONCURRENT=5

# Frontend
REACT_APP_GEOSERVER_URL=https://*************/geoserver
REACT_APP_DEMO_MODE=false
FRONTEND_URL=http://your-domain.com
```

### 3. Create Data Directories
```bash
# Create necessary directories
mkdir -p ../../{logs/{uiengine,nginx},data/uiengine,cache/uiengine,reports}

# Set permissions
chmod -R 755 ../../{logs,data,cache,reports}
```

### 4. Build and Deploy
```bash
# Build all services
docker-compose -f docker-compose.linux.yml build

# Start services
docker-compose -f docker-compose.linux.yml up -d

# Check status
docker-compose -f docker-compose.linux.yml ps
```

## 🧪 Testing All Endpoints

### Automated Testing
```bash
# Make test script executable
chmod +x deployment/scripts/test-endpoints-linux.sh

# Run comprehensive tests
./deployment/scripts/test-endpoints-linux.sh

# Test with verbose output
./deployment/scripts/test-endpoints-linux.sh http://localhost true

# Test remote deployment
./deployment/scripts/test-endpoints-linux.sh http://your-server.com
```

### Manual Testing
```bash
# Health check
curl http://localhost/health

# API documentation
curl http://localhost/api-docs.json

# OWS capabilities
curl http://localhost/api/ows/capabilities

# Dataset listing
curl http://localhost/api/datasets

# Alert rules
curl http://localhost/api/alert-rules

# Frontend
curl http://localhost/
```

## 📊 Monitoring & Management

### Service Management
```bash
# View logs
docker-compose -f docker-compose.linux.yml logs -f

# View specific service logs
docker-compose -f docker-compose.linux.yml logs -f uiengine

# Restart services
docker-compose -f docker-compose.linux.yml restart

# Stop services
docker-compose -f docker-compose.linux.yml down

# Update services
docker-compose -f docker-compose.linux.yml pull
docker-compose -f docker-compose.linux.yml up -d
```

### Health Monitoring
```bash
# Continuous health monitoring
watch -n 30 'curl -s http://localhost/health | jq .'

# Service status
docker-compose -f docker-compose.linux.yml ps

# Resource usage
docker stats
```

### Log Management
```bash
# View application logs
tail -f ../../logs/uiengine/app.log

# View nginx access logs
tail -f ../../logs/nginx/access.log

# View nginx error logs
tail -f ../../logs/nginx/error.log
```

## 🔒 Security Configuration

### SSL/HTTPS Setup (Optional)
```bash
# Create SSL directory
mkdir -p ssl

# Copy your SSL certificates
cp your-cert.pem ssl/cert.pem
cp your-private-key.key ssl/private.key

# Update nginx configuration for HTTPS
# Uncomment HTTPS server block in deployment/configs/proxy.conf

# Restart nginx
docker-compose -f docker-compose.linux.yml restart nginx
```

### Firewall Configuration
```bash
# Allow HTTP/HTTPS traffic
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow SSH (if needed)
sudo ufw allow 22/tcp

# Enable firewall
sudo ufw enable
```

## 🚨 Troubleshooting

### Common Issues

| Issue | Symptoms | Solution |
|-------|----------|----------|
| **Port conflicts** | `bind: address already in use` | Change ports in `.env` or stop conflicting services |
| **Permission denied** | Docker permission errors | Add user to docker group: `sudo usermod -aG docker $USER` |
| **Service unhealthy** | Health checks failing | Check logs: `docker-compose logs service-name` |
| **GeoServer connectivity** | OWS endpoints failing | Verify network connectivity to `*************` |
| **Build failures** | Docker build errors | Clear cache: `docker system prune -a` |

### Debug Commands
```bash
# Check if ports are in use
sudo netstat -tulpn | grep :80

# Test GeoServer connectivity
curl -I https://*************/geoserver

# Check Docker daemon
sudo systemctl status docker

# View Docker system info
docker system df
docker system events

# Container inspection
docker inspect sansa-uiengine
```

### Performance Tuning
```bash
# Increase Docker resources (if needed)
# Edit /etc/docker/daemon.json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}

# Restart Docker
sudo systemctl restart docker
```

## 📈 Scaling & Production

### Load Balancing
```yaml
# Scale frontend instances
docker-compose -f docker-compose.linux.yml up -d --scale frontend=3

# Scale UIEngine instances
docker-compose -f docker-compose.linux.yml up -d --scale uiengine=2
```

### Backup Strategy
```bash
# Backup data directories
tar -czf backup-$(date +%Y%m%d).tar.gz logs/ data/ cache/ reports/

# Backup configuration
tar -czf config-backup-$(date +%Y%m%d).tar.gz deployment/
```

## 🎯 Deployment Verification Checklist

- [ ] All containers are running: `docker-compose ps`
- [ ] Health endpoint returns 200: `curl http://localhost/health`
- [ ] Frontend loads: `curl http://localhost/`
- [ ] API documentation accessible: `curl http://localhost/api-docs.json`
- [ ] OWS capabilities work: `curl http://localhost/api/ows/capabilities`
- [ ] Logs are being generated: `ls -la ../../logs/`
- [ ] All endpoint tests pass: `./test-endpoints-linux.sh`

## 📚 Additional Resources

- **API Documentation**: `http://localhost/api-docs`
- **Health Monitoring**: `http://localhost/health`
- **Nginx Status**: `http://localhost/nginx-status` (internal only)
- **Container Logs**: `docker-compose logs -f`

## 🆘 Support

For issues and support:
1. Check the troubleshooting section above
2. Review container logs: `docker-compose logs`
3. Verify network connectivity to GeoServer
4. Ensure all environment variables are correctly set

This deployment guide provides a complete solution for running the SANSA Flood Monitoring System on Linux with Docker, ensuring scalability, security, and maintainability.