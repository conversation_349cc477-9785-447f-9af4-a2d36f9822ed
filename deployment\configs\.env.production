# SANSA Flood Mapping - Production Environment Configuration

# Application Environment
NODE_ENV=production
DOMAIN=localhost
PORT=3000

# Frontend URLs (for containerized deployment)
FRONTEND_URL=http://localhost:3000
UIENGINE_URL=http://localhost:3001

# External SANSA Service Provision (GeoServer)
GEOSERVER_URL=https://*************/geoserver

# Database Configuration (PostgreSQL + PostGIS)
DB_HOST=database
DB_PORT=5432
POSTGRES_DB=sansa_flood_db
POSTGRES_USER=sansa_user
POSTGRES_PASSWORD=secure_password_123

# UI Engine (Node.js Service) Configuration
UIENGINE_PORT=3001
API_BASE_URL=/api

# Security Configuration
JWT_SECRET=your_production_jwt_secret_here
CORS_ORIGIN=*

# Logging Configuration
LOG_LEVEL=info

# Feature Toggles
ENABLE_POSTGIS=true
ENABLE_ANALYTICS=true
ENABLE_ALERTS=true

# Data Mode (auto|demo|live)
DATA_MODE=auto

# Docker Network
DOCKER_NETWORK=sansa-network

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
