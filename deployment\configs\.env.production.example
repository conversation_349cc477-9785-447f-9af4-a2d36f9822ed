# =============================================================================
# SANSA Flood Mapping System - Production Environment Configuration
# =============================================================================

# Database Configuration
POSTGRES_DB=sansa_flood_db
POSTGRES_USER=sansa_user
POSTGRES_PASSWORD=CHANGE_THIS_SECURE_PASSWORD_123!
POSTGRES_HOST=database
POSTGRES_PORT=5432

# Backend Configuration
NODE_ENV=production
PORT=3001
JWT_SECRET=CHANGE_THIS_SUPER_SECURE_JWT_SECRET_MIN_32_CHARACTERS_LONG
LOG_LEVEL=info

# External Services
GEOSERVER_URL=https://*************/geoserver
GEOSERVER_USERNAME=admin
GEOSERVER_PASSWORD=geoserver

# Frontend Configuration (Reverse Proxy Setup)
REACT_APP_API_URL=/api
REACT_APP_GEOSERVER_URL=https://*************/geoserver
REACT_APP_ENVIRONMENT=production
REACT_APP_DEMO_MODE=false
REACT_APP_NETWORK_MODE=auto

# Security Configuration (Reverse Proxy)
CORS_ORIGIN=*
TRUSTED_PROXIES=**********/16

# SSL Configuration
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/private.key

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=sansa-backups

# Monitoring Configuration
LOG_LEVEL=info
ENABLE_METRICS=true
METRICS_PORT=9090

# Performance Configuration
MAX_CONNECTIONS=100
TIMEOUT_SECONDS=30
CACHE_TTL=3600

# =============================================================================
# IMPORTANT SECURITY NOTES:
# 1. Change all default passwords before deployment
# 2. Use strong, unique passwords for each service
# 3. Keep this file secure and never commit to version control
# 4. Regularly rotate passwords and secrets
# =============================================================================
