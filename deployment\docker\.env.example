# SANSA Flood Monitoring System - Linux Production Environment
# Copy this file to .env and customize for your deployment

# =============================================================================
# CORE APPLICATION CONFIGURATION
# =============================================================================

# Environment
NODE_ENV=production
LOG_LEVEL=info

# Network Ports
HTTP_PORT=80
HTTPS_PORT=443

# =============================================================================
# GEOSERVER CONFIGURATION
# =============================================================================

# GeoServer URLs (Update these to match your GeoServer instance)
GEOSERVER_URL=https://*************/geoserver
GEOSERVER_BASE_URL=http://*************:8080/geoserver

# GeoServer Authentication (Update with your credentials)
GEOSERVER_USERNAME=admin
GEOSERVER_PASSWORD=geoserver

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# CORS Configuration
CORS_ORIGIN=*

# JWT Configuration (CHANGE THESE IN PRODUCTION!)
JWT_SECRET=change_this_secure_jwt_secret_in_production_environment
JWT_EXPIRY=24h

# =============================================================================
# ALERT SYSTEM CONFIGURATION
# =============================================================================

# Alert Engine Settings
ALERT_ENGINE_INTERVAL_MINUTES=5
ALERT_ENGINE_MAX_CONCURRENT=5
ALERT_ENGINE_RETRY_ATTEMPTS=3
ALERT_ENGINE_RETRY_DELAY=5000

# Notification Settings (Development mode - console only)
ENABLE_EMAIL_NOTIFICATIONS=false
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_WEBSOCKET_NOTIFICATIONS=true

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# Frontend Build Arguments
REACT_APP_API_URL=/api
REACT_APP_GEOSERVER_URL=https://*************/geoserver
REACT_APP_ENVIRONMENT=production
REACT_APP_DEMO_MODE=false

# Frontend URL (Update with your domain)
FRONTEND_URL=http://localhost

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Node.js Performance
NODE_OPTIONS=--max-old-space-size=2048

# Alert System Performance
MAX_ALERT_EVENTS_PER_PAGE=50
ALERT_HISTORY_RETENTION_DAYS=90

# =============================================================================
# DATABASE CONFIGURATION (DISABLED FOR NO-DB DEPLOYMENT)
# =============================================================================

# PostGIS Database (Set to false for no-database deployment)
ENABLE_POSTGIS=false

# Database Connection (Not used when ENABLE_POSTGIS=false)
# DB_HOST=database
# DB_PORT=5432
# POSTGRES_USER=sansa_user
# POSTGRES_PASSWORD=secure_password_123
# POSTGRES_DB=sansa_flood_db

# =============================================================================
# OPTIONAL FEATURES
# =============================================================================

# Email Configuration (Optional - for production notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password
# <AUTHOR> <EMAIL>

# SMS Configuration (Optional - Twilio)
# TWILIO_ACCOUNT_SID=your-twilio-account-sid
# TWILIO_AUTH_TOKEN=your-twilio-auth-token
# TWILIO_PHONE_NUMBER=+**********

# Redis Configuration (Optional - for caching)
# REDIS_URL=redis://localhost:6379
# REDIS_PASSWORD=

# =============================================================================
# DEPLOYMENT SPECIFIC SETTINGS
# =============================================================================

# Container Resource Limits (Uncomment to customize)
# UIENGINE_MEMORY_LIMIT=2G
# UIENGINE_CPU_LIMIT=1.0
# FRONTEND_MEMORY_LIMIT=512M
# FRONTEND_CPU_LIMIT=0.5
# NGINX_MEMORY_LIMIT=256M
# NGINX_CPU_LIMIT=0.5

# SSL Configuration (Uncomment for HTTPS)
# SSL_CERT_PATH=./ssl/cert.pem
# SSL_KEY_PATH=./ssl/private.key

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Log Rotation
LOG_MAX_SIZE=10m
LOG_MAX_FILES=3

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# =============================================================================
# DEVELOPMENT OVERRIDES
# =============================================================================

# Uncomment for development/testing
# NODE_ENV=development
# LOG_LEVEL=debug
# CORS_ORIGIN=http://localhost:3000
# REACT_APP_DEMO_MODE=true

# =============================================================================
# NOTES
# =============================================================================

# 1. This configuration is optimized for Linux production deployment
# 2. Database features are disabled (ENABLE_POSTGIS=false)
# 3. Alert system runs in memory-only mode
# 4. Update GeoServer URLs to match your environment
# 5. Change JWT_SECRET in production
# 6. Configure SSL certificates for HTTPS
# 7. Update FRONTEND_URL with your actual domain
