# PostgreSQL with PostGI<PERSON> for SANSA Flood Monitoring System
# This Dockerfile is provided for completeness but won't be used in the no-database deployment

FROM postgis/postgis:15-3.3

# Add security updates
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
        curl \
        postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Create custom initialization directory
RUN mkdir -p /docker-entrypoint-initdb.d/custom

# Copy initialization scripts
COPY database/init/ /docker-entrypoint-initdb.d/custom/

# Set proper permissions
RUN chown -R postgres:postgres /docker-entrypoint-initdb.d/custom

# Create backup directory
RUN mkdir -p /backups && \
    chown postgres:postgres /backups

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD pg_isready -U ${POSTGRES_USER:-sansa_user} -d ${POSTGRES_DB:-sansa_flood_db} || exit 1

# Expose PostgreSQL port
EXPOSE 5432

# Use the default PostgreSQL entrypoint
CMD ["postgres"]
