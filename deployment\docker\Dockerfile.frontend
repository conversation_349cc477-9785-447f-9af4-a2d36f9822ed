# Multi-stage build for React frontend
FROM node:20-alpine AS builder

WORKDIR /app

# Add security updates
RUN apk add --no-cache --update curl && \
    apk upgrade && \
    rm -rf /var/cache/apk/*

# Copy package files first for better caching
COPY frontend/package*.json ./
COPY frontend/src/ ./src/
COPY frontend/public/ ./public/
COPY frontend/tsconfig.json ./
COPY frontend/vite.config.ts ./
COPY frontend/*.config.js ./

# Install dependencies with security audit
RUN npm ci --only=production && \
    npm audit fix --force || true

# Build arguments
ARG REACT_APP_API_URL
ARG REACT_APP_GEOSERVER_URL
ARG REACT_APP_ENVIRONMENT
ARG REACT_APP_DEMO_MODE

# Environment variables
ENV REACT_APP_API_URL=$REACT_APP_API_URL
ENV REACT_APP_GEOSERVER_URL=$REACT_APP_GEOSERVER_URL
ENV REACT_APP_ENVIRONMENT=$REACT_APP_ENVIRONMENT
ENV REACT_APP_DEMO_MODE=$REACT_APP_DEMO_MODE

# Build application
RUN npm run build

# Production stage with latest nginx
FROM nginx:alpine-slim

# Add security updates
RUN apk add --no-cache --update curl && \
    apk upgrade && \
    rm -rf /var/cache/apk/*

# Copy built assets
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY deployment/configs/frontend.nginx.conf /etc/nginx/conf.d/default.conf

# Configure nginx to run on port 3000
RUN sed -i 's/listen 80;/listen 3000;/' /etc/nginx/conf.d/default.conf

# Create non-root user for nginx
RUN addgroup -g 1001 -S nginx-app && \
    adduser -S nginx-app -u 1001 && \
    chown -R nginx-app:nginx-app /usr/share/nginx/html && \
    chown -R nginx-app:nginx-app /var/cache/nginx && \
    chown -R nginx-app:nginx-app /var/run && \
    chmod -R 755 /usr/share/nginx/html

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000 || exit 1

EXPOSE 3000
CMD ["nginx", "-g", "daemon off;"]
