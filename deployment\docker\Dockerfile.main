# SANSA Flood Mapping - Main Application Dockerfile
# Builds frontend and serves with nginx

# Build stage - Use distroless Node.js for better security
FROM gcr.io/distroless/nodejs20-debian12:latest AS runtime-base
FROM node:20-alpine AS build

# Add security updates and create non-root user
RUN apk add --no-cache --update dumb-init curl && \
    apk upgrade && \
    rm -rf /var/cache/apk/* && \
    addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /app

# Copy frontend package files
COPY frontend/package*.json ./

# Install dependencies with security audit (as non-root)
USER nodejs
RUN npm ci --only=production && \
    npm audit fix --force || true

# Copy frontend source code and build
COPY --chown=nodejs:nodejs frontend/ .
RUN npm run build

# Production stage - Use secure nginx base image
FROM nginxinc/nginx-unprivileged:1.25-alpine

# Add security updates
RUN apk add --no-cache --update && \
    apk upgrade && \
    rm -rf /var/cache/apk/*

# Copy built frontend assets from build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Copy updated nginx configuration
COPY deployment/configs/main.nginx.conf /etc/nginx/conf.d/default.conf

# Set proper permissions for unprivileged nginx
USER root
RUN chown -R nginx:nginx /usr/share/nginx/html
USER nginx

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose port (unprivileged nginx uses 8080)
EXPOSE 8080

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
