# Nginx Reverse Proxy for SANSA Flood Monitoring System
FROM nginx:alpine

# Add security updates and required packages
RUN apk add --no-cache --update curl dumb-init && \
    apk upgrade && \
    rm -rf /var/cache/apk/*

# Remove default nginx configuration
RUN rm -f /etc/nginx/conf.d/default.conf

# Copy nginx configurations
COPY deployment/configs/nginx.conf /etc/nginx/nginx.conf
COPY deployment/configs/proxy.conf /etc/nginx/conf.d/proxy.conf

# Create necessary directories
RUN mkdir -p /var/log/nginx /var/cache/nginx /var/run/nginx

# Create non-root user for nginx
RUN addgroup -g 1001 -S nginx-proxy && \
    adduser -S nginx-proxy -u 1001 -G nginx-proxy

# Set proper permissions
RUN chown -R nginx-proxy:nginx-proxy /var/log/nginx && \
    chown -R nginx-proxy:nginx-proxy /var/cache/nginx && \
    chown -R nginx-proxy:nginx-proxy /var/run/nginx && \
    chmod -R 755 /var/log/nginx /var/cache/nginx /var/run/nginx

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:80/health || exit 1

EXPOSE 80 443

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]
CMD ["nginx", "-g", "daemon off;"]
