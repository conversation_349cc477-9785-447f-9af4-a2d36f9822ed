# UIEngine Microservice Dockerfile for Linux Production
FROM node:20-alpine AS builder

WORKDIR /app

# Add security updates and required packages
RUN apk add --no-cache --update curl dumb-init && \
    apk upgrade && \
    rm -rf /var/cache/apk/*

# Copy package files first for better caching
COPY uiengine/package*.json ./

# Install all dependencies (including devDependencies for build)
RUN npm ci && \
    npm audit fix --force || true

# Copy source code
COPY uiengine/src/ ./src/
COPY uiengine/tsconfig.json ./

# Build application
RUN npm run build

# Production stage
FROM node:20-alpine AS production

WORKDIR /app

# Add security updates and required packages
RUN apk add --no-cache --update curl dumb-init && \
    apk upgrade && \
    rm -rf /var/cache/apk/*

# Copy package files
COPY uiengine/package*.json ./

# Install only production dependencies
RUN npm ci --only=production && \
    npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 -G nodejs

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/data /app/cache /app/reports && \
    chown -R nodejs:nodejs /app && \
    chmod -R 755 /app

# Switch to non-root user
USER nodejs

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

EXPOSE 3001

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]
