# SANSA Flood Monitoring System - Linux Production Deployment
# Separate services without database dependency
version: '3.8'

services:
  # UIEngine Microservice
  uiengine:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile.uiengine
    container_name: sansa-uiengine
    restart: unless-stopped
    environment:
      # Core Configuration
      NODE_ENV: production
      PORT: 3001
      LOG_LEVEL: ${LOG_LEVEL:-info}
      
      # Disable PostGIS features
      ENABLE_POSTGIS: "false"
      
      # GeoServer Configuration
      GEOSERVER_URL: ${GEOSERVER_URL:-https://*************/geoserver}
      GEOSERVER_BASE_URL: ${GEOSERVER_BASE_URL:-http://*************:8080/geoserver}
      GEOSERVER_USERNAME: ${GEOSERVER_USERNAME:-admin}
      GEOSERVER_PASSWORD: ${GEOSERVER_PASSWORD:-geoserver}
      
      # Security
      CORS_ORIGIN: ${CORS_ORIGIN:-*}
      JWT_SECRET: ${JWT_SECRET:-change_this_in_production}
      JWT_EXPIRY: ${JWT_EXPIRY:-24h}
      
      # Alert System (Memory-based)
      ENABLE_ALERT_SYSTEM: "true"
      ALERT_ENGINE_INTERVAL_MINUTES: ${ALERT_ENGINE_INTERVAL_MINUTES:-5}
      ALERT_ENGINE_MAX_CONCURRENT: ${ALERT_ENGINE_MAX_CONCURRENT:-5}
      
      # Notifications (Console mode for development)
      ENABLE_EMAIL_NOTIFICATIONS: "false"
      ENABLE_SMS_NOTIFICATIONS: "false"
      ENABLE_WEBSOCKET_NOTIFICATIONS: "true"
      
      # Frontend URL
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost}
      
      # Performance
      NODE_OPTIONS: "--max-old-space-size=2048"
    
    volumes:
      - uiengine_logs:/app/logs
      - uiengine_data:/app/data
      - uiengine_cache:/app/cache
      - uiengine_reports:/app/reports
    
    networks:
      - sansa-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Frontend Service
  frontend:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile.frontend
      args:
        REACT_APP_API_URL: /api
        REACT_APP_GEOSERVER_URL: ${REACT_APP_GEOSERVER_URL:-https://*************/geoserver}
        REACT_APP_ENVIRONMENT: production
        REACT_APP_DEMO_MODE: ${REACT_APP_DEMO_MODE:-false}
    
    container_name: sansa-frontend
    restart: unless-stopped
    
    networks:
      - sansa-network
    
    depends_on:
      uiengine:
        condition: service_healthy
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Nginx Reverse Proxy
  nginx:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile.nginx
    
    container_name: sansa-nginx
    restart: unless-stopped
    
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    
    volumes:
      - nginx_logs:/var/log/nginx
      # Uncomment for SSL certificates
      # - ./ssl:/etc/nginx/ssl:ro
    
    networks:
      - sansa-network
    
    depends_on:
      frontend:
        condition: service_healthy
      uiengine:
        condition: service_healthy
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 64M
          cpus: '0.1'

# Named volumes for data persistence
volumes:
  uiengine_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/../../logs/uiengine
  
  uiengine_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/../../data/uiengine
  
  uiengine_cache:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/../../cache/uiengine
  
  uiengine_reports:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/../../reports
  
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/../../logs/nginx

# Custom network for service communication
networks:
  sansa-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
