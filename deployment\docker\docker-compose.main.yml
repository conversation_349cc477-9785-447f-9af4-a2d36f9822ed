version: '3.8'

services:
  # Main application (frontend + nginx)
  app:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile.main
    container_name: sansa-app
    ports:
      - "3000:8080"
    environment:
      - NODE_ENV=production
    networks:
      - sansa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
  # UI Engine Service
  uiengine:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile.uiengine
    container_name: sansa-uiengine
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: postgresql://${POSTGRES_USER:-sansa_user}:${POSTGRES_PASSWORD:-secure_password_123}@database:5432/${POSTGRES_DB:-sansa_flood_db}
      GEOSERVER_URL: ${GEOSERVER_URL:-https://*************/geoserver}
      CORS_ORIGIN: ${CORS_ORIGIN:-*}
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_here}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    volumes:
      - ../../logs:/app/logs
      - ../../data:/app/data
    expose:
      - "3001"
    depends_on:
      database:
        condition: service_healthy
    networks:
      - sansa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Database
  database:
    image: postgis/postgis:15-3.3
    container_name: sansa-database
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-sansa_flood_db}
      POSTGRES_USER: ${POSTGRES_USER:-sansa_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_123}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../database/init:/docker-entrypoint-initdb.d
      - ../../database/backups:/backups
    ports:
      - "5432:5432"
    networks:
      - sansa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-sansa_user} -d ${POSTGRES_DB:-sansa_flood_db}"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:

networks:
  sansa-network:
    driver: bridge
