version: '3.8'

services:
  uiengine:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile.uiengine
    container_name: sansa-uiengine
    environment:
      NODE_ENV: production
      PORT: 3001
      ENABLE_POSTGIS: "false"
      GEOSERVER_URL: ${GEOSERVER_URL:-https://*************/geoserver}
      CORS_ORIGIN: ${CORS_ORIGIN:-*}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    volumes:
      - ../../logs:/app/logs
      - ../../data:/app/data
    expose:
      - "3001"
    networks:
      - sansa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile.frontend
    container_name: sansa-frontend
    environment:
      NODE_ENV: production
    expose:
      - "3000"
    networks:
      - sansa-network
    restart: unless-stopped
    depends_on:
      uiengine:
        condition: service_healthy

  nginx:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile.nginx
    container_name: sansa-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../configs/nginx.conf:/etc/nginx/nginx.conf:ro
      - ../configs/frontend.nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ../../logs/nginx:/var/log/nginx
      - /etc/letsencrypt:/etc/letsencrypt:ro
    networks:
      - sansa-network
    restart: unless-stopped
    depends_on:
      - frontend
      - uiengine

networks:
  sansa-network:
    driver: bridge

volumes:
  nginx_logs:
    driver: local
