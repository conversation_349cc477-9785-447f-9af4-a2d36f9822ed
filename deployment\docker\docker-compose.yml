version: '3.8'

services:
  database:
    image: postgis/postgis:15-3.3
    container_name: sansa-database
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-sansa_flood_db}
      POSTGRES_USER: ${POSTGRES_USER:-sansa_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_123}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../database/init:/docker-entrypoint-initdb.d
      - ../../database/backups:/backups
    ports:
      - "5432:5432"
    networks:
      - sansa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-sansa_user} -d ${POSTGRES_DB:-sansa_flood_db}"]
      interval: 30s
      timeout: 10s
      retries: 3
  uiengine:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile.uiengine
    container_name: sansa-uiengine
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: postgresql://${POSTGRES_USER:-sansa_user}:${POSTGRES_PASSWORD:-secure_password_123}@database:5432/${POSTGRES_DB:-sansa_flood_db}
      GEOSERVER_URL: ${GEOSERVER_URL:-https://*************/geoserver}
      CORS_ORIGIN: ${CORS_ORIGIN:-*}
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_here}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    volumes:
      - ../../logs:/app/logs
      - ../../data:/app/data
    expose:
      - "3001"
    depends_on:
      database:
        condition: service_healthy
    networks:
      - sansa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile.frontend
      args:
        REACT_APP_API_URL: ${REACT_APP_API_URL:-/api}
        REACT_APP_GEOSERVER_URL: ${REACT_APP_GEOSERVER_URL:-https://*************/geoserver}
        REACT_APP_ENVIRONMENT: production
        REACT_APP_DEMO_MODE: ${REACT_APP_DEMO_MODE:-false}
    container_name: sansa-frontend
    expose:
      - "3000"
    depends_on:
      uiengine:
        condition: service_healthy
    networks:
      - sansa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    build:
      context: ..
      dockerfile: deployment/docker/Dockerfile.nginx
    container_name: sansa-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../configs/nginx.conf:/etc/nginx/nginx.conf:ro
      - ../../ssl:/etc/nginx/ssl:ro
      - ../../logs/nginx:/var/log/nginx
    depends_on:
      - frontend
      - uiengine
    networks:
      - sansa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local

networks:
  sansa-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
