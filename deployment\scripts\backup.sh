#!/bin/bash

# SANSA Flood Mapping System - Backup Script
set -e

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_DIR="$( cd "$SCRIPT_DIR/../.." &> /dev/null && pwd )"
BACKUP_DIR="$PROJECT_DIR/database/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() { echo -e "${GREEN}[BACKUP]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

cd "$PROJECT_DIR"

print_status "Starting backup process..."
mkdir -p "$BACKUP_DIR"

# Database backup
print_status "Creating database backup..."
docker compose -f deployment/docker/docker-compose.yml exec -T database pg_dump -U sansa_user sansa_flood_db > "$BACKUP_DIR/db_backup_$DATE.sql"

if [ $? -eq 0 ]; then
    print_status "Database backup completed: db_backup_$DATE.sql"
    gzip "$BACKUP_DIR/db_backup_$DATE.sql"
    print_status "Database backup compressed: db_backup_$DATE.sql.gz"
else
    print_error "Database backup failed"
    exit 1
fi

# Application data backup
print_status "Creating application data backup..."
if [ -d "$PROJECT_DIR/data" ] && [ "$(ls -A $PROJECT_DIR/data)" ]; then
    tar -czf "$BACKUP_DIR/app_data_$DATE.tar.gz" -C "$PROJECT_DIR" data/
    print_status "Application data backup completed: app_data_$DATE.tar.gz"
fi

# Configuration backup
print_status "Creating configuration backup..."
tar -czf "$BACKUP_DIR/config_$DATE.tar.gz" \
    --exclude="*.log" \
    --exclude="node_modules" \
    --exclude=".git" \
    -C "$PROJECT_DIR" \
    .env.production \
    deployment/ \
    ssl/ 2>/dev/null || true

print_status "Configuration backup completed: config_$DATE.tar.gz"

# Clean old backups
print_status "Cleaning old backups (keeping $RETENTION_DAYS days)..."
find "$BACKUP_DIR" -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete

# Backup summary
print_status "Backup Summary:"
print_status "  • Database: $(ls -lh $BACKUP_DIR/db_backup_$DATE.sql.gz | awk '{print $5}')"
if [ -f "$BACKUP_DIR/app_data_$DATE.tar.gz" ]; then
    print_status "  • App Data: $(ls -lh $BACKUP_DIR/app_data_$DATE.tar.gz | awk '{print $5}')"
fi
print_status "  • Config: $(ls -lh $BACKUP_DIR/config_$DATE.tar.gz | awk '{print $5}')"
print_status "  • Location: $BACKUP_DIR"

print_status "Backup process completed successfully!"
