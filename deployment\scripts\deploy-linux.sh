#!/bin/bash

# SANSA Flood Monitoring System - Linux Deployment Script
# Deploys separate Docker services without database dependency

set -euo pipefail

# ============================================================================
# CONFIGURATION SECTION - MODIFY THESE VALUES BEFORE DEPLOYMENT
# ============================================================================

# Server Configuration (UPDATE THESE!)
SERVER_IP="*************"  # Change to your server's IP address
DOMAIN_NAME=""             # Optional: your domain name (leave empty if using IP)

# GeoServer Configuration (UPDATE IF DIFFERENT!)
DEPLOY_GEOSERVER_URL="https://*************/geoserver"
DEPLOY_GEOSERVER_USERNAME="admin"
DEPLOY_GEOSERVER_PASSWORD="geoserver"

# Security Configuration (CHANGE IN PRODUCTION!)
DEPLOY_JWT_SECRET="change_this_secure_jwt_secret_in_production_min_32_chars"
DEPLOY_CORS_ORIGIN="*"  # Change to specific domains in production

# Port Configuration
DEPLOY_HTTP_PORT="80"
DEPLOY_HTTPS_PORT="443"

# ============================================================================
# END CONFIGURATION SECTION
# ============================================================================

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DOCKER_DIR="$PROJECT_ROOT/deployment/docker"
ENV_FILE="$PROJECT_ROOT/.env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Function to create necessary directories
create_directories() {
    log_info "Creating necessary directories..."
    
    local dirs=(
        "$PROJECT_ROOT/logs/uiengine"
        "$PROJECT_ROOT/logs/nginx"
        "$PROJECT_ROOT/data/uiengine"
        "$PROJECT_ROOT/cache/uiengine"
        "$PROJECT_ROOT/reports"
    )
    
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_info "Created directory: $dir"
        fi
    done
    
    # Set proper permissions
    chmod -R 755 "$PROJECT_ROOT/logs" "$PROJECT_ROOT/data" "$PROJECT_ROOT/cache" "$PROJECT_ROOT/reports"
    
    log_success "Directories created successfully"
}

# Function to create environment file
create_env_file() {
    log_info "Creating environment configuration..."
    
    if [[ ! -f "$ENV_FILE" ]]; then
        cat > "$ENV_FILE" << EOF
# SANSA Flood Monitoring - Linux Production Environment
NODE_ENV=production
LOG_LEVEL=info

# Ports
HTTP_PORT=${DEPLOY_HTTP_PORT}
HTTPS_PORT=${DEPLOY_HTTPS_PORT}

# GeoServer Configuration
GEOSERVER_URL=${DEPLOY_GEOSERVER_URL}
GEOSERVER_BASE_URL=${DEPLOY_GEOSERVER_URL}
GEOSERVER_USERNAME=${DEPLOY_GEOSERVER_USERNAME}
GEOSERVER_PASSWORD=${DEPLOY_GEOSERVER_PASSWORD}

# Security (CHANGE THESE IN PRODUCTION!)
CORS_ORIGIN=${DEPLOY_CORS_ORIGIN}
JWT_SECRET=${DEPLOY_JWT_SECRET}
JWT_EXPIRY=24h

# Alert System
ALERT_ENGINE_INTERVAL_MINUTES=5
ALERT_ENGINE_MAX_CONCURRENT=5

# Frontend Configuration
REACT_APP_GEOSERVER_URL=${DEPLOY_GEOSERVER_URL}
REACT_APP_DEMO_MODE=false
FRONTEND_URL=http://${SERVER_IP}

# Performance
NODE_OPTIONS=--max-old-space-size=2048
EOF
        log_success "Environment file created: $ENV_FILE"
        log_warning "Please review and update the environment variables in $ENV_FILE"
    else
        log_info "Environment file already exists: $ENV_FILE"
    fi
}

# Function to build and deploy services
deploy_services() {
    log_info "Building and deploying services..."
    
    cd "$PROJECT_ROOT"

    # Pull latest base images
    log_info "Pulling latest base images..."
    docker-compose pull --ignore-pull-failures || true

    # Build services
    log_info "Building services..."
    docker-compose build --no-cache

    # Start services
    log_info "Starting services..."
    docker-compose up -d

    log_success "Services deployed successfully"
}

# Function to wait for services to be healthy
wait_for_services() {
    log_info "Waiting for services to become healthy..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "Health check attempt $attempt/$max_attempts..."
        
        if curl -f http://localhost/health &> /dev/null; then
            log_success "All services are healthy!"
            return 0
        fi
        
        sleep 10
        ((attempt++))
    done
    
    log_error "Services failed to become healthy within the timeout period"
    return 1
}

# Function to show service status
show_status() {
    log_info "Service Status:"
    cd "$PROJECT_ROOT"
    docker-compose ps
    
    echo ""
    log_info "Service Logs (last 10 lines):"
    docker-compose -f docker-compose.linux.yml logs --tail=10
}

# Function to run endpoint tests
run_tests() {
    log_info "Running endpoint tests..."
    
    # Wait a bit more for services to fully initialize
    sleep 30
    
    # Test health endpoint
    if curl -f http://localhost/health &> /dev/null; then
        log_success "Health endpoint: OK"
    else
        log_error "Health endpoint: FAILED"
    fi
    
    # Test API documentation
    if curl -f http://localhost/api-docs.json &> /dev/null; then
        log_success "API documentation: OK"
    else
        log_error "API documentation: FAILED"
    fi
    
    # Test OWS capabilities
    if curl -f http://localhost/api/ows/capabilities &> /dev/null; then
        log_success "OWS capabilities: OK"
    else
        log_warning "OWS capabilities: FAILED (may be due to GeoServer connectivity)"
    fi
    
    # Test frontend
    if curl -f http://localhost/ &> /dev/null; then
        log_success "Frontend: OK"
    else
        log_error "Frontend: FAILED"
    fi
}

# Function to display deployment information
show_deployment_info() {
    log_success "Deployment completed successfully!"
    echo ""
    echo "🌐 Access URLs:"
    echo "   Frontend:        http://localhost"
    echo "   API:             http://localhost/api"
    echo "   API Docs:        http://localhost/api-docs"
    echo "   Health Check:    http://localhost/health"
    echo ""
    echo "📊 Management Commands:"
    echo "   View logs:       cd $DOCKER_DIR && docker-compose -f docker-compose.linux.yml logs -f"
    echo "   Stop services:   cd $DOCKER_DIR && docker-compose -f docker-compose.linux.yml down"
    echo "   Restart:         cd $DOCKER_DIR && docker-compose -f docker-compose.linux.yml restart"
    echo "   Status:          cd $DOCKER_DIR && docker-compose -f docker-compose.linux.yml ps"
    echo ""
    echo "📁 Data Directories:"
    echo "   Logs:            $PROJECT_ROOT/logs"
    echo "   Data:            $PROJECT_ROOT/data"
    echo "   Cache:           $PROJECT_ROOT/cache"
    echo "   Reports:         $PROJECT_ROOT/reports"
    echo ""
    echo "⚙️  Configuration:"
    echo "   Environment:     $ENV_FILE"
}

# Main deployment function
main() {
    log_info "Starting SANSA Flood Monitoring System deployment..."
    
    check_prerequisites
    create_directories
    create_env_file
    deploy_services
    
    if wait_for_services; then
        run_tests
        show_status
        show_deployment_info
    else
        log_error "Deployment failed - services are not healthy"
        show_status
        exit 1
    fi
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log_info "Stopping services..."
        cd "$DOCKER_DIR"
        docker-compose -f docker-compose.linux.yml down
        log_success "Services stopped"
        ;;
    "restart")
        log_info "Restarting services..."
        cd "$DOCKER_DIR"
        docker-compose -f docker-compose.linux.yml restart
        log_success "Services restarted"
        ;;
    "status")
        show_status
        ;;
    "logs")
        cd "$DOCKER_DIR"
        docker-compose -f docker-compose.linux.yml logs -f
        ;;
    "test")
        run_tests
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|status|logs|test}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Deploy all services (default)"
        echo "  stop     - Stop all services"
        echo "  restart  - Restart all services"
        echo "  status   - Show service status"
        echo "  logs     - Show service logs"
        echo "  test     - Run endpoint tests"
        exit 1
        ;;
esac
