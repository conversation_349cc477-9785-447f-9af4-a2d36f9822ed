#!/bin/bash
set -e

# SANSA Flood Mapping System - Deployment Script
echo "🚀 Starting SANSA Flood Mapping System Deployment..."

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() { echo -e "${GREEN}[INFO]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_DIR="$( cd "$SCRIPT_DIR/../.." &> /dev/null && pwd )"

print_status "Project directory: $PROJECT_DIR"
cd "$PROJECT_DIR"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if environment file exists
if [ ! -f ".env.production" ]; then
    print_warning "Environment file not found. Creating from template..."
    cp deployment/configs/.env.production.example .env.production
    print_warning "Please edit .env.production with your configuration before continuing"
    read -p "Press enter to continue after editing the environment file..."
fi

# Create required directories
print_status "Creating required directories..."
mkdir -p {logs,data,backups,ssl,database/init,database/backups}
chmod 755 logs data backups ssl

# Build and start services
print_status "Building and starting services..."
docker compose -f deployment/docker/docker-compose.yml --env-file .env.production up -d --build

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 30

# Check service health
print_status "Checking service health..."
services=("database" "uiengine" "frontend" "nginx")
for service in "${services[@]}"; do
    if docker compose -f deployment/docker/docker-compose.yml ps --services --filter "status=running" | grep -q "$service"; then
        print_status "✓ $service is running"
    else
        print_error "✗ $service is not running"
        docker compose -f deployment/docker/docker-compose.yml logs "$service"
        exit 1
    fi
done

# Test endpoints
print_status "Testing endpoints..."
if curl -f http://localhost/health > /dev/null 2>&1; then
    print_status "✓ Nginx health check passed"
else
    print_warning "✗ Nginx health check failed"
fi

if curl -f http://localhost/ > /dev/null 2>&1; then
    print_status "✓ Frontend accessible"
else
    print_warning "✗ Frontend not accessible"
fi

# Setup SSL certificates (if not already configured)
if [ ! -f "ssl/cert.pem" ] || [ ! -f "ssl/private.key" ]; then
    print_warning "SSL certificates not found. Setting up self-signed certificates for development..."
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout ssl/private.key \
        -out ssl/cert.pem \
        -subj "/C=ZA/ST=Gauteng/L=Pretoria/O=SANSA/CN=localhost"
    
    print_status "Self-signed SSL certificates created"
    print_warning "For production, please configure proper SSL certificates"
    
    # Restart nginx to load certificates
    docker compose -f deployment/docker/docker-compose.yml restart nginx
fi

print_status "🎉 Deployment completed successfully!"
print_status ""
print_status "📊 Service Status:"
docker compose -f deployment/docker/docker-compose.yml ps
print_status ""
print_status "🌐 Access your application:"
print_status "  • Frontend: http://localhost"
print_status "  • API: http://localhost/api"
print_status "  • HTTPS: https://localhost (if SSL configured)"
print_status ""
print_status "📋 Next steps:"
print_status "  • Configure your domain name and proper SSL certificates"
print_status "  • Set up monitoring and alerting"
print_status "  • Review security settings"
print_status "  • Test the application functionality"
