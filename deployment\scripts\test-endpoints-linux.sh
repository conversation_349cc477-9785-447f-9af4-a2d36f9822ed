#!/bin/bash

# SANSA Flood Monitoring System - Linux Endpoint Testing Script
# Comprehensive testing of all API endpoints

set -euo pipefail

# Configuration
BASE_URL="${1:-http://localhost}"
VERBOSE="${2:-false}"
OUTPUT_DIR="$(pwd)/test-results"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test results
declare -a TEST_RESULTS=()
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${CYAN}[TEST]${NC} $1"
}

# Function to test an endpoint
test_endpoint() {
    local url="$1"
    local method="${2:-GET}"
    local description="$3"
    local data="${4:-}"
    local expected_status="${5:-200}"
    
    log_test "Testing: $description"
    echo "   URL: $url"
    echo "   Method: $method"
    
    ((TOTAL_TESTS++))
    
    local curl_cmd="curl -s -w '%{http_code}' -o /tmp/response.json"
    
    if [[ "$method" == "POST" && -n "$data" ]]; then
        curl_cmd="$curl_cmd -X POST -H 'Content-Type: application/json' -d '$data'"
    elif [[ "$method" != "GET" ]]; then
        curl_cmd="$curl_cmd -X $method"
    fi
    
    curl_cmd="$curl_cmd '$url'"
    
    local http_code
    if http_code=$(eval "$curl_cmd" 2>/dev/null); then
        if [[ "$http_code" == "$expected_status" ]]; then
            log_success "✅ PASSED"
            ((PASSED_TESTS++))
            TEST_RESULTS+=("PASS: $description")
            
            if [[ "$VERBOSE" == "true" ]]; then
                echo "   Response:"
                cat /tmp/response.json | jq . 2>/dev/null || cat /tmp/response.json
                echo ""
            fi
        else
            log_error "❌ FAILED - Expected status $expected_status, got $http_code"
            ((FAILED_TESTS++))
            TEST_RESULTS+=("FAIL: $description (HTTP $http_code)")
        fi
    else
        log_error "❌ FAILED - Connection error"
        ((FAILED_TESTS++))
        TEST_RESULTS+=("FAIL: $description (Connection error)")
    fi
    
    echo ""
}

# Function to create test data
create_test_data() {
    # Test alert rule data
    TEST_ALERT_RULE='{
        "name": "Test Water Level Alert",
        "description": "Test alert for high water levels",
        "dataset_name": "flood:water_levels",
        "field_name": "water_height",
        "threshold_value": 5.0,
        "threshold_operator": "greater_than",
        "is_active": true,
        "polling_interval_minutes": 5,
        "notification_email": "<EMAIL>"
    }'
    
    # Test ROI data
    TEST_ROI='{
        "name": "Test Region",
        "description": "Test region for endpoint testing",
        "geometry": {
            "type": "Polygon",
            "coordinates": [[[27.5, -26.5], [28.5, -26.5], [28.5, -27.5], [27.5, -27.5], [27.5, -26.5]]]
        },
        "created_by": "test_user"
    }'
}

# Function to setup test environment
setup_test_environment() {
    log_info "Setting up test environment..."
    
    # Create output directory
    mkdir -p "$OUTPUT_DIR"
    
    # Check if jq is available for JSON formatting
    if ! command -v jq &> /dev/null; then
        log_warning "jq not found - JSON responses will not be formatted"
    fi
    
    # Create test data
    create_test_data
    
    log_success "Test environment ready"
}

# Function to run core system tests
test_core_system() {
    log_info "Testing Core System Endpoints..."
    
    test_endpoint "$BASE_URL/health" "GET" "Health Check"
    test_endpoint "$BASE_URL/api-docs.json" "GET" "API Documentation"
}

# Function to test OWS endpoints
test_ows_endpoints() {
    log_info "Testing OWS (OGC Web Services) Endpoints..."
    
    test_endpoint "$BASE_URL/api/ows/capabilities" "GET" "WMS/WFS Capabilities"
    test_endpoint "$BASE_URL/api/ows/features?typeName=geonode:africa_mosaic_optmised&maxFeatures=10" "GET" "WFS Features"
    test_endpoint "$BASE_URL/api/ows/legend?layer=geonode:africa_mosaic_optmised&format=image/png" "GET" "WMS Legend"
    test_endpoint "$BASE_URL/api/ows/search?query=africa" "GET" "Spatial Search"
}

# Function to test dataset endpoints
test_dataset_endpoints() {
    log_info "Testing Dataset Management Endpoints..."
    
    test_endpoint "$BASE_URL/api/datasets" "GET" "List All Datasets"
    test_endpoint "$BASE_URL/api/datasets/summary" "GET" "Dataset Summary"
    test_endpoint "$BASE_URL/api/datasets/search?query=flood" "GET" "Search Datasets"
}

# Function to test alert system endpoints
test_alert_endpoints() {
    log_info "Testing Alert System Endpoints..."
    
    test_endpoint "$BASE_URL/api/alert-rules" "GET" "List Alert Rules"
    test_endpoint "$BASE_URL/api/alert-events" "GET" "List Alert Events"
    
    # Test creating an alert rule
    test_endpoint "$BASE_URL/api/alert-rules" "POST" "Create Alert Rule" "$TEST_ALERT_RULE" "201"
}

# Function to test report endpoints
test_report_endpoints() {
    log_info "Testing Report Generation Endpoints..."
    
    test_endpoint "$BASE_URL/api/reports" "GET" "List Available Reports"
}

# Function to test ROI endpoints
test_roi_endpoints() {
    log_info "Testing ROI (Region of Interest) Endpoints..."
    
    test_endpoint "$BASE_URL/api/roi" "GET" "List ROIs"
    
    # Test creating a ROI (may fail if PostGIS is disabled)
    test_endpoint "$BASE_URL/api/roi" "POST" "Create ROI" "$TEST_ROI" "201"
}

# Function to test discovery endpoints
test_discovery_endpoints() {
    log_info "Testing Discovery Endpoints..."
    
    test_endpoint "$BASE_URL/api/discoveries" "GET" "Layer Discovery"
}

# Function to generate test report
generate_test_report() {
    local report_file="$OUTPUT_DIR/test-report-$(date +%Y%m%d-%H%M%S).txt"
    local json_report="$OUTPUT_DIR/test-results-$(date +%Y%m%d-%H%M%S).json"
    
    # Text report
    {
        echo "SANSA Flood Monitoring System - Endpoint Test Report"
        echo "=================================================="
        echo "Date: $(date)"
        echo "Base URL: $BASE_URL"
        echo "Total Tests: $TOTAL_TESTS"
        echo "Passed: $PASSED_TESTS"
        echo "Failed: $FAILED_TESTS"
        echo "Success Rate: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
        echo ""
        echo "Test Results:"
        echo "============="
        for result in "${TEST_RESULTS[@]}"; do
            echo "$result"
        done
    } > "$report_file"
    
    # JSON report
    {
        echo "{"
        echo "  \"timestamp\": \"$(date -Iseconds)\","
        echo "  \"base_url\": \"$BASE_URL\","
        echo "  \"summary\": {"
        echo "    \"total\": $TOTAL_TESTS,"
        echo "    \"passed\": $PASSED_TESTS,"
        echo "    \"failed\": $FAILED_TESTS,"
        echo "    \"success_rate\": $(( PASSED_TESTS * 100 / TOTAL_TESTS ))"
        echo "  },"
        echo "  \"results\": ["
        local first=true
        for result in "${TEST_RESULTS[@]}"; do
            if [[ "$first" == "true" ]]; then
                first=false
            else
                echo ","
            fi
            echo -n "    \"$result\""
        done
        echo ""
        echo "  ]"
        echo "}"
    } > "$json_report"
    
    log_success "Test report generated: $report_file"
    log_success "JSON report generated: $json_report"
}

# Function to display summary
display_summary() {
    echo ""
    echo "=========================================="
    echo "           TEST SUMMARY REPORT"
    echo "=========================================="
    echo ""
    echo "📊 Statistics:"
    echo "   Total Tests: $TOTAL_TESTS"
    echo "   Passed: $PASSED_TESTS"
    echo "   Failed: $FAILED_TESTS"
    echo "   Success Rate: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    echo ""
    
    if [[ $FAILED_TESTS -gt 0 ]]; then
        echo "❌ Failed Tests:"
        for result in "${TEST_RESULTS[@]}"; do
            if [[ "$result" == FAIL* ]]; then
                echo "   • ${result#FAIL: }"
            fi
        done
        echo ""
    fi
    
    echo "✅ Passed Tests:"
    for result in "${TEST_RESULTS[@]}"; do
        if [[ "$result" == PASS* ]]; then
            echo "   • ${result#PASS: }"
        fi
    done
    echo ""
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        log_success "🎉 All tests passed!"
    else
        log_warning "⚠️  Some tests failed. Check the logs for details."
    fi
}

# Main function
main() {
    echo "🚀 SANSA Flood Monitoring System - Endpoint Testing"
    echo "Base URL: $BASE_URL"
    echo "Verbose: $VERBOSE"
    echo ""
    
    setup_test_environment
    
    # Run all test suites
    test_core_system
    test_ows_endpoints
    test_dataset_endpoints
    test_alert_endpoints
    test_report_endpoints
    test_roi_endpoints
    test_discovery_endpoints
    
    # Generate reports and display summary
    generate_test_report
    display_summary
    
    # Exit with appropriate code
    if [[ $FAILED_TESTS -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# Handle script arguments
if [[ "${1:-}" == "--help" || "${1:-}" == "-h" ]]; then
    echo "Usage: $0 [BASE_URL] [VERBOSE]"
    echo ""
    echo "Arguments:"
    echo "  BASE_URL  - Base URL for testing (default: http://localhost)"
    echo "  VERBOSE   - Show detailed responses (true/false, default: false)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Test localhost"
    echo "  $0 http://your-server.com            # Test remote server"
    echo "  $0 http://localhost true             # Test with verbose output"
    exit 0
fi

# Run main function
main
