networks:
  sansa_network:
    driver: bridge

volumes:
  frontend_build:
  uiengine_cache:
  uiengine_logs:
  nginx_ssl:
  nginx_logs:

services:
  uiengine:
    build:
      context: .
      dockerfile: deployment/docker/Dockerfile.uiengine
    container_name: uiengine
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      GEOSERVER_URL: ${GEOSERVER_URL:-https://10.150.16.184/geoserver}
      CORS_ORIGIN: ${CORS_ORIGIN:-*}
      CACHE_TTL: ${CACHE_TTL:-3600}
      CACHE_REFRESH: ${CACHE_REFRESH:-0 0 * * *}
      # Disable database connections for database-free deployment
      DISABLE_DATABASE: "true"
      DISABLE_POSTGIS: "true"
    ports:
      - "3001:3001"
    volumes:
      - uiengine_cache:/app/cache
      - uiengine_logs:/app/logs
    networks:
      - sansa_network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: frontend
    restart: unless-stopped
    environment:
      REACT_APP_API_BASE: ${REACT_APP_API_BASE:-/api}
      NODE_ENV: ${NODE_ENV:-production}
    volumes:
      - frontend_build:/usr/share/nginx/html
    depends_on:
      - uiengine
    networks:
      - sansa_network

  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "8080:80"  # Alternative port if 80 is busy
      # - "443:443" # Uncomment for HTTPS
    depends_on:
      - frontend
      - uiengine
    volumes:
      - frontend_build:/usr/share/nginx/html
      - nginx_ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    networks:
      - sansa_network
