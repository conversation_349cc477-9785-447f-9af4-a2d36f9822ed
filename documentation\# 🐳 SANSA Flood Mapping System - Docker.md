SANSA Flood Mapping System - Docker Deployment Guide

## 📋 Overview

This guide provides complete instructions for deploying the SANSA Flood Mapping System using Docker containers on a newly provisioned Linux server. The system uses a multi-container architecture with React frontend, Node.js backend, PostgreSQL database, and Nginx reverse proxy.

## 🏗️ System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │  React Frontend │    │ Node.js Backend │
│   (Port 80/443) │────│   (Port 3000)   │────│   (Port 3001)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │  Static Assets  │    │ PostgreSQL+GIS  │
         │              │    (Volume)     │    │   (Port 5432)   │
         └──────────────┴─────────────────┘    └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │  External GeoServer  │
                        │ (*************:443) │
                        └─────────────────┘
```

## 🔧 Prerequisites

### **Server Requirements:**
- **OS**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **CPU**: 4+ cores recommended
- **RAM**: 8GB+ recommended (4GB minimum)
- **Storage**: 50GB+ available space
- **Network**: Internet access and access to GeoServer network

### **Software Dependencies:**
- Docker Engine 24.0+
- Docker Compose 2.0+
- Git
- curl/wget

## 📦 Pre-Deployment Setup

### **Step 1: Server Preparation**

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y \
    apt-transport-https \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    git \
    ufw

# Configure firewall
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable
```

### **Step 2: Docker Installation**

```bash
# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Add Docker repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker

# Verify installation
docker --version
docker compose version
```

### **Step 3: Application Source Code**

```bash
# Clone the repository
cd /opt
sudo git clone <REPOSITORY_URL> sansa-flood-mapping
sudo chown -R $USER:$USER /opt/sansa-flood-mapping
cd /opt/sansa-flood-mapping

# Create required directories
mkdir -p {logs,data,backups,ssl}
```

## 🐳 Docker Configuration Files

### **Root Directory Structure:**
```
/opt/sansa-flood-mapping/
├── docker-compose.yml
├── docker-compose.prod.yml
├── .env.production
├── nginx/
│   ├── Dockerfile
│   ├── nginx.conf
│   └── ssl/
├── frontend/
│   ├── Dockerfile
│   ├── Dockerfile.prod
│   └── nginx.conf
├── backend/
│   ├── Dockerfile
│   ├── Dockerfile.prod
│   └── .env.example
├── database/
│   ├── init/
│   │   ├── 01-create-extensions.sql
│   │   └── 02-init-schema.sql
│   └── backups/
└── scripts/
    ├── deploy.sh
    ├── backup.sh
    └── restore.sh
```

### **docker-compose.yml**

```yaml
version: '3.8'

services:
  # PostgreSQL Database with PostGIS
  database:
    image: postgis/postgis:15-3.3
    container_name: sansa-database
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-sansa_flood_db}
      POSTGRES_USER: ${POSTGRES_USER:-sansa_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_123}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/backups:/backups
    ports:
      - "5432:5432"
    networks:
      - sansa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-sansa_user} -d ${POSTGRES_DB:-sansa_flood_db}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: sansa-backend
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: postgresql://${POSTGRES_USER:-sansa_user}:${POSTGRES_PASSWORD:-secure_password_123}@database:5432/${POSTGRES_DB:-sansa_flood_db}
      GEOSERVER_URL: ${GEOSERVER_URL:-https://*************/geoserver}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost}
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_here}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    ports:
      - "3001:3001"
    depends_on:
      database:
        condition: service_healthy
    networks:
      - sansa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React Application
  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile.prod
      args:
        REACT_APP_API_URL: ${REACT_APP_API_URL:-http://localhost:3001/api}
        REACT_APP_GEOSERVER_URL: ${REACT_APP_GEOSERVER_URL:-https://*************/geoserver}
        REACT_APP_ENVIRONMENT: production
        REACT_APP_DEMO_MODE: ${REACT_APP_DEMO_MODE:-false}
    container_name: sansa-frontend
    volumes:
      - ./nginx/frontend.conf:/etc/nginx/conf.d/default.conf:ro
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - sansa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: sansa-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - sansa-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local

networks:
  sansa-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### **Environment Configuration (.env.production)**

```bash
# Database Configuration
POSTGRES_DB=sansa_flood_db
POSTGRES_USER=sansa_user
POSTGRES_PASSWORD=SecurePassword123!@#
POSTGRES_HOST=database
POSTGRES_PORT=5432

# Backend Configuration
NODE_ENV=production
JWT_SECRET=your-super-secure-jwt-secret-here-min-32-chars
LOG_LEVEL=info
CORS_ORIGIN=https://your-domain.com

# External Services
GEOSERVER_URL=https://*************/geoserver
GEOSERVER_USERNAME=admin
GEOSERVER_PASSWORD=geoserver

# Frontend Configuration
REACT_APP_API_URL=https://your-domain.com/api
REACT_APP_GEOSERVER_URL=https://*************/geoserver
REACT_APP_ENVIRONMENT=production
REACT_APP_DEMO_MODE=false
REACT_APP_NETWORK_MODE=auto

# SSL Configuration
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/private.key

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=sansa-backups
```

## 📁 Docker Files

### **Frontend Dockerfile (frontend/Dockerfile.prod)**

```dockerfile
# Multi-stage build for React frontend
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY src/ ./src/
COPY public/ ./public/
COPY tsconfig.json ./
COPY vite.config.ts ./

# Install dependencies
RUN npm ci --only=production

# Build arguments
ARG REACT_APP_API_URL
ARG REACT_APP_GEOSERVER_URL
ARG REACT_APP_ENVIRONMENT
ARG REACT_APP_DEMO_MODE

# Set environment variables
ENV REACT_APP_API_URL=$REACT_APP_API_URL
ENV REACT_APP_GEOSERVER_URL=$REACT_APP_GEOSERVER_URL
ENV REACT_APP_ENVIRONMENT=$REACT_APP_ENVIRONMENT
ENV REACT_APP_DEMO_MODE=$REACT_APP_DEMO_MODE

# Build application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built files
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY frontend/nginx.conf /etc/nginx/conf.d/default.conf

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80 || exit 1

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### **Backend Dockerfile (backend/Dockerfile.prod)**

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache curl

# Copy package files
COPY backend/package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY backend/src/ ./src/
COPY backend/tsconfig.json ./

# Build TypeScript
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Create required directories
RUN mkdir -p /app/logs /app/data && chown -R nodejs:nodejs /app

USER nodejs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/api/health || exit 1

EXPOSE 3001

CMD ["npm", "start"]
```

### **Nginx Configuration (nginx/nginx.conf)**

```nginx
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;

    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript 
               application/javascript application/xml+rss 
               application/json application/xml;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=1r/s;

    # Upstream servers
    upstream backend {
        server frontend:80;
        keepalive 32;
    }

    upstream api {
        server backend:3001;
        keepalive 32;
    }

    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name _;
        return 301 https://$server_name$request_uri;
    }

    # Main server block
    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/private.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://api/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # Frontend routes
        location / {
            limit_req zone=general burst=10 nodelay;
            proxy_pass http://backend/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            
            # Handle client-side routing
            try_files $uri $uri/ /index.html;
        }

        # Static assets caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
```

## 🚀 Deployment Process

### **Step 1: Initial Deployment**

```bash
# Navigate to application directory
cd /opt/sansa-flood-mapping

# Copy environment file
cp .env.example .env.production

# Edit environment variables
nano .env.production

# Build and start services
docker compose -f docker-compose.yml --env-file .env.production up -d --build

# Verify deployment
docker compose ps
docker compose logs -f
```

### **Step 2: SSL Certificate Setup**

```bash
# Option 1: Let's Encrypt (Recommended)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# Option 2: Self-signed (Development)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout ./nginx/ssl/private.key \
    -out ./nginx/ssl/cert.pem

# Restart nginx
docker compose restart nginx
```

### **Step 3: Database Initialization**

```bash
# Run database migrations
docker compose exec backend npm run migrate

# Seed initial data (if applicable)
docker compose exec backend npm run seed

# Verify database connection
docker compose exec database psql -U sansa_user -d sansa_flood_db -c "\dt"
```

### **Step 4: Health Verification**

```bash
# Check all services
docker compose ps

# Test endpoints
curl -f http://localhost/health
curl -f http://localhost/api/health

# View logs
docker compose logs nginx
docker compose logs backend
docker compose logs frontend
docker compose logs database
```

## 📊 Monitoring and Maintenance

### **Log Management**

```bash
# View real-time logs
docker compose logs -f [service_name]

# Log rotation setup
sudo nano /etc/logrotate.d/sansa-flood-mapping
```

### **Backup Script (scripts/backup.sh)**

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/sansa-flood-mapping/database/backups"

# Database backup
docker compose exec -T database pg_dump -U sansa_user sansa_flood_db > "$BACKUP_DIR/db_backup_$DATE.sql"

# Compress backup
gzip "$BACKUP_DIR/db_backup_$DATE.sql"

# Clean old backups (keep 30 days)
find "$BACKUP_DIR" -name "*.sql.gz" -mtime +30 -delete

echo "Backup completed: db_backup_$DATE.sql.gz"
```

### **Update Script (scripts/update.sh)**

```bash
#!/bin/bash
set -e

echo "Starting SANSA Flood Mapping System Update..."

# Pull latest code
git pull origin main

# Backup database
./scripts/backup.sh

# Update containers
docker compose -f docker-compose.yml --env-file .env.production up -d --build

# Verify deployment
sleep 30
curl -f http://localhost/health || exit 1

echo "Update completed successfully!"
```

## 🔒 Security Considerations

### **Firewall Configuration**

```bash
# Allow only necessary ports
sudo ufw allow 22/tcp   # SSH
sudo ufw allow 80/tcp   # HTTP
sudo ufw allow 443/tcp  # HTTPS

# Block database port from external access
sudo ufw deny 5432/tcp
```

### **Docker Security**

```bash
# Run Docker in rootless mode (optional)
dockerd-rootless-setuptool.sh install

# Enable Docker Content Trust
export DOCKER_CONTENT_TRUST=1

# Regular security updates
sudo apt update && sudo apt upgrade -y
docker system prune -f
```

## 🆘 Troubleshooting

### **Common Issues**

1. **Service won't start:**
   ```bash
   docker compose logs [service_name]
   docker compose restart [service_name]
   ```

2. **Database connection issues:**
   ```bash
   docker compose exec database pg_isready -U sansa_user
   ```

3. **Network connectivity:**
   ```bash
   docker network ls
   docker network inspect sansa-flood-mapping_sansa-network
   ```

4. **SSL certificate issues:**
   ```bash
   sudo certbot renew --dry-run
   ```

### **Performance Optimization**

```bash
# Monitor resource usage
docker stats

# Optimize database
docker compose exec database vacuumdb -U sansa_user -d sansa_flood_db -z

# Clear Docker cache
docker system prune -f
docker volume prune -f
```

## 📋 Deployment Checklist

- [ ] Server meets minimum requirements
- [ ] Docker and Docker Compose installed
- [ ] Application source code deployed
- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] Database initialized and migrated
- [ ] All services running and healthy
- [ ] External GeoServer connectivity verified
- [ ] Backup system configured
- [ ] Monitoring and logging setup
- [ ] Security hardening completed
- [ ] Performance optimization applied

## 🎯 Post-Deployment Testing

1. **Frontend Testing:**
   - Application loads correctly
   - Layer visualization works
   - Date range selection functions
   - Network switching (live/demo) operates

2. **Backend Testing:**
   - API endpoints respond correctly
   - Database queries execute successfully
   - GeoServer integration functional
   - Authentication system working

3. **Performance Testing:**
   - Load testing with concurrent users
   - Memory and CPU usage monitoring
   - Database performance verification
   - Network latency testing

## 📞 Support and Documentation

- **Application Logs:** `/opt/sansa-flood-mapping/logs/`
- **Database Backups:** `/opt/sansa-flood-mapping/database/backups/`
- **Configuration Files:** `/opt/sansa-flood-mapping/.env.production`
- **Docker Compose:** `/opt/sansa-flood-mapping/docker-compose.yml`

---

**Deployment Guide Version:** 1.0  
**Last Updated:** June 13, 2025  
**Compatibility:** Docker 24.0+, Ubuntu 20.04+, CentOS 8+
