# Backward Compatibility & Graceful Degradation

## Overview

The SANSA Flood Mapping application is designed to work with or without PostGIS, ensuring backward compatibility and graceful degradation when spatial database features are not available.

## Application Modes

### 🟢 Full Mode (PostGIS Available)
- ✅ All core flood mapping features
- ✅ Spatial database with PostGIS
- ✅ ROI (Region of Interest) management
- ✅ Spatial analysis capabilities
- ✅ Polygon drawing and storage
- ✅ Advanced geospatial queries

### 🟡 Minimal Mode (PostGIS Unavailable)
- ✅ All core flood mapping features
- ✅ GeoServer integration
- ✅ Map visualization
- ✅ Basic flood monitoring
- ❌ ROI management (gracefully disabled)
- ❌ Spatial analysis features

## Configuration

### Environment Variables

```bash
# Enable/disable PostGIS features
ENABLE_POSTGIS=true     # Set to 'false' to disable PostGIS
NODE_ENV=production     # Set to 'minimal' for minimal mode

# Database configuration (optional)
DB_HOST=database
DB_PORT=5432
POSTGRES_USER=sansa_user
POSTGRES_PASSWORD=secure_password_123
POSTGRES_DB=sansa_flood_db
```

### Docker Deployment Options

#### Option 1: Full Deployment (with PostGIS)
```bash
# Use the complete docker-compose.yml
docker-compose -f deployment/docker/docker-compose.yml up -d
```

#### Option 2: Minimal Deployment (without PostGIS)
```bash
# Create a minimal docker-compose without database
docker-compose -f deployment/docker/docker-compose.minimal.yml up -d
```

#### Option 3: Disable PostGIS in existing deployment
```bash
# Set environment variable to disable PostGIS
export ENABLE_POSTGIS=false
docker-compose -f deployment/docker/docker-compose.yml up -d
```

## Backend Behavior

### Startup Process

1. **Module Loading**: Conditionally loads PostGIS modules
   ```
   ✅ PostGIS modules loaded successfully
   OR
   ⚠️ PostGIS modules not available, running without spatial features
   ```

2. **Route Registration**: Conditionally registers PostGIS routes
   ```
   ✅ PostGIS ROI routes enabled at /api/roi
   OR
   ⚠️ PostGIS not available - ROI endpoints will return 503
   ```

3. **Health Checks**: Non-blocking database connection tests
   ```
   ✅ PostGIS integration verified and ready
   OR
   ⚠️ PostGIS integration not available, continuing without spatial features
   ```

### API Endpoints

#### Always Available (Core Features)
- `GET /api/health` - Health check with feature status
- `GET /api/ows/*` - GeoServer proxy endpoints
- All existing flood mapping functionality

#### Conditionally Available (PostGIS Features)
- `POST /api/roi` - Create ROI (503 if PostGIS unavailable)
- `GET /api/roi` - Get ROIs (503 if PostGIS unavailable)
- `PUT /api/roi/:id` - Update ROI (503 if PostGIS unavailable)
- `DELETE /api/roi/:id` - Delete ROI (503 if PostGIS unavailable)
- `POST /api/roi/analysis` - Spatial analysis (503 if PostGIS unavailable)

### Error Responses

When PostGIS is not available, ROI endpoints return:
```json
{
  "success": false,
  "error": "PostGIS features not available",
  "message": "PostGIS database is not configured. Please check your database connection and ensure PostGIS dependencies are installed.",
  "fallback": true
}
```

## Frontend Behavior

### Feature Detection

The frontend automatically detects PostGIS availability:

```javascript
// Check if spatial features are available
const available = await roiApiService.checkAvailability();

if (available) {
  // Show polygon drawing tools
  // Enable ROI management
  // Display spatial analysis options
} else {
  // Hide spatial features
  // Show informational message
  // Disable polygon drawing
}
```

### Graceful Degradation

1. **UI Components**: Spatial features are conditionally rendered
2. **Error Handling**: Clear messages when features are unavailable
3. **Fallback Behavior**: Core functionality remains fully operational

## Health Check Response

The `/api/health` endpoint provides feature status:

```json
{
  "status": "ok",
  "timestamp": "2025-06-13T10:30:00.000Z",
  "database": "connected",     // or "disconnected"
  "postgis": "available",      // or "unavailable"
  "features": {
    "core": true,              // Always true
    "spatial": true            // true only if PostGIS available
  }
}
```

## Deployment Scenarios

### Scenario 1: Development Environment
```bash
# Install all dependencies
npm install

# Start with PostGIS
npm run dev
```

### Scenario 2: Production with PostGIS
```bash
# Deploy with full docker-compose
docker-compose -f deployment/docker/docker-compose.yml up -d
```

### Scenario 3: Production without PostGIS
```bash
# Deploy without database container
ENABLE_POSTGIS=false docker-compose -f deployment/docker/docker-compose.yml up -d
```

### Scenario 4: Minimal Installation
```bash
# Install only core dependencies
npm install --production --omit=optional

# Start in minimal mode
NODE_ENV=minimal npm start
```

## Troubleshooting

### PostGIS Connection Issues

1. **Check Health Endpoint**: Visit `/api/health` to see status
2. **Review Logs**: Look for PostGIS-related warnings
3. **Verify Environment**: Ensure database variables are set
4. **Test Connection**: Try connecting to PostgreSQL directly

### Common Issues

#### "PostGIS modules not available"
- **Cause**: Missing database dependencies
- **Solution**: Install `pg`, `knex`, `joi` packages OR set `ENABLE_POSTGIS=false`

#### "Database connection failed"
- **Cause**: PostgreSQL not running or unreachable
- **Solution**: Check database container status and connection parameters

#### "PostGIS not available"
- **Cause**: PostGIS extension not installed in database
- **Solution**: Verify PostGIS is installed in PostgreSQL OR run in minimal mode

## Migration Guide

### From Previous Version (without PostGIS)
1. **No changes required** - Application runs exactly as before
2. **Optional**: Add PostGIS for enhanced features
3. **Recommended**: Use environment variables to control features

### Adding PostGIS to Existing Deployment
1. **Install dependencies**: `npm install pg knex joi @types/pg`
2. **Add database container** to docker-compose
3. **Set environment variables** for database connection
4. **Restart application** - PostGIS features will be automatically enabled

### Removing PostGIS from Deployment
1. **Set environment variable**: `ENABLE_POSTGIS=false`
2. **Restart application** - Will continue without spatial features
3. **Optional**: Remove database container and dependencies

## Best Practices

1. **Always test** both modes in your environment
2. **Monitor health endpoint** for feature availability
3. **Use environment variables** to control features
4. **Provide clear user feedback** when features are unavailable
5. **Plan for graceful degradation** in your frontend design

## Conclusion

This design ensures that:
- ✅ **Existing deployments continue to work** unchanged
- ✅ **PostGIS features are optional** and can be disabled
- ✅ **Clear error messages** guide users when features are unavailable
- ✅ **Graceful degradation** maintains core functionality
- ✅ **Easy migration path** for adding or removing PostGIS
