# GeoServer Certificate Validation Fix

## Problem
The SANSA Flood Mapping application was failing to connect to the GeoServer at `https://10.150.16.184/geoserver` due to SSL certificate validation errors. These errors occurred because the GeoServer is using a self-signed or untrusted certificate.

## Solution
We implemented a server-side solution to bypass certificate validation in the UI Engine's requests to GeoServer. This approach is safer than disabling certificate validation in the browser and maintains the proper security model where the UI Engine acts as a proper proxy to the GeoServer.

## Implementation Details

### 1. Created Secure Request Utility
- Created a new utility file `secureRequest.js` in the UI Engine to handle secure requests with certificate validation bypass
- Added TypeScript declaration file `secureRequest.d.ts` for proper type support
- The utility exports `secureGet` and `securePost` functions that use axios with certificate validation disabled

### 2. Updated GeoServer Service
- Modified `geoServerService.ts` to use the secure request utility instead of direct axios calls
- Removed the inline httpsAgent configuration in favor of the centralized utility
- This ensures all requests to the GeoServer have certificate validation bypassed

### 3. Updated OWS Router
- Modified `ows.ts` to use the secure request utility for GeoServer requests
- Created an update helper script `update-axios-calls.js` to assist in replacing multiple instances
- This ensures all proxy requests through the OWS endpoints have certificate validation bypassed

### 4. Added Startup Validation
- Created `geoServerValidator.js` to validate the GeoServer connection on startup
- Added TypeScript declaration file `geoServerValidator.d.ts` for proper type support
- Modified `server.ts` to run the validation when the server starts
- This provides early feedback if the GeoServer is unreachable

### 5. Created Test Scripts
- Added `test-certificate-bypass.js` to verify the secure request utility works correctly
- The test script connects to the GeoServer and verifies that the connection succeeds

## Security Considerations
- Certificate validation bypass is intended for development environments only
- For production, proper certificates should be installed on the GeoServer
- The bypass is implemented only on the server side (UI Engine), not in the browser
- This approach maintains the proper security model where client connections remain validated

## Next Steps
1. Test the UI Engine with the frontend to verify layer discovery works correctly
2. Monitor for any remaining certificate errors
3. For production deployment, consider proper certificate installation on the GeoServer
