# 🚀 SANSA Flood Mapping - Installation Guide

## 📦 Pre-Deployment Setup

### **Step 1: Server Preparation**

```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release git ufw
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable
```

### **Step 2: Docker Installation**

```bash
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
sudo usermod -aG docker $USER
newgrp docker
docker --version
docker compose version
```

### **Step 3: Application Source Code**

```bash
cd /opt
sudo git clone <REPOSITORY_URL> sansa-flood-mapping
sudo chown -R $USER:$USER /opt/sansa-flood-mapping
cd /opt/sansa-flood-mapping
mkdir -p {logs,data,backups,ssl}
```

## 🚀 Deployment Process

### **Step 1: Initial Deployment**

```bash
cd /opt/sansa-flood-mapping
cp deployment/configs/.env.production.example .env.production

# Edit environment variables - IMPORTANT: Update domains and passwords
nano .env.production

# Create required directories
mkdir -p {logs,data,backups,ssl,database/init,database/backups}

# Build and start all services
docker compose -f deployment/docker/docker-compose.yml --env-file .env.production up -d --build

# Check all containers are running
docker compose -f deployment/docker/docker-compose.yml ps

# Monitor startup logs
docker compose -f deployment/docker/docker-compose.yml logs -f
```

### **Step 2: SSL Certificate Setup**

```bash
# Option 1: Let's Encrypt (Production - Recommended)
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ./ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ./ssl/private.key
sudo chown $USER:$USER ./ssl/*

# Option 2: Self-signed (Development/Testing)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout ./ssl/private.key \
    -out ./ssl/cert.pem \
    -subj "/C=ZA/ST=Gauteng/L=Pretoria/O=SANSA/CN=localhost"

# Restart nginx to load certificates
docker compose -f deployment/docker/docker-compose.yml restart nginx
```

### **Step 3: Database Initialization**

```bash
# Wait for database to be ready
docker compose -f deployment/docker/docker-compose.yml exec database pg_isready -U sansa_user

# Run database migrations (if you have them)
# docker compose -f deployment/docker/docker-compose.yml exec backend npm run migrate

# Seed initial data (if you have seed data)
# docker compose -f deployment/docker/docker-compose.yml exec backend npm run seed

# Test database connection
docker compose -f deployment/docker/docker-compose.yml exec database psql -U sansa_user -d sansa_flood_db -c "\dt"
```

### **Step 4: Health Verification**

```bash
# Check all containers are running and healthy
docker compose -f deployment/docker/docker-compose.yml ps

# Test reverse proxy endpoints
curl -f http://localhost/health                    # Nginx health check
curl -f http://localhost/                          # Frontend (React app)
curl -f http://localhost/api/                      # Backend API (via proxy)

# Test SSL (if configured)
curl -f https://localhost/health                   # HTTPS health check

# Check individual service logs
docker compose -f deployment/docker/docker-compose.yml logs nginx
docker compose -f deployment/docker/docker-compose.yml logs backend
docker compose -f deployment/docker/docker-compose.yml logs frontend
docker compose -f deployment/docker/docker-compose.yml logs database

# Monitor all services
docker compose -f deployment/docker/docker-compose.yml logs -f --tail=50
```

## 📋 Post-Installation Checklist

- [ ] All Docker containers running and healthy
- [ ] Database connection successful
- [ ] SSL certificates configured
- [ ] External services accessible
- [ ] Backup system operational
- [ ] Monitoring configured
- [ ] Security hardening applied

For troubleshooting, see TROUBLESHOOTING.md.
