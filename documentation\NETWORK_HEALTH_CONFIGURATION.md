# Network Health System Configuration

The SANSA Flood Mapping application includes an optional Network Health System that can be completely disabled via configuration. This document explains how to control this feature.

## Environment Variables

### Core Network Health Control

#### `VITE_ENABLE_NETWORK_HEALTH`
- **Type**: Boolean (string)
- **Default**: `true` (enabled)
- **Description**: Master toggle for the entire network health system
- **Values**:
  - `"true"` or omitted: Enable network health monitoring (default)
  - `"false"`: Completely disable network health system

**Example**:
```bash
# Disable network health system entirely
VITE_ENABLE_NETWORK_HEALTH=false

# Enable network health system (default behavior)
VITE_ENABLE_NETWORK_HEALTH=true
# or omit the variable entirely
```

#### `VITE_DEFAULT_DATA_MODE`
- **Type**: String
- **Default**: `"live"`
- **Description**: When network health is disabled, this determines the data mode
- **Values**:
  - `"live"`: Use live data sources
  - `"demo"`: Use demo/mock data

**Example**:
```bash
# Use demo data when network health is disabled
VITE_ENABLE_NETWORK_HEALTH=false
VITE_DEFAULT_DATA_MODE=demo
```

### Legacy Network Health Configuration (when enabled)

These variables only apply when `VITE_ENABLE_NETWORK_HEALTH=true`:

#### `VITE_NETWORK_MODE`
- **Type**: String
- **Default**: `"auto"`
- **Values**: `"auto"`, `"live"`, `"demo"`

#### `VITE_ENABLE_FALLBACK`
- **Type**: Boolean
- **Default**: `true`

#### `VITE_HEALTH_CHECK_INTERVAL`
- **Type**: Number (milliseconds)
- **Default**: `30000` (30 seconds)

#### `VITE_MAX_RETRIES`
- **Type**: Number
- **Default**: `3`

#### `VITE_RETRY_DELAY`
- **Type**: Number (milliseconds)
- **Default**: `2000` (2 seconds)

## Configuration Scenarios

### Scenario 1: Completely Disable Network Health
**Use Case**: Simple deployment where you don't want any network monitoring or fallback complexity.

```bash
VITE_ENABLE_NETWORK_HEALTH=false
VITE_DEFAULT_DATA_MODE=live
```

**Result**:
- No network health checks
- No DataSourceIndicator in UI
- No DataSourceControls in UI
- No NetworkChoiceModal
- No health monitoring intervals
- Application always uses live data mode
- Simplified, lightweight operation

### Scenario 2: Demo-Only Mode (Network Health Disabled)
**Use Case**: Demo environment or training deployment.

```bash
VITE_ENABLE_NETWORK_HEALTH=false
VITE_DEFAULT_DATA_MODE=demo
```

**Result**:
- No network health checks
- No network health UI components
- Application always uses demo data
- Perfect for disconnected environments

### Scenario 3: Full Network Health Enabled (Default)
**Use Case**: Production deployment with resilience and monitoring.

```bash
VITE_ENABLE_NETWORK_HEALTH=true
VITE_NETWORK_MODE=auto
VITE_HEALTH_CHECK_INTERVAL=30000
```

**Result**:
- Full network health monitoring
- Automatic fallback to demo mode if services unavailable
- User choice modal for network issues
- Real-time health indicators in UI
- Continuous monitoring

## UI Component Behavior

### When Network Health is ENABLED (`VITE_ENABLE_NETWORK_HEALTH=true`):
- **DataSourceIndicator**: Shows in NavBar with live status
- **DataSourceControls**: Available in Sidebar and NavBar for manual control
- **NetworkChoiceModal**: Appears when services are unavailable
- **Health Monitoring**: Continuous background checks

### When Network Health is DISABLED (`VITE_ENABLE_NETWORK_HEALTH=false`):
- **DataSourceIndicator**: Not rendered (returns `null`)
- **DataSourceControls**: Not rendered (returns `null`)
- **NetworkChoiceModal**: Not rendered (returns `null`)
- **Health Monitoring**: No background processes
- **Performance**: Reduced overhead, faster startup

## Application Startup Behavior

### Network Health Enabled:
1. App renders immediately with demo data
2. 100ms delay, then network health initialization begins
3. If services available: Switch to live mode
4. If services unavailable: Show NetworkChoiceModal
5. User chooses data mode or retries

### Network Health Disabled:
1. App renders immediately
2. Uses configured default data mode (`VITE_DEFAULT_DATA_MODE`)
3. No network checks or delays
4. Simple, predictable behavior

## Code Integration

### Service Layer
```typescript
// Abstraction layer handles the logic
import { 
  useNetworkHealth, 
  getCurrentDataMode, 
  initializeNetworkHealth 
} from './services/networkHealthAbstraction';

// These functions work regardless of network health state
const dataMode = getCurrentDataMode(); // Returns 'live' or 'demo'
const isLive = isUsingLiveData(); // Boolean check
```

### Component Layer
```typescript
// Conditional components only render when enabled
import { 
  DataSourceIndicator, 
  DataSourceControls 
} from '../DataSource/ConditionalComponents';

// These return null when network health is disabled
<DataSourceIndicator className="navbar-indicator" />
<DataSourceControls compact />
```

## Deployment Examples

### Docker Compose - Simple Mode
```yaml
services:
  frontend:
    build: ./frontend
    environment:
      - VITE_ENABLE_NETWORK_HEALTH=false
      - VITE_DEFAULT_DATA_MODE=live
```

### Docker Compose - Full Monitoring
```yaml
services:
  frontend:
    build: ./frontend
    environment:
      - VITE_ENABLE_NETWORK_HEALTH=true
      - VITE_NETWORK_MODE=auto
      - VITE_HEALTH_CHECK_INTERVAL=30000
```

### Kubernetes ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: sansa-config
data:
  VITE_ENABLE_NETWORK_HEALTH: "false"
  VITE_DEFAULT_DATA_MODE: "demo"
```

## Migration Guide

### From Network Health Enabled to Disabled

1. **Set Environment Variable**:
   ```bash
   VITE_ENABLE_NETWORK_HEALTH=false
   ```

2. **Choose Data Mode**:
   ```bash
   VITE_DEFAULT_DATA_MODE=live  # or demo
   ```

3. **Rebuild Application**:
   ```bash
   npm run build
   ```

4. **Verify Behavior**:
   - No network health components in UI
   - No background network checks
   - Application starts faster
   - Consistent data mode

### From Disabled to Enabled

1. **Set Environment Variable**:
   ```bash
   VITE_ENABLE_NETWORK_HEALTH=true
   ```

2. **Configure Network Settings**:
   ```bash
   VITE_NETWORK_MODE=auto
   VITE_HEALTH_CHECK_INTERVAL=30000
   ```

3. **Rebuild and Test**:
   - Verify health indicators appear
   - Test network failure scenarios
   - Confirm modal behavior

## Troubleshooting

### Network Health Not Disabling
- Ensure `VITE_ENABLE_NETWORK_HEALTH=false` is set correctly
- Rebuild the application after changing environment variables
- Check browser developer tools for any errors

### Components Still Rendering
- Verify the environment variable is being read correctly
- Check that you're importing from `ConditionalComponents`
- Clear browser cache and rebuild

### Performance Issues
- If network health is causing slowdowns, disable it with `VITE_ENABLE_NETWORK_HEALTH=false`
- Consider increasing `VITE_HEALTH_CHECK_INTERVAL` for less frequent checks

## Summary

The Network Health System in SANSA is fully configurable and can be completely disabled for simpler deployments. Use `VITE_ENABLE_NETWORK_HEALTH=false` to create a lightweight, deterministic application without any network monitoring overhead.
