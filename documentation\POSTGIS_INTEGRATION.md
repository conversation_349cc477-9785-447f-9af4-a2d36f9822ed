# PostGIS Integration for SANSA Flood Mapping

## Overview

This document outlines the PostGIS integration implemented for the SANSA Flood Mapping project, providing spatial database capabilities for polygon/ROI (Region of Interest) queries and geospatial analysis.

## Current PostGIS Status

✅ **IMPLEMENTED:**
- PostGIS database container in Docker setup
- Database initialization with spatial tables
- Backend API endpoints for ROI management
- Spatial query capabilities
- Performance-optimized spatial indexes

❌ **NOT YET IMPLEMENTED:**
- Frontend integration with backend ROI endpoints
- Real-time spatial analysis
- Advanced geospatial processing

## Database Schema

### Core Tables

#### `regions_of_interest`
Stores user-drawn polygons and regions for analysis:
- `id` - Primary key
- `name` - User-defined name
- `description` - Optional description
- `geometry` - PostGIS POLYGON geometry (EPSG:4326)
- `area_sqkm` - Automatically calculated area
- `status` - active/archived/processing
- `metadata` - JSON metadata
- `created_at/updated_at` - Timestamps

#### `spatial_analyses`
Stores results of spatial analysis operations:
- `roi_id` - Reference to ROI
- `analysis_type` - flood_extent/water_level/damage_assessment/temporal_change
- `results` - JSON results
- `geometry` - Result geometries
- `processing_time_ms` - Performance tracking

#### `flood_events`
Historical flood event data:
- `event_name` - Event identifier
- `event_date` - When the flood occurred
- `affected_geometry` - Spatial extent of flooding
- `water_level_m` - Peak water level
- `population_affected` - Impact metrics

#### `water_measurements`
Point-based water level measurements:
- `measurement_point` - PostGIS POINT geometry
- `water_level_m` - Measured water level
- `measurement_date` - When measured
- `quality_indicators` - Data quality metrics

## API Endpoints

### ROI Management
- **POST** `/api/roi` - Create new ROI from polygon
- **GET** `/api/roi` - Get all ROIs with statistics
- **GET** `/api/roi/:id` - Get specific ROI
- **PUT** `/api/roi/:id` - Update ROI
- **DELETE** `/api/roi/:id` - Delete ROI

### Spatial Queries
- **POST** `/api/roi/intersect` - Find ROIs intersecting with geometry
- **POST** `/api/roi/analysis` - Perform spatial analysis
- **GET** `/api/roi/:id/flood-events` - Get flood events within ROI
- **GET** `/api/roi/:id/water-measurements` - Get measurements within ROI

## Performance Enhancements with PostGIS

### 1. Spatial Indexing
```sql
-- GIST indexes for fast spatial queries
CREATE INDEX idx_roi_geometry ON regions_of_interest USING GIST (geometry);
CREATE INDEX idx_flood_events_geometry ON flood_events USING GIST (affected_geometry);
CREATE INDEX idx_water_measurements_point ON water_measurements USING GIST (measurement_point);
```

### 2. Optimized Spatial Queries
- **ST_Intersects()** - Fast polygon intersection
- **ST_Within()** - Point-in-polygon queries
- **ST_Buffer()** - Buffer zone analysis
- **ST_Area()** - Automatic area calculations

### 3. Generated Columns
```sql
-- Automatically calculated area in square kilometers
area_sqkm DECIMAL GENERATED ALWAYS AS (ST_Area(geometry::geography) / 1000000) STORED
```

### 4. Spatial Views
```sql
-- Pre-computed statistics for fast dashboard queries
CREATE VIEW roi_with_stats AS
SELECT r.*, COUNT(sa.id) as analysis_count, MAX(sa.analysis_date) as last_analysis_date
FROM regions_of_interest r LEFT JOIN spatial_analyses sa ON r.id = sa.roi_id
GROUP BY r.id;
```

## Integration Benefits

### Current Capabilities
1. **Fast Polygon Storage** - Native spatial data types
2. **Efficient Spatial Queries** - Optimized for GIS operations
3. **Area Calculations** - Automatic area computation
4. **Spatial Relationships** - Intersection, containment, buffer analysis
5. **Performance Monitoring** - Query timing and optimization

### Future Enhancements
1. **Raster Analysis** - Satellite imagery processing
2. **Temporal Analysis** - Time-series spatial data
3. **Advanced Analytics** - Machine learning integration
4. **Real-time Processing** - Stream processing capabilities

## Usage Examples

### Creating a ROI
```javascript
const newROI = {
  name: "Vaal River Critical Zone",
  description: "High-risk flood monitoring area",
  geometry: {
    type: "Polygon",
    coordinates: [[[27.5, -26.5], [28.5, -26.5], [28.5, -27.5], [27.5, -27.5], [27.5, -26.5]]]
  },
  created_by: "user123"
};

const response = await fetch('/api/roi', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(newROI)
});
```

### Spatial Analysis
```javascript
const analysis = {
  roi_id: 1,
  analysis_type: "flood_extent",
  start_date: "2024-01-01",
  end_date: "2024-12-31",
  buffer_meters: 1000
};

const response = await fetch('/api/roi/analysis', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(analysis)
});
```

## Next Steps

### Immediate (Phase 1)
1. **Frontend Integration** - Connect React components to ROI APIs
2. **Map Visualization** - Display ROIs and analysis results
3. **User Interface** - ROI management dashboard

### Medium-term (Phase 2)
1. **Real-time Analysis** - Live flood monitoring
2. **Advanced Queries** - Complex spatial relationships
3. **Performance Optimization** - Query tuning and caching

### Long-term (Phase 3)
1. **Machine Learning** - Predictive flood modeling
2. **Satellite Integration** - Automated imagery processing
3. **Mobile Support** - Field data collection

## Performance Considerations

### Database Optimization
- Spatial indexes are automatically created
- Generated columns avoid recalculation
- Connection pooling for scalability
- Query performance monitoring

### Recommended Settings
```sql
-- PostgreSQL/PostGIS optimization
shared_preload_libraries = 'postgis-3'
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
```

### Monitoring Queries
```sql
-- Check index usage
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats WHERE tablename = 'regions_of_interest';

-- Monitor query performance
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
WHERE query LIKE '%ST_%' 
ORDER BY total_time DESC;
```

## Installation Notes

The PostGIS integration requires:
1. PostGIS Docker container (included in docker-compose.yml)
2. Updated backend dependencies (pg, knex, joi)
3. Database initialization scripts (runs automatically)
4. Environment variables for database connection

All components are ready for deployment with the existing Docker setup.
