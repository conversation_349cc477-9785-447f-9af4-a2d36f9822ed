# 🐳 SANSA Flood Mapping System - Docker Deployment

## 📋 Overview

This directory contains all the necessary files and documentation for deploying the SANSA Flood Mapping System using Docker containers on a newly provisioned Linux server. The system uses a multi-container architecture with React frontend, Node.js UI Engine, PostgreSQL database, and Nginx reverse proxy.

## 🏗️ System Architecture (Production Reverse Proxy)

```
                    ┌─────────────────────┐
                    │    Nginx Proxy      │ ← SSL Termination
                    │   (Port 80/443)     │ ← Rate Limiting
                    │   Entry Point       │ ← Security Headers
                    └─────────┬───────────┘
                              │
                ┌─────────────┼─────────────┐
                │ /api/*      │        /*   │
                │             │             │
       ┌────────▼──────┐     │    ┌────────▼──────┐
       │ Node.js API   │     │    │ React Frontend│
       │ (Internal)    │     │    │ (Internal)    │
       │ Port 3001     │     │    │ Port 3000     │
       └───────┬───────┘     │    └───────────────┘
               │             │             │
      ┌────────▼──────┐      │    ┌────────▼──────┐
      │PostgreSQL+GIS │      │    │ Static Assets │
      │(Internal 5432)│      │    │   (Volume)    │
      └───────────────┘      │    └───────────────┘
                             │
                   ┌─────────▼───────┐
                   │ External GeoServer │
                   │(10.150.16.184:443)│
                   └─────────────────┘
```

**Traffic Flow:**
- External requests → Nginx Proxy (80/443)
- `/api/*` → UI Engine Container (3001)
- `/*` → Frontend Container (3000)
- All containers communicate via internal Docker network

## 📁 Directory Structure

```
deployment/
├── README.md                    # This file
├── INSTALLATION.md              # Complete installation guide
├── docker/                     # Docker configuration files
│   ├── docker-compose.yml      # Main docker compose file
│   ├── docker-compose.prod.yml # Production overrides
│   ├── Dockerfile.frontend     # Frontend container
│   ├── Dockerfile.uiengine      # UI Engine container
│   └── Dockerfile.nginx        # Nginx reverse proxy
├── scripts/                    # Deployment and maintenance scripts
│   ├── deploy.sh               # Main deployment script
│   ├── backup.sh               # Backup script
│   ├── update.sh               # Update script
│   └── restore.sh              # Restore script
└── configs/                    # Configuration files
    ├── .env.production.example # Environment variables template
    ├── nginx.conf              # Nginx configuration
    └── frontend.nginx.conf     # Frontend nginx config
```

## 🔧 Prerequisites

### **Server Requirements:**
- **OS**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **CPU**: 4+ cores recommended
- **RAM**: 8GB+ recommended (4GB minimum)
- **Storage**: 50GB+ available space
- **Network**: Internet access and access to GeoServer network

### **Software Dependencies:**
- Docker Engine 24.0+
- Docker Compose 2.0+
- Git
- curl/wget

## 🚀 Quick Start

1. **Server Setup**: Follow [INSTALLATION.md](INSTALLATION.md) for server preparation
2. **Configuration**: Copy and edit environment files from `configs/`
3. **Deploy**: Run `scripts/deploy.sh` to start the deployment
4. **Verify**: Check all services are running correctly

## 📖 Documentation

- **[INSTALLATION.md](INSTALLATION.md)** - Step-by-step installation guide

## 🛠️ Scripts

- **`deploy.sh`** - Main deployment script with health checks
- **`backup.sh`** - Database and application backup
- **`update.sh`** - Update application to latest version
- **`restore.sh`** - Restore from backup

## 🔧 Configuration Files

- **`docker-compose.yml`** - Main container orchestration
- **`.env.production.example`** - Environment variables template
- **`nginx.conf`** - Reverse proxy configuration

## 📊 Monitoring

The deployment includes:
- Health checks for all services
- Automated log rotation
- Backup scheduling
- Performance monitoring endpoints

## 🆘 Support

If you encounter issues:
1. Review container logs: `docker compose logs [service]`
2. Verify configuration settings
3. Check system resources and network connectivity

## 🔒 Security

Security features included:
- SSL/TLS configuration
- Firewall settings
- Password security
- Regular security updates

---

**Version:** 1.0  
**Last Updated:** June 13, 2025  
**Compatibility:** Docker 24.0+, Ubuntu 20.04+, CentOS 8+
