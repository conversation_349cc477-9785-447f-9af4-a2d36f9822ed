# 🪟 Running SANSA Flood Mapping on Windows

## Prerequisites

1. **Install Docker Desktop for Windows**
   - Download: https://www.docker.com/products/docker-desktop/
   - Install and restart your computer
   - Make sure Docker Desktop is running

2. **Enable WSL 2 (if prompted)**
   - Open PowerShell as Administrator: `wsl --install`
   - Restart if needed

3. **Verify Installation**
   ```powershell
   docker --version
   docker compose version
   ```

## Quick Start (Windows)

### Option 1: Minimal Setup (Recommended for Testing)

1. **Open PowerShell in the project directory**
   ```powershell
   cd "c:\src\saruwe\ESRI\SANSA FLOOD MAPPING\src\Temp\project-bolt-sb1-xigtdbge\project"
   ```

2. **Create environment file**
   ```powershell
   copy "deployment\configs\.env.production.example" ".env.production"
   ```

3. **Edit the environment file** (use Notepad or VS Code)
   ```powershell
   notepad .env.production
   ```
   Update these key values:
   ```
   DOMAIN=localhost
   NODE_ENV=development
   FRONTEND_URL=http://localhost:3000
   BACKEND_URL=http://localhost:3001
   ```

4. **Run the minimal Docker setup**
   ```powershell
   docker compose -f deployment\docker\docker-compose.minimal.yml up --build
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - Database: localhost:5432

### Option 2: Full Production Setup with Nginx

1. **Create SSL certificates for localhost** (optional)
   ```powershell
   mkdir ssl
   # You can skip SSL for local testing
   ```

2. **Run full setup**
   ```powershell
   docker compose -f deployment\docker\docker-compose.yml --env-file .env.production up --build
   ```

3. **Access via Nginx proxy**
   - Application: http://localhost
   - API: http://localhost/api

## 🔧 Common Windows Issues & Solutions

### Issue 1: "Drive not shared" error
**Solution:** 
- Open Docker Desktop → Settings → Resources → File Sharing
- Add your project drive (C:\ usually)
- Click "Apply & Restart"

### Issue 2: Port already in use
**Solution:**
```powershell
# Check what's using the port
netstat -ano | findstr :3000
netstat -ano | findstr :3001
netstat -ano | findstr :80

# Kill the process if needed (replace PID with actual process ID)
taskkill /PID <PID> /F
```

### Issue 3: Path issues with Windows
**Solution:** Use forward slashes in Docker commands:
```powershell
docker compose -f deployment/docker/docker-compose.yml up
```

## 🚀 Development Workflow

### Start Services
```powershell
docker compose -f deployment\docker\docker-compose.minimal.yml up -d
```

### View Logs
```powershell
docker compose logs -f
docker compose logs frontend
docker compose logs backend
docker compose logs database
```

### Stop Services
```powershell
docker compose down
```

### Rebuild After Changes
```powershell
docker compose down
docker compose up --build
```

### Database Access
```powershell
# Connect to PostgreSQL
docker compose exec database psql -U sansa_user -d sansa_flood_db
```

## 📊 Health Checks

```powershell
# Check running containers
docker compose ps

# Test endpoints
curl http://localhost:3001/health  # Backend health
curl http://localhost:3000         # Frontend

# If using Nginx proxy:
curl http://localhost/api/health   # Backend via proxy
curl http://localhost              # Frontend via proxy
```

## 🛑 Troubleshooting

### View Container Status
```powershell
docker compose ps
docker compose logs <service-name>
```

### Restart Individual Services
```powershell
docker compose restart backend
docker compose restart frontend
docker compose restart database
```

### Clean Reset
```powershell
docker compose down -v  # Removes volumes too
docker system prune -a  # Clean up unused images
```

### Check Docker Desktop
- Make sure Docker Desktop is running
- Check for any error notifications
- Try restarting Docker Desktop if issues persist

## 🎯 Quick Commands Cheat Sheet

```powershell
# Start development environment
docker compose -f deployment\docker\docker-compose.minimal.yml up -d

# View all logs
docker compose logs -f

# Stop everything
docker compose down

# Rebuild and start
docker compose up --build

# Clean restart
docker compose down -v && docker compose up --build
```
