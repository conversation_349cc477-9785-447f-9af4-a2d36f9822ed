# Development environment variables for frontend
# These will be used by Vite during development

# API Configuration  
# In development, connect directly to the backend
VITE_API_BASE_URL=http://localhost:3001/api

# GeoServer Configuration
VITE_GEOSERVER_URL=https://*************/geoserver

# Network Configuration
VITE_NETWORK_MODE=auto
VITE_ENABLE_FALLBACK=true
VITE_HEALTH_CHECK_INTERVAL=30000
VITE_MAX_RETRIES=3
VITE_RETRY_DELAY=2000
