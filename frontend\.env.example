# SANSA Flood Mapping Environment Configuration

# =============================================================================
# NETWORK HEALTH SYSTEM CONFIGURATION
# =============================================================================

# Master toggle for network health monitoring
# Set to 'false' to completely disable the network health system
# When disabled, no health checks, indicators, or modals will be shown
VITE_ENABLE_NETWORK_HEALTH=true

# Default data mode when network health is disabled
# Only used when VITE_ENABLE_NETWORK_HEALTH=false
# Options: 'live' | 'demo'
VITE_DEFAULT_DATA_MODE=live

# =============================================================================
# NETWORK HEALTH SETTINGS (only when VITE_ENABLE_NETWORK_HEALTH=true)
# =============================================================================

# Network mode for health-enabled deployments
# Options: 'auto' | 'live' | 'demo'
VITE_NETWORK_MODE=auto

# Enable fallback mechanisms when services are unavailable
VITE_ENABLE_FALLBACK=true

# Health check interval in milliseconds (30 seconds)
VITE_HEALTH_CHECK_INTERVAL=30000

# Maximum retry attempts for failed connections
VITE_MAX_RETRIES=3

# Delay between retry attempts in milliseconds (2 seconds)
VITE_RETRY_DELAY=2000

# =============================================================================
# QUICK CONFIGURATION EXAMPLES
# =============================================================================

# Example 1: Completely disable network health (simplest deployment)
# VITE_ENABLE_NETWORK_HEALTH=false
# VITE_DEFAULT_DATA_MODE=live

# Example 2: Demo-only mode (training/disconnected environments)
# VITE_ENABLE_NETWORK_HEALTH=false
# VITE_DEFAULT_DATA_MODE=demo

# Example 3: Full monitoring enabled (production deployment)
# VITE_ENABLE_NETWORK_HEALTH=true
# VITE_NETWORK_MODE=auto
