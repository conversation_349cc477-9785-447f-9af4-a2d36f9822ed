# Production environment variables for frontend
# Linux Server: *************

# API Configuration
# In production, we use relative paths that get proxied by Nginx
VITE_API_BASE_URL=/api

# GeoServer Configuration
VITE_GEOSERVER_URL=https://*************/geoserver

# Network Configuration
VITE_NETWORK_MODE=auto
VITE_ENABLE_FALLBACK=true
VITE_HEALTH_CHECK_INTERVAL=30000
VITE_MAX_RETRIES=3
VITE_RETRY_DELAY=2000

# Server Configuration
VITE_SERVER_IP=*************
