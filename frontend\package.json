{"name": "flood-monitoring-system", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@turf/turf": "^7.2.0", "axios": "^1.6.7", "bootstrap": "^5.3.7", "chart.js": "^4.4.1", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-bootstrap": "^2.10.10", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-leaflet": "^4.2.1", "react-leaflet-draw": "^0.20.4", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/leaflet": "^1.9.8", "@types/leaflet-draw": "^1.0.11", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/xml2js": "^0.4.14", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}