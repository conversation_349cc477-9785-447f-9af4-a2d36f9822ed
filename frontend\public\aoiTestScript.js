/**
 * Quick test script to verify AOI clipping functionality
 * Run this in the browser console to test the clipping workflow
 */

// Test script for AOI clipping verification
window.testAOIClipping = async () => {
  console.log('🧪 Starting AOI Clipping Test...');
  
  // Check if the debugging tools are available
  if (window.aoiDebugger) {
    console.log('✅ AOI Debugger available');
    
    // Test the full debugging workflow
    try {
      await window.aoiDebugger.runFullTest();
    } catch (error) {
      console.error('❌ AOI debugging test failed:', error);
    }
  } else {
    console.warn('⚠️ AOI Debugger not available - manual testing required');
  }
  
  // Check current AOI data
  if (window.currentAOIData) {
    console.log('✅ Current AOI Data available:', window.currentAOIData);
    
    // Test WKT conversion if geometry is available
    if (window.currentAOIData.geometry) {
      try {
        // Test the WKT converter
        const testGeometry = window.currentAOIData.geometry;
        console.log('🔧 Testing WKT conversion...');
        console.log('Input geometry:', testGeometry);
        
        // Since convertGeoJSONToWKT might not be globally available, 
        // we'll check if CQL_FILTER is being applied to the layers
        const wmsTiles = document.querySelectorAll('img[src*="wms"]');
        console.log(`🗺️ Found ${wmsTiles.length} WMS tile images`);
        
        let cqlFilterFound = false;
        wmsTiles.forEach((tile, index) => {
          const src = tile.src;
          if (src.includes('CQL_FILTER')) {
            console.log(`✅ WMS tile ${index + 1} has CQL_FILTER:`, src);
            cqlFilterFound = true;
          } else {
            console.log(`❌ WMS tile ${index + 1} has no CQL_FILTER:`, src);
          }
        });
        
        if (cqlFilterFound) {
          console.log('🎯 SUCCESS: CQL_FILTER is being applied to WMS layers!');
        } else {
          console.log('❌ FAILURE: No CQL_FILTER found in WMS requests');
        }
        
      } catch (error) {
        console.error('❌ WKT conversion test failed:', error);
      }
    } else {
      console.warn('⚠️ No geometry available in current AOI data');
    }
  } else {
    console.warn('⚠️ No current AOI data available');
  }
  
  console.log('🧪 AOI Clipping Test completed');
};

// Test the WKT converter directly if available
window.testWKTConverter = (geometry) => {
  console.log('🔧 Testing WKT Converter with geometry:', geometry);
  
  try {
    // Create a simple test polygon if no geometry provided
    const testGeometry = geometry || {
      type: 'Polygon',
      coordinates: [[[25, -30], [26, -30], [26, -29], [25, -29], [25, -30]]]
    };
    
    console.log('Input geometry:', testGeometry);
    
    // For now, just log the geometry structure since we can't access the converter directly
    console.log('Geometry type:', testGeometry.type);
    console.log('Coordinates:', testGeometry.coordinates);
    
    if (testGeometry.type === 'Polygon' && testGeometry.coordinates) {
      const coords = testGeometry.coordinates[0];
      const coordPairs = coords.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
      const expectedWKT = `POLYGON((${coordPairs}))`;
      console.log('Expected WKT:', expectedWKT);
    }
    
  } catch (error) {
    console.error('❌ WKT converter test failed:', error);
  }
};

console.log('🧪 AOI Testing Tools loaded!');
console.log('📋 Available commands:');
console.log('  - window.testAOIClipping() : Run full AOI clipping test');
console.log('  - window.testWKTConverter(geometry) : Test WKT conversion');
console.log('  - window.currentAOIData : Check current AOI data');
console.log('  - window.aoiDebugger.runFullTest() : Run comprehensive debugging');
