import { useState, useEffect, useCallback } from 'react';
import { Container } from 'react-bootstrap';
import NavBar from './components/NavBar/NavBar';
import Sidebar from './components/Sidebar/Sidebar';
import MobileSidebarToggle from './components/Sidebar/MobileSidebarToggle';
import MapComponent from './components/Map/MapComponent';
import AnalyticsDashboard from './components/Analytics/AnalyticsDashboard';
import ToolsPanel from './components/Tools/ToolsPanel';
import RegionalFilterModal from './components/RegionalFilter/RegionalFilterModal';
import DownloadModal from './components/Download/DownloadModal';
import { useDiscoveryLayers } from './hooks/useDiscoveryLayers';
import { initializeWKTConverter } from './services/aoiClippingService';
import { ToastProvider } from './contexts/ToastContext';
import ToastNotification from './components/Toast/ToastNotification';
import { useToast } from './contexts/ToastContext';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

// Main App Content Component (uses toast hook)
function AppContent() {
  const { toasts, removeToast } = useToast();
  const { layers, selectedLayers, handleLayerToggle, isLoading, error } = useDiscoveryLayers();

  const [currentView, setCurrentView] = useState<'map' | 'analytics'>('map');
  const [isToolsPanelOpen, setIsToolsPanelOpen] = useState(false);

  const [dateRange, setDateRange] = useState({
    startDate: '2025/05/20',
    endDate: '2025/05/20'
  });
  const [drawnItems, setDrawnItems] = useState<any>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Layer opacity state
  const [layerOpacities, setLayerOpacities] = useState<{ [layerName: string]: number }>({});

  const handleOpacityChange = (layerName: string, opacity: number) => {
    setLayerOpacities(prev => ({
      ...prev,
      [layerName]: opacity
    }));
  };

  // AOI state management
  const [isDrawingMode, setIsDrawingMode] = useState(false);
  const [hasDrawnArea, setHasDrawnArea] = useState(false);

  // Regional AOI state management
  const [aoiMethod, setAOIMethod] = useState<'drawn' | 'regional' | 'pin-based'>('drawn');
  const [hasRegionalSelection, setHasRegionalSelection] = useState(false);
  const [showRegionalModal, setShowRegionalModal] = useState(false);
  const [regionalAOI, setRegionalAOI] = useState<any>(null);

  // Pin-based AOI state
  const [hasPinBasedSelection, setHasPinBasedSelection] = useState(false);

  // AOI Preview data for sidebar (both drawn and administrative boundaries)
  const [aoiPreviewData, setAoiPreviewData] = useState<any>(null);

  // Download modal state
  const [showDownloadModal, setShowDownloadModal] = useState(false);

  // Basemap state management
  const [selectedBasemap, setSelectedBasemap] = useState('osm:osm'); // Default to OpenStreetMap

  // Coordinate pin mode state
  const [coordinatePinMode, setCoordinatePinMode] = useState(false);
  const [currentCoordinates, setCurrentCoordinates] = useState('');

  // Legend user mode state
  const [legendUserMode, setLegendUserMode] = useState<'simple' | 'advanced'>('simple');

  // Legend panel visibility state
  const [showLegendPanel, setShowLegendPanel] = useState(false);
  const [selectedLegendLayerName, setSelectedLegendLayerName] = useState<string | null>(null);

  // Interactive boundary highlighting state
  const [highlightedBoundaries, setHighlightedBoundaries] = useState<GeoJSON.Feature[]>([]);

  const handleDateChange = (type: 'startDate' | 'endDate', value: string) => {
    setDateRange(prev => ({ ...prev, [type]: value }));
  };

  const handleSearch = (query: string) => {
    console.log('Searching for:', query);
  };

  const handlePreviewData = () => {
    alert(`🔍 Data Preview Requested!\n\nLayers: ${selectedLayers.join(', ') || 'None'}\nDate Range: ${dateRange.startDate} to ${dateRange.endDate}\n\nCheck the console for details.`);
    console.log('Previewing data for layers:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
  };

  const handleDownloadData = () => {
    console.log('Downloading data for layers:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
    setShowDownloadModal(true);
  };

  const handleDownloadComplete = () => {
    // Close the download modal
    setShowDownloadModal(false);

    // Clear all application states after successful download
    // Reset layer selections
    selectedLayers.forEach(layerName => {
      handleLayerToggle(layerName); // Deselect all layers
    });

    // Reset AOI states
    setAOIMethod('drawn');
    setHasRegionalSelection(false);
    setRegionalAOI(null);
    setAoiPreviewData(null);

    // Reset drawing states
    setIsDrawingMode(false);
    setHasDrawnArea(false);
    setDrawnItems(null);

    // Clear highlighted boundaries
    setHighlightedBoundaries([]);

    // Reset layer opacities
    setLayerOpacities({});

    // Reset coordinate pin mode
    setCoordinatePinMode(false);
    setCurrentCoordinates('');

    // Reset date range to default
    setDateRange({
      startDate: '2025/05/20',
      endDate: '2025/05/20'
    });
  };

  const handleQueryTemporalData = () => {
    alert(`🔍 Temporal Query Executed!\n\nLayers: ${selectedLayers.join(', ') || 'None'}\nDate Range: ${dateRange.startDate} to ${dateRange.endDate}\n\nCheck the console for details.`);
    console.log('Temporal layers selected:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
  };

  const handleToolsToggle = () => {
    setIsToolsPanelOpen(prev => !prev);
  };

  const handleToolsPanelClose = () => {
    setIsToolsPanelOpen(false);
  };

  // Reset all application state (for Home button)
  const resetAllStates = () => {
    console.log('🏠 Home button clicked - Resetting all application states');

    // Reset layer selections
    selectedLayers.forEach(layerName => {
      handleLayerToggle(layerName); // Deselect all layers (toggle will turn them off if currently on)
    });

    // Reset date range to default
    setDateRange({
      startDate: '2025/05/20',
      endDate: '2025/05/20'
    });

    // Reset drawing states
    setIsDrawingMode(false);
    setHasDrawnArea(false);
    setDrawnItems(null);

    // Reset AOI states
    setAOIMethod('drawn');
    setHasRegionalSelection(false);
    setRegionalAOI(null);
    setAoiPreviewData(null);

    // Reset coordinate pin mode
    setCoordinatePinMode(false);
    setCurrentCoordinates('');

    // Reset basemap to default
    setSelectedBasemap('osm:osm');

    // Reset legend states
    setLegendUserMode('simple');
    setShowLegendPanel(false);
    setSelectedLegendLayerName(null);

    // Clear highlighted boundaries
    setHighlightedBoundaries([]);

    // Reset layer opacities
    setLayerOpacities({});

    // Close all modals and panels
    setShowRegionalModal(false);
    setShowDownloadModal(false);
    setIsToolsPanelOpen(false);

    // Reset view to map
    setCurrentView('map');

    // Clear any cached data or localStorager
    try {
      // Clear any temporary cache data (but preserve user preferences like sidebar state)
      sessionStorage.removeItem('aoiPreviewData');
      sessionStorage.removeItem('selectedLayers');
      sessionStorage.removeItem('highlightedBoundaries');
    } catch (e) {
      console.warn('Could not clear session storage:', e);
    }
  };

  // AOI handlers
  const handleDrawModeToggle = (isDrawing: boolean) => {
    console.log('🎨 handleDrawModeToggle called with:', isDrawing);
    setIsDrawingMode(isDrawing);

    if (isDrawing) {
      // Clear other AOI methods when drawing is activated
      setCoordinatePinMode(false);
      setCurrentCoordinates('');
      setHasPinBasedSelection(false);
      setHasRegionalSelection(false);
      setRegionalAOI(null);
      setAoiPreviewData(null);
      setAOIMethod('drawn');

      console.log('🎨 Drawing mode activated - cleared other AOI methods');
    } else {
      console.log('🎨 Drawing mode deactivated');
    }
  };

  const handleClearDrawnArea = () => {
    setHasDrawnArea(false);
    setDrawnItems(null);
  };

  const handleAOIComplete = (aoiData: any, dateRange: any) => {
    console.log('AOI Complete:', aoiData, dateRange);
    setHasDrawnArea(true);
    // Here you would typically process the AOI data for download
  };

  // Handle AOI preview data from both drawn polygons and administrative boundaries
  const handleAOIPreview = (aoiData: any) => {
    // Check if the AOI data is actually different - comprehensive comparison
    const currentData = aoiPreviewData;
    const newData = aoiData;

    // Handle null cases first
    if (!currentData && !newData) {
      return;
    }

    if (!currentData && newData) {
      setAoiPreviewData(aoiData);
      return;
    }

    if (currentData && !newData) {
      setAoiPreviewData(null);
      return;
    }

    if (currentData && newData) {
      // Compare key identifying properties
      const typeChanged = currentData.type !== newData.type;
      const levelChanged = currentData.level !== newData.level;
      const nameChanged = currentData.name !== newData.name;
      const codeChanged = currentData.code !== newData.code;

      // Compare selection details for administrative boundaries
      const selectionChanged = JSON.stringify(currentData.selectionDetails) !== JSON.stringify(newData.selectionDetails);

      // Compare bounds with tolerance for floating point precision
      const currentBounds = currentData.bounds;
      const newBounds = newData.bounds;
      let boundsChanged = false;

      if (currentBounds && newBounds) {
        const tolerance = 0.001; // Small tolerance for floating point comparison
        boundsChanged = (
          Math.abs(currentBounds.north - newBounds.north) > tolerance ||
          Math.abs(currentBounds.south - newBounds.south) > tolerance ||
          Math.abs(currentBounds.east - newBounds.east) > tolerance ||
          Math.abs(currentBounds.west - newBounds.west) > tolerance
        );
      } else {
        boundsChanged = currentBounds !== newBounds; // One is null/undefined
      }

      const hasSignificantChange = typeChanged || levelChanged || nameChanged || codeChanged || boundsChanged || selectionChanged;


      if (!hasSignificantChange) {
        return;
      }
    }
    setAoiPreviewData(aoiData);
  };

  // Handle AOI download from sidebar preview card
  const handleAOIDownload = (selectedLayers: string[], aoiData: any) => {
    console.log('Downloading AOI data:', { selectedLayers, aoiData });
    // Store AOI and layers for download modal
    setAoiPreviewData(aoiData);
    // Force close the legend panel
    setShowLegendPanel(false);
    setTimeout(() => {
      // Show download modal after a slight delay to ensure legend panel is closed
      setShowDownloadModal(true);
    }, 100);
  };

  const handleBasemapChange = (basemapName: string) => {
    setSelectedBasemap(basemapName);
    console.log('Basemap changed to:', basemapName);
  };

  // Handle coordinate pin mode toggle
  const handleCoordinatePinModeToggle = (enabled: boolean) => {
    console.log('handleCoordinatePinModeToggle called with:', enabled);
    setCoordinatePinMode(enabled);

    if (enabled) {
      // Clear other AOI methods when pin mode is enabled
      setHasDrawnArea(false);
      setIsDrawingMode(false);
      setHasRegionalSelection(false);
      setRegionalAOI(null);
      setAoiPreviewData(null);
      setHasPinBasedSelection(false);
      setAOIMethod('pin-based');
    } else {
      // Clear pin-based data when pin mode is disabled (X button clicked)
      setCurrentCoordinates('');
      setHasPinBasedSelection(false);
      setAoiPreviewData(null);
      setAOIMethod('drawn'); // Reset to default method
      console.log('Pin mode disabled - cleared coordinates and AOI data');
    }

    console.log('Coordinate pin mode:', enabled ? 'enabled' : 'disabled');
  };

  // Handle map click for coordinate pin
  const handleMapClick = (latlng: { lat: number, lng: number }) => {
    if (coordinatePinMode) {
      // Format coordinates to 6 decimal places
      const formattedCoordinates = `${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
      setCurrentCoordinates(formattedCoordinates);
      setCoordinatePinMode(false); // Turn off pin mode after placing

      // Set pin-based AOI method and mark as having pin selection
      setAOIMethod('pin-based');
      setHasPinBasedSelection(true);

      console.log('Coordinates set to:', formattedCoordinates);
      console.log('Pin-based AOI method activated');
    }
  };

  // Handle layer info - show legend panel and trigger info modal
  const handleShowLayerInfo = (layerName: string) => {
    setSelectedLegendLayerName(layerName);
    setShowLegendPanel(true);
    // The legend panel will handle showing the layer info modal
  };

  const handleCloseLegendPanel = () => {
    setShowLegendPanel(false);
    setSelectedLegendLayerName(null);
  };

  // Regional AOI handlers
  const handleAOIMethodChange = (method: 'drawn' | 'regional' | 'pin-based') => {
    setAOIMethod(method);

    // Clear existing selections when switching methods
    if (method === 'drawn') {
      setHasRegionalSelection(false);
      setHasPinBasedSelection(false);
      setCurrentCoordinates('');
    } else if (method === 'regional') {
      setHasDrawnArea(false);
      setIsDrawingMode(false);
      setHasPinBasedSelection(false);
      setCurrentCoordinates('');
    } else if (method === 'pin-based') {
      setHasDrawnArea(false);
      setIsDrawingMode(false);
      setHasRegionalSelection(false);
    }

    // Clear pin mode when switching to other AOI methods (except pin-based)
    if (method !== 'pin-based') {
      setCoordinatePinMode(false);
      setCurrentCoordinates('');
    }
  };

  const handleConfigureRegions = () => {
    setShowRegionalModal(true);
  };

  const handleClearRegionalSelection = () => {

    // Clear regional selection states
    setHasRegionalSelection(false);
    setRegionalAOI(null);
    setAoiPreviewData(null);

    // Clear all toggled layers
    selectedLayers.forEach(layerName => {
      handleLayerToggle(layerName); // This will deselect all currently selected layers
    });

    // Clear highlighted boundaries
    setHighlightedBoundaries([]);

    // Reset layer opacities
    setLayerOpacities({});

    // Clear drawing states
    setIsDrawingMode(false);
    setHasDrawnArea(false);
    setDrawnItems(null);

    // Reset coordinate pin mode
    setCoordinatePinMode(false);
    setCurrentCoordinates('');
  };

  const handleClearPinBasedSelection = () => {
    setHasPinBasedSelection(false);
    setCurrentCoordinates('');
    setCoordinatePinMode(false);
    setAoiPreviewData(null);
    setAOIMethod('drawn'); // Reset to default method
    console.log('Pin-based AOI selection cleared');
  };



  const handleApplyRegionalSelection = (regions: any[], boundaryLayer: string) => {

    // Calculate combined bounds from regions
    const bounds = calculateRegionalBounds(regions);
    const totalArea = calculateRegionalArea(regions);

    const regionalData = {
      method: 'regional' as const,
      boundaryLayer,
      selectedRegions: regions,
      combinedBounds: bounds,
      totalArea,
      bounds, // For compatibility with existing workflow
      area: totalArea // For compatibility with existing workflow
    };

    setRegionalAOI(regionalData);
    setHasRegionalSelection(true);
    setShowRegionalModal(false);

  };

  // Helper functions for regional bounds calculation
  const calculateRegionalBounds = (regions: any[]) => {
    // Simple bounds calculation - can be enhanced
    return {
      north: -22.0, // Mock bounds for South Africa
      south: -35.0,
      east: 33.0,
      west: 16.0
    };
  };

  const calculateRegionalArea = (regions: any[]) => {
    // Mock area calculation - sum of region areas
    return regions.length * 1000; // Mock: 1000 km² per region
  };

  // Interactive boundary filtering handlers - memoized to prevent infinite re-renders
  const handleBoundaryHighlight = useCallback((features: GeoJSON.Feature[]) => {
    setHighlightedBoundaries(features);
  }, []);

  const handleBoundaryRegionSelection = useCallback((features: GeoJSON.Feature[]) => {

    if (features.length > 0) {
      // Set AOI method to regional and mark as having regional selection
      setAOIMethod('regional');
      setHasRegionalSelection(true);
      setRegionalAOI(features);

      // Calculate bounds from features for preview
      let bounds = { north: -Infinity, south: Infinity, east: -Infinity, west: Infinity };
      features.forEach(feature => {
        if (feature.geometry && feature.geometry.type === 'Polygon') {
          const coords = feature.geometry.coordinates[0];
          coords.forEach((coord: any) => {
            const [lng, lat] = coord;
            bounds.north = Math.max(bounds.north, lat);
            bounds.south = Math.min(bounds.south, lat);
            bounds.east = Math.max(bounds.east, lng);
            bounds.west = Math.min(bounds.west, lng);
          });
        } else if (feature.geometry && feature.geometry.type === 'MultiPolygon') {
          // Handle MultiPolygon geometries
          feature.geometry.coordinates.forEach((polygon: any) => {
            polygon[0].forEach((coord: any) => {
              const [lng, lat] = coord;
              bounds.north = Math.max(bounds.north, lat);
              bounds.south = Math.min(bounds.south, lat);
              bounds.east = Math.max(bounds.east, lng);
              bounds.west = Math.min(bounds.west, lng);
            });
          });
        }
      });

      // Create unified geometry for clipping
      let unifiedGeometry: GeoJSON.Geometry;
      if (features.length === 1) {
        unifiedGeometry = features[0].geometry!;
      } else {
        // Combine multiple features into a MultiPolygon
        const polygons: number[][][][] = [];
        features.forEach(feature => {
          if (feature.geometry?.type === 'Polygon') {
            polygons.push(feature.geometry.coordinates);
          } else if (feature.geometry?.type === 'MultiPolygon') {
            polygons.push(...feature.geometry.coordinates);
          }
        });
        unifiedGeometry = {
          type: 'MultiPolygon',
          coordinates: polygons
        };
      }

      // Generate a descriptive name from the selected features
      let aoiName = 'Interactive boundary selection';
      if (features.length === 1) {
        const feature = features[0];
        const props = feature.properties || {};

        // Try to extract meaningful names from feature properties
        const provinceName = props.adm1_en || props.province;
        const districtName = props.adm2_en || props.district || props.municipali;
        const municipalityName = props.adm3_en || props.municipality;
        const wardName = props.adm4_en || props.wardlabel;

        // Build hierarchical name
        const nameParts = [];
        if (wardName) nameParts.push(wardName);
        if (municipalityName) nameParts.push(municipalityName);
        if (districtName) nameParts.push(districtName);
        if (provinceName) nameParts.push(provinceName);

        if (nameParts.length > 0) {
          aoiName = nameParts.join(', ');
        } else if (props.name) {
          aoiName = props.name;
        }
      } else if (features.length > 1) {
        aoiName = `${features.length} selected boundaries`;
      }

      setAoiPreviewData({
        type: 'interactive-boundaries',
        features: features,
        count: features.length,
        bounds: bounds,
        area: calculateRegionalArea(features), // Rough estimation
        name: aoiName, // Add descriptive name
        // Add geometry for clipping support
        geometry: unifiedGeometry,
        // Add feature for compatibility with existing clipping logic
        feature: features.length === 1 ? features[0] : {
          type: 'Feature',
          properties: {
            name: aoiName,
            source: 'interactive-selection'
          },
          geometry: unifiedGeometry
        }
      });
    }
  }, []);

  // Mobile sidebar handlers
  const handleMobileSidebarToggle = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const handleMobileSidebarClose = () => {
    setIsMobileSidebarOpen(false);
  };

  // Close mobile sidebar when clicking outside or on backdrop
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobileSidebarOpen && window.innerWidth <= 767) {
        const target = event.target as Element;
        const sidebar = document.querySelector('.sidebar');
        const toggle = document.querySelector('.mobile-sidebar-toggle');

        if (sidebar && !sidebar.contains(target) && toggle && !toggle.contains(target)) {
          setIsMobileSidebarOpen(false);
        }
      }
    };

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isMobileSidebarOpen) {
        setIsMobileSidebarOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscapeKey);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isMobileSidebarOpen]);

  // Close mobile sidebar on window resize to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 767 && isMobileSidebarOpen) {
        setIsMobileSidebarOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobileSidebarOpen]);

  useEffect(() => {
    const checkSidebarState = () => {
      const state = localStorage.getItem('sidebarCollapsed') === 'true';
      setSidebarCollapsed(state);
      console.log('Sidebar collapsed state loaded:', state);
    };

    checkSidebarState();

    const handleStorageChange = () => {
      checkSidebarState();
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Initialize WKT converter on app startup
  useEffect(() => {
    const initWKT = async () => {
      try {
        await initializeWKTConverter();
        console.log('✅ WKT converter initialized successfully');
      } catch (error) {
        console.error('❌ Failed to initialize WKT converter:', error);
      }
    };
    initWKT();
  }, []);

  return (
    <div className="app-wrapper">
      {/* Toast Notifications */}
      <ToastNotification toasts={toasts} onDismiss={removeToast} />

      <NavBar onNavigate={setCurrentView} onToolsToggle={handleToolsToggle} onHomeReset={resetAllStates} />
      {currentView === 'analytics' ? (
        <Container fluid className="app-container">
          <AnalyticsDashboard />
        </Container>
      ) : (
        <Container fluid className="app-container">
          {/* Mobile Sidebar Toggle */}
          <MobileSidebarToggle
            isOpen={isMobileSidebarOpen}
            onToggle={handleMobileSidebarToggle}
          />

          {/* Mobile Sidebar Backdrop */}
          <div
            className={`sidebar-backdrop ${isMobileSidebarOpen ? 'show' : ''}`}
            onClick={handleMobileSidebarClose}
          />

          <div
            className="main-content"
            // style={{
            //   display: 'flex',
            //   height: '100%',
            //   width: '100%',
            //   minHeight: 'calc(100vh - 60px)',
            //   position: 'relative'
            // }}
          >
            <Sidebar
              layers={layers}
              selectedLayerNames={selectedLayers}
              onLayerChange={handleLayerToggle}
              dateRange={dateRange}
              onDateChange={handleDateChange}
              onSearch={handleSearch}
              onPreviewData={handlePreviewData}
              onDownloadData={handleDownloadData}
              onQueryTemporalData={handleQueryTemporalData}
              isLoading={isLoading}
              error={error}
              onDrawModeToggle={handleDrawModeToggle}
              isDrawingMode={isDrawingMode}
              hasDrawnArea={hasDrawnArea}
              onClearDrawnArea={handleClearDrawnArea}
              aoiMethod={aoiMethod}
              onAOIMethodChange={handleAOIMethodChange}
              hasRegionalSelection={hasRegionalSelection}
              onConfigureRegions={handleConfigureRegions}
              onClearRegionalSelection={handleClearRegionalSelection}
              hasPinBasedSelection={hasPinBasedSelection}
              onClearPinBasedSelection={handleClearPinBasedSelection}
              selectedBasemap={selectedBasemap}
              onBasemapChange={handleBasemapChange}
              // onShowLayerInfo={handleShowLayerInfo}
              onCoordinatePinModeToggle={handleCoordinatePinModeToggle}
              currentCoordinates={currentCoordinates}
              aoiPreviewData={aoiPreviewData}
              onAOIDownload={handleAOIDownload}
              onAOIPreview={handleAOIPreview}
              // Mobile props
              isMobileOpen={isMobileSidebarOpen}
              onMobileClose={handleMobileSidebarClose}
              onBoundaryHighlight={handleBoundaryHighlight}
              onBoundaryRegionSelection={handleBoundaryRegionSelection}
              layerOpacities={layerOpacities}
              onOpacityChange={handleOpacityChange}
            />
            <div className="map-container" style={{ flex: 1, width: '100%', height: '100%' }}>
              <MapComponent
                selectedLayerNames={selectedLayers}
                dateRange={dateRange}
                onDrawComplete={setDrawnItems}
                isDrawingMode={isDrawingMode}
                onDrawModeChange={handleDrawModeToggle}
                onAOIComplete={handleAOIComplete}
                sidebarCollapsed={sidebarCollapsed}
                selectedBasemap={selectedBasemap}
                onBasemapChange={handleBasemapChange}
                legendUserMode={legendUserMode}
                onLegendUserModeChange={setLegendUserMode}
                isCoordinatePinMode={coordinatePinMode}
                onCoordinateSelected={handleMapClick}
                onAOIPreview={handleAOIPreview}
                highlightedBoundaries={highlightedBoundaries}
                aoiData={aoiPreviewData}
                layerOpacities={layerOpacities}
                onOpacityChange={handleOpacityChange}
              />
            </div>
          </div>
        </Container>
      )}
      <ToolsPanel isOpen={isToolsPanelOpen} onClose={handleToolsPanelClose} />

      {/* Regional Filter Modal */}
      <RegionalFilterModal
        show={showRegionalModal}
        onHide={() => setShowRegionalModal(false)}
        onApplySelection={handleApplyRegionalSelection}
      />

      {/* Download Modal */}
      <DownloadModal
        show={showDownloadModal}
        onHide={() => setShowDownloadModal(false)}
        onDownloadComplete={handleDownloadComplete}
        selectedLayers={selectedLayers}
        aoiData={aoiPreviewData}
        dateRange={dateRange}
      />
    </div>
  );
}

// Main App Component with Toast Provider
function App() {
  return (
    <ToastProvider>
      <AppContent />
    </ToastProvider>
  );
}

export default App;
