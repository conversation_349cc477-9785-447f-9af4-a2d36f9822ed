import { useState } from 'react';
import { Container } from 'react-bootstrap';
import NavBar from './components/NavBar/NavBar';
import Sidebar from './components/Sidebar/Sidebar';
import MapComponent from './components/Map/MapComponent';
import { useDiscoveryLayers } from './hooks/useDiscoveryLayers';

function SimpleApp() {
  const [message, setMessage] = useState('SANSA Flood Monitoring System');
  const [currentView, setCurrentView] = useState<'map' | 'analytics'>('map');
  const { layers, selectedLayers, handleLayerToggle } = useDiscoveryLayers();
  const [dateRange, setDateRange] = useState({
    startDate: '2025/05/20',
    endDate: '2025/05/20'
  });
  const [drawnItems, setDrawnItems] = useState<any>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const handleDateChange = (type: 'startDate' | 'endDate', value: string) => {
    setDateRange(prev => ({ ...prev, [type]: value }));
  };

  const handleSearch = (query: string) => {
    console.log('Searching for:', query);
  };

  const handlePreviewData = () => {
    console.log('Preview data');
  };

  const handleDownloadData = () => {
    console.log('Download data');
  };

  const handleQueryTemporalData = () => {
    console.log('Query temporal data');
  };
  
  return (
    <div style={{ fontFamily: 'Arial, sans-serif' }}>
      <NavBar onNavigate={setCurrentView} />
      <Container fluid>
        <div style={{ display: 'flex', height: 'calc(100vh - 60px)' }}>
          <div style={{ width: '300px', backgroundColor: '#f8f9fa' }}>
            <Sidebar
              layers={layers}
              selectedLayerNames={selectedLayers}
              onLayerChange={handleLayerToggle}
              dateRange={dateRange}
              onDateChange={handleDateChange}
              onSearch={handleSearch}
              onPreviewData={handlePreviewData}
              onDownloadData={handleDownloadData}
              onQueryTemporalData={handleQueryTemporalData}
            />
          </div>
          <div style={{ flex: 1 }}>
            <MapComponent
              selectedLayerNames={selectedLayers}
              dateRange={dateRange}
              onDrawComplete={setDrawnItems}
            />
          </div>
        </div>
      </Container>
    </div>
  );
}

export default SimpleApp;
