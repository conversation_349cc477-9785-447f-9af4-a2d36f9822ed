import React, { useState, useEffect } from 'react';
import { <PERSON>dal, Button, Form, Row, Col, Card, Badge, ListGroup, Spinner, <PERSON><PERSON>, ButtonGroup } from 'react-bootstrap';
import { MapPin, Calendar, Layers, Download, Eye, EyeOff, Info, Image, RefreshCw, Map, Sliders } from 'lucide-react';
import { generateAOIScreenshot } from '../../services/geoserverService';

interface AOIPreviewModalProps {
  show: boolean;
  onHide: () => void;
  onDownload: (selectedLayers: string[], dateRange: { startDate: string; endDate: string }) => void;
  aoiData: {
    coordinates: any;
    bounds: {
      north: number;
      south: number;
      east: number;
      west: number;
    };
    area: number; // in square kilometers
  };
  dateRange?: {
    startDate?: string;
    endDate?: string;
  };
  availableLayers: Array<{
    name: string;
    title: string;
    temporal?: boolean;
    queryable?: boolean;
  }>;
  selectedLayers: string[]; // Add selected layers prop
  selectedBasemap?: string; // Current basemap selection
  onBasemapChange?: (basemapName: string) => void; // Basemap change handler
}

// Base map options
const BASE_MAP_OPTIONS = [
  { name: 'osm:osm', title: 'OpenStreetMap' },
  { name: 'ne:ne_50m_admin_0_countries', title: 'World Countries' },
  { name: 'ne:ne_10m_admin_0_countries', title: 'World Countries (Detailed)' },
  { name: 'topp:states', title: 'US States' },
  { name: 'cite:BasicPolygons', title: 'Basic Polygons' }
];

// Custom Basemap Accordion Component
interface BasemapAccordionProps {
  localBasemap: string;
  onBasemapChange: (basemapName: string) => void;
  selectedLayers: string[];
  layerOpacities: Record<string, number>;
  onOpacityChange: (layerName: string, opacity: number) => void;
}

const BasemapAccordion: React.FC<BasemapAccordionProps> = ({
  localBasemap,
  onBasemapChange,
  selectedLayers,
  layerOpacities,
  onOpacityChange
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="mb-4">
      {/* Accordion Header */}
      <div
        className="custom-accordion-header"
        onClick={() => setIsExpanded(!isExpanded)}
        style={{
          cursor: 'pointer',
          fontWeight: 500,
          fontSize: '0.95rem',
          background: '#f8f9fa',
          border: '1px solid #dee2e6',
          padding: '0.75rem 1rem',
          marginBottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderRadius: '0.375rem'
        }}
      >
        <div className="d-flex align-items-center">
          <span className="me-2" style={{ fontSize: '0.8rem' }}>
            {isExpanded ? '▼' : '▶'}
          </span>
          <span className="text-primary">Basemap & Layer Controls</span>
        </div>
        <Badge bg="info" style={{ fontSize: '0.7rem' }}>
          {selectedLayers.length + 1} layers
        </Badge>
      </div>

      {/* Accordion Body */}
      {isExpanded && (
        <div
          className="custom-accordion-body"
          style={{
            border: '1px solid #dee2e6',
            borderTop: 'none',
            padding: '1rem',
            background: '#fff',
            borderBottomLeftRadius: '0.375rem',
            borderBottomRightRadius: '0.375rem'
          }}
        >
          {/* Basemap Selection */}
          <div className="mb-4">
            <h6 className="text-secondary mb-3" style={{ fontSize: '0.9rem' }}>
              Basemap Selection
            </h6>
            <div className="ps-2">
              {BASE_MAP_OPTIONS.map(basemap => (
                <Form.Check
                  key={basemap.name}
                  type="radio"
                  name="preview-basemap"
                  id={`preview-basemap-${basemap.name}`}
                  label={basemap.title}
                  checked={localBasemap === basemap.name}
                  onChange={() => onBasemapChange(basemap.name)}
                  className="mb-2"
                  style={{ fontSize: '0.85rem' }}
                />
              ))}
              <small className="text-muted">
                Changes will regenerate the preview automatically
              </small>
            </div>
          </div>

          {/* Layer Opacity Controls */}
          {selectedLayers.length > 0 && (
            <div>
              <h6 className="text-secondary mb-3" style={{ fontSize: '0.9rem' }}>
                Layer Opacity
              </h6>
              <div className="ps-2">
                {selectedLayers.map(layerName => (
                  <div key={layerName} className="mb-3">
                    <div className="d-flex justify-content-between align-items-center mb-1">
                      <small className="text-muted fw-medium">{layerName}</small>
                      <Badge bg="secondary" style={{ fontSize: '0.7rem' }}>
                        {Math.round((layerOpacities[layerName] || 1) * 100)}%
                      </Badge>
                    </div>
                    <Form.Range
                      min={0}
                      max={1}
                      step={0.1}
                      value={layerOpacities[layerName] || 1}
                      onChange={(e) => onOpacityChange(layerName, parseFloat(e.target.value))}
                      className="opacity-slider"
                    />
                  </div>
                ))}
                <small className="text-muted">
                  Adjust layer transparency for better visualization
                </small>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const AOIPreviewModal: React.FC<AOIPreviewModalProps> = ({
  show,
  onHide,
  onDownload,
  aoiData,
  dateRange,
  availableLayers,
  selectedLayers,
  selectedBasemap = 'osm:osm',
  onBasemapChange
}) => {
  // Screenshot generation state
  const [screenshotUrl, setScreenshotUrl] = useState<string | null>(null);
  const [isGeneratingScreenshot, setIsGeneratingScreenshot] = useState(false);
  const [screenshotError, setScreenshotError] = useState<string | null>(null);

  // Local basemap selection for preview (independent of sidebar)
  const [localBasemap, setLocalBasemap] = useState(selectedBasemap);

  // Layer opacity state
  const [layerOpacities, setLayerOpacities] = useState<Record<string, number>>(() => {
    const initialOpacities: Record<string, number> = {};
    selectedLayers.forEach(layer => {
      initialOpacities[layer] = 1.0; // Default to full opacity
    });
    return initialOpacities;
  });

  const handleDownload = () => {
    onDownload(selectedLayers, dateRange || { startDate: '', endDate: '' });
  };

  // Generate screenshot when modal opens or basemap changes
  useEffect(() => {
    if (show) {
      // Add a small delay to ensure the modal is fully rendered
      const timer = setTimeout(() => {
        generateScreenshot();
      }, 500);

      return () => clearTimeout(timer);
    }

    // Cleanup blob URL when modal closes
    return () => {
      if (screenshotUrl) {
        URL.revokeObjectURL(screenshotUrl);
        setScreenshotUrl(null);
      }
    };
  }, [show, selectedLayers, dateRange, aoiData.bounds, localBasemap, layerOpacities]);

  // Update local basemap when prop changes
  useEffect(() => {
    setLocalBasemap(selectedBasemap);
  }, [selectedBasemap]);

  // Update layer opacities when selected layers change
  useEffect(() => {
    const newOpacities: Record<string, number> = {};
    selectedLayers.forEach(layer => {
      newOpacities[layer] = layerOpacities[layer] || 1.0;
    });
    setLayerOpacities(newOpacities);
  }, [selectedLayers]);

  const generateScreenshot = async () => {
    console.log('Starting screenshot generation...');
    setIsGeneratingScreenshot(true);
    setScreenshotError(null);

    // Cleanup previous screenshot URL
    if (screenshotUrl) {
      URL.revokeObjectURL(screenshotUrl);
      setScreenshotUrl(null);
    }

    try {
      console.log('Screenshot params:', {
        bounds: aoiData.bounds,
        selectedLayers,
        selectedBasemap: localBasemap,
        dateRange,
        layerCount: selectedLayers.length
      });

      const url = await generateAOIScreenshot({
        bounds: aoiData.bounds,
        selectedLayers,
        selectedBasemap: localBasemap,
        dateRange,
        dimensions: { width: 800, height: 600 },
        format: 'png',
        layerOpacities
      });

      console.log('Screenshot generated successfully, URL:', url);
      setScreenshotUrl(url);
    } catch (error) {
      console.error('Failed to generate screenshot:', error);
      setScreenshotError(error instanceof Error ? error.message : 'Failed to generate screenshot');
    } finally {
      setIsGeneratingScreenshot(false);
    }
  };

  const handleRetryScreenshot = () => {
    generateScreenshot();
  };

  const handleBasemapChange = (basemapName: string) => {
    setLocalBasemap(basemapName);
    console.log('Preview basemap changed to:', basemapName);
  };

  const handleOpacityChange = (layerName: string, opacity: number) => {
    setLayerOpacities(prev => ({
      ...prev,
      [layerName]: opacity
    }));
  };

  const formatCoordinate = (coord: number, isLat: boolean = false) => {
    const direction = isLat ? (coord >= 0 ? 'N' : 'S') : (coord >= 0 ? 'E' : 'W');
    return `${Math.abs(coord).toFixed(4)}°${direction}`;
  };

  const formatArea = (area: number) => {
    if (area < 1) {
      return `${(area * 1000000).toFixed(0)} m²`;
    } else if (area < 100) {
      return `${area.toFixed(2)} km²`;
    } else {
      return `${area.toFixed(0)} km²`;
    }
  };

  const calculateDaysDuration = () => {
    if (!dateRange?.startDate || !dateRange?.endDate) {
      return 'No date range';
    }

    try {
      const start = new Date(dateRange.startDate.replace(/\//g, '-'));
      const end = new Date(dateRange.endDate.replace(/\//g, '-'));

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return 'Invalid dates';
      }

      const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
      return `${days} days`;
    } catch (error) {
      return 'Invalid dates';
    }
  };

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return 'Not specified';

    try {
      const date = new Date(dateStr.replace(/\//g, '-'));
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      return date.toLocaleDateString();
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Removed displayLayers since we're not showing layer selection anymore

  return (
    <Modal 
      show={show} 
      onHide={onHide} 
      size="xl" 
      centered
      backdrop="static"
    >
      <Modal.Header 
        closeButton 
        style={{ 
          backgroundColor: 'var(--bs-primary)', 
          color: 'white',
          border: 'none'
        }}
      >
        <Modal.Title style={{ color: 'white' }}>
          Area of Interest Preview
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body className="p-0">
        <Row className="g-0">
          {/* Left Panel - Details */}
          <Col md={6} className="p-4 border-end">
            <div className="mb-4">
              <h6 className="text-primary mb-3">
                Geographic Details
              </h6>
              <Card className="border-0 bg-light">
                <Card.Body className="p-3">
                  <Row className="g-2">
                    <Col xs={6}>
                      <small className="text-muted">North:</small>
                      <div className="fw-bold">{formatCoordinate(aoiData.bounds.north, true)}</div>
                    </Col>
                    <Col xs={6}>
                      <small className="text-muted">South:</small>
                      <div className="fw-bold">{formatCoordinate(aoiData.bounds.south, true)}</div>
                    </Col>
                    <Col xs={6}>
                      <small className="text-muted">East:</small>
                      <div className="fw-bold">{formatCoordinate(aoiData.bounds.east)}</div>
                    </Col>
                    <Col xs={6}>
                      <small className="text-muted">West:</small>
                      <div className="fw-bold">{formatCoordinate(aoiData.bounds.west)}</div>
                    </Col>
                    <Col xs={12} className="mt-2 pt-2 border-top">
                      <small className="text-muted">Total Area:</small>
                      <div className="fw-bold text-primary">{formatArea(aoiData.area)}</div>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
            </div>

            <div className="mb-4">
              <h6 className="text-primary mb-3">
                Temporal Range
              </h6>
              <Card className="border-0 bg-light">
                <Card.Body className="p-3">
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      <small className="text-muted">From:</small>
                      <div className="fw-bold">{formatDate(dateRange?.startDate)}</div>
                    </div>
                    <div className="text-center">
                      <small className="text-muted">Duration:</small>
                      <div className="fw-bold">
                        {calculateDaysDuration()}
                      </div>
                    </div>
                    <div>
                      <small className="text-muted">To:</small>
                      <div className="fw-bold">{formatDate(dateRange?.endDate)}</div>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </div>

            {/* Basemap & Layer Controls - Custom Accordion */}
            <BasemapAccordion
              localBasemap={localBasemap}
              onBasemapChange={handleBasemapChange}
              selectedLayers={selectedLayers}
              layerOpacities={layerOpacities}
              onOpacityChange={handleOpacityChange}
            />
          </Col>

          {/* Right Panel - Area Preview Screenshot */}
          <Col md={6} className="p-4">
            <div className="mb-3">
              <h6 className="text-primary mb-0">
                Area Preview
              </h6>
            </div>

            <div className="preview-screenshot-container">
              <Card className="border-0 bg-light h-100">
                <Card.Body className="d-flex flex-column align-items-center justify-content-center text-center p-4">
                  {/* Loading State */}
                  {isGeneratingScreenshot && (
                    <div
                      className="preview-loading"
                      style={{
                        width: '100%',
                        height: '300px',
                        backgroundColor: '#f8f9fa',
                        border: '2px dashed #dee2e6',
                        borderRadius: '8px',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#6c757d'
                      }}
                    >
                      <Spinner animation="border" size="sm" className="mb-3" />
                      <h6 className="text-muted mb-2">Generating Preview</h6>
                      <p className="text-muted small mb-2">
                        Creating screenshot of selected area...
                      </p>
                      <div className="mt-3">
                        <Badge bg="secondary" className="me-2">
                          {Math.round(aoiData.area)} km²
                        </Badge>
                        <Badge bg="info">
                          {selectedLayers.length} layers
                        </Badge>
                      </div>
                    </div>
                  )}

                  {/* Error State */}
                  {!isGeneratingScreenshot && screenshotError && (
                    <div
                      className="preview-error"
                      style={{
                        width: '100%',
                        height: '300px',
                        backgroundColor: '#f8f9fa',
                        border: '2px dashed #dc3545',
                        borderRadius: '8px',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#dc3545'
                      }}
                    >
                      <h6 className="text-danger mb-2">Preview Generation Failed</h6>
                      <Alert variant="danger" className="small mb-3 text-center">
                        {screenshotError}
                      </Alert>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={handleRetryScreenshot}
                      >
                        Retry
                      </Button>
                    </div>
                  )}

                  {/* Success State - Show Screenshot */}
                  {!isGeneratingScreenshot && !screenshotError && screenshotUrl && (
                    <div className="preview-screenshot" style={{ width: '100%' }}>
                      <img
                        src={screenshotUrl}
                        alt="Area of Interest Preview"
                        style={{
                          width: '100%',
                          height: 'auto',
                          maxHeight: '300px',
                          objectFit: 'contain',
                          borderRadius: '8px',
                          border: '1px solid #dee2e6'
                        }}
                      />
                      <div className="mt-3">
                        <Badge bg="success" className="me-2">
                          {Math.round(aoiData.area)} km²
                        </Badge>
                        <Badge bg="info" className="me-2">
                          {selectedLayers.length} layers
                        </Badge>
                        {dateRange?.startDate && dateRange?.endDate && (
                          <Badge bg="secondary">
                            {calculateDaysDuration()}
                          </Badge>
                        )}
                      </div>
                      <Button
                        variant="outline-secondary"
                        size="sm"
                        onClick={handleRetryScreenshot}
                        className="mt-2"
                      >
                        Regenerate
                      </Button>
                    </div>
                  )}

                  {/* No Layers Selected State */}
                  {!isGeneratingScreenshot && !screenshotError && selectedLayers.length === 0 && (
                    <div
                      className="preview-no-layers"
                      style={{
                        width: '100%',
                        height: '300px',
                        backgroundColor: '#f8f9fa',
                        border: '2px dashed #ffc107',
                        borderRadius: '8px',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#856404'
                      }}
                    >
                      <h6 className="text-warning mb-2">No Layers Selected</h6>
                      <p className="text-muted small mb-2">
                        Please select layers from the sidebar to generate a preview.<br/>
                        <strong>Tip:</strong> Include base map layers for geographic context.
                      </p>
                      <div className="mt-3">
                        <Badge bg="secondary" className="me-2">
                          {Math.round(aoiData.area)} km²
                        </Badge>
                        <Badge bg="warning">
                          0 layers
                        </Badge>
                      </div>
                    </div>
                  )}
                </Card.Body>
              </Card>
            </div>
          </Col>
        </Row>
      </Modal.Body>
      
      <Modal.Footer className="d-flex justify-content-between">
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={() => handleDownload([], dateRange)}
        >
          Prepare Download
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default AOIPreviewModal;
