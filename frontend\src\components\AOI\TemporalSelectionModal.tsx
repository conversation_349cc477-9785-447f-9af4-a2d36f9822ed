import React, { useState, useEffect } from 'react';
import { Modal, Button, Form, Row, Col, Collapse } from 'react-bootstrap';
import { Calendar, Clock, ChevronRight, ChevronDown, ChevronUp } from 'lucide-react';

interface TemporalSelectionModalProps {
  show: boolean;
  onHide: () => void;
  onConfirm: (selection: {
    dateRange: { startDate: string; endDate: string };
    analysisType: string;
    expectedOutputs: string[];
  }) => void;
  aoiCoordinates?: any;
  preSelectedDateRange?: { startDate: string; endDate: string };
}

const TemporalSelectionModal: React.FC<TemporalSelectionModalProps> = ({
  show,
  onHide,
  onConfirm,
  aoiCoordinates,
  preSelectedDateRange
}) => {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [useSlider, setUseSlider] = useState(false);
  const [showQuickSelect, setShowQuickSelect] = useState(false);
  const [showCustomRange, setShowCustomRange] = useState(false);
  const [analysisType, setAnalysisType] = useState('static_mosaic');
  const [showAnalysisInfo, setShowAnalysisInfo] = useState(false);

  // Pre-populate dates when modal opens with pre-selected date range
  useEffect(() => {
    if (show && preSelectedDateRange) {
      // Convert from MM/DD/YYYY to YYYY-MM-DD format for date inputs
      const formatForInput = (dateStr: string) => {
        const parts = dateStr.split('/');
        if (parts.length === 3) {
          return `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
        }
        return dateStr;
      };

      setStartDate(formatForInput(preSelectedDateRange.startDate));
      setEndDate(formatForInput(preSelectedDateRange.endDate));
    } else if (show && !preSelectedDateRange) {
      // Clear dates if no pre-selected range
      setStartDate('');
      setEndDate('');
    }
  }, [show, preSelectedDateRange]);

  // Generate suggested date ranges
  const generateSuggestedRanges = () => {
    const now = new Date();
    const currentYear = now.getFullYear();
    
    return [
      {
        label: 'Last 30 Days',
        start: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
        end: now
      },
      {
        label: 'Current Year',
        start: new Date(currentYear, 0, 1),
        end: now
      },
      {
        label: 'Flood Season (Oct-Mar)',
        start: new Date(currentYear - 1, 9, 1), // October previous year
        end: new Date(currentYear, 2, 31) // March current year
      },
      {
        label: 'Dry Season (Apr-Sep)',
        start: new Date(currentYear, 3, 1), // April
        end: new Date(currentYear, 8, 30) // September
      }
    ];
  };

  const formatDateForInput = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  const handleSuggestedRange = (start: Date, end: Date) => {
    setStartDate(formatDateForInput(start));
    setEndDate(formatDateForInput(end));
  };

  // Analysis type configurations
  const analysisTypes = {
    static_mosaic: {
      name: 'Static Mosaic',
      description: 'Single composite image for the selected date range',
      outputs: ['Composite WMS layer', 'Download URL', 'Coverage statistics'],
      processingTime: 'Fast (< 30 seconds)',
      icon: '📸',
      recommended: false
    },
    ndvi_trend: {
      name: 'NDVI Trend Analysis',
      description: 'Vegetation health trends over time with statistical analysis',
      outputs: ['Trend statistics', 'Time-series chart', 'Stacked layers', 'Statistical summary'],
      processingTime: 'Medium (1-3 minutes)',
      icon: '📈',
      recommended: true
    },
    vci_analysis: {
      name: 'Vegetation Condition Index (VCI)',
      description: 'Drought condition assessment and vegetation stress analysis',
      outputs: ['VCI time-series', 'Drought severity classification', 'Threshold analysis'],
      processingTime: 'Medium (1-3 minutes)',
      icon: '🌱',
      recommended: true
    },
    anomaly_detection: {
      name: 'Anomaly Detection',
      description: 'Statistical anomaly identification in temporal data',
      outputs: ['Anomaly events', 'Statistical analysis', 'Deviation metrics'],
      processingTime: 'Medium (2-4 minutes)',
      icon: '🔍',
      recommended: false
    },
    seasonal_patterns: {
      name: 'Seasonal Pattern Analysis',
      description: 'Seasonal and cyclical pattern identification',
      outputs: ['Monthly averages', 'Seasonal trends', 'Cyclical patterns'],
      processingTime: 'Medium (2-4 minutes)',
      icon: '🍂',
      recommended: false
    }
  };

  const handleConfirm = () => {
    if (startDate && endDate) {
      const selectedAnalysis = analysisTypes[analysisType as keyof typeof analysisTypes];
      onConfirm({
        dateRange: {
          startDate: startDate.split('-').join('/'),
          endDate: endDate.split('-').join('/')
        },
        analysisType,
        expectedOutputs: selectedAnalysis.outputs
      });
    }
  };

  const suggestedRanges = generateSuggestedRanges();

  return (
    <Modal 
      show={show} 
      onHide={onHide} 
      size="lg" 
      centered
      backdrop="static"
    >
      <Modal.Header 
        closeButton 
        style={{ 
          backgroundColor: 'var(--bs-primary)', 
          color: 'white',
          border: 'none'
        }}
      >
        <Modal.Title style={{ color: 'white' }}>
          <Clock size={20} className="me-2" />
          Select Temporal Range for Area of Interest
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body className="p-4">
        <div className="mb-4">
          <h6 className="text-muted mb-3">
            <Calendar size={16} className="me-2" />
            Choose a time period for your area analysis
          </h6>

          {/* Pre-selected Date Range Indicator */}
          {preSelectedDateRange && (
            <div className="mb-3 p-3 bg-info bg-opacity-10 border border-info rounded">
              <small className="text-info fw-bold">
                ✓ Using previously selected date range: {new Date(preSelectedDateRange.startDate.split('/').join('-')).toLocaleDateString()} - {new Date(preSelectedDateRange.endDate.split('/').join('-')).toLocaleDateString()}
              </small>
            </div>
          )}
          
          {/* Collapsible Quick Selection */}
          <div className="mb-3">
            <Button
              variant="outline-secondary"
              size="sm"
              onClick={() => setShowQuickSelect(!showQuickSelect)}
              className="w-100 d-flex align-items-center justify-content-between"
              style={{ fontSize: '0.85rem' }}
            >
              <span>Quick Selection</span>
              {showQuickSelect ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
            <Collapse in={showQuickSelect}>
              <div className="mt-2">
                <div className="d-flex flex-wrap gap-2">
                  {suggestedRanges.map((range, index) => (
                    <Button
                      key={index}
                      variant="outline-primary"
                      size="sm"
                      onClick={() => {
                        handleSuggestedRange(range.start, range.end);
                        setShowQuickSelect(false);
                      }}
                      className="text-nowrap"
                      style={{ fontSize: '0.75rem' }}
                    >
                      {range.label}
                    </Button>
                  ))}
                </div>
              </div>
            </Collapse>
          </div>

          {/* Collapsible Custom Date Selection */}
          <div className="mb-4">
            <Button
              variant="outline-secondary"
              size="sm"
              onClick={() => setShowCustomRange(!showCustomRange)}
              className="w-100 d-flex align-items-center justify-content-between"
              style={{ fontSize: '0.85rem' }}
            >
              <span>Custom Date Range</span>
              {showCustomRange ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </Button>
            <Collapse in={showCustomRange}>
              <div className="border rounded p-3 bg-light mt-2">
            <Row>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Start Date:</Form.Label>
                  <Form.Control
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    max={formatDateForInput(new Date())}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>End Date:</Form.Label>
                  <Form.Control
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    min={startDate}
                    max={formatDateForInput(new Date())}
                  />
                </Form.Group>
              </Col>
                </Row>
              </div>
            </Collapse>
          </div>

          {/* Analysis Type Selection */}
          <div className="mb-4">
            <h6 className="mb-3">
              <span className="me-2">🔬</span>
              Analysis Type
            </h6>
            <div className="row g-2">
              {Object.entries(analysisTypes).map(([key, config]) => (
                <div key={key} className="col-12">
                  <div
                    className={`card h-100 cursor-pointer border ${analysisType === key ? 'border-primary bg-primary bg-opacity-10' : 'border-light'}`}
                    onClick={() => setAnalysisType(key)}
                    style={{ cursor: 'pointer' }}
                  >
                    <div className="card-body p-3">
                      <div className="d-flex align-items-start">
                        <div className="me-3">
                          <span style={{ fontSize: '1.5rem' }}>{config.icon}</span>
                          {config.recommended && (
                            <div className="mt-1">
                              <span className="badge bg-success" style={{ fontSize: '0.7rem' }}>Recommended</span>
                            </div>
                          )}
                        </div>
                        <div className="flex-grow-1">
                          <div className="d-flex align-items-center mb-1">
                            <Form.Check
                              type="radio"
                              name="analysisType"
                              checked={analysisType === key}
                              onChange={() => setAnalysisType(key)}
                              className="me-2"
                            />
                            <h6 className="mb-0">{config.name}</h6>
                          </div>
                          <p className="text-muted small mb-2">{config.description}</p>
                          <div className="small">
                            <div className="text-muted mb-1">
                              <strong>Processing Time:</strong> {config.processingTime}
                            </div>
                            <div className="text-muted">
                              <strong>Outputs:</strong> {config.outputs.slice(0, 2).join(', ')}
                              {config.outputs.length > 2 && ` +${config.outputs.length - 2} more`}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Expected Outputs Preview */}
          {analysisType && (
            <div className="mb-4 p-3 bg-light rounded">
              <h6 className="mb-2">
                <span className="me-2">📊</span>
                Expected Outputs for {analysisTypes[analysisType as keyof typeof analysisTypes].name}
              </h6>
              <ul className="mb-0 small">
                {analysisTypes[analysisType as keyof typeof analysisTypes].outputs.map((output, index) => (
                  <li key={index} className="text-muted">{output}</li>
                ))}
              </ul>
              <div className="mt-2 small text-muted">
                <strong>Processing Time:</strong> {analysisTypes[analysisType as keyof typeof analysisTypes].processingTime}
              </div>
            </div>
          )}

          {/* Date Range Summary */}
          {startDate && endDate && (
            <div className="mt-3 p-3 bg-info bg-opacity-10 border border-info rounded">
              <small className="text-info fw-bold">
                Selected Range: {new Date(startDate).toLocaleDateString()} - {new Date(endDate).toLocaleDateString()}
                <br />
                Duration: {Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24))} days
              </small>
            </div>
          )}
        </div>
      </Modal.Body>
      
      <Modal.Footer className="d-flex justify-content-between align-items-center">
        <div className="small text-muted">
          {analysisType && (
            <>
              <span className="me-2">{analysisTypes[analysisType as keyof typeof analysisTypes].icon}</span>
              <strong>{analysisTypes[analysisType as keyof typeof analysisTypes].name}</strong>
              {analysisTypes[analysisType as keyof typeof analysisTypes].recommended && (
                <span className="badge bg-success ms-2" style={{ fontSize: '0.7rem' }}>Recommended</span>
              )}
            </>
          )}
        </div>
        <div>
          <Button variant="secondary" onClick={onHide} className="me-2">
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleConfirm}
            disabled={!startDate || !endDate}
            className="d-flex align-items-center"
          >
            {analysisType === 'static_mosaic' ? 'Generate Mosaic' : 'Start Analysis'}
            <ChevronRight size={16} className="ms-1" />
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default TemporalSelectionModal;
