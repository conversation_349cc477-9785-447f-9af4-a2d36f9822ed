/* Phase 4: Analytics Dashboard Styles */
.analytics-dashboard {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  border-bottom: 2px solid #e5e7eb;
  border-radius: 8px 8px 0 0;
}

.dashboard-header h2 {
  color: #1f2937;
  margin: 0;
  font-size: 28px;
}

/* Blue header styling for analytics dashboard */
.dashboard-header.app-header-blue {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: #ffffff !important;
  border-bottom: 2px solid #004085 !important;
  margin: -20px -20px 30px -20px;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.dashboard-header.app-header-blue h2 {
  color: #ffffff !important;
  font-weight: 600;
}

.dashboard-header.app-header-blue .offline-indicator {
  color: #ffffff !important;
}

.dashboard-header.app-header-blue .offline-badge {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.dashboard-header.app-header-blue .offline-text {
  color: rgba(255, 255, 255, 0.9) !important;
}

.header-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.dashboard-header.app-header-blue .header-controls .timeframe-selector {
  background: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  color: #333 !important;
}

.dashboard-header.app-header-blue .header-controls .refresh-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
  transition: all 0.2s ease;
}

.dashboard-header.app-header-blue .header-controls .refresh-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  transform: scale(1.05);
}

.timeframe-selector {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 150px;
}

.refresh-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  background: #2563eb;
}

.refresh-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  color: #6b7280;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message button {
  background: #dc2626;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.error-message.warning {
  background: #fef3c7;
  border-color: #fbbf24;
  color: #92400e;
}

.error-message.warning span {
  color: #92400e;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 32px;
  margin-right: 15px;
}

.stat-content h3 {
  margin: 0 0 5px 0;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-number {
  margin: 0;
  font-size: 28px;
  font-weight: bold;
  color: #1f2937;
}

/* Chart Section */
.chart-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-section h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 20px;
}

.simple-chart {
  display: flex;
  align-items: end;
  gap: 8px;
  height: 200px;
  padding: 20px 0;
  overflow-x: auto;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}

.bar {
  min-height: 10px;
  width: 20px;
  border-radius: 2px 2px 0 0;
  transition: all 0.3s ease;
}

.bar-label {
  margin-top: 10px;
  text-align: center;
  font-size: 11px;
  color: #6b7280;
}

.bar-label .date {
  font-weight: 500;
  margin-bottom: 4px;
}

.bar-label .values {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* Regional Analysis */
.region-analysis {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.region-analysis h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 20px;
}

.region-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.region-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 20px;
}

.region-card h4 {
  margin: 0 0 15px 0;
  color: #1f2937;
  font-size: 16px;
}

.region-stats {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.region-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.region-stat .label {
  color: #6b7280;
  font-size: 14px;
}

.region-stat .value {
  font-weight: 600;
  font-size: 14px;
}

.region-stat .value.high {
  color: #dc2626;
}

.region-stat .value.medium {
  color: #d97706;
}

.region-stat .value.low {
  color: #059669;
}

/* Report Generation */
.report-generation {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.report-generation h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
}

.form-group select,
.form-group input[type="date"],
.form-group input[type="text"] {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

.checkbox-group {
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  margin: 0;
  cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 8px;
}

.generate-btn {
  padding: 12px 24px;
  background: #059669;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.2s;
  min-width: 200px;
}

.generate-btn:hover:not(:disabled) {
  background: #047857;
}

.generate-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Offline Mode Indicators */
.offline-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 6px;
  margin-left: 20px;
}

.offline-badge {
  background: #f59e0b;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.offline-text {
  font-size: 12px;
  color: #92400e;
}

.demo-notice {
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 6px;
  padding: 10px 15px;
  margin-bottom: 15px;
  font-size: 14px;
  color: #92400e;
  display: flex;
  align-items: center;
  gap: 8px;
}

.demo-label {
  background: #fbbf24;
  color: #92400e;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analytics-dashboard {
    padding: 15px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .header-controls {
    justify-content: space-between;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .region-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .simple-chart {
    height: 150px;
  }
  
  .chart-bar {
    min-width: 40px;
  }
  
  .bar {
    width: 16px;
  }
}
