// Phase 4: Analytics Dashboard Component with Database Decoupling
import React, { useState, useEffect } from 'react';
import './AnalyticsDashboard.css';
import { fetchReportOptions, fetchAnalytics, generateFallbackAnalytics } from '../../services/analyticsServiceAbstraction';
import type { ReportOptions, AnalyticsData } from '../../services/analyticsServiceAbstraction';

const AnalyticsDashboard: React.FC = () => {
  const [timeframe, setTimeframe] = useState('7d');
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [reportOptions, setReportOptions] = useState<ReportOptions | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOfflineMode, setIsOfflineMode] = useState(false);
  
  // Report generation state
  const [reportForm, setReportForm] = useState({
    reportType: 'alert_history',
    format: 'csv',
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    includeCharts: false,
    region: ''
  });
  const [generatingReport, setGeneratingReport] = useState(false);

  useEffect(() => {
    fetchAnalyticsData(timeframe);
    // Always fetch options; abstraction will fall back if reporting disabled
    fetchReportOptions().then(options => setReportOptions(options));
  }, [timeframe]);

  const fetchAnalyticsData = async (period: string) => {
    setLoading(true);
    setError(null);
    try {
      const { data, fallback } = await fetchAnalytics(period);
      setAnalytics(data);
      setIsOfflineMode(fallback);
    } catch (err) {
      console.warn('Analytics fetch error, using fallback data:', err);
      setAnalytics(generateFallbackAnalytics(period));
      setIsOfflineMode(true);
      setError('📊 Analytics in offline mode - Showing fallback data.');
    } finally {
      setLoading(false);
    }
  };

  const generateReport = async () => {
    if (isOfflineMode) {
      // Generate a simple offline report
      const reportData = {
        ...analytics,
        reportType: reportForm.reportType,
        format: reportForm.format,
        generatedAt: new Date().toISOString(),
        offlineMode: true,
        note: 'This report was generated in offline mode with demo data'
      };
      
      const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics-report-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      return;
    }

    setGeneratingReport(true);
    
    try {
      const response = await fetch('/api/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportForm),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `report-${Date.now()}.${reportForm.format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error generating report:', err);
      setError('Failed to generate report. Please try again later.');
    } finally {
      setGeneratingReport(false);
    }
  };

  const handleReportFormChange = (field: string, value: any) => {
    setReportForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading && !analytics) {
    return (
      <div className="analytics-dashboard">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="analytics-dashboard">
      <div className="dashboard-header app-header-blue">
        <h2>📊 Flood Risk Analytics & Reporting</h2>
        {isOfflineMode && (
          <div className="offline-indicator">
            <span className="offline-badge">🔌 Offline Mode</span>
            <span className="offline-text">Database unavailable - showing demo data</span>
          </div>
        )}
        <div className="header-controls">
          <select 
            value={timeframe} 
            onChange={(e) => setTimeframe(e.target.value)}
            className="timeframe-selector"
          >
            {reportOptions?.periods.map(period => (
              <option key={period.value} value={period.value}>
                {period.label}
              </option>
            ))}
          </select>
          <button 
            onClick={() => fetchAnalyticsData(timeframe)}
            className="refresh-btn"
            disabled={loading}
          >
            🔄 Refresh
          </button>
        </div>
      </div>

      {error && (
        <div className={`error-message ${isOfflineMode ? 'warning' : 'error'}`}>
          <span>{error}</span>
          {!isOfflineMode && (
            <button onClick={() => fetchAnalyticsData(timeframe)}>Retry</button>
          )}
        </div>
      )}

      {analytics && (
        <>
          {/* Summary Stats */}
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">🚨</div>
              <div className="stat-content">
                <h3>Total Alerts</h3>
                <p className="stat-number">{analytics.summaryStats.totalAlerts}</p>
                {isOfflineMode && <span className="demo-label">Demo Data</span>}
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">📊</div>
              <div className="stat-content">
                <h3>Active Datasets</h3>
                <p className="stat-number">{analytics.summaryStats.totalDatasets}</p>
                {isOfflineMode && <span className="demo-label">Demo Data</span>}
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">⚙️</div>
              <div className="stat-content">
                <h3>Alert Rules</h3>
                <p className="stat-number">{analytics.summaryStats.activeRules}</p>
                {isOfflineMode && <span className="demo-label">Demo Data</span>}
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">⏱️</div>
              <div className="stat-content">
                <h3>Avg Response Time</h3>
                <p className="stat-number">{analytics.summaryStats.avgResponseTime}ms</p>
                {isOfflineMode && <span className="demo-label">Demo Data</span>}
              </div>
            </div>
          </div>

          {/* Risk Trend Chart */}
          <div className="chart-container">
            <h3>Risk Trend Analysis</h3>
            {isOfflineMode && (
              <div className="demo-notice">
                📊 Demo chart data - Connect to database for real trend analysis
              </div>
            )}
            <div className="trend-chart">
              {analytics.riskTrend.map((point, index) => (
                <div key={index} className="trend-point">
                  <div className="trend-bar" style={{ height: `${point.riskLevel * 10}%` }}></div>
                  <div className="trend-date">{point.date}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Region Analysis */}
          <div className="region-analysis">
            <h3>Regional Risk Assessment</h3>
            {isOfflineMode && (
              <div className="demo-notice">
                🗺️ Demo regional data - Connect to database for real spatial analysis
              </div>
            )}
            <div className="region-grid">
              {analytics.regionAnalysis.map((region, index) => (
                <div key={index} className="region-card">
                  <h4>{region.region}</h4>
                  <div className="region-stats">
                    <div className="region-stat">
                      <span className="stat-label">Risk Score:</span>
                      <span className="stat-value">{region.riskScore.toFixed(1)}</span>
                    </div>
                    <div className="region-stat">
                      <span className="stat-label">Alerts:</span>
                      <span className="stat-value">{region.alertCount}</span>
                    </div>
                    <div className="region-stat">
                      <span className="stat-label">Population at Risk:</span>
                      <span className="stat-value">{region.populationAtRisk.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Report Generation */}
          <div className="report-section">
            <h3>Generate Report</h3>
            {isOfflineMode && (
              <div className="demo-notice">
                📄 Limited report options in offline mode - JSON demo reports available
              </div>
            )}
            <div className="report-form">
              <div className="form-group">
                <label>Report Type:</label>
                <select 
                  value={reportForm.reportType}
                  onChange={(e) => handleReportFormChange('reportType', e.target.value)}
                >
                  {reportOptions?.reportTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="form-group">
                <label>Format:</label>
                <select 
                  value={reportForm.format}
                  onChange={(e) => handleReportFormChange('format', e.target.value)}
                >
                  {reportOptions?.formats.map(format => (
                    <option key={format.value} value={format.value}>
                      {format.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="form-group">
                <label>Start Date:</label>
                <input 
                  type="date" 
                  value={reportForm.startDate}
                  onChange={(e) => handleReportFormChange('startDate', e.target.value)}
                />
              </div>
              <div className="form-group">
                <label>End Date:</label>
                <input 
                  type="date" 
                  value={reportForm.endDate}
                  onChange={(e) => handleReportFormChange('endDate', e.target.value)}
                />
              </div>
              <button 
                className="generate-btn"
                onClick={generateReport}
                disabled={generatingReport}
              >
                {generatingReport ? 'Generating...' : '📊 Generate Report'}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default AnalyticsDashboard;
