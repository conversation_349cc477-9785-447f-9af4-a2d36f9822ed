import React, { useMemo } from 'react';
import { GeoJSON } from 'react-leaflet';
import L from 'leaflet';

interface BoundaryHighlightLayerProps {
  features: GeoJSON.Feature[];
  onFeatureClick?: (feature: GeoJSON.Feature) => void;
}

const BoundaryHighlightLayer: React.FC<BoundaryHighlightLayerProps> = ({
  features,
  onFeatureClick
}) => {
  // Don't render if no features
  if (!features || features.length === 0) {
    return null;
  }

  // Memoize GeoJSON data to prevent unnecessary re-renders
  const geoJsonData: GeoJSON.FeatureCollection = useMemo(() => ({
    type: 'FeatureCollection',
    features: features
  }), [features]);

  // Create a stable key based on feature count and first feature timestamp
  const layerKey = useMemo(() => {
    const timestamp = features[0]?.properties?.timestamp || Date.now();
    return `boundary-highlight-${features.length}-${timestamp}`;
  }, [features]);

  const onEachFeature = (feature: GeoJSON.Feature, layer: L<PERSON>Layer) => {
    // Add click handler
    if (onFeatureClick) {
      layer.on('click', () => {
        onFeatureClick(feature);
      });
    }

    // Add popup with boundary information
    const props = feature.properties || {};
    const popupContent = `
      <div style="font-size: 12px;">
        <strong>Administrative Boundary</strong><br/>
        ${props.adm1_en ? `<strong>Province:</strong> ${props.adm1_en}<br/>` : ''}
        ${props.adm2_en ? `<strong>District:</strong> ${props.adm2_en}<br/>` : ''}
        ${props.adm3_en ? `<strong>Municipality:</strong> ${props.adm3_en}<br/>` : ''}
        ${props.adm4_en ? `<strong>Ward:</strong> ${props.adm4_en}<br/>` : ''}
      </div>
    `;
    
    layer.bindPopup(popupContent);

    // Add hover effects - enhanced visibility on hover while maintaining fill
    layer.on('mouseover', function(this: L.Path) {
      this.setStyle({
        fillColor: '#dc3545', // Red fill on hover for clear indication
        fillOpacity: 0.35, // Slightly more opaque on hover
        color: '#dc3545', // Red outline on hover
        weight: 3,
        opacity: 1
      });
    });

    layer.on('mouseout', function(this: L.Path) {
      this.setStyle({
        fillColor: '#007bff', // Blue fill by default
        fillOpacity: 0.25, // Standard opacity
        color: '#007bff', // Blue outline by default
        weight: 2,
        opacity: 0.9 // Slightly transparent outline
      });
    });
  };

  const pathOptions = {
    fillColor: 'transparent', // No fill - outline only for clear data layer visibility
    fillOpacity: 0, // No fill opacity - completely transparent
    color: '#007bff', // Blue outline for boundary visibility
    weight: 2,
    opacity: 0.9, // Opaque outline for clear boundary indication
    dashArray: '5, 5',
    fill: false, // Explicitly disable fill
    // Ensure boundaries don't cover data layers
    pane: 'overlayPane' // Use overlay pane with lower z-index than data layers
  };

  return (
    <GeoJSON
      key={layerKey}
      data={geoJsonData}
      pathOptions={pathOptions}
      onEachFeature={onEachFeature}
      // Use a specific pane to control z-index layering
      pane="overlayPane"
    />
  );
};

export default BoundaryHighlightLayer;
