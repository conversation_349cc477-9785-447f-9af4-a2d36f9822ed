/* Override Bootstrap modal styles to ensure it appears above other elements */
.modal-backdrop {
  z-index: 9000 !important;
}

.modal {
  z-index: 9999 !important;
}

/* Ensure modal content appears above all other elements */
.modal-content {
  position: relative;
  z-index: 10000 !important;
}

/* Style for format cards */
.format-card {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  position: relative;
  user-select: none;
}

.format-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.format-card.selected {
  border-color: #007bff !important;
  background-color: rgba(0, 123, 255, 0.05);
}

/* Ensure checkbox is clickable and visible */
.format-card .form-check {
  position: relative;
  z-index: 2;
}

/* Make the checkbox easier to interact with */
.format-card .form-check-input {
  cursor: pointer;
  width: 20px;
  height: 20px;
  margin-top: 0.125rem;
}

/* Mobile-friendly format cards */
@media (max-width: 767px) {
  .format-card {
    margin-bottom: 1rem;
    padding: 1rem;
  }

  .format-card .form-check-input {
    width: 24px;
    height: 24px;
  }

  .format-card .form-check-label {
    font-size: 1rem;
    line-height: 1.5;
    padding-left: 0.75rem;
  }
}

@media (max-width: 479px) {
  .format-card {
    padding: 0.875rem;
  }

  .format-card .form-check-input {
    width: 22px;
    height: 22px;
  }

  .format-card .form-check-label {
    font-size: 0.9rem;
  }
}
