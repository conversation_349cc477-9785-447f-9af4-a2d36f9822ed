import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Collapse } from 'react-bootstrap';
import { useState } from 'react';

interface ServiceError {
  success: false;
  error: string;
  details?: {
    message: string;
    type?: string;
    timestamp: string;
    suggestion?: string;
    endpoints?: Record<string, string>;
  };
}

interface ServiceErrorDisplayProps {
  error: ServiceError | null;
  service: string;
  onRetry?: () => void;
  className?: string;
}

export const ServiceErrorDisplay: React.FC<ServiceErrorDisplayProps> = ({
  error,
  service,
  onRetry,
  className = ''
}) => {
  const [showDetails, setShowDetails] = useState(false);

  if (!error) return null;

  const getErrorTypeVariant = (type?: string) => {
    switch (type) {
      case 'network': return 'warning';
      case 'configuration': return 'danger';
      default: return 'secondary';
    }
  };

  const getErrorIcon = (type?: string) => {
    switch (type) {
      case 'network': return '🌐';
      case 'configuration': return '⚙️';
      default: return '❌';
    }
  };

  return (
    <Alert variant="warning" className={`service-error-display ${className}`}>
      <div className="d-flex justify-content-between align-items-start">
        <div className="flex-grow-1">
          <Alert.Heading className="h6 mb-2">
            {getErrorIcon(error.details?.type)} {service} Service Error
          </Alert.Heading>
          
          <p className="mb-2">{error.error}</p>
          
          {error.details?.type && (
            <Badge 
              bg={getErrorTypeVariant(error.details.type)} 
              className="me-2 mb-2"
            >
              {error.details.type.toUpperCase()} ERROR
            </Badge>
          )}
          
          {error.details?.suggestion && (
            <div className="alert alert-info alert-sm mb-2">
              <strong>💡 Suggestion:</strong> {error.details.suggestion}
            </div>
          )}
        </div>
        
        <div className="ms-3">
          {onRetry && (
            <Button 
              variant="outline-primary" 
              size="sm" 
              onClick={onRetry}
              className="me-2"
            >
              🔄 Retry
            </Button>
          )}
          
          <Button
            variant="outline-secondary"
            size="sm"
            onClick={() => setShowDetails(!showDetails)}
            aria-expanded={showDetails}
          >
            {showDetails ? '▼' : '▶'} Details
          </Button>
        </div>
      </div>

      <Collapse in={showDetails}>
        <div className="mt-3 pt-3 border-top">
          <div className="row">
            <div className="col-md-6">
              <h6>Error Details</h6>
              <ul className="list-unstyled small">
                <li><strong>Message:</strong> {error.details?.message || 'No details available'}</li>
                <li><strong>Time:</strong> {error.details?.timestamp ? new Date(error.details.timestamp).toLocaleString() : 'Unknown'}</li>
                <li><strong>Type:</strong> {error.details?.type || 'Unknown'}</li>
              </ul>
            </div>
            
            {error.details?.endpoints && (
              <div className="col-md-6">
                <h6>Service Endpoints</h6>
                <ul className="list-unstyled small">
                  {Object.entries(error.details.endpoints).map(([name, url]) => (
                    <li key={name}>
                      <strong>{name}:</strong> 
                      <code className="ms-1">{url}</code>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
          
          <div className="mt-2">
            <h6>Troubleshooting Steps</h6>
            <ol className="small">
              {error.details?.type === 'network' ? (
                <>
                  <li>Check if the server is running and accessible</li>
                  <li>Verify network connectivity</li>
                  <li>Check firewall settings</li>
                  <li>Verify SSL certificates if using HTTPS</li>
                </>
              ) : error.details?.type === 'configuration' ? (
                <>
                  <li>Check server configuration files</li>
                  <li>Verify service is properly configured</li>
                  <li>Check if required layers/datasets are published</li>
                  <li>Review server logs for configuration errors</li>
                </>
              ) : (
                <>
                  <li>Check server logs for detailed error information</li>
                  <li>Verify service configuration</li>
                  <li>Check network connectivity</li>
                  <li>Contact system administrator if issue persists</li>
                </>
              )}
            </ol>
          </div>
        </div>
      </Collapse>
    </Alert>
  );
};

export default ServiceErrorDisplay;
