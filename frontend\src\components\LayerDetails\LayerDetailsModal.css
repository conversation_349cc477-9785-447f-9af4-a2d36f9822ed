/* Layer Details Modal Styles */
.layer-details-modal .modal-dialog {
  max-width: 900px;
}

.layer-details-modal .modal-header {
  border-bottom: none;
  padding: 1rem 1.5rem;
}

.layer-details-modal .modal-title {
  font-size: 1.1rem;
  font-weight: 600;
}

/* Tabs Styling */
.layer-details-tabs {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  margin: 0;
}

.layer-details-tabs .nav-link {
  color: #495057;
  border: none;
  border-bottom: 3px solid transparent;
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.layer-details-tabs .nav-link:hover {
  background-color: #e9ecef;
  color: #1e4080;
  border-bottom-color: #1e4080;
}

.layer-details-tabs .nav-link.active {
  background-color: white;
  color: #1e4080;
  border-bottom-color: #1e4080;
  font-weight: 600;
}

/* Content Area */
.layer-details-content {
  min-height: 500px;
  max-height: 600px;
  overflow-y: auto;
}

.layer-details-content .tab-pane {
  padding: 0;
}

/* Metadata Content */
.layer-metadata-content {
  padding: 1.5rem;
}

.metadata-sections .card {
  border: 1px solid #e9ecef;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metadata-sections .card-header {
  font-weight: 600;
  font-size: 0.9rem;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
}

.metadata-sections .card-body {
  padding: 1rem;
}

.metadata-sections h6 {
  font-size: 0.85rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.metadata-sections p {
  font-size: 0.85rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.metadata-sections .badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .layer-details-modal .modal-dialog {
    max-width: 95%;
    margin: 0.5rem;
  }
  
  .layer-details-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .layer-metadata-content {
    padding: 1rem;
  }
  
  .metadata-sections .card-body {
    padding: 0.75rem;
  }
}

/* Loading and Error States */
.layer-details-content .spinner-border {
  color: #1e4080;
}

.layer-details-content .alert {
  margin: 1rem;
  font-size: 0.9rem;
}

/* Custom Scrollbar */
.layer-details-content::-webkit-scrollbar {
  width: 6px;
}

.layer-details-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.layer-details-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.layer-details-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Tab Content Specific Styles */
.tab-pane .p-3 {
  padding: 1.5rem !important;
}

.tab-pane h6 {
  color: #1e4080;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.tab-pane .text-muted {
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

/* Badge Enhancements */
.metadata-sections .badge.bg-success {
  background-color: #28a745 !important;
}

.metadata-sections .badge.bg-secondary {
  background-color: #6c757d !important;
}

.metadata-sections .badge.bg-info {
  background-color: #17a2b8 !important;
}

/* Card Header Colors */
.metadata-sections .card-header.bg-primary {
  background-color: #1e4080 !important;
}

.metadata-sections .card-header.bg-info {
  background-color: #17a2b8 !important;
}

.metadata-sections .card-header.bg-warning {
  background-color: #ffc107 !important;
}

.metadata-sections .card-header.bg-success {
  background-color: #28a745 !important;
}

/* Animation for tab transitions */
.tab-content .tab-pane {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.tab-content .tab-pane.active {
  opacity: 1;
}

/* Close button styling */
.modal-header .btn-outline-light {
  border: none;
  padding: 0.25rem 0.5rem;
}

.modal-header .btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Styles Tab Specific */
.layer-styles-content .styles-grid {
  max-height: 400px;
  overflow-y: auto;
}

.layer-styles-content .style-option {
  transition: all 0.2s ease;
}

.layer-styles-content .style-option:hover {
  background-color: #f8f9fa !important;
  border-color: #1e4080 !important;
}

.layer-styles-content .selected-style {
  background-color: #e3f2fd !important;
  border-color: #1e4080 !important;
}

.layer-styles-content .default-style {
  border-left: 4px solid #28a745;
}

/* Download Tab Specific */
.layer-download-content .format-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.layer-download-content .spatial-options .form-check {
  padding-left: 1.5rem;
}

.layer-download-content .form-control {
  border-radius: 4px;
  border: 1px solid #ced4da;
}

/* Analytics Tab Specific */
.layer-analytics-content .stat-item {
  padding: 1rem;
  border-radius: 6px;
  background-color: #f8f9fa;
  margin-bottom: 0.5rem;
}

.layer-analytics-content .stat-item h4 {
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.layer-analytics-content .metric-item {
  padding: 0.75rem;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.layer-analytics-content .progress {
  background-color: #e9ecef;
}

/* Advanced Tab Specific */
.layer-advanced-content .analysis-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.layer-advanced-content .input-group {
  margin-bottom: 0.5rem;
}

.layer-advanced-content pre {
  font-size: 0.75rem;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  max-height: 150px;
  overflow-y: auto;
}

.layer-advanced-content .list-unstyled li {
  margin-bottom: 0.25rem;
}

/* Enhanced Card Styling */
.metadata-sections .card,
.styles-sections .card,
.download-sections .card,
.analytics-sections .card,
.advanced-sections .card {
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.metadata-sections .card:hover,
.styles-sections .card:hover,
.download-sections .card:hover,
.analytics-sections .card:hover,
.advanced-sections .card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Button Enhancements */
.layer-details-modal .btn {
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.layer-details-modal .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Badge Enhancements */
.layer-details-modal .badge {
  font-weight: 500;
  padding: 0.35rem 0.6rem;
  border-radius: 4px;
}

/* Responsive Enhancements */
@media (max-width: 992px) {
  .layer-details-modal .modal-dialog {
    max-width: 90%;
  }

  .layer-styles-content .style-option .row {
    flex-direction: column;
  }

  .layer-download-content .format-options {
    flex-direction: column;
  }

  .layer-analytics-content .stat-item {
    text-align: center;
    margin-bottom: 1rem;
  }

  .layer-advanced-content .analysis-options {
    flex-direction: column;
  }
}

@media (max-width: 576px) {
  .layer-details-modal .modal-dialog {
    margin: 0.25rem;
    max-width: calc(100% - 0.5rem);
  }

  .layer-details-tabs .nav-link {
    padding: 0.5rem 0.25rem;
    font-size: 0.75rem;
  }

  .layer-details-content {
    max-height: 70vh;
  }

  .metadata-sections .card-body,
  .styles-sections .card-body,
  .download-sections .card-body,
  .analytics-sections .card-body,
  .advanced-sections .card-body {
    padding: 0.75rem;
  }
}

/* Loading States */
.layer-details-modal .spinner-border {
  color: #1e4080;
}

.layer-details-modal .text-center {
  padding: 2rem 1rem;
}

/* Accessibility Improvements */
.layer-details-modal .btn:focus,
.layer-details-modal .form-control:focus {
  box-shadow: 0 0 0 0.2rem rgba(30, 64, 128, 0.25);
  border-color: #1e4080;
}

.layer-details-modal .nav-link:focus {
  box-shadow: inset 0 0 0 2px rgba(30, 64, 128, 0.25);
}

/* Print Styles */
@media print {
  .layer-details-modal .modal-header,
  .layer-details-tabs {
    background-color: #1e4080 !important;
    color: white !important;
    -webkit-print-color-adjust: exact;
  }

  .layer-details-modal .card-header {
    -webkit-print-color-adjust: exact;
  }

  .layer-details-modal .btn {
    display: none;
  }
}
