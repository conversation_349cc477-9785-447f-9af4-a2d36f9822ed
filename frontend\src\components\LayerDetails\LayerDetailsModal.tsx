import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>v, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Form } from 'react-bootstrap';
import { 
  Info, 
  Palette, 
  Download, 
  BarChart3, 
  Settings, 
  Share2, 
  X, 
  Calendar, 
  MapPin, 
  Database,
  Eye,
  Layers,
  Clock,
  Globe,
  FileText,
  Tag
} from 'lucide-react';
import { LayerDiscovery } from '../../types/discovery';
import { API_CONFIG } from '../../config';
import './LayerDetailsModal.css';

interface LayerDetailsModalProps {
  show: boolean;
  onHide: () => void;
  layer: LayerDiscovery | null;
  onStyleChange?: (layerName: string, styleName: string) => void;
  onDownload?: (layerName: string, options: DownloadOptions) => void;
}

interface DownloadOptions {
  format: string;
  spatialFilter?: 'viewport' | 'drawn' | 'none';
  temporalFilter?: {
    startDate: string;
    endDate: string;
  };
  attributeFilter?: string;
}

interface LayerMetadata {
  basic: {
    title: string;
    abstract: string;
    attribution?: string;
    keywords: string[];
  };
  technical: {
    crs: string[];
    bbox: any;
    minScale?: number;
    maxScale?: number;
    queryable: boolean;
  };
  temporal?: {
    hasTemporalData: boolean;
    extent?: string;
    defaultTime?: string;
    resolution?: string;
  };
  service: {
    wmsSupported: boolean;
    wfsSupported: boolean;
    wmtsSupported: boolean;
  };
}

const LayerDetailsModal: React.FC<LayerDetailsModalProps> = ({
  show,
  onHide,
  layer,
  onStyleChange,
  onDownload
}) => {
  const [activeTab, setActiveTab] = useState('metadata');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [layerMetadata, setLayerMetadata] = useState<LayerMetadata | null>(null);
  const [availableStyles, setAvailableStyles] = useState<any[]>([]);
  const [selectedStyle, setSelectedStyle] = useState<string>('');

  // Fetch comprehensive layer metadata when modal opens
  useEffect(() => {
    if (show && layer) {
      console.log('🔍 LayerDetailsModal: Opening for layer:', layer.name);
      fetchLayerDetails();
    }
  }, [show, layer]);

  const fetchLayerDetails = async () => {
    if (!layer) return;

    setLoading(true);
    setError(null);

    try {
      // Fetch layer metadata from backend with retry logic
      const response = await fetch(`${API_CONFIG.BASE_URL}/ows/layer-metadata/${encodeURIComponent(layer.name)}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        cache: 'no-cache' // Prevent caching issues
      });

      if (!response.ok) {
        // Provide more specific error messages
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new Error(`Failed to fetch layer details (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      
      // Transform data to our metadata structure
      const metadata: LayerMetadata = {
        basic: {
          title: data.title || layer.title,
          abstract: data.abstract || layer.abstract || 'No description available',
          attribution: data.attribution || layer.attribution,
          keywords: data.keywords || layer.keywords || []
        },
        technical: {
          crs: data.crs || ['EPSG:4326'],
          bbox: data.bbox || layer.bbox,
          minScale: data.minScaleDenominator,
          maxScale: data.maxScaleDenominator,
          queryable: data.queryable || layer.queryable || false
        },
        temporal: layer.temporal ? {
          hasTemporalData: true,
          extent: layer.temporal.extent,
          defaultTime: layer.temporal.default,
          resolution: layer.temporal.units
        } : undefined,
        service: {
          wmsSupported: layer.supports?.WMS || true,
          wfsSupported: layer.supports?.WFS || false,
          wmtsSupported: layer.supports?.WMTS || false
        }
      };
      
      setLayerMetadata(metadata);
      
      // Fetch available styles
      const stylesResponse = await fetch(`${API_CONFIG.OWS_BASE_URL}/styles/${encodeURIComponent(layer.name)}`);
      if (stylesResponse.ok) {
        const stylesData = await stylesResponse.json();
        setAvailableStyles(stylesData.styles || []);
        setSelectedStyle(stylesData.defaultStyle || '');
      }
      
    } catch (err: any) {
      console.error('Layer details fetch error:', err);
      setError(err.message || 'Failed to fetch layer details');
      // No fallback metadata - only show actual data from geoserver
      setLayerMetadata(null);
    } finally {
      setLoading(false);
    }
  };

  const handleStyleChange = (styleName: string) => {
    setSelectedStyle(styleName);
    if (onStyleChange && layer) {
      onStyleChange(layer.name, styleName);
    }
  };

  const handleDownload = (format: string) => {
    if (onDownload && layer) {
      const options: DownloadOptions = {
        format,
        spatialFilter: 'none'
      };
      onDownload(layer.name, options);
    }
  };

  const renderMetadataTab = () => (
    <div className="layer-metadata-content">
      {loading ? (
        <div className="text-center p-4">
          <Spinner animation="border" size="sm" className="me-2" />
          Loading layer details...
        </div>
      ) : error ? (
        <div className="p-4">
          <Alert variant="warning" className="mb-3">
            <div className="d-flex justify-content-between align-items-start">
              <div>
                <strong>No Layer Details Available</strong>
                <p className="mb-0 mt-2">
                  Unable to retrieve detailed information from the geoserver for this layer.
                </p>
                <small className="text-muted">Error: {error}</small>
              </div>
              <Button
                variant="outline-warning"
                size="sm"
                onClick={fetchLayerDetails}
                disabled={loading}
                className="ms-3"
              >
                {loading ? 'Retrying...' : 'Retry'}
              </Button>
            </div>
          </Alert>

          <div className="text-center text-muted">
            <p>This layer may still be functional on the map, but detailed metadata is not available.</p>
            <p>Contact your system administrator if this issue persists.</p>
          </div>
        </div>
      ) : layerMetadata ? (
        <div className="metadata-sections">
          {/* Basic Information */}
          <Card className="mb-3">
            <Card.Header className="bg-primary text-white">
              <Info size={16} className="me-2" />
              Basic Information
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={12}>
                  <h6><FileText size={14} className="me-2" />Title</h6>
                  <p className="text-muted">{layerMetadata.basic.title}</p>
                </Col>
                <Col md={12}>
                  <h6><FileText size={14} className="me-2" />Description</h6>
                  <p className="text-muted">{layerMetadata.basic.abstract}</p>
                </Col>
                {layerMetadata.basic.attribution && (
                  <Col md={12}>
                    <h6><Tag size={14} className="me-2" />Attribution</h6>
                    <p className="text-muted">{layerMetadata.basic.attribution}</p>
                  </Col>
                )}
                {layerMetadata.basic.keywords.length > 0 && (
                  <Col md={12}>
                    <h6><Tag size={14} className="me-2" />Keywords</h6>
                    <div>
                      {layerMetadata.basic.keywords.map((keyword, index) => (
                        <Badge key={index} bg="secondary" className="me-1 mb-1">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </Col>
                )}
              </Row>
            </Card.Body>
          </Card>

          {/* Technical Details */}
          <Card className="mb-3">
            <Card.Header className="bg-info text-white">
              <Settings size={16} className="me-2" />
              Technical Details
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <h6><Globe size={14} className="me-2" />Coordinate Systems</h6>
                  <div>
                    {layerMetadata.technical.crs.map((crs, index) => (
                      <Badge key={index} bg="info" className="me-1 mb-1">
                        {crs}
                      </Badge>
                    ))}
                  </div>
                </Col>
                <Col md={6}>
                  <h6><Eye size={14} className="me-2" />Capabilities</h6>
                  <div>
                    <Badge bg={layerMetadata.technical.queryable ? "success" : "secondary"} className="me-1 mb-1">
                      {layerMetadata.technical.queryable ? "Queryable" : "Not Queryable"}
                    </Badge>
                  </div>
                </Col>
                {(layerMetadata.technical.minScale || layerMetadata.technical.maxScale) && (
                  <Col md={12}>
                    <h6><MapPin size={14} className="me-2" />Scale Range</h6>
                    <p className="text-muted">
                      {layerMetadata.technical.minScale && `Min: 1:${layerMetadata.technical.minScale.toLocaleString()}`}
                      {layerMetadata.technical.minScale && layerMetadata.technical.maxScale && ' | '}
                      {layerMetadata.technical.maxScale && `Max: 1:${layerMetadata.technical.maxScale.toLocaleString()}`}
                    </p>
                  </Col>
                )}
              </Row>
            </Card.Body>
          </Card>

          {/* Temporal Information */}
          {layerMetadata.temporal && (
            <Card className="mb-3">
              <Card.Header className="bg-warning text-dark">
                <Clock size={16} className="me-2" />
                Temporal Information
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <h6><Calendar size={14} className="me-2" />Time Extent</h6>
                    <p className="text-muted">{layerMetadata.temporal.extent || 'Not specified'}</p>
                  </Col>
                  <Col md={6}>
                    <h6><Clock size={14} className="me-2" />Default Time</h6>
                    <p className="text-muted">{layerMetadata.temporal.defaultTime || 'Not specified'}</p>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          )}

          {/* Service Information */}
          <Card className="mb-3">
            <Card.Header className="bg-success text-white">
              <Database size={16} className="me-2" />
              Service Capabilities
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={12}>
                  <h6><Layers size={14} className="me-2" />Supported Services</h6>
                  <div>
                    <Badge bg={layerMetadata.service.wmsSupported ? "success" : "secondary"} className="me-1 mb-1">
                      WMS {layerMetadata.service.wmsSupported ? "✓" : "✗"}
                    </Badge>
                    <Badge bg={layerMetadata.service.wfsSupported ? "success" : "secondary"} className="me-1 mb-1">
                      WFS {layerMetadata.service.wfsSupported ? "✓" : "✗"}
                    </Badge>
                    <Badge bg={layerMetadata.service.wmtsSupported ? "success" : "secondary"} className="me-1 mb-1">
                      WMTS {layerMetadata.service.wmtsSupported ? "✓" : "✗"}
                    </Badge>
                  </div>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </div>
      ) : (
        <div className="p-4 text-center text-muted">
          <Info size={48} className="mb-3 opacity-50" />
          <h5>No Metadata Available</h5>
          <p>Layer metadata could not be retrieved from the geoserver.</p>
          <p>The layer may still be functional on the map.</p>
        </div>
      )}
    </div>
  );

  const renderStylesTab = () => (
    <div className="layer-styles-content">
      {loading ? (
        <div className="text-center p-4">
          <Spinner animation="border" size="sm" className="me-2" />
          Loading styles...
        </div>
      ) : error ? (
        <Alert variant="danger">
          <strong>Error:</strong> {error}
        </Alert>
      ) : (
        <div className="styles-sections p-3">
          {/* Current Style */}
          <Card className="mb-3">
            <Card.Header className="bg-primary text-white">
              <Palette size={16} className="me-2" />
              Current Style
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={8}>
                  <h6>Active Style: {selectedStyle || 'Default'}</h6>
                  <p className="text-muted">
                    This is how the layer currently appears on the map
                  </p>
                </Col>
                <Col md={4} className="text-end">
                  {layer && (
                    <img
                      src={layer.legendUrl}
                      alt="Current legend"
                      className="img-fluid"
                      style={{ maxHeight: '100px', border: '1px solid #ddd' }}
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  )}
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* Available Styles */}
          <Card className="mb-3">
            <Card.Header className="bg-info text-white">
              <Palette size={16} className="me-2" />
              Available Styles
            </Card.Header>
            <Card.Body>
              {availableStyles.length > 0 ? (
                <div className="styles-grid">
                  {availableStyles.map((style, index) => (
                    <div
                      key={index}
                      className={`style-option ${style.isDefault ? 'default-style' : ''} ${selectedStyle === style.name ? 'selected-style' : ''}`}
                      onClick={() => handleStyleChange(style.name)}
                      style={{
                        cursor: 'pointer',
                        border: selectedStyle === style.name ? '2px solid #1e4080' : '1px solid #ddd',
                        borderRadius: '6px',
                        padding: '0.75rem',
                        marginBottom: '0.5rem',
                        backgroundColor: selectedStyle === style.name ? '#f8f9fa' : 'white'
                      }}
                    >
                      <Row>
                        <Col md={8}>
                          <h6 className="mb-1">
                            {style.title || style.name}
                            {style.isDefault && (
                              <Badge bg="success" className="ms-2">Default</Badge>
                            )}
                          </h6>
                          <p className="text-muted small mb-0">
                            {style.abstract || 'No description available'}
                          </p>
                        </Col>
                        <Col md={4} className="text-end">
                          <img
                            src={style.legendUrl}
                            alt={`${style.name} legend`}
                            className="img-fluid"
                            style={{ maxHeight: '60px', border: '1px solid #ddd' }}
                            onError={(e) => {
                              (e.target as HTMLImageElement).style.display = 'none';
                            }}
                          />
                        </Col>
                      </Row>
                    </div>
                  ))}
                </div>
              ) : (
                <Alert variant="info">
                  <Info size={16} className="me-2" />
                  No additional styles available for this layer
                </Alert>
              )}
            </Card.Body>
          </Card>

          {/* Style Information */}
          <Card className="mb-3">
            <Card.Header className="bg-warning text-dark">
              <Info size={16} className="me-2" />
              Style Information
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <h6>Legend Format</h6>
                  <p className="text-muted">PNG Image</p>
                </Col>
                <Col md={6}>
                  <h6>Style Type</h6>
                  <p className="text-muted">SLD (Styled Layer Descriptor)</p>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </div>
      )}
    </div>
  );

  const renderDownloadTab = () => (
    <div className="layer-download-content">
      <div className="download-sections p-3">
        {/* Download Formats */}
        <Card className="mb-3">
          <Card.Header className="bg-primary text-white">
            <Download size={16} className="me-2" />
            Available Formats
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={6}>
                <h6>Vector Formats</h6>
                <div className="format-options">
                  <Button
                    variant="outline-primary"
                    size="sm"
                    className="me-2 mb-2"
                    onClick={() => handleDownload('geojson')}
                  >
                    <Download size={12} className="me-1" />
                    GeoJSON
                  </Button>
                  <Button
                    variant="outline-primary"
                    size="sm"
                    className="me-2 mb-2"
                    onClick={() => handleDownload('shapefile')}
                  >
                    <Download size={12} className="me-1" />
                    Shapefile
                  </Button>
                  <Button
                    variant="outline-primary"
                    size="sm"
                    className="me-2 mb-2"
                    onClick={() => handleDownload('kml')}
                  >
                    <Download size={12} className="me-1" />
                    KML
                  </Button>
                </div>
              </Col>
              <Col md={6}>
                <h6>Raster Formats</h6>
                <div className="format-options">
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    className="me-2 mb-2"
                    onClick={() => handleDownload('png')}
                  >
                    <Download size={12} className="me-1" />
                    PNG
                  </Button>
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    className="me-2 mb-2"
                    onClick={() => handleDownload('jpeg')}
                  >
                    <Download size={12} className="me-1" />
                    JPEG
                  </Button>
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    className="me-2 mb-2"
                    onClick={() => handleDownload('geotiff')}
                  >
                    <Download size={12} className="me-1" />
                    GeoTIFF
                  </Button>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Spatial Filtering */}
        <Card className="mb-3">
          <Card.Header className="bg-info text-white">
            <MapPin size={16} className="me-2" />
            Spatial Filtering
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={12}>
                <h6>Download Area</h6>
                <div className="spatial-options">
                  <Form.Check
                    type="radio"
                    id="spatial-all"
                    name="spatialFilter"
                    label="Entire layer"
                    defaultChecked
                    className="mb-2"
                  />
                  <Form.Check
                    type="radio"
                    id="spatial-viewport"
                    name="spatialFilter"
                    label="Current map view"
                    className="mb-2"
                  />
                  <Form.Check
                    type="radio"
                    id="spatial-drawn"
                    name="spatialFilter"
                    label="Drawn area (if available)"
                    disabled
                    className="mb-2"
                  />
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Temporal Filtering */}
        {layerMetadata?.temporal && (
          <Card className="mb-3">
            <Card.Header className="bg-warning text-dark">
              <Calendar size={16} className="me-2" />
              Temporal Filtering
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <h6>Start Date</h6>
                  <Form.Control
                    type="date"
                    size="sm"
                    defaultValue={layerMetadata.temporal.defaultTime?.split('T')[0]}
                  />
                </Col>
                <Col md={6}>
                  <h6>End Date</h6>
                  <Form.Control
                    type="date"
                    size="sm"
                    defaultValue={layerMetadata.temporal.defaultTime?.split('T')[0]}
                  />
                </Col>
              </Row>
              <Row className="mt-2">
                <Col md={12}>
                  <small className="text-muted">
                    Available time range: {layerMetadata.temporal.extent || 'Not specified'}
                  </small>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        )}

        {/* Download Information */}
        <Card className="mb-3">
          <Card.Header className="bg-success text-white">
            <Info size={16} className="me-2" />
            Download Information
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={6}>
                <h6>Coordinate System</h6>
                <p className="text-muted">
                  {layerMetadata?.technical.crs?.[0] || 'EPSG:4326'}
                </p>
              </Col>
              <Col md={6}>
                <h6>Data Source</h6>
                <p className="text-muted">
                  {layerMetadata?.service.wfsSupported ? 'WFS Service' : 'WMS Service'}
                </p>
              </Col>
              <Col md={12}>
                <Alert variant="info" className="mb-0">
                  <Info size={14} className="me-2" />
                  Downloads are processed on the server and may take a few moments for large datasets.
                </Alert>
              </Col>
            </Row>
          </Card.Body>
        </Card>
      </div>
    </div>
  );

  const renderAnalyticsTab = () => (
    <div className="layer-analytics-content">
      <div className="analytics-sections p-3">
        {/* Layer Statistics */}
        <Card className="mb-3">
          <Card.Header className="bg-primary text-white">
            <BarChart3 size={16} className="me-2" />
            Layer Statistics
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={4}>
                <div className="stat-item text-center">
                  <h4 className="text-primary">~</h4>
                  <small className="text-muted">Features</small>
                </div>
              </Col>
              <Col md={4}>
                <div className="stat-item text-center">
                  <h4 className="text-success">
                    {layerMetadata?.technical.bbox ?
                      `${(layerMetadata.technical.bbox.maxx - layerMetadata.technical.bbox.minx).toFixed(2)}°` :
                      '~'
                    }
                  </h4>
                  <small className="text-muted">Width (degrees)</small>
                </div>
              </Col>
              <Col md={4}>
                <div className="stat-item text-center">
                  <h4 className="text-info">
                    {layerMetadata?.technical.bbox ?
                      `${(layerMetadata.technical.bbox.maxy - layerMetadata.technical.bbox.miny).toFixed(2)}°` :
                      '~'
                    }
                  </h4>
                  <small className="text-muted">Height (degrees)</small>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Data Quality */}
        <Card className="mb-3">
          <Card.Header className="bg-success text-white">
            <Database size={16} className="me-2" />
            Data Quality
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={6}>
                <h6>Service Status</h6>
                <Badge bg="success" className="mb-2">
                  <Globe size={12} className="me-1" />
                  Online
                </Badge>
              </Col>
              <Col md={6}>
                <h6>Data Freshness</h6>
                <Badge bg="info" className="mb-2">
                  <Clock size={12} className="me-1" />
                  {layerMetadata?.temporal ? 'Real-time' : 'Static'}
                </Badge>
              </Col>
              <Col md={12}>
                <h6>Capabilities</h6>
                <div>
                  <Badge bg={layerMetadata?.technical.queryable ? "success" : "secondary"} className="me-1 mb-1">
                    {layerMetadata?.technical.queryable ? "Queryable" : "View Only"}
                  </Badge>
                  <Badge bg={layerMetadata?.service.wfsSupported ? "success" : "secondary"} className="me-1 mb-1">
                    {layerMetadata?.service.wfsSupported ? "Downloadable" : "Display Only"}
                  </Badge>
                  <Badge bg={layerMetadata?.temporal ? "success" : "secondary"} className="me-1 mb-1">
                    {layerMetadata?.temporal ? "Temporal" : "Static"}
                  </Badge>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Usage Information */}
        <Card className="mb-3">
          <Card.Header className="bg-info text-white">
            <Eye size={16} className="me-2" />
            Usage Information
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={6}>
                <h6>Layer Type</h6>
                <p className="text-muted">
                  {layerMetadata?.service.wfsSupported ? 'Vector Layer' : 'Raster Layer'}
                </p>
              </Col>
              <Col md={6}>
                <h6>Update Frequency</h6>
                <p className="text-muted">
                  {layerMetadata?.temporal ? 'Dynamic' : 'Static'}
                </p>
              </Col>
              <Col md={12}>
                <h6>Recommended Use</h6>
                <p className="text-muted">
                  {layerMetadata?.technical.queryable
                    ? 'Suitable for analysis and data extraction'
                    : 'Best for visualization and display purposes'
                  }
                </p>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Performance Metrics */}
        <Card className="mb-3">
          <Card.Header className="bg-warning text-dark">
            <Settings size={16} className="me-2" />
            Performance Metrics
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={4}>
                <div className="metric-item">
                  <h6>Load Time</h6>
                  <div className="progress mb-2" style={{ height: '8px' }}>
                    <div
                      className="progress-bar bg-success"
                      style={{ width: '85%' }}
                    ></div>
                  </div>
                  <small className="text-muted">Fast</small>
                </div>
              </Col>
              <Col md={4}>
                <div className="metric-item">
                  <h6>Data Size</h6>
                  <div className="progress mb-2" style={{ height: '8px' }}>
                    <div
                      className="progress-bar bg-info"
                      style={{ width: '60%' }}
                    ></div>
                  </div>
                  <small className="text-muted">Medium</small>
                </div>
              </Col>
              <Col md={4}>
                <div className="metric-item">
                  <h6>Complexity</h6>
                  <div className="progress mb-2" style={{ height: '8px' }}>
                    <div
                      className="progress-bar bg-warning"
                      style={{ width: '40%' }}
                    ></div>
                  </div>
                  <small className="text-muted">Simple</small>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Card>
      </div>
    </div>
  );

  const renderAdvancedTab = () => (
    <div className="layer-advanced-content">
      {!layerMetadata ? (
        <div className="p-4 text-center text-muted">
          <Settings size={48} className="mb-3 opacity-50" />
          <h5>No Advanced Information Available</h5>
          <p>Advanced layer information requires metadata from the geoserver.</p>
          <p>Please ensure the layer metadata can be retrieved.</p>
        </div>
      ) : (
        <div className="advanced-sections p-3">
        {/* Feature Information */}
        <Card className="mb-3">
          <Card.Header className="bg-primary text-white">
            <Eye size={16} className="me-2" />
            Feature Information
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={12}>
                <h6>Query Features</h6>
                <p className="text-muted">
                  {layerMetadata?.technical.queryable
                    ? 'Click on the map to get detailed information about features in this layer'
                    : 'This layer does not support feature queries'
                  }
                </p>
                <Button
                  variant={layerMetadata?.technical.queryable ? "primary" : "secondary"}
                  size="sm"
                  disabled={!layerMetadata?.technical.queryable}
                >
                  <Eye size={12} className="me-1" />
                  {layerMetadata?.technical.queryable ? 'Enable Feature Info' : 'Not Available'}
                </Button>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Sharing Options */}
        <Card className="mb-3">
          <Card.Header className="bg-info text-white">
            <Share2 size={16} className="me-2" />
            Sharing & Export
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={6}>
                <h6>Layer URL</h6>
                <div className="input-group input-group-sm mb-2">
                  <Form.Control
                    type="text"
                    value={`${API_CONFIG.BASE_URL}/ows/layer-metadata/${encodeURIComponent(layer?.name || '')}`}
                    readOnly
                    size="sm"
                  />
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(`${API_CONFIG.BASE_URL}/ows/layer-metadata/${encodeURIComponent(layer?.name || '')}`);
                    }}
                  >
                    Copy
                  </Button>
                </div>
              </Col>
              <Col md={6}>
                <h6>WMS Service URL</h6>
                <div className="input-group input-group-sm mb-2">
                  <Form.Control
                    type="text"
                    value={`${API_CONFIG.BASE_URL}/ows?SERVICE=WMS&REQUEST=GetCapabilities`}
                    readOnly
                    size="sm"
                  />
                  <Button
                    variant="outline-secondary"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(`${API_CONFIG.BASE_URL}/ows?SERVICE=WMS&REQUEST=GetCapabilities`);
                    }}
                  >
                    Copy
                  </Button>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Spatial Analysis */}
        <Card className="mb-3">
          <Card.Header className="bg-success text-white">
            <Settings size={16} className="me-2" />
            Spatial Analysis
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={12}>
                <h6>Available Operations</h6>
                <div className="analysis-options">
                  <Button
                    variant="outline-success"
                    size="sm"
                    className="me-2 mb-2"
                    disabled={!layerMetadata?.service.wfsSupported}
                  >
                    <MapPin size={12} className="me-1" />
                    Spatial Query
                  </Button>
                  <Button
                    variant="outline-success"
                    size="sm"
                    className="me-2 mb-2"
                    disabled={!layerMetadata?.service.wfsSupported}
                  >
                    <BarChart3 size={12} className="me-1" />
                    Attribute Analysis
                  </Button>
                  <Button
                    variant="outline-success"
                    size="sm"
                    className="me-2 mb-2"
                    disabled={!layerMetadata?.temporal}
                  >
                    <Calendar size={12} className="me-1" />
                    Temporal Analysis
                  </Button>
                </div>
                {!layerMetadata?.service.wfsSupported && (
                  <Alert variant="warning" className="mt-2 mb-0">
                    <Info size={14} className="me-2" />
                    Advanced spatial analysis requires WFS support. This layer only supports visualization.
                  </Alert>
                )}
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Technical Information */}
        <Card className="mb-3">
          <Card.Header className="bg-warning text-dark">
            <Database size={16} className="me-2" />
            Technical Information
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={6}>
                <h6>Service Endpoints</h6>
                <ul className="list-unstyled small">
                  <li><strong>WMS:</strong> {layerMetadata?.service.wmsSupported ? '✓ Available' : '✗ Not supported'}</li>
                  <li><strong>WFS:</strong> {layerMetadata?.service.wfsSupported ? '✓ Available' : '✗ Not supported'}</li>
                  <li><strong>WMTS:</strong> {layerMetadata?.service.wmtsSupported ? '✓ Available' : '✗ Not supported'}</li>
                </ul>
              </Col>
              <Col md={6}>
                <h6>Layer Properties</h6>
                <ul className="list-unstyled small">
                  <li><strong>Queryable:</strong> {layerMetadata?.technical.queryable ? 'Yes' : 'No'}</li>
                  <li><strong>Temporal:</strong> {layerMetadata?.temporal ? 'Yes' : 'No'}</li>
                  <li><strong>Cascaded:</strong> No</li>
                </ul>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* API Examples */}
        <Card className="mb-3">
          <Card.Header className="bg-secondary text-white">
            <FileText size={16} className="me-2" />
            API Examples
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={12}>
                <h6>GetMap Request</h6>
                <pre className="bg-light p-2 small">
{`GET ${API_CONFIG.BASE_URL}/ows?
SERVICE=WMS&
REQUEST=GetMap&
LAYERS=${layer?.name}&
STYLES=&
FORMAT=image/png&
TRANSPARENT=true&
VERSION=1.1.1&
WIDTH=256&
HEIGHT=256&
SRS=EPSG:4326&
BBOX=-180,-90,180,90`}
                </pre>
              </Col>
              {layerMetadata?.service.wfsSupported && (
                <Col md={12}>
                  <h6>GetFeature Request</h6>
                  <pre className="bg-light p-2 small">
{`GET ${API_CONFIG.BASE_URL}/ows?
SERVICE=WFS&
REQUEST=GetFeature&
TYPENAME=${layer?.name}&
VERSION=1.0.0&
OUTPUTFORMAT=application/json`}
                  </pre>
                </Col>
              )}
            </Row>
          </Card.Body>
        </Card>
      </div>
      )}
    </div>
  );

  if (!layer) return null;

  return (
    <Modal 
      show={show} 
      onHide={onHide} 
      size="lg" 
      className="layer-details-modal"
      backdrop="static"
    >
      <Modal.Header className="bg-primary text-white">
        <Modal.Title className="d-flex align-items-center">
          <Layers size={20} className="me-2" />
          Layer Details: {layer.title || layer.name}
        </Modal.Title>
        <Button variant="outline-light" size="sm" onClick={onHide}>
          <X size={16} />
        </Button>
      </Modal.Header>
      
      <Modal.Body className="p-0">
        <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k || 'metadata')}>
          <Nav variant="tabs" className="layer-details-tabs">
            <Nav.Item>
              <Nav.Link eventKey="metadata">
                <Info size={16} className="me-2" />
                Metadata
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="styles">
                <Palette size={16} className="me-2" />
                Styles
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="download">
                <Download size={16} className="me-2" />
                Download
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="analytics">
                <BarChart3 size={16} className="me-2" />
                Analytics
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="advanced">
                <Settings size={16} className="me-2" />
                Advanced
              </Nav.Link>
            </Nav.Item>
          </Nav>
          
          <Tab.Content className="layer-details-content">
            <Tab.Pane eventKey="metadata">
              {renderMetadataTab()}
            </Tab.Pane>
            
            <Tab.Pane eventKey="styles">
              {renderStylesTab()}
            </Tab.Pane>
            
            <Tab.Pane eventKey="download">
              {renderDownloadTab()}
            </Tab.Pane>
            
            <Tab.Pane eventKey="analytics">
              {renderAnalyticsTab()}
            </Tab.Pane>
            
            <Tab.Pane eventKey="advanced">
              {renderAdvancedTab()}
            </Tab.Pane>
          </Tab.Content>
        </Tab.Container>
      </Modal.Body>
    </Modal>
  );
};

export default LayerDetailsModal;
