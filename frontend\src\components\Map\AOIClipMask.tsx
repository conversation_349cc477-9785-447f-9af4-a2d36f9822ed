import React, { useMemo } from 'react';
import { Polygon, LayerGroup } from 'react-leaflet';

interface AOIClipMaskProps {
  geometry?: GeoJSON.Polygon | GeoJSON.MultiPolygon | null;
  maskFill?: string;
  maskOpacity?: number;
  outlineColor?: string;
  outlineWeight?: number;
  outlineDash?: string;
  enabled?: boolean;
}

const AOIClipMask: React.FC<AOIClipMaskProps> = ({
  geometry,
  maskFill = '#007bff', // Changed from black to blue theme
  maskOpacity = 0.7, // 70% opacity as requested
  outlineColor = '#007bff',
  outlineWeight = 2,
  outlineDash = '5, 5',
  enabled = true
}) => {
  // Memoize coordinate processing to avoid recomputing unless geometry changes
  const processedCoordinates = useMemo(() => {
    if (!geometry || !enabled) {
      return null;
    }

    const worldBounds: [number, number][] = [
      [85, -180], [85, 180], [-85, 180], [-85, -180], [85, -180]
    ];

    const processPolygon = (coordinates: number[][][]): {
      maskPositions: [number, number][][];
      outlinePositions: [number, number][];
    } => {
      // Convert outer ring from GeoJSON [lng, lat] to Leaflet [lat, lng]
      const outerRing = coordinates[0].map((coord: number[]) => [coord[1], coord[0]] as [number, number]);
      
      // For the mask, we need world bounds with AOI hole
      const maskPositions = [worldBounds, outerRing];
      
      // If there are inner rings (holes), we need to re-fill them with mask
      if (coordinates.length > 1) {
        const innerRings = coordinates.slice(1).map(ring => 
          ring.map((coord: number[]) => [coord[1], coord[0]] as [number, number])
        );
        maskPositions.push(...innerRings);
      }

      return {
        maskPositions,
        outlinePositions: outerRing
      };
    };

    if (geometry.type === 'Polygon') {
      return [processPolygon(geometry.coordinates)];
    } else if (geometry.type === 'MultiPolygon') {
      return geometry.coordinates.map(polygon => processPolygon(polygon));
    }

    return null;
  }, [geometry, enabled]);

  if (!processedCoordinates || !enabled) {
    return null;
  }

  return (
    <LayerGroup>
      {processedCoordinates.map((coords, index) => (
        <React.Fragment key={`aoi-clip-mask-${index}`}>
          {/* Mask polygon with AOI hole(s) */}
          <Polygon
            positions={coords.maskPositions}
            pathOptions={{
              fillColor: maskFill,
              fillOpacity: maskOpacity,
              stroke: false,
              interactive: false
            }}
            pane="aoiClipPane"
          />
          {/* AOI boundary outline with consistent styling */}
          <Polygon
            positions={coords.outlinePositions}
            pathOptions={{
              fillColor: outlineColor,
              fillOpacity: 0.25, // Semi-transparent fill for consistency
              color: outlineColor,
              weight: outlineWeight,
              opacity: 0.9,
              dashArray: outlineDash,
              interactive: false
            }}
          />
        </React.Fragment>
      ))}
    </LayerGroup>
  );
};

export default AOIClipMask;