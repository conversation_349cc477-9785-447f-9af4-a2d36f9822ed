/**
 * AOI-True WMS Tile Layer Component
 * 
 * This component replaces the standard WMSTileLayer with one that uses
 * the AOI-True Clipping system. It automatically determines layer type
 * and generates appropriate tile URLs with server-side clipping.
 */

import React, { useEffect, useState, useMemo } from 'react';
import { TileLayer } from 'react-leaflet';
import { getAOITrueClippingService } from '../../services/aoiTrueClippingService';
import { getLayerCapabilitiesService } from '../../services/layerCapabilitiesService';



export interface AOITrueWMSTileLayerProps {
  layerName: string;
  aoiId?: string;
  time?: string;
  format?: string;
  srs?: string;
  opacity?: number;
  attribution?: string;
  bounds?: [[number, number], [number, number]];
  maxZoom?: number;
  minZoom?: number;
  tileSize?: number;
  noWrap?: boolean;
  onLoading?: () => void;
  onLoad?: () => void;
  onError?: (error: any) => void;
}

export const AOITrueWMSTileLayer: React.FC<AOITrueWMSTileLayerProps> = ({
  layerName,
  aoiId,
  time,
  format = 'image/png',
  srs = 'EPSG:4326',
  opacity = 1.0,
  attribution,
  bounds,
  maxZoom = 19,
  minZoom = 2,
  tileSize = 256,
  noWrap = true,
  onLoading,
  onLoad,
  onError
}) => {
  const [layerType, setLayerType] = useState<'vector' | 'raster' | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const aoiTrueClippingService = getAOITrueClippingService();
  const layerCapabilitiesService = getLayerCapabilitiesService();

  // Determine layer type on mount
  useEffect(() => {
    const determineLayerType = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log(`🔍 Determining layer type for: ${layerName}`);

        const type = await layerCapabilitiesService.getLayerType(layerName);

        if (!type) {
          // This should not happen anymore with the improved fallback system
          console.error(`❌ Could not determine layer type for: ${layerName} even with fallbacks`);
          throw new Error(`Could not determine layer type for: ${layerName}. All detection methods failed.`);
        }

        setLayerType(type);
        console.log(`✅ Layer ${layerName} identified as: ${type}`);
        
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        console.error(`❌ Failed to determine layer type for ${layerName}:`, errorMessage);
        setError(errorMessage);
        onError?.(err);
      } finally {
        setIsLoading(false);
      }
    };

    determineLayerType();
  }, [layerName, layerCapabilitiesService, onError]);

  // Generate tile URL template
  const tileUrlTemplate = useMemo(() => {
    if (!layerType) {
      return '';
    }

    // Use current AOI ID if not explicitly provided
    const effectiveAOIId = aoiId || aoiTrueClippingService.getCurrentAOIId();

    // Generate base URL template for Leaflet
    // Leaflet will replace {z}, {x}, {y} with actual tile coordinates
    const baseUrl = aoiTrueClippingService.generateTileURL(
      layerType,
      0, // Placeholder - Leaflet will replace
      0, // Placeholder - Leaflet will replace
      0, // Placeholder - Leaflet will replace
      {
        layer: layerName,
        aoiId: effectiveAOIId || undefined,
        time,
        format,
        srs
      }
    );

    // Replace the placeholder coordinates with Leaflet template variables
    const urlTemplate = baseUrl
      .replace('/0/0/0.png', '/{z}/{x}/{y}.png');

    console.log(`🗺️ Generated tile URL template for ${layerName} (${layerType}):`, urlTemplate);

    return urlTemplate;
  }, [layerType, layerName, aoiId, time, format, srs, aoiTrueClippingService]);

  // Create unique key for layer re-rendering when parameters change
  const layerKey = useMemo(() => {
    const effectiveAOIId = aoiId || aoiTrueClippingService.getCurrentAOIId();
    return [
      layerName,
      layerType,
      effectiveAOIId || 'no-aoi',
      time || 'no-time',
      format,
      srs
    ].join('|');
  }, [layerName, layerType, aoiId, time, format, srs, aoiTrueClippingService]);

  // Handle loading events
  const handleTileLoadStart = () => {
    console.log(`🔄 Loading tiles for layer: ${layerName}`);
    onLoading?.();
  };

  const handleTileLoad = () => {
    console.log(`✅ Tiles loaded for layer: ${layerName}`);
    onLoad?.();
  };

  const handleTileError = (error: any) => {
    console.error(`❌ Tile loading error for layer ${layerName}:`, error);
    onError?.(error);
  };

  // Don't render if still loading or error
  if (isLoading) {
    console.log(`⏳ Layer ${layerName} still determining type...`);
    return null;
  }

  if (error || !layerType || !tileUrlTemplate) {
    console.warn(`⚠️ Cannot render layer ${layerName}: ${error || 'No tile URL template'}`);
    return null;
  }

  return (
    <TileLayer
      key={layerKey}
      url={tileUrlTemplate}
      opacity={opacity}
      attribution={attribution || `${layerName} (AOI-True Clipping)`}
      bounds={bounds}
      maxZoom={maxZoom}
      minZoom={minZoom}
      tileSize={tileSize}
      noWrap={noWrap}
      eventHandlers={{
        loading: handleTileLoadStart,
        load: handleTileLoad,
        tileerror: handleTileError
      }}
    />
  );
};

export default AOITrueWMSTileLayer;
