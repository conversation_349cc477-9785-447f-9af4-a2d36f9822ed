/**
 * AOI Boundary Stroke Component
 *
 * This component renders the AOI boundary outline on top of the sandwich stack.
 * It sits at the highest z-index to ensure the boundary is always visible.
 *
 * Features:
 * - Transparent fill by default (shows data layers underneath)
 * - Hover effect with translucent highlight and dotted outline
 * - Interactive boundary for better user experience
 */

import React, { useState } from 'react';
import { GeoJSON } from 'react-leaflet';
import L from 'leaflet';

// Types
type Ring = [number, number][];                // [lng, lat]
type Polygon = Ring[];                         // [outer, hole1, ...]
type MultiPolygon = Polygon[];                 // multiple polygons

interface Props {
  // AOI geometry in EPSG:4326, lng/lat (multipolygon supported)
  aoi: MultiPolygon | Polygon;

  // Styling options
  color?: string;
  weight?: number;
  opacity?: number;
  dashArray?: string;
  hoverColor?: string;
  hoverFillOpacity?: number;

  // Pane for z-index control
  pane?: string;
}

export default function AoiBoundaryStroke({
  aoi,
  color = '#007bff',
  weight = 2,
  opacity = 0.9,
  dashArray = '5, 5',
  hoverColor = '#dc3545', // Red for hover
  hoverFillOpacity = 0.3,
  pane = 'aoi-boundary-stroke'
}: Props) {

  const [isHovered, setIsHovered] = useState(false);

  if (!aoi) return null;

  // Convert AOI to GeoJSON format
  const geoJsonData: GeoJSON.FeatureCollection = {
    type: 'FeatureCollection',
    features: []
  };

  // Normalize AOI to MultiPolygon
  const mp: MultiPolygon = Array.isArray(aoi[0][0][0]) ? (aoi as MultiPolygon) : [aoi as Polygon];

  // Convert each polygon to a GeoJSON feature
  mp.forEach((polygon, index) => {
    const feature: GeoJSON.Feature<GeoJSON.Polygon> = {
      type: 'Feature',
      properties: { id: `aoi-polygon-${index}` },
      geometry: {
        type: 'Polygon',
        coordinates: polygon
      }
    };
    geoJsonData.features.push(feature);
  });

  // Dynamic styling based on hover state - OUTLINE ONLY, NO FILL EVER
  const pathOptions = {
    fillColor: 'transparent', // Always transparent - no fill ever
    fillOpacity: 0, // Always 0 - completely transparent fill
    color: isHovered ? hoverColor : color,
    weight: isHovered ? weight + 1 : weight, // Slightly thicker on hover
    opacity,
    dashArray: isHovered ? '3, 3' : dashArray, // Dotted on hover, dashed normally
    interactive: true, // Enable interaction for hover effects
    fill: false, // Explicitly disable fill
    stroke: true // Ensure stroke is enabled
  };



  // Event handlers for hover effects
  const onEachFeature = (feature: any, layer: L.Layer) => {
    layer.on({
      mouseover: () => {
        setIsHovered(true);
      },
      mouseout: () => {
        setIsHovered(false);
      }
    });
  };

  return (
    <GeoJSON
      key={`aoi-boundary-${JSON.stringify(aoi)}-${isHovered}`}
      data={geoJsonData}
      pathOptions={pathOptions}
      onEachFeature={(feature, layer) => {
        // Apply additional styling to ensure no fill
        if (layer instanceof L.Path) {
          layer.setStyle({
            ...pathOptions,
            fillOpacity: 0,
            fill: false
          });
        }
        onEachFeature(feature, layer);
      }}
      pane={pane}
    />
  );
}
