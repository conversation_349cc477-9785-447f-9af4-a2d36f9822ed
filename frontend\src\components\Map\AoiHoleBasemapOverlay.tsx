/**
 * AOI-Hole Basemap Overlay Component
 * 
 * This component implements the "sandwich" approach for AOI visual clipping:
 * - Bottom: Normal basemap (unchanged)
 * - Middle: Data layers (sat mosaics, floods, etc.)
 * - Above them: AOI-hole basemap overlay that only draws inside the AOI's BBOX and punches a transparent hole where the AOI is
 * - Very top: AOI boundary stroke
 * 
 * This approach ensures anything outside the AOI is visually covered by the basemap copy,
 * and everything inside the AOI shows the toggled layers. No dimming. No server changes.
 */

import L from "leaflet";
import { useEffect } from "react";
import { useMap } from "react-leaflet";

// Types
type Ring = [number, number][];                // [lng, lat]
type Polygon = Ring[];                         // [outer, hole1, ...]
type MultiPolygon = Polygon[];                 // multiple polygons

interface Props {
  // Basemap source (same as your normal base)
  tileUrl: string;                  // e.g. "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
  subdomains?: string | string[];
  tileSize?: number;                // default 256
  attribution?: string;

  // AOI geometry in EPSG:4326, lng/lat (multipolygon supported)
  aoi: MultiPolygon | Polygon;

  // AOI bbox as Leaflet bounds (lat,lng)
  // If you don't pass it, we compute it from the AOI.
  bbox?: L.LatLngBoundsLiteral;

  // zIndex pane ordering (should sit ABOVE data layers)
  pane?: string;                    // default: "aoi-hole-basemap"
}

export default function AoiHoleBasemapOverlay({
  tileUrl,
  subdomains,
  tileSize = 256,
  attribution,
  aoi,
  bbox,
  pane = "aoi-hole-basemap",
}: Props) {
  const map = useMap();

  useEffect(() => {
    if (!aoi || !map) return;

    console.log('🕳️ Setting up AOI-hole basemap overlay');

    // --- Pane order: above data layers so it can cover outside AOI ---
    if (!map.getPane(pane)) {
      const p = map.createPane(pane)!;
      p.style.zIndex = "480";             // base ~200, data ~400, hole overlay ~480, AOI stroke ~600
      p.style.pointerEvents = "none";
      console.log(`✅ Created pane "${pane}" with z-index 480`);
    }

    // Normalize AOI to MultiPolygon
    const mp: MultiPolygon = Array.isArray(aoi[0][0][0]) ? (aoi as MultiPolygon) : [aoi as Polygon];

    // Compute bbox if not passed
    const aoiBounds = bbox
      ? L.latLngBounds(bbox)
      : L.latLngBounds(
          mp.flat(2).map(([lng, lat]) => L.latLng(lat, lng))
        );

    // --- Custom GridLayer that punches a hole along AOI ---
    const AoiHoleLayer = (L.GridLayer as any).extend({
      initialize: function (options: any) {
        L.GridLayer.prototype.initialize.call(this, options);
        (this as any)._gen = 0;           // generation counter to cancel stale tile draws
        (this as any)._destroyed = false; // set on remove
      },

      onRemove: function (map: L.Map) {
        // Invalidate all in-flight tiles
        (this as any)._destroyed = true;
        (this as any)._gen = ((this as any)._gen || 0) + 1;
        try {
          return L.GridLayer.prototype.onRemove.call(this, map);
        } catch (e) {
          console.warn('⚠️ AOI-hole layer onRemove warning:', e);
        }
      },

      createTile: function (this: L.GridLayer, coords: L.Coords, done: (err?: any, tile?: HTMLCanvasElement) => void) {
        const canvas = L.DomUtil.create("canvas", "leaflet-tile") as HTMLCanvasElement;
        const size = this.getTileSize();
        canvas.width = size.x;
        canvas.height = size.y;
        const ctx = canvas.getContext("2d")!;

        const self: any = this;
        const genAtCreate = self._gen || 0;

        try {
          const mapRef = (this as any)._map as L.Map;
          if (!mapRef) {
            done(undefined, canvas);
            return canvas;
          }

          // Prepare offscreen buffer for double-buffering
          const buffer = document.createElement('canvas');
          buffer.width = size.x;
          buffer.height = size.y;
          const bctx = buffer.getContext('2d')!;

          // Use Leaflet's tile coordinate to pixel conversion for this tile
          const nwPoint = coords.scaleBy(size);

          // Build tile request to basemap
          const url = (this as any)._buildTileUrl(coords);

          const img = new Image();
          if (tileUrl.startsWith('http')) img.crossOrigin = 'anonymous';
          img.onload = () => {
            // Cancel stale draws from previous generations or after removal
            if (genAtCreate !== self._gen || self._destroyed) {
              done(undefined, canvas);
              return;
            }

            // Draw basemap tile into offscreen buffer
            bctx.drawImage(img, 0, 0, size.x, size.y);

            // Punch AOI hole using destination-out
            bctx.save();
            bctx.globalCompositeOperation = 'destination-out';

            // helper: project [lng,lat] → pixel in this tile
            const toPx = (lng: number, lat: number) => {
              const latLng = L.latLng(lat, lng);
              const point = mapRef.project(latLng, coords.z);
              const tilePoint = point.subtract(nwPoint);
              return [tilePoint.x, tilePoint.y];
            };

            const path = new Path2D();
            for (const poly of mp) {
              // outer ring
              const outer = poly[0];
              if (!outer?.length) continue;
              let i = 0;
              for (const [lng, lat] of outer) {
                const [x, y] = toPx(lng, lat);
                if (i === 0) path.moveTo(x, y);
                else path.lineTo(x, y);
                i++;
              }
              path.closePath();
              // holes
              for (let h = 1; h < poly.length; h++) {
                const ring = poly[h];
                let j = 0;
                for (const [lng, lat] of ring) {
                  const [x, y] = toPx(lng, lat);
                  if (j === 0) path.moveTo(x, y);
                  else path.lineTo(x, y);
                  j++;
                }
                path.closePath();
              }
            }

            // Erase AOI interior (evenodd for multipolygons/holes)
            bctx.fill(path, 'evenodd');
            // Slightly inflate erase to cover tile seam rounding
            bctx.lineJoin = 'round';
            bctx.lineCap = 'round';
            bctx.lineWidth = 1.5; // small overdraw
            bctx.stroke(path);

            bctx.restore();

            // Swap buffer into visible tile in one go (atomic)
            ctx.clearRect(0, 0, size.x, size.y);
            ctx.drawImage(buffer, 0, 0);

            done(undefined, canvas);
          };
          img.onerror = (e) => {
            if (genAtCreate !== self._gen || self._destroyed) {
              done(undefined, canvas);
              return;
            }
            console.error('❌ Failed to load basemap tile for AOI-hole overlay:', e);
            done(e);
          };
          img.src = url;

          return canvas;
        } catch (error) {
          console.error('❌ Error creating AOI-hole tile:', error);
          done(error);
          return canvas;
        }
      },

      // Build tile URL from xyz template
      _buildTileUrl: function (this: L.GridLayer, coords: L.Coords) {
        const data: any = {
          x: coords.x,
          y: coords.y,
          z: coords.z,
          s: (Array.isArray(subdomains) ? subdomains : (subdomains ? [subdomains] : ['a', 'b', 'c']))[(coords.x + coords.y) % 3]
        };
        // simple template replace
        return tileUrl.replace(/\{ *([xyzs]) *\}/g, (_: any, m: string) => String(data[m]));
      },


    });

    const layer = new AoiHoleLayer({
      tileSize,
      pane,
      attribution,
      updateWhenIdle: true,
      updateWhenZooming: false,
      keepBuffer: 3
    });
    layer.addTo(map);

    // Cancel stale tiles on interaction start; refresh once at end
    const bumpGen = () => { (layer as any)._gen = ((layer as any)._gen || 0) + 1; };
    const refresh = () => { try { layer.redraw(); } catch (_) {} };

    map.on('zoomstart', bumpGen);
    map.on('movestart', bumpGen);
    map.on('zoomend', refresh);
    map.on('moveend', refresh);

    // Cleanup
    return () => {
      map.off('zoomstart', bumpGen);
      map.off('movestart', bumpGen);
      map.off('zoomend', refresh);
      map.off('moveend', refresh);
      map.removeLayer(layer);
    };
  }, [aoi, JSON.stringify(bbox), tileUrl, subdomains, tileSize, attribution, map, pane]);

  return null;
}
