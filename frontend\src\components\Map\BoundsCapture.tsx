import { useEffect } from 'react';
import { useMap, useMapEvents } from 'react-leaflet';
import { LatLngBounds } from 'leaflet';

interface BoundsCaptureProps {
  onBoundsChange: (bounds: LatLngBounds) => void;
}

const BoundsCapture: React.FC<BoundsCaptureProps> = ({ onBoundsChange }) => {
  const map = useMap();

  // Capture initial bounds
  useEffect(() => {
    if (map) {
      onBoundsChange(map.getBounds());
    }
  }, [map, onBoundsChange]);

  // Listen for map events that change bounds
  useMapEvents({
    moveend: () => {
      onBoundsChange(map.getBounds());
    },
    zoomend: () => {
      onBoundsChange(map.getBounds());
    },
    resize: () => {
      onBoundsChange(map.getBounds());
    }
  });

  return null; // This component doesn't render anything
};

export default BoundsCapture;
