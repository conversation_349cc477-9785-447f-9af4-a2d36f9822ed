import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { X, RefreshCw } from 'lucide-react';
import './Styling/LoadingOverlay.css';

interface LoadingOverlayProps {
  loadingLayers: { [key: string]: boolean };
  wmsLayers: any[];
  errorLayers: { [key: string]: string };
  onRetryLayer?: (layerName: string) => void;
  onCloseLoader?: () => void;
  layerProgress?: { [key: string]: number }; // Real progress from tile loading
}

interface LayerProgress {
  name: string;
  displayName: string;
  progress: number;
  isComplete: boolean;
  hasError: boolean;
  errorMessage?: string;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ 
  loadingLayers, 
  wmsLayers, 
  errorLayers, 
  onRetryLayer, 
  onCloseLoader,
  layerProgress: externalProgress 
}) => {
  const [layerProgress, setLayerProgress] = useState<{ [key: string]: LayerProgress }>({});
  const [dismissedLayers, setDismissedLayers] = useState<Set<string>>(new Set());
  const [retryingLayers, setRetryingLayers] = useState<Set<string>>(new Set());

  // Get display name for a layer - fully dynamic
  const getLayerDisplayName = (layerName: string) => {
    const layer = wmsLayers.find(l => l.name === layerName);
    if (layer?.title) return layer.title;

    // Fallback to formatted layer name (remove namespace and format)
    return layerName.replace(/^[^:]+:/, '').replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Initialize progress for new loading layers
  useEffect(() => {
    const currentlyLoading = Object.keys(loadingLayers).filter(key => loadingLayers[key]);
    const currentErrors = Object.keys(errorLayers);
    
    currentlyLoading.forEach(layerName => {
      if (!layerProgress[layerName]) {
        setLayerProgress(prev => ({
          ...prev,
          [layerName]: {
            name: layerName,
            displayName: getLayerDisplayName(layerName),
            progress: externalProgress?.[layerName] || 0,
            isComplete: false,
            hasError: false
          }
        }));
      }
    });

    // Handle error layers - keep them in progress state but mark as error
    currentErrors.forEach(layerName => {
      if (layerProgress[layerName] && !layerProgress[layerName].hasError) {
        setLayerProgress(prev => ({
          ...prev,
          [layerName]: {
            ...prev[layerName],
            hasError: true,
            errorMessage: errorLayers[layerName],
            isComplete: true
          }
        }));      } else if (!layerProgress[layerName]) {
        // Create error layer entry if it doesn't exist
        setLayerProgress(prev => ({
          ...prev,
          [layerName]: {
            name: layerName,
            displayName: getLayerDisplayName(layerName),
            progress: externalProgress?.[layerName] || 0,
            isComplete: true,
            hasError: true,
            errorMessage: errorLayers[layerName]
          }
        }));
      }
    });
  }, [loadingLayers, errorLayers, wmsLayers, externalProgress]); // Removed layerProgress from dependencies to prevent circular updates

  // Clean up completed layers only when they're actually done (not dismissed manually)
  useEffect(() => {
    const completedLayers = Object.keys(layerProgress).filter(key => 
      !loadingLayers[key] && !errorLayers[key] && layerProgress[key] && !layerProgress[key].hasError && layerProgress[key].progress >= 100
    );
    
    if (completedLayers.length > 0) {
      // Auto-dismiss successfully completed layers after a delay
      const timeout = setTimeout(() => {
        setDismissedLayers(prev => new Set([...prev, ...completedLayers]));
      }, 2000); // Show success for 2 seconds before auto-dismiss

      return () => clearTimeout(timeout);
    }
  }, [loadingLayers, errorLayers, layerProgress]);

  // Update progress from external source (real tile loading)
  useEffect(() => {
    if (externalProgress) {
      setLayerProgress(prev => {
        const updated = { ...prev };
        Object.keys(externalProgress).forEach(layerName => {
          if (updated[layerName] && !updated[layerName].hasError) {
            updated[layerName] = {
              ...updated[layerName],
              progress: Math.min(100, externalProgress[layerName])
            };
          }
        });
        return updated;
      });
    }  }, [externalProgress]);

  // No auto-dismiss for errors - only manual dismiss
  const activeLayerProgress = Object.values(layerProgress).filter(layer =>
    !dismissedLayers.has(layer.name)
  );
  
  const handleDismissLayer = (layerName: string) => {
    setDismissedLayers(prev => new Set([...prev, layerName]));
  };

  const handleRetryLayer = async (layerName: string) => {
    if (onRetryLayer) {
      setRetryingLayers(prev => new Set([...prev, layerName]));
      
      // Reset layer state for retry
      setLayerProgress(prev => ({
        ...prev,
        [layerName]: {
          ...prev[layerName],
          hasError: false,
          errorMessage: undefined,
          progress: 0,
          isComplete: false
        }
      }));

      // Clear from dismissed layers if it was dismissed
      setDismissedLayers(prev => {
        const newSet = new Set(prev);
        newSet.delete(layerName);
        return newSet;
      });

      try {
        await onRetryLayer(layerName);
      } catch (error) {
        console.error(`Retry failed for layer ${layerName}:`, error);
      } finally {
        setRetryingLayers(prev => {
          const newSet = new Set(prev);
          newSet.delete(layerName);
          return newSet;
        });
      }
    }
  };

  const handleCloseOverlay = () => {
    if (onCloseLoader) {
      onCloseLoader();
    } else {
      // Dismiss all layers if no close handler provided
      const allLayerNames = Object.keys(layerProgress);
      setDismissedLayers(new Set(allLayerNames));
    }
  };
  
  if (activeLayerProgress.length === 0) {
    return null;
  }
  return (
    <div className="loading-overlay">
      <div className="loading-content">
        <div className="loading-header app-header-blue">
          <div className="d-flex align-items-center">
            <Spinner animation="border" size="sm" className="me-2" />
            <h4 className="mb-0">Loading Map Layers</h4>
          </div>
          <Button
            variant="outline-light"
            size="sm"
            className="close-overlay-btn"
            onClick={handleCloseOverlay}
            title="Close loader"
          >
            <X size={16} />
          </Button>
        </div>
        
        <div className="loading-layers">
          {activeLayerProgress.map((layer) => (
            <div key={layer.name} className={`loading-layer-item ${layer.hasError ? 'error' : layer.progress >= 100 ? 'success' : ''}`}>
              <div className="layer-info">
                <span className="layer-name">
                  {layer.hasError ? (
                    <>
                      <span className="error-icon">❌</span>
                      Failed to load {layer.displayName}
                    </>
                  ) : layer.progress >= 100 ? (
                    <>
                      <span className="success-icon">✅</span>
                      {layer.displayName} loaded
                    </>
                  ) : (
                    `Loading ${layer.displayName}`
                  )}
                </span>
                <div className="layer-controls">
                  <span className="layer-percentage">{Math.round(layer.progress)}%</span>
                  {(layer.hasError || layer.progress >= 100) && (
                    <Button
                      variant="link"
                      size="sm"
                      className="dismiss-layer-btn"
                      onClick={() => handleDismissLayer(layer.name)}
                      title="Close this layer"
                    >
                      <X size={14} />
                    </Button>
                  )}
                </div>
              </div>
              
              <ProgressBar 
                now={layer.progress} 
                variant={layer.hasError ? "danger" : layer.progress >= 100 ? "success" : layer.progress > 80 ? "info" : "primary"}
                animated={!layer.hasError && layer.progress < 100}
                className="layer-progress"
              />
              
              {layer.hasError && layer.errorMessage && (
                <div className="error-message">
                  <small>{layer.errorMessage}</small>
                  <div className="error-actions mt-2">
                    {onRetryLayer && (
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => handleRetryLayer(layer.name)}
                        className="retry-btn"
                        disabled={retryingLayers.has(layer.name)}
                      >
                        {retryingLayers.has(layer.name) ? (
                          <>
                            <Spinner animation="border" size="sm" className="me-1" />
                            Retrying...
                          </>
                        ) : (
                          <>
                            <RefreshCw size={12} className="me-1" />
                            Retry Loading
                          </>
                        )}
                      </Button>
                    )}
                    <Button
                      variant="outline-secondary"
                      size="sm"
                      onClick={() => handleDismissLayer(layer.name)}
                      className="dismiss-btn ms-2"
                    >
                      Dismiss
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
        
        {activeLayerProgress.length > 1 && (
          <div className="overall-progress">
            <small className="text-muted">
              {activeLayerProgress.filter(l => !l.hasError && l.progress < 100).length} layer{activeLayerProgress.filter(l => !l.hasError && l.progress < 100).length !== 1 ? 's' : ''} loading
              {activeLayerProgress.filter(l => l.hasError).length > 0 && (
                <>, {activeLayerProgress.filter(l => l.hasError).length} failed</>
              )}
              {activeLayerProgress.filter(l => l.progress >= 100 && !l.hasError).length > 0 && (
                <>, {activeLayerProgress.filter(l => l.progress >= 100 && !l.hasError).length} completed</>
              )}
            </small>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoadingOverlay;
