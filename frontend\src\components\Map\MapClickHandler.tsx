import { useMapEvents } from 'react-leaflet';
import { WMSLayer } from '../../services/geoserverService';

interface MapClickHandlerProps {
  wmsLayers: WMSLayer[];
  onFeatureInfoClick: (event: any, queryableLayers: WMSLayer[]) => void;
  isDrawingMode: boolean;
  isCoordinatePinMode?: boolean;
  onCoordinateSelected?: (latlng: {lat: number, lng: number}) => void;
  setPinCoordinates?: (coords: {lat: number, lng: number} | null) => void;
  onPinPlaced?: (latlng: {lat: number, lng: number}) => void;
}

const MapClickHandler: React.FC<MapClickHandlerProps> = ({
  wmsLayers,
  onFeatureInfoClick,
  isDrawingMode,
  isCoordinatePinMode,
  onCoordinateSelected,
  setPinCoordinates,
  onPinPlaced
}) => {
  useMapEvents({
    click: (e) => {
      // Don't handle clicks during drawing mode
      if (isDrawingMode) {
        return;
      }

      // Handle coordinate pin mode
      if (isCoordinatePinMode) {
        const coords = { lat: e.latlng.lat, lng: e.latlng.lng };
        setPinCoordinates?.(coords);
        onCoordinateSelected?.(coords);
        onPinPlaced?.(coords);
        return;
      }

      // Feature info functionality disabled
      // const queryableLayers = wmsLayers.filter(layer => layer.queryable);
      // if (queryableLayers.length > 0) {
      //   onFeatureInfoClick(e, queryableLayers);
      // }
    }
  });

  return null; // This component doesn't render anything
};

export default MapClickHandler;
