// Map component for SANSA Flood Monitoring
import React, { useEffect, useState, useCallback, useRef } from 'react';
import {<PERSON><PERSON><PERSON><PERSON>, TileLayer, WMSTileLayer,useMap, Popup, Marker, Polygon, LayerGroup
} from 'react-leaflet';
import { AOITrueWMSTileLayer } from './AOITrueWMSTileLayer';
import L from 'leaflet';
import 'leaflet-draw';
import 'leaflet-draw/dist/leaflet.draw.css'; 
import { fetchAvailableWMSLayers, WMSLayer, fetchFeatureInfo, GetFeatureInfoParams } from '../../services/geoserverService';
import { discoverLayers} from '../../services/discoveryService';
import { fetchFloodRiskData, fetchLayerData } from '../../services/mapService';
import { API_CONFIG } from '../../config';
// Dynamic import for WKT converter to avoid bundling conflicts
// import { convertGeoJSONToWKT } from '../../utils/wktConverter';

import MapLegend from './MapLegend';
import LoadingOverlay from './LoadingOverlay';
import FeatureInfoPopup, { FeatureInfo } from './FeatureInfoPopup';
import TemporalSelectionModal from '../AOI/TemporalSelectionModal';
import TimeSeriesResultsModal from '../TimeSeriesAnalysis/TimeSeriesResultsModal';
import { TimeSeriesService, TimeSeriesQuery } from '../../services/timeSeriesService';
// import PinAreaSelectionModal from '../Pin/PinAreaSelectionModal'; // Removed: AOI generated directly in Sidebar
import BoundaryHighlightLayer from '../Boundary/BoundaryHighlightLayer';

import AOIMapController from './AOIMapController';
import BoundsCapture from './BoundsCapture';
import MapResizeHandler from './MapResizeHandler';
import MapClickHandler from './MapClickHandler';
import DrawingController from './DrawingController';
// import AOIClipMask from './AOIClipMask'; // Removed: masking disabled for raster layers
import AoiHoleBasemapOverlay from './AoiHoleBasemapOverlay';
import AoiBoundaryStroke from './AoiBoundaryStroke';
import PaneSetup from './PaneSetup';
import { applyAOIClipping, applyClippingToParams, detectAndCacheGeometryField } from '../../services/aoiClippingService';
import { generateAOIFromDrawing } from '../../utils/coordinateAOIGenerator';
import { useAOITrueClipping } from '../../hooks/useAOITrueClipping';
import { getLayerCapabilitiesService } from '../../services/layerCapabilitiesService';
import { useToast } from '../../contexts/ToastContext';
import {
  convertGeoJSONToAOIFormat,
  convertBoundsToLeafletFormat,
  getBasemapConfig,
  hasValidAOIGeometry,
  extractGeometryFromAOI
} from '../../utils/aoiGeometryConverter';
import './Styling/MapComponent.css';

// Patch for leaflet-draw bug - must be done after leaflet-draw is loaded
setTimeout(() => {
  if (typeof (L as any).GeometryUtil !== 'undefined' && (L as any).GeometryUtil.readableArea) {
    const originalReadableArea = (L as any).GeometryUtil.readableArea;
    (L as any).GeometryUtil.readableArea = function(area: number, isMetric?: boolean, precision?: number) {
      try {
        // Fix the missing 'type' parameter issue
        if (arguments.length === 1) {
          // If only area is provided, default to metric
          return originalReadableArea.call(this, area, true, 2);
        } else if (arguments.length === 2) {
          // If area and isMetric are provided, add default precision
          return originalReadableArea.call(this, area, isMetric, 2);
        } else {
          // All parameters provided
          return originalReadableArea.call(this, area, isMetric, precision);
        }
      } catch (error) {
        console.warn('Error in readableArea function, using fallback:', error);
        // Fallback to simple area display
        const metric = isMetric !== false;
        if (metric) {
          return area > 1000000 ? (area / 1000000).toFixed(2) + ' km²' : area.toFixed(2) + ' m²';
        } else {
          return area > 4046.86 ? (area / 4046.86).toFixed(2) + ' acres' : area.toFixed(2) + ' sq ft';
        }
      }
    };
  }
}, 100);

const DEBUG = process.env.NODE_ENV !== 'production' && (window as any).__MAP_DEBUG__ === true;

interface MapComponentProps {
  selectedLayerNames: string[];
  dateRange: {
    startDate: string;
    endDate: string;
  };
  selectedTime?: string; // Add selectedTime prop for temporal layers
  onDrawComplete: (layers: any) => void;
  // AOI functionality
  isDrawingMode: boolean;
  onDrawModeChange: (isDrawing: boolean) => void;
  onAOIComplete: (aoiData: any, dateRange: any) => void;
  onAOIPreview?: (aoiData: any) => void;
  sidebarCollapsed?: boolean; // Add sidebarCollapsed prop for map resize
  // Basemap functionality
  selectedBasemap?: string;
  onBasemapChange?: (basemapName: string) => void;
  // Legend user mode
  legendUserMode?: 'simple' | 'advanced';
  onLegendUserModeChange?: (mode: 'simple' | 'advanced') => void;
  // Coordinate functionality
  isCoordinatePinMode?: boolean;
  onCoordinateSelected?: (latlng: {lat: number, lng: number}) => void;

  // Boundary highlighting
  highlightedBoundaries?: GeoJSON.Feature[];
  // AOI clipping
  aoiData?: {
    type: 'administrative' | 'drawn' | 'pin' | 'interactive-boundaries' | 'pin-based';
    level?: 'province' | 'district' | 'municipality' | 'ward';
    name?: string;
    code?: string;
    // Pin-based AOI specific properties
    shape?: 'square' | 'circle';
    size?: number;
    bounds?: {
      north: number;
      south: number;
      east: number;
      west: number;
    };
    area?: number;
    coordinates?: { lat: number; lng: number } | any; // Union type for pin coordinates or drawn polygon coordinates
    // Add geometry for precise clipping
    geometry?: GeoJSON.Geometry;
    feature?: GeoJSON.Feature;
    timestamp?: string;
    selectionDetails?: {
      provinceName?: string;
      district?: string;
      municipality?: string;
    };
    // Additional properties for interactive boundaries
    features?: GeoJSON.Feature[];
    count?: number;
  };
  // Layer opacity control
  layerOpacities?: { [layerName: string]: number };
  onOpacityChange?: (layerName: string, opacity: number) => void;
}



const MapRecentre = ({ trigger, center, zoom }: { trigger: boolean, center: [number, number], zoom: number }) => {
  const map = useMap();
  useEffect(() => {
    if (trigger) map.setView(center, zoom);
  }, [trigger, center, zoom, map]);
  return null;
};

const MapComponent: React.FC<MapComponentProps> = ({
  selectedLayerNames,
  dateRange,
  selectedTime,
  onDrawComplete,
  isDrawingMode,
  onDrawModeChange,
  onAOIPreview,
  sidebarCollapsed,
  selectedBasemap = 'osm:osm',
  isCoordinatePinMode = false,
  onCoordinateSelected,
  highlightedBoundaries = [],
  aoiData,
  layerOpacities: externalLayerOpacities
}) => {
  // Toast notifications for user feedback
  const { showClippingFallback } = useToast();

  // AOI-True Clipping system integration
  const { aoiState, createAOI, deleteAOI } = useAOITrueClipping();

  // Define hasValidAOI at component level for use throughout the component
  const hasValidAOI = aoiData && (aoiData.geometry || aoiData.bounds);

  const [center] = useState<[number, number]>([-29.0, 24.0]);
  const [zoom] = useState<number>(6);
  const [wmsLayers, setWmsLayers] = useState<WMSLayer[]>([]);
  const [mapBounds, setMapBounds] = useState<any>(null);
  const [loadingLayers, setLoadingLayers] = useState<{ [key: string]: boolean }>({});
  const [layerProgress, setLayerProgress] = useState<{ [key: string]: number }>({});
  const [tileLoadingState, setTileLoadingState] = useState<{ [key: string]: { loaded: number; total: number } }>({});
  const [floodRiskAreas, setFloodRiskAreas] = useState<any[]>([]);
  const [dwsVillages, setDwsVillages] = useState<any[]>([]);
  const [activePopup, setActivePopup] = useState<string | null>(null);
  const [errorLayers, setErrorLayers] = useState<{ [key: string]: string }>({});
  const [pinCoordinates, setPinCoordinates] = useState<{ lat: number, lng: number } | null>(null);
  const [layerCapabilities, setLayerCapabilities] = useState<{ [key: string]: { supportsCQL: boolean } | null }>({});

  // Track which layers have had notifications shown to prevent spam
  const notificationShownRef = useRef<Set<string>>(new Set());

  // Track whether any layers are using server-side clipping to avoid visual masking conflicts
  const [serverSideClippedLayers, setServerSideClippedLayers] = useState<Set<string>>(new Set());

  // Track when server-side clipped layers have finished loading
  const [serverSideLayersLoaded, setServerSideLayersLoaded] = useState<Set<string>>(new Set());

  // Compute whether any currently selected layers are using server-side clipping
  const hasServerSideClippedLayers = selectedLayerNames.some(layerName =>
    serverSideClippedLayers.has(layerName)
  );

  // Check if all server-side clipped layers have finished loading
  const allServerSideLayersLoaded = Array.from(serverSideClippedLayers).every(layerName =>
    serverSideLayersLoaded.has(layerName)
  );

  // Visual masking should apply when:
  // 1. There are no server-side layers (vector layers only), OR
  // 2. All server-side layers have finished loading (including BBOX fallback)
  // This ensures the hole-punching overlay always applies for proper polygon clipping
  const shouldApplyVisualMasking = !hasServerSideClippedLayers || allServerSideLayersLoaded;

  // Track server-side clipping layers and clean up when layers are removed
  useEffect(() => {
    // Determine which layers should use server-side clipping
    const newServerSideClippedLayers = new Set<string>();

    if (hasValidAOI) {
      selectedLayerNames.forEach(layerName => {
        // Check if this layer is a known raster layer
        const KNOWN_RASTER_LAYERS = [
          'geonode:2024-12-30_sa_mosaic_final2',
          'geonode:africa_mosaic_optmised',
          'geonode:flood_risk_layer_1m',
          'geonode:flood_risk_layer_3m',
          'geonode:flood_risk_layer_5m',
          'geonode:global_seviri_infrared_mosaic',
          'geonode:optimised_mosaic_t35jpf_20250614t074609_tci_10m',
          'geonode:ortho',
          'geonode:rgb_mosaic',
          'geonode:soil_moisture'
        ];

        if (KNOWN_RASTER_LAYERS.includes(layerName)) {
          newServerSideClippedLayers.add(layerName);
        }
      });
    }

    setServerSideClippedLayers(newServerSideClippedLayers);

    // Clean up loaded state for layers that are no longer selected or no longer server-side
    setServerSideLayersLoaded(prev => {
      const newLoaded = new Set<string>();
      for (const layerName of prev) {
        if (selectedLayerNames.includes(layerName) && newServerSideClippedLayers.has(layerName)) {
          newLoaded.add(layerName);
        }
      }
      return newLoaded;
    });
  }, [selectedLayerNames, hasValidAOI]);

  // Fetch capabilities for selected layers when they change
  useEffect(() => {
    const layerCapabilitiesService = getLayerCapabilitiesService();

    selectedLayerNames.forEach(layerName => {
      // Skip if already cached or being fetched
      if (layerCapabilities[layerName] !== undefined) {
        return;
      }

      // Fetch capabilities
      layerCapabilitiesService.getLayerCapabilities(layerName)
        .then(capabilities => {
          if (capabilities) {
            console.log(`🔍 Layer capabilities for "${layerName}":`, {
              type: capabilities.type,
              supportsCQL: capabilities.supports.cql,
              geometryField: capabilities.geometryField
            });
            // Cache the result
            setLayerCapabilities(prev => ({
              ...prev,
              [layerName]: { supportsCQL: capabilities.supports.cql }
            }));
          }
        })
        .catch(capError => {
          console.warn(`Failed to fetch capabilities for "${layerName}":`, capError);
          setLayerCapabilities(prev => ({
            ...prev,
            [layerName]: null
          }));
        });
    });
  }, [selectedLayerNames.slice().sort().join(',')]); 

  // Clear notification tracking when layers or AOI changes
  useEffect(() => {
    notificationShownRef.current.clear();
  }, [selectedLayerNames.join(','), aoiData?.feature?.id, aoiData?.bounds]);

  // Reset pin coordinates when exiting coordinate pin mode
  useEffect(() => {
    if (!isCoordinatePinMode) {
      setPinCoordinates(null);
      // setShowPinAreaModal(false); // Removed: AOI generated directly in Sidebar
    }
  }, [isCoordinatePinMode]);

  const [internalLayerOpacities, setInternalLayerOpacities] = useState<{ [layerName: string]: number }>({});

  // Use external opacity state if provided, otherwise use internal state
  const layerOpacities = externalLayerOpacities || internalLayerOpacities;
  // Cache of geometry fields per layer
  const [geometryFields, setGeometryFields] = useState<Record<string, string>>({});

  // Consolidated geometry field pre-fetching with optimized performance
  useEffect(() => {
    let isCancelled = false;

    (async () => {
      const entries: Record<string, string> = { ...geometryFields }; // Start with existing cache

      // Determine which layers need geometry field detection
      let layersToProcess: string[] = [];

      if (hasValidAOI && wmsLayers.length > 0) {
        // When AOI is active, pre-fetch ALL available layers for future toggling
        layersToProcess = wmsLayers
          .filter(layer => !entries[layer.name])
          .map(layer => layer.name);
      } else {
        // When no AOI, only fetch for currently selected layers
        layersToProcess = selectedLayerNames.filter(layerName => !entries[layerName]);
      }

      if (layersToProcess.length === 0) {
        //All required geometry fields already cached, no need to fetch
        return;
      }
      // Throttle requests to prevent resource exhaustion - process layers in small batches
      // Process 3 layers at a time
      // 500ms delay between batches
      const batchSize = 2; 
      const delay = 500; 

      for (let i = 0; i < layersToProcess.length; i += batchSize) {
        if (isCancelled) break;

        const batch = layersToProcess.slice(i, i + batchSize);
        console.log(`Processing geometry field batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(layersToProcess.length/batchSize)}`);

        // Process batch in parallel
        await Promise.all(batch.map(async (layerName) => {
          try {
            console.log(`${hasValidAOI ? 'Pre-fetching' : 'Fetching'} geometry field for layer: ${layerName}`);
            const geometryField = await detectAndCacheGeometryField(layerName);
            entries[layerName] = geometryField;
            //prefetched geometry field
          } catch (e) {
            //failed to prefecth geometry field 
            entries[layerName] = 'geometry'; // Fallback
          }
        }));

        // Delay between batches to prevent overwhelming the server
        if (i + batchSize < layersToProcess.length && !isCancelled) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      if (!isCancelled) {
        setGeometryFields(entries);
      }
    })();

    return () => { isCancelled = true; };
  }, [
    selectedLayerNames,
    hasValidAOI,
    wmsLayers.length, // Use length instead of full array to avoid unnecessary re-runs
    aoiData?.feature?.id,
    aoiData?.bounds,
    aoiData?.geometry?.type
  ]);

  // Feature info popup state
  const [featureInfo, setFeatureInfo] = useState<FeatureInfo | null>(null);

  // AOI state management
  const [drawnPolygon, setDrawnPolygon] = useState<any>(null);
  const [showTemporalModal, setShowTemporalModal] = useState(false);
  const [showTimeSeriesResults, setShowTimeSeriesResults] = useState(false);
  const [timeSeriesResult, setTimeSeriesResult] = useState<any>(null);
  const [timeSeriesLoading, setTimeSeriesLoading] = useState(false);
  const [timeSeriesError, setTimeSeriesError] = useState<string | null>(null);

  // Tile error tracking to prevent infinite retries
  const [, setFailedTiles] = useState<Set<string>>(new Set());
  const [, setLayerErrorCounts] = useState<Record<string, number>>({});
  const [disabledLayers, setDisabledLayers] = useState<Set<string>>(new Set());

  // Legend functionality moved to sidebar - no layer tracking needed for map legend
  const [popupPosition, setPopupPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  // Get default bounds for South Africa when no AOI is selected
  const getDefaultBounds = useCallback((): [[number, number], [number, number]] => {
    // South Africa bounding box
    return [
      [-34.8, 16.3], // Southwest corner [lat, lng]
      [-22.1, 32.9]  // Northeast corner [lat, lng]
    ];
  }, []);

  const getLayerBounds = useCallback((bbox: any) => {
    if (!bbox) {
      // Return default South Africa bounds when layer has no bbox
      return getDefaultBounds();
    }

    const { miny, minx, maxy, maxx } = bbox;

    // Convert to numbers and validate
    const minY = Number(miny);
    const minX = Number(minx);
    const maxY = Number(maxy);
    const maxX = Number(maxx);

    // Check for invalid coordinates
    if (isNaN(minY) || isNaN(minX) || isNaN(maxY) || isNaN(maxX)) {
      console.warn('Invalid bbox coordinates detected, using default bounds:', { miny, minx, maxy, maxx });
      return getDefaultBounds();
    }

    // Validate coordinate ranges
    if (minY < -90 || minY > 90 || maxY < -90 || maxY > 90 ||
        minX < -180 || minX > 180 || maxX < -180 || maxX > 180) {
      console.warn('Bbox coordinates out of valid range, using default bounds:', { minY, minX, maxY, maxX });
      return getDefaultBounds();
    }

    // Ensure min < max
    if (minY >= maxY || minX >= maxX) {
      console.warn('Invalid bbox: min >= max, using default bounds:', { minY, minX, maxY, maxX });
      return getDefaultBounds();
    }

    return [[minY, minX], [maxY, maxX]] as [[number, number], [number, number]];
  }, [getDefaultBounds]);



  const isLayerVisible = useCallback((layer: WMSLayer) => {
    if (!layer || !layer.name) {
      return false;
    }

    const layerBaseName = layer.name.includes(':') ? layer.name.split(':')[1] : layer.name;
    const isVisible = selectedLayerNames.includes(layer.name) || selectedLayerNames.includes(layerBaseName);

    return isVisible;
  }, [selectedLayerNames]);

  // Sort visible layers by render order for proper cartographic stacking
  const getSortedVisibleLayers = useCallback((layers: WMSLayer[]) => {
    const visibleLayers = layers.filter(isLayerVisible);

    // Convert WMSLayer to LayerDiscovery format for sorting
    const layersWithRenderOrder = visibleLayers.map(layer => ({
      ...layer,
      renderOrder: (layer as any).renderOrder || { priority: 0 }
    }));

    // Sort by render order priority (lower priority renders first/bottom)
    const sortedLayers = layersWithRenderOrder.sort((a, b) => {
      const priorityA = a.renderOrder?.priority || 0;
      const priorityB = b.renderOrder?.priority || 0;
      return priorityA - priorityB;
    });

    return sortedLayers;
  }, [isLayerVisible]);





  // Cleanup opacity state when layers are removed (only for internal state)
  useEffect(() => {
    const visibleLayerNames = wmsLayers.filter(isLayerVisible).map(layer => layer.name);

    if (!externalLayerOpacities) {
      setInternalLayerOpacities(prev => {
        const cleaned = { ...prev };
        Object.keys(cleaned).forEach(layerName => {
          if (!visibleLayerNames.includes(layerName)) {
            delete cleaned[layerName];
          }
        });
        return cleaned;
      });
    }

    // Clear error tracking for layers that are no longer visible
    setLayerErrorCounts(prev => {
      const newCounts = { ...prev };
      Object.keys(newCounts).forEach(layerName => {
        if (!visibleLayerNames.includes(layerName)) {
          delete newCounts[layerName];
        }
      });
      return newCounts;
    });

    // Clear failed tiles for layers that are no longer visible
    setFailedTiles(prev => {
      const newSet = new Set<string>();
      prev.forEach(tileKey => {
        const layerName = tileKey.split('-')[0];
        if (visibleLayerNames.includes(layerName)) {
          newSet.add(tileKey);
        }
      });
      return newSet;
    });
  }, [wmsLayers]); // Removed isLayerVisible from dependencies to prevent re-renders

  // AOI-True Clipping: Create AOI in backend when aoiData changes
  useEffect(() => {
    const createBackendAOI = async () => {
      if (!hasValidAOI || !aoiData.geometry) {
        // Clear any existing AOI if no valid AOI data
        if (aoiState.aoiId) {
          console.log('🗑️ Clearing AOI - no valid AOI data');
          await deleteAOI();
        }
        return;
      }

      // Check if we already have an AOI for this data
      const currentAOISignature = `${aoiData.type}-${aoiData.name || aoiData.level || 'custom'}-${aoiData.timestamp || 'unknown'}`;
      const existingAOISignature = aoiState.aoiId ? aoiState.aoiId.split('-').slice(0, -1).join('-') : null;

      if (existingAOISignature === currentAOISignature) {
        console.log('🎯 AOI already exists in backend:', aoiState.aoiId);
        return;
      }

      try {
        console.log('🎯 Creating AOI in backend for clipping:', {
          type: aoiData.type,
          name: aoiData.name || aoiData.level,
          hasGeometry: !!aoiData.geometry
        });

        // Create AOI in backend
        await createAOI({
          mode: aoiData.type as 'administrative' | 'drawn' | 'pin',
          geometry: aoiData.geometry,
          dateRange: (aoiData as any).dateRange,
          metadata: {
            name: aoiData.name || aoiData.level || 'Custom AOI',
            level: aoiData.level,
            code: aoiData.code
          }
        });

        console.log('✅ AOI created in backend successfully');
      } catch (error) {
        console.error('❌ Failed to create AOI in backend:', error);
      }
    };

    createBackendAOI();
  }, [aoiData, hasValidAOI, createAOI, deleteAOI, aoiState.aoiId]);

  useEffect(() => {
    const loadAllLayers = async () => {
      try {
        // Load layers from discovery service (includes both local and remote with render order)
        const discoveryResult = await discoverLayers();
        const allLayers = discoveryResult.layers || [];

        allLayers.forEach(layer => {
          if (layer.renderOrder) {
            console.log(`  ${layer.name}: ${layer.renderOrder.category} (priority: ${layer.renderOrder.priority})`);
          }
        });

        // Convert discovery layers to WMSLayer format for compatibility
        const wmsCompatibleLayers = allLayers.map(layer => {
          // Handle different bbox formats
          let bbox;
          const layerBbox = (layer as any).bbox;

          if (layerBbox && Array.isArray(layerBbox) && layerBbox.length === 4) {
            // Array format: [minx, miny, maxx, maxy]
            bbox = {
              minx: layerBbox[0],
              miny: layerBbox[1],
              maxx: layerBbox[2],
              maxy: layerBbox[3]
            };
          } else if (layerBbox && typeof layerBbox === 'object') {
            // Object format: {minx, miny, maxx, maxy}
            bbox = {
              minx: layerBbox.minx || layerBbox[0] || -180,
              miny: layerBbox.miny || layerBbox[1] || -90,
              maxx: layerBbox.maxx || layerBbox[2] || 180,
              maxy: layerBbox.maxy || layerBbox[3] || 90
            };
          } else {
            // Default global bounds
            bbox = {
              minx: -180,
              miny: -90,
              maxx: 180,
              maxy: 90
            };
          }

          // Validate bbox values
          if (isNaN(bbox.minx) || isNaN(bbox.miny) || isNaN(bbox.maxx) || isNaN(bbox.maxy)) {
            console.warn(`Invalid bbox for layer ${layer.name}, using global bounds`);
            bbox = { minx: -180, miny: -90, maxx: 180, maxy: 90 };
          }

          return {
            name: layer.name,
            title: layer.title || layer.name,
            type: 'raster',
            queryable: (layer as any).queryable || false,
            bbox: bbox,
            // Add remote layer properties
            isRemote: (layer as any).isRemote || false,
            serviceType: (layer as any).serviceType,
            remoteUrl: (layer as any).remoteUrl,
            url: (layer as any).url,
            // Preserve render order information for proper stacking
            renderOrder: layer.renderOrder,
            geometry: layer.geometry,
            styles: layer.styles,
            defaultStyle: layer.defaultStyle
          };
        });


        setWmsLayers(wmsCompatibleLayers as WMSLayer[]);
      } catch (error) {
        console.error('MapComponent: Failed to load layers from discovery:', error);

        // Fallback to local WMS layers only
        try {
          const localLayers = await fetchAvailableWMSLayers();
          setWmsLayers(localLayers);
        } catch (fallbackError) {
          console.error('MapComponent: Fallback also failed:', fallbackError);
          setWmsLayers([]);
        }
      }
    };
    loadAllLayers();
  }, []);
  //Use this for the default loading layers
  useEffect(() => {
    if (selectedLayerNames.includes('floodRisk') && mapBounds) {
      fetchFloodRiskData(mapBounds, dateRange)
        .then(setFloodRiskAreas)
        .catch(console.error);
    } else {
      setFloodRiskAreas([]);
    }
  }, [selectedLayerNames, dateRange, mapBounds]);

  useEffect(() => {
    if (selectedLayerNames.includes('dwsVillage') && mapBounds) {
      fetchLayerData('dwsVillage', mapBounds)
        .then(setDwsVillages)
        .catch(console.error);
    } else {
      setDwsVillages([]);
    }
  }, [selectedLayerNames, mapBounds]);

  // Track the last toggled layer for expanded legend
  useEffect(() => {

    // Legend functionality moved to sidebar - no layer tracking needed
  }, [selectedLayerNames, wmsLayers]);

  const handleCreated = useCallback((e: any) => {
    if (e.layerType === 'polygon') {
      const geoJSON = e.layer.toGeoJSON();

      // Generate AOI data from drawn polygon
      try {
        const drawingAOIData = generateAOIFromDrawing(geoJSON);

        // Convert to format compatible with existing AOI system
        const aoiData = {
          type: 'drawn' as const,
          bounds: drawingAOIData.bounds,
          area: drawingAOIData.area,
          geometry: drawingAOIData.geometry,
          feature: drawingAOIData.feature,
          name: drawingAOIData.name,
          timestamp: new Date().toISOString()
        };

        // Exit drawing mode
        onDrawModeChange(false);

        // Trigger AOI preview callback to integrate with existing workflow
        if (onAOIPreview) {
          onAOIPreview(aoiData);
        }

        // Also call the legacy onDrawComplete for backward compatibility
        onDrawComplete(geoJSON);

      } catch (error) {
        console.error('Failed to generate drawing-based AOI:', error);
        // Fallback to legacy behavior
        onDrawComplete(geoJSON);
        onDrawModeChange(false);
      }
    }
  }, [onDrawComplete, onDrawModeChange, onAOIPreview]);

  // Handle feature info click
  const handleFeatureInfoClick = useCallback(async (event: any, queryableLayers: WMSLayer[]) => {
    const { latlng, containerPoint } = event;

    // Set popup position based on click coordinates
    setPopupPosition({ x: containerPoint.x + 10, y: containerPoint.y - 10 });

    try {
      // For now, query the first queryable layer
      // TODO: In the future, we could query multiple layers and combine results
      const layer = queryableLayers[0];

      // Get map dimensions
      const mapSize = event.target.getSize();
      const bounds = event.target.getBounds();

      const params: GetFeatureInfoParams = {
        layers: layer.name,
        queryLayers: layer.name,
        x: Math.round(containerPoint.x),
        y: Math.round(containerPoint.y),
        width: mapSize.x,
        height: mapSize.y,
        bbox: `${bounds.getWest()},${bounds.getSouth()},${bounds.getEast()},${bounds.getNorth()}`,
        srs: 'EPSG:4326',
        infoFormat: 'application/json',
        featureCount: 10
      };

      const response = await fetchFeatureInfo(params);

      // Parse GeoServer response (format may vary)
      let features = [];
      if (response && response.features) {
        features = response.features;
      } else if (response && Array.isArray(response)) {
        features = response;
      } else if (response && typeof response === 'object') {
        // Handle other response formats
        features = [{ properties: response }];
      }

      if (features.length > 0) {
        setFeatureInfo({
          layerName: layer.title || layer.name,
          features: features,
          coordinates: {
            lat: latlng.lat,
            lng: latlng.lng
          }
        });
      }
    } catch (error) {
      console.error('Error fetching feature info:', error);
      // Could show an error message to user here
    }
  }, []);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'red';
      case 'moderate': return 'yellow';
      case 'low': return 'green';
      default: return 'blue';
    }
  };

  const handleMarkerClick = useCallback((layerName: string) => {
    setActivePopup(prev => prev === layerName ? null : layerName);
  }, []);

  const handleTileLoadStart = useCallback((layerName: string) => {
    setTileLoadingState(prevState => {
      const current = prevState[layerName] || { loaded: 0, total: 0 };
      return {
        ...prevState,
        [layerName]: { ...current, total: current.total + 1 }
      };
    });
  }, []);

  const handleTileLoad = useCallback((layerName: string) => {
    setTileLoadingState(prevState => {
      const current = prevState[layerName] || { loaded: 0, total: 0 };
      const newState = {
        ...prevState,
        [layerName]: { ...current, loaded: current.loaded + 1 }
      };

      if (current.total > 0) {
        const newLoaded = current.loaded + 1;
        const progress = Math.round((newLoaded / current.total) * 100);
        setLayerProgress(prevProgress => ({
          ...prevProgress,
          [layerName]: Math.min(progress, 100)
        }));
      }
      console.log(tileLoadingState[layerName]);
      return newState;
    });
  }, []);

  const handleLayerLoading = useCallback((layerName: string) => {
    setLoadingLayers(prev => ({ ...prev, [layerName]: true }));
    setLayerProgress(prev => ({ ...prev, [layerName]: 0 }));
    // Reset tile loading state when starting to load
    setTileLoadingState(prev => ({ ...prev, [layerName]: { loaded: 0, total: 0 } }));
  }, []);

  const handleLayerLoad = useCallback((layerName: string) => {
    setLoadingLayers(prev => ({ ...prev, [layerName]: false }));
    setLayerProgress(prev => ({ ...prev, [layerName]: 100 }));
    setErrorLayers(prev => {
      const updated = { ...prev };
      delete updated[layerName];
      return updated;
    });

    // Clear the completed loading state after a short delay to prevent lingering
    setTimeout(() => {
      setLayerProgress(prev => {
        const updated = { ...prev };
        delete updated[layerName];
        return updated;
      });
      setTileLoadingState(prev => {
        const updated = { ...prev };
        delete updated[layerName];
        return updated;
      });
    }, 2000);
  }, []);

  const handleLayerError = useCallback((layerName: string, error: any) => {
    // Increment error count for this layer
    setLayerErrorCounts(prev => {
      const newCount = (prev[layerName] || 0) + 1;
      const updatedCounts = { ...prev, [layerName]: newCount };

      // If this layer has had too many errors, disable it and stop retries
      if (newCount >= 10) {
        console.error(`Layer "${layerName}" has failed ${newCount} times - disabling to prevent infinite loops`);
        setDisabledLayers(prevDisabled => new Set(prevDisabled).add(layerName));
        setErrorLayers(prev => ({ ...prev, [layerName]: 'Layer disabled due to repeated failures' }));
        return updatedCounts;
      }

      // If this layer has had too many errors, stop reporting to prevent spam
      if (newCount > 5) {
        console.warn(`⚠️ Layer "${layerName}" has failed ${newCount} times - throttling error reporting`);
        return updatedCounts;
      }

      console.error(`❌ Layer error for "${layerName}" (attempt ${newCount}):`, error);
      return updatedCounts;
    });

    setLoadingLayers(prev => ({ ...prev, [layerName]: false }));
    setErrorLayers(prev => ({ ...prev, [layerName]: error.message || 'Failed to load layer' }));
  }, []);

  // Debug summary function


  // Get the time parameter for temporal layers
  const getTimeParameter = useCallback(() => {
    // Use selectedTime if provided, otherwise use date range
    if (selectedTime) {
      return selectedTime;
    }

    if (!dateRange.startDate || !dateRange.endDate) {
      return undefined;
    }

    // Convert to ISO 8601 format for WMS TIME parameter
    const startISO = new Date(dateRange.startDate).toISOString().split('T')[0];
    const endISO = new Date(dateRange.endDate).toISOString().split('T')[0];

    return `${startISO}/${endISO}`;
  }, [selectedTime, dateRange]);

  // Effect to refresh layers when date range changes (Phase 3 implementation)
  useEffect(() => {
    // Calculate time parameter directly to avoid dependency issues
    let timeParam: string | undefined;
    if (selectedTime) {
      timeParam = selectedTime;
    } else if (dateRange.startDate && dateRange.endDate) {
      const startISO = new Date(dateRange.startDate).toISOString().split('T')[0];
      const endISO = new Date(dateRange.endDate).toISOString().split('T')[0];
      timeParam = `${startISO}/${endISO}`;
    }

    if (timeParam && selectedLayerNames.length > 0) {
      console.log(`Date range changed, refreshing temporal layers with TIME: ${timeParam}`);

      // Force layer refresh by clearing and reloading layer progress
      setLayerProgress(prev => {
        const newProgress = { ...prev };
        selectedLayerNames.forEach(layerName => {
          newProgress[layerName] = 0;
        });
        return newProgress;
      });
    }
  }, [dateRange, selectedTime, selectedLayerNames]);


  // Function to test WMS URL accessibility (call from console)
  const testWmsUrl = useCallback(async (layerName: string) => {
    const baseUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy`;
    const testParams = new URLSearchParams({
      SERVICE: 'WMS',
      VERSION: '1.1.1',
      REQUEST: 'GetMap',
      LAYERS: layerName,
      STYLES: '',
      FORMAT: 'image/png',
      TRANSPARENT: 'true',
      SRS: 'EPSG:4326',
      BBOX: '-180,-90,180,90',
      WIDTH: '256',
      HEIGHT: '256'
    });
    const testUrl = `${baseUrl}?${testParams.toString()}`;


    try {
      const response = await fetch(testUrl);
      if (!response.ok) {
        const errorText = await response.text();
        console.error(` WMS Error Response:`, errorText);
      }
    } catch (error) {
      console.error(` WMS URL test failed:`, error);
    }
  }, []);

  // Make debug functions available globally for console debugging in development
  useEffect(() => {
    if (DEBUG) {
      (window as any).testWmsUrl = testWmsUrl;
      // Initialize AOI debugger
      console.log('AOI Debugging tools available:');
      console.log('- window.aoiDebugger.runFullTest() - Run comprehensive tests');
      console.log('- window.aoiDebugger.testCurrentAOI() - Test current AOI state');
      console.log('- window.testWmsUrl(layerName) - Test WMS layer accessibility');
      console.log('- window.debugLayerMatching() - Debug layer matching');
      console.log('- window.testFullExtentMode() - Test layer rendering without AOI');

      // Add test function for full extent mode
      (window as any).testFullExtentMode = () => {
        console.log('Testing Full Extent Mode:');
        console.log('Current AOI Data:', aoiData);
        console.log('Selected Layers:', selectedLayerNames);
        console.log('Expected behavior: Layers should render at full extent without clipping');

        if (aoiData) {
          console.log('⚠️ AOI is currently active - to test full extent mode, clear AOI selection');
        } else {
          console.log('No AOI active - full extent mode should be working');
          console.log('Try toggling layers to see full extent rendering');
        }
      };
    }

    return () => {
      if (DEBUG) {
        delete (window as any).testWmsUrl;
        delete (window as any).debugLayerMatching;
        delete (window as any).testFullExtentMode;
      }
    };
  }, [testWmsUrl]);

  const handleCloseLoader = useCallback(() => {
    setLoadingLayers({});
    setErrorLayers({});
    setLayerProgress({});
  }, []);

  // AOI workflow handlers
  const handleTemporalConfirm = useCallback(async (selection: {
    dateRange: { startDate: string; endDate: string };
    analysisType: string;
    expectedOutputs: string[];
  }) => {
    setShowTemporalModal(false);

    // If it's a static mosaic, use the existing AOI preview workflow
    if (selection.analysisType === 'static_mosaic' && onAOIPreview && drawnPolygon) {
      const coordinates = drawnPolygon.geometry.coordinates[0];
      const lats = coordinates.map((coord: number[]) => coord[1]);
      const lngs = coordinates.map((coord: number[]) => coord[0]);

      const bounds = {
        north: Math.max(...lats),
        south: Math.min(...lats),
        east: Math.max(...lngs),
        west: Math.min(...lngs)
      };

      // Calculate area (simple bounding box area)
      const area = Math.abs((bounds.east - bounds.west) * (bounds.north - bounds.south)) * 111 * 111;

      const aoiData = {
        type: 'drawn',
        coordinates: drawnPolygon,
        bounds,
        area,
        dateRange: selection.dateRange
      };

      onAOIPreview(aoiData);
      return;
    }

    // For time-series analysis, perform the analysis
    if (drawnPolygon && selectedLayerNames.length > 0) {
      try {
        setTimeSeriesLoading(true);
        setTimeSeriesError(null);
        setShowTimeSeriesResults(true);

        // Build query for time-series analysis
        const query: TimeSeriesQuery = {
          geometry: drawnPolygon.geometry,
          startDate: selection.dateRange.startDate.replace(/\//g, '-'),
          endDate: selection.dateRange.endDate.replace(/\//g, '-'),
          analysisType: selection.analysisType as any,
          layers: selectedLayerNames
        };

        // Validate query with enhanced temporal layer validation
        const validation = await TimeSeriesService.validateQuery(query);
        if (!validation.valid) {
          throw new Error(validation.errors.join(', '));
        }

        // Show warnings if any
        if (validation.warnings.length > 0) {
          console.warn('⚠️ Analysis warnings:', validation.warnings);
          // You could show these warnings to the user in a toast or alert
        }

        // Perform analysis
        const result = await TimeSeriesService.performAnalysis(query);
        setTimeSeriesResult(result);

      } catch (error) {
        console.error('Time-series analysis failed:', error);
        setTimeSeriesError(error instanceof Error ? error.message : 'Analysis failed');
      } finally {
        setTimeSeriesLoading(false);
      }
    }
  }, [drawnPolygon, onAOIPreview, selectedLayerNames]);

  // Pin area selection handlers - simplified since we generate AOI directly in Sidebar
  const handlePinPlaced = useCallback((latlng: { lat: number, lng: number }) => {
    console.log('Pin placed at:', latlng);
    // Pin placement is now handled directly by the coordinate selection workflow
    // No modal needed - AOI is generated automatically in Sidebar when coordinates are set
  }, []);

  return (
    <div className="map-wrapper">
      <MapContainer
        center={center}
        zoom={zoom}
        scrollWheelZoom
        className={`map ${isDrawingMode ? 'drawing-mode' : ''} ${isCoordinatePinMode ? 'pin-mode' : ''}`}
        worldCopyJump={false}
        maxBounds={[[-90, -180], [90, 180]]}
        maxBoundsViscosity={1.0}
        minZoom={2}
        maxZoom={19}
        touchZoom={true}
        doubleClickZoom={true}
        dragging={true}
        zoomControl={true}
        tapTolerance={15}
        bounceAtZoomLimits={false}
      >
        <MapRecentre trigger={selectedLayerNames.length === 0 && !hasValidAOI} center={center} zoom={zoom} />

        {/* Setup custom panes for AOI sandwich layering */}
        <PaneSetup />
        
        {/* Dynamic Basemap Layer */}
        {selectedBasemap === 'osm:osm' ? (
          <TileLayer
            attribution='&copy; OpenStreetMap contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            noWrap={true}
          />
        ) : (
          <WMSTileLayer
            key={`basemap-${selectedBasemap}`}
            url={`${API_CONFIG.BASE_URL}/ows/wms-proxy`}
            layers={selectedBasemap}
            format="image/jpeg"
            transparent={false}
            version="1.1.1"
            attribution="GeoServer Basemap"
            opacity={1}
            maxZoom={19}
            minZoom={2}
            tileSize={256}
            noWrap={true}
            updateWhenIdle={true}
            updateWhenZooming={false}
            keepBuffer={2}
          />
        )}
        <LayerGroup>
          {/* Show warnings for selected layers that can't be found */}
          {selectedLayerNames.length > 0 && (
            <div className="layer-warnings" style={{
              position: 'absolute',
              zIndex: 1000,
              top: 10,
              right: 10,
              maxWidth: '300px',
              pointerEvents: 'none'
            }}>
              {selectedLayerNames.filter(name => !wmsLayers.some(layer =>
                layer.name === name || (layer.name.includes(':') && layer.name.split(':')[1] === name)
              )).map(name => (
                <div key={`warning-${name}`} style={{
                  backgroundColor: 'rgba(255, 193, 7, 0.8)',
                  color: '#212529',
                  padding: '5px 10px',
                  borderRadius: '4px',
                  marginBottom: '5px',
                  fontSize: '12px',
                  fontWeight: 'bold'
                }}>
                  Layer not found: {name}
                </div>
              ))}
              {/* Show warnings for disabled layers */}
              {selectedLayerNames.filter(name => disabledLayers.has(name)).map(name => (
                <div key={`disabled-${name}`} style={{
                  backgroundColor: 'rgba(220, 53, 69, 0.8)',
                  color: '#fff',
                  padding: '5px 10px',
                  borderRadius: '4px',
                  marginBottom: '5px',
                  fontSize: '12px',
                  fontWeight: 'bold'
                }}>
                  Layer "{name}" disabled (too many errors)
                </div>
              ))}
            </div>
          )}
          {getSortedVisibleLayers(wmsLayers).filter(layer => {
            const disabled = disabledLayers.has(layer.name);
            return !disabled;
          }).map(layer => {
            const isVisible = isLayerVisible(layer);

            if (!isVisible) return null;

            const bounds = getLayerBounds(layer.bbox);
            console.log(`Layer "${layer.name}" bounds:`, bounds);

            // Skip layer if bounds are invalid
            if (bounds && bounds.some(coord => coord.some(val => isNaN(val)))) {
              console.error(`❌ Skipping layer "${layer.name}" due to invalid bounds:`, bounds);
              return null;
            }

            // Check if this is a remote layer
            const isRemoteLayer = (layer as any).isRemote;

            // Dynamic format detection based on layer properties
            const format = (layer as any).formats?.includes('image/png') ? 'image/jpeg' : 'image/png';
            const transparent = format === 'image/png';

            // Determine the appropriate URL based on layer type
            let baseLayerUrl: string;
            let layerName: string;

            // Always use the WMS proxy for both local and remote layers
            // The proxy will handle the conversion for remote layers
            baseLayerUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy`;
            layerName = layer.name;

            // Get temporal parameter for this layer
            const timeParam = getTimeParameter();

            // Enhanced temporal parameter debugging
            const isTemporalLayer = layer.temporal || (layer as any).isTemporal;
            if (isTemporalLayer) {
              console.log(`Temporal layer "${layer.name}":`, {
                hasTimeParam: !!timeParam,
                timeParam: timeParam,
                dateRange: dateRange,
                selectedTime: selectedTime,
                layerTemporal: layer.temporal
              });
            } else if (timeParam) {
              console.log(`⚠️ Non-temporal layer "${layer.name}" has time parameter: ${timeParam}`);
            }

            // Build final layer URL with parameters
            let layerUrl = baseLayerUrl;

            // Add temporal and AOI clipping parameters
            const urlParams = new URLSearchParams();

            // Add essential WMS parameters to prevent "No service" errors
            urlParams.set('SERVICE', 'WMS');
            urlParams.set('REQUEST', 'GetMap');
            urlParams.set('LAYERS', layerName);
            urlParams.set('FORMAT', format);
            urlParams.set('TRANSPARENT', transparent.toString());
            urlParams.set('VERSION', '1.1.1');

            if (timeParam && isTemporalLayer) {
              urlParams.set('TIME', timeParam);
              console.log(`Adding TIME parameter for temporal layer "${layer.name}": ${timeParam}`);
            }

            // Unified AOI clipping: Apply only if AOI data exists and is valid
            if (hasValidAOI) {
              console.log(`🔧 Applying unified AOI clipping to layer "${layer.name}":`, {
                hasGeometry: !!aoiData.geometry,
                hasFeature: !!aoiData.feature,
                hasBounds: !!aoiData.bounds,
                aoiType: aoiData?.type || 'unknown'
              });

              try {
                // Get layer capabilities from cache (fetched in useEffect)
                let supportsCQL: boolean | undefined;

                if (layerCapabilities[layer.name] !== undefined) {
                  supportsCQL = layerCapabilities[layer.name]?.supportsCQL;
                  console.log(`🔍 Using cached capabilities for "${layer.name}": supportsCQL=${supportsCQL}`);
                } else {
                  // Use undefined to trigger heuristic fallback while capabilities are being fetched
                  supportsCQL = undefined;
                }

                // Use unified clipping service with capability-driven decisions
                const clippingResult = applyAOIClipping({
                  geometry: aoiData.geometry,
                  bounds: aoiData.bounds,
                  layerName: layer.name,
                  geometryField: geometryFields[layer.name],
                  maxWKTLength: 6000,
                  enableFallback: true,
                  supportsCQL
                });

                // Store clipping result for layer rendering decision
                let useServerSideClipping = false;

                if (clippingResult.success) {
                  // Handle different clipping methods
                  if (clippingResult.method === 'SERVER') {
                    // For SERVER method, don't apply clipping parameters - let AOITrueWMSTileLayer handle it
                    useServerSideClipping = true;
                    console.log(`🖼️ Server-side clipping delegated for "${layer.name}"`);
                  } else {
                    // Apply clipping parameters to URL for CQL_FILTER and BBOX methods
                    applyClippingToParams(urlParams, clippingResult);
                    console.log(`✅ ${clippingResult.method} clipping applied to "${layer.name}":`, clippingResult.parameters);
                  }

                  // Show notification based on clipping method (defer to avoid render cycle issues)


                  if (clippingResult.geometryField) {
                    console.log(`Using geometry field: ${clippingResult.geometryField}`);
                  }
                  if (clippingResult.wktLength) {
                    console.log(`WKT length: ${clippingResult.wktLength} characters`);
                  }
                } else {
                  console.warn(`⚠️ Clipping failed for "${layer.name}": ${clippingResult.error}`);
                  if (clippingResult.fallbackReason) {
                    console.warn(`Reason: ${clippingResult.fallbackReason}`);
                  }
                  console.log(`Layer will render at full extent`);
                }

                // Store the server-side clipping decision for layer rendering
                (layer as any).useServerSideClipping = useServerSideClipping;

              } catch (error) {
                console.error(`❌ Unified clipping service error for "${layer.name}":`, error);
                console.log(`Layer will render at full extent`);
              }
            } else {
              // No AOI clipping - render full layer extent
              console.log(`Rendering full extent for layer "${layer.name}" (no AOI restrictions)`);
            }

            if (urlParams.toString()) {
              console.log(`Final urlParams before adding to layerUrl:`, urlParams.toString());
              layerUrl += `?${urlParams.toString()}`;
              console.log(`Final layerUrl after adding params:`, layerUrl);
            }



            // Smart bounds handling: AOI bounds vs full layer bounds
            let effectiveBounds = bounds;
            let boundsDescription = 'layer default bounds';

            if (hasValidAOI && aoiData?.bounds && bounds) {
              // Use AOI bounds for clipping when AOI is active
              effectiveBounds = [
                [aoiData.bounds.south, aoiData.bounds.west],
                [aoiData.bounds.north, aoiData.bounds.east]
              ] as [[number, number], [number, number]];
              boundsDescription = 'AOI clipped bounds';

              // Validate bounds
              const boundsValid = effectiveBounds.every(coord => coord.every(val => !isNaN(val) && isFinite(val)));
              console.log(`Layer "${layer.name}" clipped to AOI bounds:`, {
                effectiveBounds,
                originalBounds: bounds,
                aoiBounds: aoiData.bounds,
                boundsValid,
                boundsDescription
              });

              if (!boundsValid) {
                console.error(`❌ Invalid bounds detected for layer "${layer.name}" - this may cause "out of bounds" errors`);
              }
            } else {
              // Use full layer bounds when no AOI or for full-extent viewing
              boundsDescription = hasValidAOI ? 'layer bounds (AOI has no bounds)' : 'full layer bounds (no AOI)';
              console.log(`Layer "${layer.name}" using ${boundsDescription}:`, effectiveBounds);
            }

            // Build AOI signature to force tile refresh on AOI change
            const aoiSignature = hasValidAOI
              ? (aoiData.feature?.id as any) || (aoiData.bounds ? `${aoiData.bounds.west},${aoiData.bounds.south},${aoiData.bounds.east},${aoiData.bounds.north}` : 'aoi-no-bounds')
              : 'full-extent';

            // Log the complete WMS URL for debugging
            // if (hasValidAOI) {
            //   console.log(`Complete WMS URL for layer "${layer.name}":`, layerUrl);

            //   if (urlParams.has('CQL_FILTER')) {
            //     const cqlFilter = urlParams.get('CQL_FILTER');
            //     console.log(`CQL_FILTER being applied:`, cqlFilter?.substring(0, 200) + '...');
            //   } else if (urlParams.has('BBOX')) {
            //     console.log(`BBOX clipping being applied:`, urlParams.get('BBOX'));
            //   } else {
            //     console.log(`⚠️ No clipping parameters found in URL - this may be the issue!`);
            //   }
            // }

            // Check if we should use server-side clipping (from stored decision above)
            const useServerSideClipping = (layer as any).useServerSideClipping || false;

            // Use real AOI ID from backend (created by the useEffect above)
            const aoiId = aoiState.aoiId || undefined;

            // Conditionally render AOITrueWMSTileLayer for server-side clipping or WMSTileLayer for client-side
            if (useServerSideClipping) {
              return (
                <AOITrueWMSTileLayer
                  key={`${layer.name}-${aoiSignature}`} // Include AOI signature to force re-render when AOI changes
                  layerName={layerName}
                  aoiId={aoiId}
                  time={timeParam}
                  format={format}
                  srs="EPSG:4326"
                  opacity={layerOpacities[layer.name] ?? 1.0}
                  attribution={`${layer.title}${isRemoteLayer ? ' (Remote)' : ''} (Server-side AOI Clipping)`}
                  bounds={effectiveBounds}
                  maxZoom={19}
                  minZoom={2}
                  tileSize={256}
                  noWrap={true}
                  onLoading={() => {
                    console.log(`Layer "${layer.name}" loading with server-side clipping...`);
                    handleLayerLoading(layer.name);
                  }}
                  onLoad={() => {
                    handleLayerLoad(layer.name);
                    // Track that this server-side layer has loaded
                    setServerSideLayersLoaded(prev => new Set(prev).add(layer.name));
                  }}
                  onError={(e) => {
                    console.error(`🧩 Layer "${layer.name}" server-side clipping error:`, e);
                    handleLayerError(layer.name, e);
                  }}
                />
              );
            } else {
              // Use regular WMSTileLayer for client-side clipping or no AOI
              return (
                <WMSTileLayer
                  key={`${layer.name}-${aoiSignature}`} // Include AOI signature to force re-render when AOI changes
                  url={layerUrl}
                  layers={layerName}
                  format={format}
                  transparent={transparent}
                  version="1.1.1"
                  bounds={effectiveBounds}
                  attribution={`${layer.title}${isRemoteLayer ? ' (Remote)' : ''}${hasValidAOI ? ' (AOI Clipped)' : ' (Full Extent)'}`}
                  opacity={layerOpacities[layer.name] ?? 1.0}
                  maxZoom={19}
                  minZoom={2}
                  tileSize={256}
                  noWrap={true}
                  updateWhenIdle={true}
                  updateWhenZooming={false}
                  keepBuffer={2}
                  eventHandlers={{
                    loading: () => {
                      console.log(`Layer "${layer.name}" loading... (${isRemoteLayer ? 'Remote' : 'Local'}${hasValidAOI ? ' with AOI clipping' : ' full extent'})`);
                      handleLayerLoading(layer.name);
                    },
                    load: () => {
                      console.log(`Layer "${layer.name}" loaded successfully (${isRemoteLayer ? 'Remote' : 'Local'}${hasValidAOI ? ' with AOI clipping' : ' full extent'})`);
                      handleLayerLoad(layer.name);

                      // Track raster layers as server-side loaded even when using BBOX fallback
                      const isRasterLayer = serverSideClippedLayers.has(layer.name);
                      if (isRasterLayer) {
                        console.log(`📦 Raster layer "${layer.name}" loaded with BBOX fallback - tracking as server-side loaded`);
                        setServerSideLayersLoaded(prev => new Set(prev).add(layer.name));
                      }
                    },
                    error: async (e) => {
                      console.error(`🧩 Layer "${layer.name}" tile error (${isRemoteLayer ? 'Remote' : 'Local'}):`, e);

                      // Try to get more details about the error
                      if (e.sourceTarget && e.sourceTarget._url) {
                        const failedUrl = e.sourceTarget._url;
                        console.error(`❌ Failed URL: ${failedUrl}`);

                        // Try to fetch the URL directly to see the server response
                        try {
                          const response = await fetch(failedUrl);
                          if (!response.ok) {
                            const errorText = await response.text();
                            console.error(`Server response (${response.status}):`, errorText);

                            // Check if it's a CQL filter error
                            if (errorText.includes('CQL') || errorText.includes('INTERSECTS') || errorText.includes('geometry')) {
                              console.error(`CQL Filter Error detected - this suggests geometry field name or CQL syntax issue`);
                            }
                          }
                        } catch (fetchError) {
                          console.error(`❌ Could not fetch error details:`, fetchError);
                        }
                      }

                      handleLayerError(layer.name, e);
                    },
                    tileerror: (e) => {
                      console.error(`🧩 Layer "${layer.name}" tile error (${isRemoteLayer ? 'Remote' : 'Local'}):`, e);
                      handleLayerError(layer.name, e);
                    },
                    tileloadstart: () => {
                      handleTileLoadStart(layer.name);
                    },
                    tileload: () => {
                      handleTileLoad(layer.name);
                    }
                  }}
                />
              );
            }
          })}
        </LayerGroup>

        {/* AOI-Hole Basemap Overlay - Sandwich approach for visual clipping */}
        {/* Apply visual clipping only after server-side clipping is complete */}
        {hasValidAOI && hasValidAOIGeometry(aoiData) && shouldApplyVisualMasking && (() => {
          try {
            console.log('🎯 AOI visual clipping: Setting up sandwich overlay', {
              aoiData,
              hasValidAOI,
              hasServerSideClippedLayers,
              allServerSideLayersLoaded,
              shouldApplyVisualMasking,
              serverSideClippedLayers: Array.from(serverSideClippedLayers),
              serverSideLayersLoaded: Array.from(serverSideLayersLoaded),
              selectedLayerNames,
              message: shouldApplyVisualMasking ? 'APPLYING VISUAL MASKING' : 'WAITING FOR SERVER-SIDE LAYERS'
            });

            const geometry = extractGeometryFromAOI(aoiData);
            if (!geometry) {
              console.warn('⚠️ No geometry extracted from AOI data');
              return null;
            }

            const aoiFormatGeometry = convertGeoJSONToAOIFormat(geometry);
            const basemapConfig = getBasemapConfig(selectedBasemap);
            const aoiBounds = aoiData?.bounds ? convertBoundsToLeafletFormat(aoiData.bounds) : undefined;

            console.log('✅ AOI visual clipping: Components ready', {
              geometryType: geometry.type,
              basemapUrl: basemapConfig.tileUrl,
              bounds: aoiBounds
            });

            return (
              <>
                {/* AOI-hole basemap overlay that covers outside AOI with basemap copy */}
                <AoiHoleBasemapOverlay
                  tileUrl={basemapConfig.tileUrl}
                  subdomains={basemapConfig.subdomains}
                  attribution={basemapConfig.attribution}
                  aoi={aoiFormatGeometry}
                  bbox={aoiBounds}
                  pane="aoi-hole-basemap"
                />

                {/* AOI boundary stroke on top - NO FILL, outline only */}
                <AoiBoundaryStroke
                  aoi={aoiFormatGeometry}
                  color="#007bff"
                  weight={2}
                  opacity={0.9}
                  dashArray="5, 5"
                  hoverColor="#dc3545"
                  hoverFillOpacity={0}
                  pane="aoi-boundary-stroke"
                />
              </>
            );
          } catch (error) {
            console.error('❌ Error rendering AOI-hole overlay:', error);
            return null;
          }
        })()}

        {/* Pin-based AOI: Pin Marker and Polygon Overlay */}
        {aoiData?.type === 'pin-based' && aoiData.coordinates && (
          <>
            {/* Pin Marker at center */}
            <Marker
              position={[aoiData.coordinates.lat, aoiData.coordinates.lng]}
              icon={L.divIcon({
                html: '<div style="background: #007bff; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);">📌</div>',
                className: 'pin-marker',
                iconSize: [20, 20],
                iconAnchor: [10, 10]
              })}
            />

            {/* AOI Polygon - Enhanced visual representation with consistent styling */}
            {aoiData.geometry && (
              <Polygon
                positions={(() => {
                  if (aoiData.geometry.type === 'Polygon') {
                    return aoiData.geometry.coordinates[0].map((coord: number[]) => [coord[1], coord[0]] as [number, number]);
                  }
                  return [];
                })()}
                pathOptions={{
                  color: '#007bff',
                  fillColor: '#007bff',
                  fillOpacity: 0, // Increased from 0.2 to 0.25 for better visibility
                  weight: 2,
                  opacity: 0.9, // Added opacity for consistent outline visibility
                  dashArray: '5, 5'
                }}
              />
            )}
          </>
        )}

        {/* AOI Clipping Mask - Disabled to remove masking for raster layers
        <AOIClipMask
          geometry={aoiData?.geometry && (aoiData.geometry.type === 'Polygon' || aoiData.geometry.type === 'MultiPolygon')
            ? aoiData.geometry as GeoJSON.Polygon | GeoJSON.MultiPolygon
            : null}
          maskFill="#007bff"
          maskOpacity={0.7}
          outlineColor="#0056b3"
          enabled={isRasterActive && Boolean(aoiData?.geometry)}
        />
        */}

        {/* Boundary Highlighting Layer for Interactive Filtering */}
        <BoundaryHighlightLayer
          features={highlightedBoundaries}
          onFeatureClick={(feature) => {
            console.log('Boundary feature clicked:', feature.properties);
          }}
        />

        <BoundsCapture onBoundsChange={setMapBounds} />
        <AOIMapController
          aoiData={aoiData}
          selectedLayerNames={selectedLayerNames}
          enableAutoZoom={true}
        />
        <MapResizeHandler sidebarCollapsed={sidebarCollapsed} />
        <MapClickHandler
          wmsLayers={wmsLayers || []} // Provide empty array fallback to prevent initialization errors
          onFeatureInfoClick={handleFeatureInfoClick}
          isDrawingMode={isDrawingMode}
          isCoordinatePinMode={isCoordinatePinMode}
          onCoordinateSelected={onCoordinateSelected}
          setPinCoordinates={setPinCoordinates}
          onPinPlaced={handlePinPlaced}
        />
        <DrawingController
          isDrawingMode={isDrawingMode}
          onCreated={handleCreated}
        />
        {floodRiskAreas.map((area, i) => (
          <Polygon key={`flood-${i}`} positions={area.coordinates} pathOptions={{ color: getRiskColor(area.risk), fillOpacity: 0.4 }} />
        ))}
        {dwsVillages.map((village, i) => (
          <Marker
            key={`village-${i}`}
            position={[village.lat, village.lng]}
            eventHandlers={{ click: () => handleMarkerClick(`village-${i}`) }}
          >
            {activePopup === `village-${i}` && (
              <Popup>
                <h3>{village.name}</h3>
                <p>Population: {village.population}</p>
              </Popup>
            )}
          </Marker>
        ))}

        {/* Coordinate Pin Marker */}
        {isCoordinatePinMode && pinCoordinates && (
          <Marker
            key="coordinate-pin"
            position={[pinCoordinates.lat, pinCoordinates.lng]}
            icon={L.divIcon({
              className: 'coordinate-pin-marker',
              html: `<div style="background-color: #ff5722; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
              iconSize: [16, 16],
              iconAnchor: [8, 8]
            })}
          >
            <Popup>
              <div>
                <strong>Selected Coordinates</strong><br />
                Latitude: {pinCoordinates.lat.toFixed(6)}<br />
                Longitude: {pinCoordinates.lng.toFixed(6)}
              </div>
            </Popup>
          </Marker>
        )}

        {/* Map Legend with proper layer ordering */}
        <MapLegend
          visibleLayers={getSortedVisibleLayers(wmsLayers)}
          position="bottomleft"
        />
        

  </MapContainer>

      <LoadingOverlay
        loadingLayers={loadingLayers}
        wmsLayers={wmsLayers}
        errorLayers={errorLayers}
        layerProgress={layerProgress}
        onRetryLayer={() => { }} // retry implementation can be added as needed
        onCloseLoader={handleCloseLoader}
      />



      {/* Feature Info Popup */}
      {featureInfo && (
        <FeatureInfoPopup
          featureInfo={featureInfo}
          onClose={() => setFeatureInfo(null)}
          position={popupPosition}
        />
      )}

      {/* AOI Temporal Selection Modal */}
      <TemporalSelectionModal
        show={showTemporalModal}
        onHide={() => setShowTemporalModal(false)}
        onConfirm={handleTemporalConfirm}
        aoiCoordinates={drawnPolygon}
        preSelectedDateRange={dateRange}
      />

      {/* Pin Area Selection Modal - Removed: AOI is now generated directly in Sidebar */}

      {/* Time-Series Analysis Results Modal */}
      <TimeSeriesResultsModal
        show={showTimeSeriesResults}
        onHide={() => {
          setShowTimeSeriesResults(false);
          setTimeSeriesResult(null);
          setTimeSeriesError(null);
        }}
        result={timeSeriesResult}
        isLoading={timeSeriesLoading}
        error={timeSeriesError}
      />

    </div>
  );
};

export default MapComponent;
