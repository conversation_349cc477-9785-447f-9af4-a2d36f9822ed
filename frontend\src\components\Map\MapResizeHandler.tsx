import { useEffect } from 'react';
import { useMap } from 'react-leaflet';

interface MapResizeHandlerProps {
  sidebarCollapsed?: boolean;
}

const MapResizeHandler: React.FC<MapResizeHandlerProps> = ({ sidebarCollapsed }) => {
  const map = useMap();

  useEffect(() => {
    if (map) {
      // Small delay to allow CSS transitions to complete
      const timer = setTimeout(() => {
        map.invalidateSize();
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [map, sidebarCollapsed]);

  return null; // This component doesn't render anything
};

export default MapResizeHandler;
