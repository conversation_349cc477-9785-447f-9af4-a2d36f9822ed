/**
 * Pane Setup Component
 * 
 * Sets up custom Leaflet panes for proper z-index layering in the AOI sandwich approach:
 * - Base layer: ~200 (default)
 * - Data layers: ~400 (default overlayPane)
 * - AOI-hole basemap overlay: ~480
 * - AOI boundary stroke: ~600
 */

import { useEffect } from 'react';
import { useMap } from 'react-leaflet';

const PaneSetup: React.FC = () => {
  const map = useMap();

  useEffect(() => {
    // Create AOI-hole basemap overlay pane (above data layers, below boundary)
    if (!map.getPane('aoi-hole-basemap')) {
      const aoiHolePane = map.createPane('aoi-hole-basemap');
      aoiHolePane.style.zIndex = '480';
      aoiHolePane.style.pointerEvents = 'none';
    }

    // Create AOI boundary stroke pane (topmost) - enable pointer events for hover
    if (!map.getPane('aoi-boundary-stroke')) {
      const aoiBoundaryPane = map.createPane('aoi-boundary-stroke');
      aoiBoundaryPane.style.zIndex = '600';
      aoiBoundaryPane.style.pointerEvents = 'auto'; // Enable hover interactions
    }

    // Ensure data layers use the default overlayPane (z-index ~400)
    // This is already the default, but we can explicitly set it if needed
    const overlayPane = map.getPane('overlayPane');
    if (overlayPane) {
      overlayPane.style.zIndex = '400';
    }

  }, [map]);

  return null; // This component doesn't render anything
};

export default PaneSetup;
