.feature-info-popup {
  max-width: 800px;
  min-width: 500px;
  pointer-events: auto;
}

.feature-info-popup .card {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  max-height: 800px;
  overflow-y: auto;
}

.feature-info-popup .card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  font-size: 0.9rem;
}

.feature-info-popup .card-body {
  font-size: 0.85rem;
}

.feature-properties {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.property-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.25rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.property-row:last-child {
  border-bottom: none;
}

.property-key {
  flex: 0 0 40%;
  font-weight: 600;
  color: #495057;
  font-size: 0.8rem;
  text-transform: capitalize;
  word-break: break-word;
}

.property-value {
  flex: 1;
  text-align: right;
  color: #212529;
  font-size: 0.8rem;
  word-break: break-word;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .feature-info-popup {
    max-width: 600px;
    min-width: 400px;
  }
  
  .property-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .property-value {
    text-align: left;
  }
}

/* Animation for popup appearance */
.feature-info-popup .card {
  animation: popupFadeIn 0.2s ease-out;
}

@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Scrollbar styling for webkit browsers */
.feature-info-popup .card::-webkit-scrollbar {
  width: 6px;
}

.feature-info-popup .card::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.feature-info-popup .card::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.feature-info-popup .card::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
