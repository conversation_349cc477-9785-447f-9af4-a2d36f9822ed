.map-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.map {
  height: 100%;
  width: 100%;
  touch-action: manipulation; /* Improve touch performance */
}

/* Drawing mode cursor */
.map.drawing-mode {
  cursor: crosshair !important;
}

.map.drawing-mode * {
  cursor: crosshair !important;
}

/* Pin mode cursor */
.map.pin-mode {
  cursor: crosshair !important;
}

.map.pin-mode * {
  cursor: crosshair !important;
}

/* Mobile touch improvements */
@media (max-width: 767px) {
  .map.drawing-mode,
  .map.drawing-mode *,
  .map.pin-mode,
  .map.pin-mode * {
    cursor: default !important; /* Use default cursor on touch devices */
  }
}

/* Leaflet Draw styles */
.leaflet-draw-toolbar {
  margin: 10px !important;
  z-index: 1000 !important;
}

.leaflet-draw-toolbar a {
  background-color: #fff !important;
  border: 2px solid rgba(0, 0, 0, 0.2) !important;
  border-radius: 4px !important;
  min-width: 30px !important;
  min-height: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 1px 5px rgba(0,0,0,0.4) !important;
}

.leaflet-draw-toolbar a:hover {
  background-color: #f4f4f4 !important;
}

/* Ensure drawing controls are visible */
.leaflet-control-draw {
  z-index: 1000 !important;
}

.leaflet-draw-actions {
  background-color: #fff !important;
  border-radius: 4px !important;
  box-shadow: 0 1px 5px rgba(0,0,0,0.4) !important;
  z-index: 1001 !important;
}

.leaflet-draw-actions a {
  background-color: #fff !important;
  border: 1px solid #ccc !important;
  color: #333 !important;
  padding: 4px 8px !important;
}

.leaflet-draw-actions a:hover {
  background-color: #f0f0f0 !important;
}

.leaflet-control-zoom a {
  color: #333;
  min-width: 30px;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile-friendly controls */
@media (max-width: 767px) {
  .leaflet-draw-toolbar a,
  .leaflet-control-zoom a {
    min-width: 44px;
    min-height: 44px;
    font-size: 16px;
    border-radius: 8px;
  }

  .leaflet-draw-toolbar {
    margin: 10px;
  }

  .leaflet-control-zoom {
    margin: 10px;
  }

  .leaflet-control-container .leaflet-top.leaflet-left {
    top: 10px;
    left: 10px;
  }

  .leaflet-control-container .leaflet-top.leaflet-right {
    top: 10px;
    right: 10px;
  }

  .leaflet-control-container .leaflet-bottom.leaflet-left {
    bottom: 10px;
    left: 10px;
  }

  .leaflet-control-container .leaflet-bottom.leaflet-right {
    bottom: 10px;
    right: 10px;
  }
}

/* Layer status overlays */
.layer-loading-overlay,
.layer-error-overlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  padding: 8px 12px;
  border-radius: 8px;
  z-index: 1000;
  font-size: 14px;
  max-width: calc(100% - 40px);
  word-wrap: break-word;
}

.layer-loading-overlay {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  display: flex;
  align-items: center;
  backdrop-filter: blur(4px);
}

.layer-error-overlay {
  background-color: rgba(255, 0, 0, 0.8);
  color: white;
  backdrop-filter: blur(4px);
}

/* Mobile overlay adjustments */
@media (max-width: 767px) {
  .layer-loading-overlay,
  .layer-error-overlay {
    bottom: 10px;
    left: 10px;
    right: 10px;
    max-width: none;
    font-size: 13px;
    padding: 10px 12px;
    text-align: center;
  }
}



/* Responsive map sizing */
@media (max-width: 767px) {
  .map-wrapper,
  .map {
    height: 100%;
    min-height: 300px;
  }
}

@media (max-width: 479px) {
  .map-wrapper,
  .map {
    min-height: 250px;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 767px) and (orientation: landscape) {
  .map-wrapper,
  .map {
    min-height: 200px;
  }
}

/* Leaflet popup improvements for mobile */
@media (max-width: 767px) {
  .leaflet-popup-content-wrapper {
    border-radius: 8px;
    max-width: calc(100vw - 40px);
  }

  .leaflet-popup-content {
    margin: 12px 16px;
    font-size: 14px;
    line-height: 1.4;
  }

  .leaflet-popup-close-button {
    width: 24px;
    height: 24px;
    font-size: 18px;
    line-height: 24px;
  }
}

/* Disable text selection on map for better touch experience */
.leaflet-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}