/* Map Legend Panel - Complete isolation from map events */
.map-legend-panel {
  position: absolute;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  /* Complete isolation from map interactions */
  pointer-events: auto !important;
  user-select: none;
  /* Prevent any map event bubbling */
  isolation: isolate;
  /* Create a new stacking context */
  transform: translateZ(0);
  /* Force hardware acceleration and isolation */
  will-change: transform;
}

/* Ensure Leaflet recognizes this as a control and doesn't interfere */
.map-legend-panel.leaflet-control {
  pointer-events: auto !important;
  background: white;
  border-radius: 8px;
}

/* Force override any Leaflet map event handling */
.leaflet-container .map-legend-panel {
  pointer-events: auto !important;
}

.leaflet-container .map-legend-panel.leaflet-control {
  pointer-events: auto !important;
}

/* Position variants */
.map-legend-panel.legend-bottomleft {
  bottom: 20px;
  left: 20px;
}

.map-legend-panel.legend-bottomright {
  bottom: 20px;
  right: 20px;
}

.map-legend-panel.legend-topleft {
  top: 20px;
  left: 20px;
}

.map-legend-panel.legend-topright {
  top: 20px;
  right: 20px;
}

/* Minimized state */
.map-legend-panel.minimized {
  width: auto;
  height: auto;
  padding: 0;
}

/* Expanded state */
.map-legend-panel.expanded {
  width: auto;
  min-width: 300px;
  max-width: calc(100vw - 40px);
  min-height: 150px;
  max-height: 80vh;
  padding: 0;
}

/* Legend Tab (Minimized State) */
.legend-tab {
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 14px;
  border-radius: 8px;
  transition: all 0.2s ease;
  white-space: nowrap;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
}

.legend-tab:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  background: linear-gradient(135deg, #0056b3, #004085);
}

/* Legend Header */
.legend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border-radius: 8px 8px 0 0;
  margin: 0;
}

.legend-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.legend-minimize-btn {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.legend-minimize-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Legend Content - Enhanced scroll isolation */
.legend-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 400px;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
  scroll-behavior: smooth;
  background: white;
  /* Complete scroll isolation */
  pointer-events: auto !important;
  overscroll-behavior: contain;
  /* Prevent any scroll events from reaching map */
  overscroll-behavior-y: contain;
  overscroll-behavior-x: none;
  /* Additional isolation */
  touch-action: pan-y;
  /* Only allow vertical panning */
}

/* Disable Leaflet interactions on legend */
.map-legend-panel,
.map-legend-panel * {
  /* Leaflet-specific class to disable map interactions */
  pointer-events: auto !important;
}

/* Override any Leaflet CSS that might interfere */
.leaflet-container .map-legend-panel {
  pointer-events: auto !important;
}

.leaflet-container .map-legend-panel * {
  pointer-events: auto !important;
}

/* Legend Item Card - Core requirement: flexible height based on image */
.legend-item-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 4px;
  transition: box-shadow 0.2s ease;
  /* Height is flexible and determined by the legend image */
}

.legend-item-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Legend Image - displays at native size, determines card height */
.legend-image {
  display: block;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  /* No size constraints - image displays at native dimensions */
  /* Image height determines the card height */
}

/* Legend Layer Name - vertically centered with image */
.legend-layer-name {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
  word-wrap: break-word;
  /* Automatically vertically centered via flex align-items: center */
}

/* Placeholder for missing images */
.legend-placeholder {
  width: 20px;
  height: 20px;
  background: #f0f0f0;
  border: 1px dashed #ccc;
  border-radius: 4px;
}



/* Scrollbar styling */
.map-legend-panel .legend-content::-webkit-scrollbar {
  width: 6px;
}

.map-legend-panel .legend-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.map-legend-panel .legend-content::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.map-legend-panel .legend-content::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}
