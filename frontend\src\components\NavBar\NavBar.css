.navbar {
  background-color: #1e3a5f;
  padding: 0.5rem 1rem;
  transition: background-color 0.3s ease, opacity 0.3s ease;
  opacity: 0.95;
  min-height: 56px;
  position: relative;
}

.navbar:hover {
  opacity: 1;
  background-color: #1e3a5f;
}

/* Brand text or fallback */
.brand-text {
  color: white;
  font-weight: 600;
  font-size: clamp(1.2rem, 2.5vw, 1.5rem);
  letter-spacing: 0.5px;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  text-decoration: none;
  cursor: pointer;
}

.brand-text:hover {
  color: #e0e0e0;
  text-decoration: none;
}

.brand-logo {
  height: 32px;
  margin-right: 8px;
  display: inline-block;
}

/* Custom Navbar Toggle */
.navbar-toggler {
  background: none;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  padding: 0.375rem 0.5rem;
  color: white;
  transition: all 0.3s ease;
}

.navbar-toggler:hover,
.navbar-toggler:focus {
  border-color: rgba(255, 255, 255, 0.6);
  background-color: rgba(255, 255, 255, 0.1);
  outline: none;
  box-shadow: none;
}

/* Nav Links */
.nav-link,
.logout-button,
.navbar-brand {
  transition: color 0.3s ease, transform 0.3s ease;
}

.nav-link {
  color: white !important;
  margin: 0 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  min-height: 44px; /* Touch-friendly */
}

.nav-link:hover {
  color: #e0e0e0 !important;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.nav-item-responsive {
  position: relative;
}

.nav-text {
  font-size: 0.9rem;
  font-weight: 500;
}

/* Show text on desktop, hide on tablet/mobile when navbar is expanded */
@media (min-width: 992px) {
  .nav-text {
    display: inline !important;
  }

  /* Ensure nav links have proper width for text */
  .navbar .navbar-collapse .ms-auto .nav-link {
    width: auto !important;
    min-width: 40px !important;
  }
}

@media (max-width: 991px) {
  /* Text will be shown in collapsed mobile menu via the mobile responsive rule */
  .navbar-collapse:not(.show) .nav-text {
    display: none !important;
  }
}

/* Tools Icon (gear) */
.nav-icon {
  margin-right: 4px;
}

/* Logout Button */
.logout-button {
  background-color: #dc3545 !important;
  border: none !important;
  padding: 0.375rem 0.75rem;
  margin-left: 0.5rem;
  transition: background-color 0.3s ease;
  opacity: 0.9;
}

.logout-button:hover {
  background-color: #c82333 !important;
  opacity: 1;
  transform: scale(1.03);
}

.navbar-end {
  display: flex;
  align-items: center;
  gap: 12px;
}

.navbar-data-indicator {
  font-size: 11px;
}

/* Collapse control */
.navbar .navbar-collapse .ms-auto {
  display: flex !important;
  align-items: center;
  justify-content: flex-end;
  width: auto;
}

.navbar .navbar-collapse .ms-auto .nav-link {
  padding: 0.5rem 0.75rem;
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap; /* Prevent text wrapping */
}

.navbar-collapse.show .ms-auto,
.navbar-collapse.collapsing .ms-auto {
  display: flex !important;
  width: 100%;
  justify-content: flex-end;
}

/* Discovery Pill */
.discover-pill {
  border-radius: 50px;
  font-size: 12px;
  padding: 4px 12px;
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid #ffffff33;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.discover-pill:hover {
  background-color: #ffffff22;
  color: #e0e0e0;
}

/* Time Slider Wrapper */
.time-slider-wrapper {
  margin-left: auto;
  margin-right: 10px;
  display: flex;
  align-items: center;
}

/* Responsive Styles */
@media (max-width: 991px) {
  .navbar-collapse {
    background-color: #1e3a5f;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 1rem;
    border-radius: 0 0 8px 8px;
  }

  .nav-link {
    margin: 0.25rem 0;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    justify-content: flex-start;
  }

  .nav-text {
    margin-left: 0.5rem;
    display: inline !important; /* Show text on mobile when navbar is collapsed */
  }

  .time-slider-wrapper {
    width: 100%;
    margin: 1rem 0;
    padding: 0 1rem;
    justify-content: center;
  }

  .logout-button {
    width: 100%;
    margin: 0.5rem 0;
    justify-content: flex-start;
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 767px) {
  .navbar {
    padding: 0.5rem;
  }

  .brand-text {
    font-size: 1.2rem;
    margin-right: 0.5rem;
  }

  .brand-text img {
    height: 36px !important;
  }

  .navbar-toggler {
    padding: 0.25rem 0.375rem;
  }
}

@media (max-width: 479px) {
  .navbar {
    padding: 0.375rem;
  }

  .brand-text {
    font-size: 1.1rem;
  }

  .brand-text img {
    height: 32px !important;
    margin-right: 4px !important;
  }

  .nav-link {
    padding: 0.625rem 0.75rem;
    font-size: 0.9rem;
  }

  .navbar-toggler {
    padding: 0.2rem 0.3rem;
  }
}

/* Flex spacer */
.flex-grow-1 {
  flex-grow: 1;
}
