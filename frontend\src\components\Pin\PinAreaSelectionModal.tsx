import React, { useState } from 'react';
import { Modal, Button, Card, Row, Col, Badge } from 'react-bootstrap';
import { MapPin, Square, Circle, Download, X } from 'lucide-react';

interface PinAreaSelectionModalProps {
  show: boolean;
  onHide: () => void;
  pinCoordinates: { lat: number; lng: number };
  onAreaSelected: (areaConfig: {
    coordinates: { lat: number; lng: number };
    shape: 'square' | 'circle';
    size: number; // in km
    bounds: {
      north: number;
      south: number;
      east: number;
      west: number;
    };
    area: number; // in km²
  }) => void;
}

const PinAreaSelectionModal: React.FC<PinAreaSelectionModalProps> = ({
  show,
  onHide,
  pinCoordinates,
  onAreaSelected
}) => {
  const [selectedShape, setSelectedShape] = useState<'square' | 'circle'>('square');
  const [selectedSize, setSelectedSize] = useState<number>(10); // Default 10km

  // Predefined area sizes
  const areaSizes = [
    { size: 1, label: '1 km²', description: 'Very small area (1x1 km)' },
    { size: 5, label: '5 km²', description: 'Small area (2.2x2.2 km)' },
    { size: 10, label: '10 km²', description: 'Medium area (3.2x3.2 km)' },
    { size: 25, label: '25 km²', description: 'Large area (5x5 km)' },
    { size: 50, label: '50 km²', description: 'Very large area (7x7 km)' },
    { size: 100, label: '100 km²', description: 'Extra large area (10x10 km)' }
  ];

  const calculateBounds = (lat: number, lng: number, shape: 'square' | 'circle', area: number) => {
    // Calculate the radius/side length based on area
    const radius = shape === 'circle' 
      ? Math.sqrt(area / Math.PI) // radius for circle
      : Math.sqrt(area) / 2; // half side length for square

    // Convert km to degrees (rough approximation: 1 degree ≈ 111 km)
    const latOffset = radius / 111;
    const lngOffset = radius / (111 * Math.cos(lat * Math.PI / 180));

    return {
      north: lat + latOffset,
      south: lat - latOffset,
      east: lng + lngOffset,
      west: lng - lngOffset
    };
  };

  const handleAreaSelect = (size: number) => {
    setSelectedSize(size);
    
    const bounds = calculateBounds(pinCoordinates.lat, pinCoordinates.lng, selectedShape, size);
    
    const areaConfig = {
      coordinates: pinCoordinates,
      shape: selectedShape,
      size,
      bounds,
      area: size
    };

    onAreaSelected(areaConfig);
  };

  const getShapeIcon = (shape: 'square' | 'circle') => {
    return shape === 'square' ? <Square size={16} /> : <Circle size={16} />;
  };

  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header className="bg-primary text-white">
        <Modal.Title className="d-flex align-items-center">
          <MapPin size={20} className="me-2" />
          Select Area Around Pin
        </Modal.Title>
        <Button 
          variant="outline-light" 
          size="sm" 
          onClick={onHide}
          className="btn-close-white"
        >
          <X size={16} />
        </Button>
      </Modal.Header>
      
      <Modal.Body>
        {/* Pin Location Info */}
        <Card className="mb-3" style={{ backgroundColor: '#f8f9fa' }}>
          <Card.Body className="py-2">
            <div className="d-flex align-items-center justify-content-between">
              <div>
                <strong>📍 Pin Location:</strong>
                <Badge bg="secondary" className="ms-2">
                  {pinCoordinates.lat.toFixed(6)}, {pinCoordinates.lng.toFixed(6)}
                </Badge>
              </div>
            </div>
          </Card.Body>
        </Card>

        {/* Shape Selection */}
        <div className="mb-4">
          <h6 className="mb-3">Select Area Shape:</h6>
          <div className="d-flex gap-2">
            <Button
              variant={selectedShape === 'square' ? 'primary' : 'outline-primary'}
              onClick={() => setSelectedShape('square')}
              className="d-flex align-items-center"
            >
              <Square size={16} className="me-2" />
              Square
            </Button>
            <Button
              variant={selectedShape === 'circle' ? 'primary' : 'outline-primary'}
              onClick={() => setSelectedShape('circle')}
              className="d-flex align-items-center"
            >
              <Circle size={16} className="me-2" />
              Circle
            </Button>
          </div>
        </div>

        {/* Area Size Selection */}
        <div className="mb-4">
          <h6 className="mb-3">Select Area Size:</h6>
          <Row>
            {areaSizes.map((area) => (
              <Col md={6} key={area.size} className="mb-3">
                <Card 
                  className={`h-100 cursor-pointer ${selectedSize === area.size ? 'border-primary bg-light' : ''}`}
                  style={{ cursor: 'pointer' }}
                  onClick={() => setSelectedSize(area.size)}
                >
                  <Card.Body className="d-flex align-items-center">
                    <div className="me-3">
                      {getShapeIcon(selectedShape)}
                    </div>
                    <div className="flex-grow-1">
                      <div className="fw-bold">{area.label}</div>
                      <small className="text-muted">{area.description}</small>
                    </div>
                    {selectedSize === area.size && (
                      <Badge bg="primary">Selected</Badge>
                    )}
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </div>

        {/* Preview Info */}
        <Card className="border-success">
          <Card.Header className="bg-success text-white">
            <h6 className="mb-0">Selected Configuration</h6>
          </Card.Header>
          <Card.Body>
            <Row>
              <Col md={6}>
                <strong>Shape:</strong> {selectedShape.charAt(0).toUpperCase() + selectedShape.slice(1)}
              </Col>
              <Col md={6}>
                <strong>Area:</strong> {selectedSize} km²
              </Col>
            </Row>
            <Row className="mt-2">
              <Col md={6}>
                <strong>Center:</strong> {pinCoordinates.lat.toFixed(4)}, {pinCoordinates.lng.toFixed(4)}
              </Col>
              <Col md={6}>
                <strong>Approximate Size:</strong> {Math.sqrt(selectedSize).toFixed(1)} x {Math.sqrt(selectedSize).toFixed(1)} km
              </Col>
            </Row>
          </Card.Body>
        </Card>
      </Modal.Body>
      
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button 
          variant="success" 
          onClick={() => handleAreaSelect(selectedSize)}
          className="d-flex align-items-center"
        >
          <Download size={16} className="me-2" />
          Create Area of Interest
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default PinAreaSelectionModal;
