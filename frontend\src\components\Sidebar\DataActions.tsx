import React from 'react';
import { But<PERSON> } from 'react-bootstrap';
import { Eye, Download, Clock } from 'lucide-react';

interface DataActionsProps {
  onPreviewData: () => void;
  onDownloadData: () => void;
  onQueryTemporalData?: () => void;
  temporalLayerName?: string;
}

const DataActions: React.FC<DataActionsProps> = ({ 
  onPreviewData, 
  onDownloadData, 
  onQueryTemporalData,
  temporalLayerName 
}) => {
  return (
    <div className="data-actions section">
      <div className="mb-2">
        <small className="text-muted">
          Actions for selected layers and date range
        </small>
      </div>
      
      <Button
        className="action-button mb-2"
        onClick={onPreviewData}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#007bff',
          borderColor: '#007bff',
          color: 'white'
        }}
      >
        <Eye size={16} className="me-2" />
        Preview Data
      </Button>
      
      <Button 
        className="action-button mb-3" 
        onClick={onDownloadData}
        style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
      >
        <Download size={16} className="me-2" />
        Download Selected Data
      </Button>
      
      {/* Temporal Query Button - Only show when applicable */}
      {onQueryTemporalData && temporalLayerName && (
        <Button 
          className="action-button mb-3" 
          variant="info"
          onClick={onQueryTemporalData}
          style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#17a2b8' }}
        >
          <Clock size={16} className="me-2" />
          Query {temporalLayerName} Data
        </Button>
      )}

      {/* Tip card removed as requested */}
      {/* <div className="info-section p-2 rounded" style={{ backgroundColor: '#f8f9fa', border: '1px solid #dee2e6' }}>
        <div className="d-flex align-items-start">
          <Info size={14} className="me-2 mt-1 text-primary" />
          <small className="text-muted">
            <strong>Tip:</strong> For temporal layers, use the Query button in the Date Range section to execute time-based searches.
          </small>
        </div>
      </div> */}
    </div>
  );
};

export default DataActions;