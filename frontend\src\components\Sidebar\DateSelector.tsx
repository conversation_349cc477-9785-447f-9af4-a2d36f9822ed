import React, { useState } from 'react';
import { Form, Button, Row, Col, Dropdown, Badge } from 'react-bootstrap';

interface DateSelectorProps {
  dateRange: {
    startDate: string;
    endDate: string;
  };
  onDateChange: (type: 'startDate' | 'endDate', value: string) => void;
  disabled?: boolean;
  onQueryData?: () => void;
  selectedLayerName?: string;
}

const DateSelector: React.FC<DateSelectorProps> = ({ dateRange, onDateChange, disabled = false, onQueryData, selectedLayerName }) => {
  const [showQuickSelect, setShowQuickSelect] = useState(false);
  // Generate suggested date ranges
  const generateSuggestedRanges = () => {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    
    return [
      {
        label: 'Last 30 Days',
        start: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
        end: now
      },
      {
        label: 'Last 3 Months',
        start: new Date(currentYear, currentMonth - 3, 1),
        end: now
      },
      {
        label: 'Last 6 Months',
        start: new Date(currentYear, currentMonth - 6, 1),
        end: now
      },
      {
        label: 'Last Year',
        start: new Date(currentYear - 1, 0, 1),
        end: new Date(currentYear - 1, 11, 31)
      },
      {
        label: 'Current Year',
        start: new Date(currentYear, 0, 1),
        end: now
      },
      {
        label: '2024 Full Year',
        start: new Date(2024, 0, 1),
        end: new Date(2024, 11, 31)
      },
      {
        label: '2023 Full Year',
        start: new Date(2023, 0, 1),
        end: new Date(2023, 11, 31)
      },
      {
        label: 'Flood Season 2024',
        start: new Date(2024, 9, 1), // October
        end: new Date(2024, 11, 31) // December
      }
    ];
  };

  const formatDateForInput = (date: Date) => {
    return date.toISOString().split('T')[0].split('-').join('/');
  };

  const handleSuggestedRange = (startDate: Date, endDate: Date) => {
    onDateChange('startDate', formatDateForInput(startDate));
    onDateChange('endDate', formatDateForInput(endDate));
  };

  const suggestedRanges = generateSuggestedRanges();
  return (
    <div className="date-selector section">
      {disabled && (
        <div className="mb-2">
          <small className="text-muted">
            Select temporal layer to enable date range controls
          </small>
        </div>
      )}

      {/* Date Controls in One Row */}
      <Row className="mb-3">
        <Col xs={6}>
          <Form.Group>
            <Form.Label className="date-label" style={{ fontSize: '0.85rem', fontWeight: '500' }}>Start Date:</Form.Label>
            <Form.Control
              type="date"
              value={dateRange.startDate.split('/').join('-')}
              onChange={(e) => {
                const date = e.target.value.split('-').join('/');
                onDateChange('startDate', date);
              }}
              className="date-input"
              disabled={disabled}
              style={disabled ? { opacity: 0.5, cursor: 'not-allowed' } : {}}
            />
          </Form.Group>
        </Col>
        <Col xs={6}>
          <Form.Group>
            <Form.Label className="date-label" style={{ fontSize: '0.85rem', fontWeight: '500' }}>End Date:</Form.Label>
            <Form.Control
              type="date"
              value={dateRange.endDate.split('/').join('-')}
              onChange={(e) => {
                const date = e.target.value.split('-').join('/');
                onDateChange('endDate', date);
              }}
              className="date-input"
              disabled={disabled}
              style={disabled ? { opacity: 0.5, cursor: 'not-allowed' } : {}}
            />
          </Form.Group>
        </Col>
      </Row>

      {/* Selected Date Range Badge */}
      {!disabled && dateRange.startDate && dateRange.endDate && (
        <div className="mb-3 text-center">
          <Badge
            bg="info"
            className="px-3 py-2"
            style={{
              fontSize: '0.85rem',
              fontWeight: '500',
              borderRadius: '15px'
            }}
          >
            📅 {new Date(dateRange.startDate.split('/').join('-')).toLocaleDateString()} - {new Date(dateRange.endDate.split('/').join('-')).toLocaleDateString()}
          </Badge>
        </div>
      )}

      {/* Date Range Quick Select Button */}
      {!disabled && (
        <div className="mb-3">
          <Button
            variant="outline-primary"
            className="w-100"
            onClick={() => setShowQuickSelect(!showQuickSelect)}
            style={{
              fontSize: '0.9rem',
              padding: '0.5rem',
              fontWeight: '500'
            }}
          >
            Date Range Quick Select
          </Button>
        </div>
      )}

      {/* Quick Select Options - Only show when expanded */}
      {!disabled && showQuickSelect && (
        <div className="mb-3">
          <small className="text-muted d-block mb-2">Choose a predefined date range:</small>
          <div className="suggested-ranges-grid">
            {suggestedRanges.map((range, index) => (
              <Button
                key={index}
                variant="outline-info"
                size="sm"
                className="suggested-range-btn mb-2 w-100"
                onClick={() => {
                  handleSuggestedRange(range.start, range.end);
                  setShowQuickSelect(false);
                }}
                style={{
                  fontSize: '0.8rem',
                  padding: '0.4rem 0.6rem',
                  textAlign: 'left'
                }}
              >
                {range.label}
              </Button>
            ))}
          </div>

          {/* Close Quick Select */}
          <div className="mt-2">
            <Button
              variant="outline-secondary"
              size="sm"
              className="w-100"
              onClick={() => setShowQuickSelect(false)}
              style={{ fontSize: '0.8rem' }}
            >
              Close Quick Select
            </Button>
          </div>
        </div>
      )}
      
      {/* Query Button - only show when temporal layers are active */}
      {!disabled && onQueryData && (
        <div className="mt-3">
          <Button 
            variant="primary"
            className="w-100 query-button"
            onClick={onQueryData}
            style={{
              backgroundColor: '#0a4273',
              borderColor: '#0a4273',
              fontWeight: '600',
              padding: '0.75rem',
              fontSize: '0.9rem'
            }}
          >
            🔍 Query {selectedLayerName || 'Temporal Data'}
          </Button>
          <small className="text-muted d-block mt-1 text-center">
            Search layers with selected date range
          </small>
        </div>
      )}
    </div>
  );
};

export default DateSelector;