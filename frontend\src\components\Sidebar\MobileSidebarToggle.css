.mobile-sidebar-toggle {
  position: fixed;
  top: 70px; /* Below navbar */
  left: 1rem;
  z-index: var(--z-fixed, 1030);
  background-color: #1e3a5f;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: none; /* Hidden by default */
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
}

.mobile-sidebar-toggle:hover {
  background-color: #2a4a7a;
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.mobile-sidebar-toggle:active {
  transform: scale(0.95);
}

.mobile-sidebar-toggle:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Show on mobile screens */
@media (max-width: 767px) {
  .mobile-sidebar-toggle {
    display: flex;
  }
}

/* Adjust position for very small screens */
@media (max-width: 479px) {
  .mobile-sidebar-toggle {
    top: 64px;
    left: 0.75rem;
    width: 44px;
    height: 44px;
  }
}

/* Landscape orientation */
@media (max-width: 767px) and (orientation: landscape) {
  .mobile-sidebar-toggle {
    top: 64px;
    width: 40px;
    height: 40px;
  }
}
