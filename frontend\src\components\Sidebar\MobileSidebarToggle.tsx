import React from 'react';
import { Menu, X } from 'lucide-react';
import './MobileSidebarToggle.css';

interface MobileSidebarToggleProps {
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
}

const MobileSidebarToggle: React.FC<MobileSidebarToggleProps> = ({
  isOpen,
  onToggle,
  className = ''
}) => {
  return (
    <button
      className={`mobile-sidebar-toggle ${className}`}
      onClick={onToggle}
      aria-label={isOpen ? 'Close sidebar' : 'Open sidebar'}
      aria-expanded={isOpen}
      type="button"
    >
      {isOpen ? <X size={24} /> : <Menu size={24} />}
    </button>
  );
};

export default MobileSidebarToggle;
