import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { ChevronDown, ChevronUp, Layers, Clock } from 'lucide-react';
import { fetchAvailableWMSLayers, WMSLayer, fetchLayerStyles } from '../../services/geoserverService';

interface MoreLayersProps {
  selectedLayers: {
    sentinel: boolean;
    floodRisk: boolean;
    cbers: boolean;
    cadastre: boolean;
    dwsVillage: boolean;
    nasaPower: boolean;
    eumetsat: boolean;
    streamflow: boolean;
    historicalFlood: boolean;
    soilMoisture: boolean;
  };
}

interface LayerWithStyles extends WMSLayer {
  temporal?: any;
  isSelected?: boolean;
}

const MoreLayers: React.FC<MoreLayersProps> = ({ selectedLayers }) => {
  const [isExpanded, setIsExpanded] = useState(false);  const [availableLayers, setAvailableLayers] = useState<LayerWithStyles[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // List of main layers that are already in the checkbox list
  const mainLayerTypes = [
    'sentinel', 'floodRisk', 'cbers', 'cadastre', 'dwsVillage', 
    'nasaPower', 'eumetsat', 'streamflow', 'historicalFlood', 'soilMoisture'
  ];
  const fetchAdditionalLayers = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const allLayers = await fetchAvailableWMSLayers();
      
      // Filter out layers that are already in the main checkbox list
      const additionalLayers = allLayers.filter(layer =>
        !layer?.type || !mainLayerTypes.includes(layer.type)
      );

      // Fetch temporal information for each additional layer
      const layersWithStyles = await Promise.all(
        additionalLayers.map(async (layer) => {
          try {
            const styles = await fetchLayerStyles(layer.name);
            return {
              ...layer,
              temporal: styles?.temporal || { hasTemporal: false },
              isSelected: false // Additional layers are not part of main selection
            };
          } catch (error) {
            console.warn(`Could not fetch styles for ${layer.name}:`, error);
            return {
              ...layer,
              temporal: { hasTemporal: false },
              isSelected: false
            };
          }
        })
      );

      setAvailableLayers(layersWithStyles);
    } catch (error) {
      console.error('Error fetching additional layers:', error);
      setError('Failed to load additional layers');
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = () => {
    if (!isExpanded && availableLayers.length === 0) {
      fetchAdditionalLayers();
    }
    setIsExpanded(!isExpanded);
  };  const handleLayerClick = (layer: LayerWithStyles) => {
    // For now, we'll just show an info message since these layers
    // aren't part of the main selectedLayers interface
    console.log('Layer clicked:', layer.name, 'Current selections:', selectedLayers);
    alert(`Layer: ${layer?.title || 'Unknown'}\nType: ${layer?.type || 'Unknown'}\nTemporal: ${layer?.temporal?.hasTemporal ? 'Yes' : 'No'}\n\nThis layer can be viewed in the Service Details modal.`);
  };

  return (
    <div className="more-layers-section">
      <button 
        className="more-layers-toggle"
        onClick={handleToggle}
        type="button"
      >
        <div className="d-flex justify-content-between align-items-center">
          <span>
            <Layers size={16} className="me-2" />
            More Layers
            {availableLayers.length > 0 && (
              <Badge bg="secondary" className="ms-2">
                {availableLayers.length}
              </Badge>
            )}
          </span>
          <span className={`toggle-icon ${isExpanded ? 'expanded' : ''}`}>
            {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </span>
        </div>
      </button>

      {isExpanded && (
        <div className="more-layers-content">
          {loading && (
            <div className="text-center py-3">
              <Spinner animation="border" size="sm" className="me-2" />
              Loading additional layers...
            </div>
          )}

          {error && (
            <Alert variant="warning" className="mb-2">
              <small>{error}</small>
            </Alert>
          )}

          {!loading && !error && availableLayers.length === 0 && (
            <div className="text-muted text-center py-2">
              <small>No additional layers available</small>
            </div>
          )}

          {!loading && availableLayers.length > 0 && (
            <div>
              <div className="mb-2">
                <small className="text-muted">
                  Click any layer for details • {availableLayers.filter(l => l.temporal?.hasTemporal).length} temporal layers
                </small>
              </div>
              
              {availableLayers.map((layer) => (
                <div
                  key={layer.name}
                  className={`more-layers-item ${layer.temporal?.hasTemporal ? 'temporal' : ''}`}
                  onClick={() => handleLayerClick(layer)}
                  title={layer.abstract || 'Click for more information'}
                >
                  <div className="d-flex justify-content-between align-items-center">
                    <span className="layer-name">
                      {layer.title || layer.name}
                    </span>
                    <div className="layer-badges">
                      {layer.temporal?.hasTemporal && (
                        <Badge bg="info" className="layer-info-badge me-1">
                          <Clock size={10} className="me-1" />
                          Temporal
                        </Badge>
                      )}
                      {layer?.type && (
                        <Badge bg="secondary" className="layer-info-badge">
                          {layer.type}
                        </Badge>
                      )}
                    </div>
                  </div>
                  {layer.abstract && (
                    <div className="layer-description mt-1">
                      <small className="text-muted">
                        {layer.abstract.length > 80 
                          ? `${layer.abstract.substring(0, 80)}...` 
                          : layer.abstract
                        }
                      </small>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MoreLayers;
