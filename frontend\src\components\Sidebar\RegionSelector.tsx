import React from 'react';
import { Button, Form, Badge } from 'react-bootstrap';
import { Pentagon, Trash2 } from 'lucide-react';

interface RegionSelectorProps {
  // Core drawing functionality
  onDrawModeToggle: (isDrawing: boolean) => void;
  isDrawingMode: boolean;
  hasDrawnArea: boolean;
  onClearDrawnArea: () => void;
}

const RegionSelector: React.FC<RegionSelectorProps> = ({
  onDrawModeToggle,
  isDrawingMode,
  hasDrawnArea,
  onClearDrawnArea
}) => {
  const handleDrawToggle = () => {
    onDrawModeToggle(!isDrawingMode);
  };

  return (
    <div className="region-selector section">
      {/* Direct Drawing Controls - No radio button selection needed */}
      <div className="mb-3">
        {/* Drawing Method Selection */}
        <div className="mb-3">
          <small className="text-muted d-block mb-2">Choose drawing method:</small>
          <div className="d-flex gap-2 align-items-center mb-2">
            <Button
              className={`action-button flex-grow-1 d-flex align-items-center justify-content-center ${isDrawingMode ? 'btn-warning' : 'btn-primary'}`}
              onClick={handleDrawToggle}
              style={{ fontSize: '0.85rem', padding: '0.5rem' }}
            >
              <Pentagon size={16} className="me-2" />
              {isDrawingMode ? 'Cancel Drawing' : 'Draw Polygon'}
            </Button>

            {hasDrawnArea && (
              <Button
                variant="outline-danger"
                size="sm"
                onClick={onClearDrawnArea}
                title="Clear drawn area"
              >
                <Trash2 size={16} />
              </Button>
            )}
          </div>


        </div>

        {/* Visual feedback for drawing mode */}
        {isDrawingMode && (
          <div className="mt-2">
            <Badge bg="info" className="w-100 text-center py-2">
              Click on the map to start drawing your area of interest
            </Badge>
          </div>
        )}

        {hasDrawnArea && !isDrawingMode && (
          <div className="mt-2">
            <Badge bg="success" className="w-100 text-center py-2">
              Area of interest defined - ready for analysis
            </Badge>
          </div>
        )}
      </div>


    </div>
  );
};

export default RegionSelector;