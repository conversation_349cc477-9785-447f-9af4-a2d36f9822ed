/* Service Details Modal Styles */
.service-details {
  margin-bottom: 20px;
}

.service-details .action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0a4273;
  border: none;
  color: white;
  padding: 12px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.service-details .action-button:hover {
  background-color: #063057;
}

/* Modal Content Styles */
.modal-xl {
  max-width: 95% !important;
}

.modal-body {
  padding: 1.5rem;
}

/* Tab Content Styles */
.nav-tabs {
  border-bottom: 2px solid #0a4273;
  margin-bottom: 1.5rem;
}

.nav-tabs .nav-item {
  margin-bottom: -1px;
}

.nav-tabs .nav-link {
  display: flex;
  align-items: center;
  color: #ffffff !important;
  border: 1px solid transparent;
  background-color: #87ceeb !important;
  border-radius: 6px 6px 0 0;
  margin-right: 2px;
  transition: all 0.3s ease;
}

.nav-tabs .nav-link span {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.nav-tabs .nav-link.active {
  color: #ffffff;
  background-color: #0a4273 !important;
  border-color: #0a4273 #0a4273 #0a4273 !important;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(10, 66, 115, 0.3);
}

.nav-tabs .nav-link:not(.active):hover {
  border-color: #5bb3d9;
}

.nav-tabs .nav-link:hover {
  color: #ffffff;
  background-color: #5bb3d9;
  transform: translateY(-1px);
}

/* Override Bootstrap Tab Styles */
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #ffffff !important;
  background-color: #0a4273 !important;
  border-color: #0a4273 !important;
}

/* Ensure tab content area has proper background */
.tab-content {
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-top: none;
  border-radius: 0 0 6px 6px;
  padding: 1rem;
}

/* Card Styles */
.card {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.card-img-top {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

/* Thumbnail Grid */
.thumbnail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.thumbnail-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.thumbnail-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Legend Images */
.legend-container {
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  text-align: center;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-image {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
}

/* Feature Info */
.feature-info-container {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 1rem;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: pre-wrap;
}

/* Service Metadata */
.metadata-container {
  padding: 1rem;
}

.metadata-field {
  margin-bottom: 0.75rem;
}

.metadata-label {
  font-weight: 600;
  color: #495057;
  margin-right: 0.5rem;
}

.metadata-value {
  color: #212529;
}

/* Accordion Styles */
.accordion-item {
  border: 1px solid #dee2e6;
  margin-bottom: 0.5rem;
}

.accordion-header button {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: none;
  width: 100%;
  padding: 1rem;
  text-align: left;
  font-weight: 500;
}

.accordion-header button:not(.collapsed) {
  background-color: #e9ecef;
  color: #0a4273;
}

.accordion-body {
  padding: 1rem;
  background-color: #fff;
}

/* Bbox Display */
.bbox-display {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.75rem;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin-top: 0.5rem;
}

/* Button Groups */
.btn-group-sm .btn {
  font-size: 0.8rem;
  padding: 0.375rem 0.75rem;
}

.btn-outline-primary {
  color: #0a4273;
  border-color: #0a4273;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #0a4273;
  border-color: #0a4273;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6c757d;
}

.loading-text {
  margin-top: 1rem;
  font-size: 1.1rem;
}

/* Error States */
.alert {
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.alert-info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

/* Badge Styles */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.5rem;
  border-radius: 4px;
}

.bg-secondary {
  background-color: #6c757d !important;
}

/* Network Error Indicator Styles */
.network-error-indicator {
  width: 8px;
  height: 8px;
  background-color: #dc3545;
  border-radius: 50%;
  margin-right: 5px;
  animation: pulse-red 2s infinite;
}

.network-error-indicator-large {
  width: 12px;
  height: 12px;
  background-color: #dc3545;
  border-radius: 50%;
  margin-right: 10px;
  flex-shrink: 0;
  animation: pulse-red 2s infinite;
}

@keyframes pulse-red {
  0% {
    opacity: 1;
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
  }
  70% {
    opacity: 0.6;
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    opacity: 1;
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

.network-error-line {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, #dc3545, #f8d7da, #dc3545);
  background-size: 200% 100%;
  animation: gradient-slide 3s ease-in-out infinite;
  margin-top: 15px;
  margin-bottom: 10px;
}

@keyframes gradient-slide {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Network Error Alert Enhancement */
.alert-danger.network-error {
  border-left: 4px solid #dc3545;
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.1);
}

.network-status-header {
  display: inline-flex;
  align-items: center;
  font-size: 0.875rem;
  color: #dc3545;
  font-weight: 500;
}

/* Styles Information Display */
.styles-info-container {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 1rem;
}

.style-item {
  background-color: #fff;
  transition: box-shadow 0.2s ease;
}

.style-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.style-details {
  border-top: 1px solid #e9ecef;
  padding-top: 0.5rem;
  margin-top: 0.5rem;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer:hover {
  text-decoration: underline;
}

/* Badge Enhancements */
.badge.bg-success {
  background-color: #198754 !important;
}

.badge.bg-secondary {
  background-color: #6c757d !important;
}

/* Style Legend Images */
.style-item img {
  border: 1px solid #dee2e6;
  border-radius: 3px;
  background-color: white;
  padding: 2px;
}

/* Temporal Information Styles */
.temporal-info-section {
  border-left: 4px solid #17a2b8;
  padding-left: 1rem;
}

.temporal-details {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.temporal-details .fw-bold {
  font-size: 0.95rem;
}

.temporal-details .text-success {
  color: #28a745 !important;
}

.temporal-details .text-danger {
  color: #dc3545 !important;
}

.temporal-details .text-primary {
  color: #007bff !important;
}

.temporal-details .badge {
  font-size: 0.75rem;
}

.temporal-details small {
  font-size: 0.8rem;
}

/* Temporal badge in accordion header */
.accordion-header .badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
}

.badge-sm {
  font-size: 0.7rem !important;
  padding: 0.25rem 0.5rem !important;
}

.accordion-header .badge.bg-info {
  background-color: #17a2b8 !important;
  color: white;
}

/* Responsive temporal display */
@media (max-width: 576px) {
  .temporal-details .row {
    text-align: center;
  }
  
  .temporal-details .col-sm-6 {
    margin-bottom: 0.5rem;
  }
}

/* Animation for temporal badges */
.badge.bg-info {
  animation: pulse-temporal 2s infinite;
}

@keyframes pulse-temporal {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-xl {
    max-width: 98% !important;
    margin: 1rem;
  }
  
  .modal-body {
    padding: 1rem;
  }
  
  .thumbnail-grid {
    grid-template-columns: 1fr;
  }  .nav-tabs .nav-link {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
    background-color: #87ceeb !important;
    color: #ffffff !important;
  }
  
  .nav-tabs .nav-link.active,
  .nav-tabs .nav-item.show .nav-link {
    background-color: #0a4273 !important;
    color: #ffffff !important;
    border-color: #0a4273 !important;
  }
  
  .card-body {
    padding: 0.75rem;
  }
  
  .feature-info-container {
    font-size: 10px;
    max-height: 250px;
  }
}

@media (max-width: 576px) {
  .modal-header {
    padding: 1rem;
  }
  
  .modal-title {
    font-size: 1.1rem;
  }
    .nav-tabs .nav-link span {
    font-size: 0.8rem;
  }
    .nav-tabs .nav-link {
    background-color: #87ceeb !important;
    color: #ffffff !important;
  }
  
  .nav-tabs .nav-link.active,
  .nav-tabs .nav-item.show .nav-link {
    background-color: #0a4273 !important;
    color: #ffffff !important;
    border-color: #0a4273 !important;
  }
  
  .accordion-header button {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
  
  .btn-group-sm .btn {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }
}
