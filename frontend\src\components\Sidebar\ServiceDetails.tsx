import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { Info, Image, MapPin, Layers, Download, Eye, FileText } from 'lucide-react';
import { 
  fetchAvailableWMSLayers, 
  WMSLayer, 
  fetchServiceMetadata,
  fetchLayerStyles,
  fetchLayerMetadata
} from '../../services/geoserverService';

interface ServiceDetailsProps {
  selectedLayers: Record<string, boolean>;
}

interface ServiceMetadata {
  title?: string;
  abstract?: string;
  version?: string;
  contact?: {
    organization?: string;
    person?: string;
    email?: string;
  };
  fees?: string;
  accessConstraints?: string;
}

interface StyleInfo {
  name: string;
  title: string;
  abstract?: string;
  legendURL?: string;
}

interface LayerDetails extends WMSLayer {
  styles?: StyleInfo[];
  metadataURLs?: string[];
  dataURLs?: string[];
  featureInfo?: any;
}
const API_BASE = "";

const ServiceDetails: React.FC<ServiceDetailsProps> = ({ selectedLayers }) => {
  console.log(selectedLayers)
  const [showModal, setShowModal] = useState(false);
  const [activeTab, setActiveTab] = useState('metadata');
  const [layers, setLayers] = useState<LayerDetails[]>([]);
  const [serviceMetadata, setServiceMetadata] = useState<ServiceMetadata | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [networkError, setNetworkError] = useState<boolean>(false);
  const [selectedLayerForDetails, setSelectedLayerForDetails] = useState<LayerDetails | null>(null);  const [legendImages, setLegendImages] = useState<{ [key: string]: string }>({});
  const [layerStyles, setLayerStyles] = useState<{ [key: string]: any }>({});
  const [layerMetadata, setLayerMetadata] = useState<{ [key: string]: any }>({});

  // Add comprehensive error tracking state
  const [errorSummary, setErrorSummary] = useState<{
    metadata: string | null;
    layers: string | null;
    legends: string | null;
    styles: string | null;
  }>({
    metadata: null,
    layers: null,
    legends: null,
    styles: null
  });

  // Fetch service metadata and layers when modal opens
  useEffect(() => {
    if (showModal) {
      fetchServiceData();
    }
  }, [showModal]);  const fetchServiceData = async () => {
    setLoading(true);
    setError(null);
    setNetworkError(false);
    setErrorSummary({ metadata: null, layers: null, legends: null, styles: null });
    
    try {
      console.log('Starting fetchServiceData...');
      
      // Fetch layers first
      console.log('Fetching layers...');
      const layersData = await fetchAvailableWMSLayers();
      console.log('Layers fetched:', layersData.length);
      setLayers(layersData);

      // Fetch service metadata (non-blocking)
      console.log('Fetching service metadata...');
      try {
        const metadata = await fetchServiceMetadata();
        console.log('Service metadata fetched:', metadata);
        setServiceMetadata(metadata);
      } catch (metadataError) {
        console.warn('Failed to fetch service metadata:', metadataError);
        setErrorSummary(prev => ({ ...prev, metadata: 'Failed to load service metadata' }));
      }

      // Fetch legend images for available layers (non-blocking)
      console.log('Fetching legend images...');
      try {
        await fetchLegendImages(layersData);
      } catch (legendError) {
        console.warn('Failed to fetch legend images:', legendError);
        setErrorSummary(prev => ({ ...prev, legends: 'Failed to load legend images' }));
      }
      
      // Fetch layer styles (non-blocking)
      console.log('Fetching layer styles...');
      try {
        await fetchLayerStylesData(layersData);
      } catch (stylesError) {
        console.warn('Failed to fetch layer styles:', stylesError);
        setErrorSummary(prev => ({ ...prev, styles: 'Failed to load style information' }));
      }
      
      console.log('All service data fetch attempts completed');
      
    } catch (err) {
      console.error('Error in fetchServiceData:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch service data';
      
      // Check if it's a network-related error
      const isNetworkError = errorMessage.includes('ETIMEDOUT') || 
                            errorMessage.includes('ECONNREFUSED') ||
                            errorMessage.includes('ENOTFOUND') ||
                            errorMessage.includes('Network Error') ||
                            errorMessage.includes('Failed to fetch') ||
                            errorMessage.includes('ERR_NETWORK') ||
                            errorMessage.includes('ERR_INTERNET_DISCONNECTED') ||
                            errorMessage.includes('timeout') ||
                            errorMessage.includes('connect');
      
      setNetworkError(isNetworkError);
      
      if (isNetworkError) {
        setError(`Network Connection Error: Unable to connect to GeoServer. Please check your network connection and ensure the GeoServer is accessible. Details: ${errorMessage}`);
      } else {
        setError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };
  const fetchLayerStylesData = async (layersData: LayerDetails[]) => {
    const stylesPromises = layersData.map(async (layer) => {
      try {
        const styles = await fetchLayerStyles(layer.name);
        return { [layer.name]: styles };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const isNetworkError = errorMessage.includes('ETIMEDOUT') || 
                              errorMessage.includes('ECONNREFUSED') ||
                              errorMessage.includes('ENOTFOUND') ||
                              errorMessage.includes('Network Error') ||
                              errorMessage.includes('Failed to fetch') ||
                              errorMessage.includes('ERR_NETWORK') ||
                              errorMessage.includes('timeout') ||
                              errorMessage.includes('connect');
        
        if (isNetworkError) {
          setNetworkError(true);
        }
        console.warn(`Failed to fetch styles for layer ${layer.name}:`, error);
        return { [layer.name]: null };
      }
    });

    const stylesResults = await Promise.all(stylesPromises);
    const stylesMap = stylesResults.reduce((acc, curr) => ({ ...acc, ...curr }), {});
    setLayerStyles(stylesMap);
  };  const fetchLegendImages = async (layersData: LayerDetails[]) => {
    console.log('Fetching legend images for layers:', layersData.map(l => l.name));
    
    const legendPromises = layersData.map(async (layer) => {
      try {
        console.log(`Fetching legend for layer: ${layer.name}`);
        
        // Use the backend proxy endpoint for legend requests to avoid CORS issues
        const response = await fetch(`${API_BASE}/legend?layer=${encodeURIComponent(layer.name)}&format=image/png`, {
          method: 'GET',
          headers: {
            'Accept': 'image/png, image/jpeg, image/gif, */*'
          },
          cache: 'no-cache'
        });
        
        if (response.ok) {
          const blob = await response.blob();
          console.log(`Legend blob received for ${layer.name}, size: ${blob.size} bytes`);
          
          // Create a URL for the blob
          const imageUrl = URL.createObjectURL(blob);
          return { [layer.name]: imageUrl };
        } else {
          console.warn(`Legend request failed for ${layer.name}: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const isNetworkError = errorMessage.includes('ETIMEDOUT') || 
                              errorMessage.includes('ECONNREFUSED') ||
                              errorMessage.includes('ENOTFOUND') ||
                              errorMessage.includes('Network Error') ||
                              errorMessage.includes('Failed to fetch') ||
                              errorMessage.includes('ERR_NETWORK') ||
                              errorMessage.includes('timeout') ||
                              errorMessage.includes('connect');
        
        if (isNetworkError) {
          setNetworkError(true);
        }
        console.warn(`Failed to fetch legend for layer ${layer.name}:`, error);
      }
      return { [layer.name]: '' };
    });

    const legendResults = await Promise.all(legendPromises);
    const legendMap = legendResults.reduce((acc, curr) => ({ ...acc, ...curr }), {});
    console.log('Legend images loaded:', Object.keys(legendMap).filter(key => legendMap[key]));
    setLegendImages(legendMap);
  };const generateMapThumbnail = (layer: LayerDetails, width = 200, height = 150): string => {
    console.log(`Generating thumbnail for layer: ${layer.name}`, layer.bbox);
    const bbox = layer.bbox;
    
    // Use the backend WMS proxy to avoid CORS issues
    const wmsUrl = `${API_BASE}/wms-proxy`;
    const params = new URLSearchParams({
      service: 'WMS',
      version: '1.1.1',
      request: 'GetMap',
      layers: layer.name,
      width: width.toString(),
      height: height.toString(),
      format: 'image/jpeg',
      styles: '',
      transparent: 'false'
    });

    if (bbox) {
      params.append('bbox', `${bbox.minx},${bbox.miny},${bbox.maxx},${bbox.maxy}`);
      params.append('srs', bbox.SRS || 'EPSG:4326');
    } else {
      // Default to South Africa bounds
      params.append('bbox', '16.3,-34.8,32.9,-22.1');
      params.append('srs', 'EPSG:4326');
    }

    return `${wmsUrl}?${params.toString()}`;
  };  const getFeatureInfo = async (layer: LayerDetails) => {
    try {
      console.log(`Fetching feature info for layer: ${layer.name}`);
      setLoading(true);
      
      const response = await fetch(`${API_BASE}/features?typeName=${encodeURIComponent(layer.name)}&outputFormat=application/json&maxFeatures=10`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        },
        cache: 'no-cache'
      });
      
      if (response.ok) {
        const featureData = await response.json();
        console.log(`Feature data received for ${layer.name}:`, featureData);
        
        // Update the selected layer with feature info and switch to feature tab
        setSelectedLayerForDetails({ ...layer, featureInfo: featureData });
        setActiveTab('features');
      } else {
        console.warn(`Feature info request failed for ${layer.name}: ${response.status} ${response.statusText}`);
        setSelectedLayerForDetails({ ...layer, featureInfo: null });
        setActiveTab('features');
      }
    } catch (error) {
      console.error('Failed to fetch feature info:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch feature information';
      
      // Check if it's a network-related error
      const isNetworkError = errorMessage.includes('ETIMEDOUT') || 
                            errorMessage.includes('ECONNREFUSED') ||
                            errorMessage.includes('ENOTFOUND') ||
                            errorMessage.includes('Network Error') ||
                            errorMessage.includes('Failed to fetch') ||
                            errorMessage.includes('ERR_NETWORK') ||
                            errorMessage.includes('timeout') ||
                            errorMessage.includes('connect');
      
      setNetworkError(isNetworkError);
      
      // Still set the layer as selected but with no feature info
      setSelectedLayerForDetails({ ...layer, featureInfo: null });
      setActiveTab('features');
      
      if (isNetworkError) {
        setError(`Network Connection Error: Unable to fetch feature information for ${layer.title}. Please check your network connection. Details: ${errorMessage}`);
      } else {
        setError(`Failed to fetch feature information for ${layer.title}: ${errorMessage}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const downloadLayerMetadata = (layer: LayerDetails) => {
    const metadata = {
      name: layer.name,
      title: layer.title,
      abstract: layer.abstract,
      type: layer.type,
      bbox: layer.bbox,
      styles: layerStyles[layer.name],
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(metadata, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${layer.name}_metadata.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  const renderServiceMetadata = () => (
    <Card>
      <Card.Header>
        <Info size={18} className="me-2" />        Service Information
        {networkError && (
          <div className="ms-2 network-status-header">
            <div className="network-error-indicator"></div>
            <small>Network Issue</small>
          </div>
        )}
      </Card.Header>
      <Card.Body>        {networkError && (
          <Alert variant="danger" className="mb-3 network-error">
            <strong>Network Connection Problem:</strong> Unable to establish connection with GeoServer. 
            Please check your network connectivity and ensure the GeoServer service is accessible.
          </Alert>
        )}        {serviceMetadata && (
          <>
            <h5>{serviceMetadata.title || 'Unknown Service'}</h5>
            <p className="text-muted">{serviceMetadata.abstract || 'No description available'}</p>
            
            <div className="mt-3">
              <strong>Version:</strong> {serviceMetadata.version || 'Unknown'}<br/>
              <strong>Organization:</strong> {serviceMetadata.contact?.organization || 'Not specified'}<br/>
              <strong>Contact:</strong> {serviceMetadata.contact?.person || 'Not specified'} ({serviceMetadata.contact?.email || 'Not specified'})<br/>
              <strong>Fees:</strong> {serviceMetadata.fees || 'Not specified'}<br/>
              <strong>Access Constraints:</strong> {serviceMetadata.accessConstraints || 'Not specified'}
            </div>          </>        )}

        {/* Component Error Summary */}
        {(errorSummary.metadata || errorSummary.legends || errorSummary.styles) && (
          <Alert variant="warning" className="mt-3">
            <strong>Partial Data Load:</strong> Some components couldn't be loaded:
            <ul className="mb-0 mt-2">
              {errorSummary.metadata && <li>Service Metadata: {errorSummary.metadata}</li>}
              {errorSummary.legends && <li>Legend Images: {errorSummary.legends}</li>}
              {errorSummary.styles && <li>Style Information: {errorSummary.styles}</li>}
            </ul>
            <small className="text-muted d-block mt-2">
              The main layer information is still available. Check your network connection and try refreshing the service details.
            </small>
          </Alert>
        )}

        {networkError && (
          <hr className="network-error-line" />
        )}
      </Card.Body>
    </Card>
  );
  const renderMapThumbnails = () => (
    <div className="row">
      {layers.map((layer) => (
        <div key={layer.name} className="col-md-6 col-lg-4 mb-3">
          <Card 
            className={`thumbnail-card ${selectedLayerForDetails?.name === layer.name ? 'border-primary' : ''}`}
            style={{ cursor: 'pointer' }}
          >
            <Card.Img 
              variant="top" 
              src={generateMapThumbnail(layer)}
              alt={layer.title}
              style={{ height: '150px', objectFit: 'cover' }}
              onClick={() => {
                console.log(`Thumbnail clicked for layer: ${layer.name}`);
                setSelectedLayerForDetails({ ...layer, featureInfo: undefined });
                setActiveTab('features');
              }}
              onError={(e) => {
                (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==';
              }}
            />            <Card.Body>              <div className="d-flex justify-content-between align-items-start mb-2">
                <Card.Title className="h6 mb-0">{layer.title}</Card.Title>
                {layerStyles[layer.name]?.temporal?.hasTemporal && (
                  <Badge bg="info" className="badge-sm">
                    <i className="fas fa-clock me-1"></i>
                    Temporal
                  </Badge>
                )}
              </div>
              <div className="mb-2">
                <Badge bg="secondary" className="me-1">{layer.type || 'Unknown'}</Badge>
                {layerStyles[layer.name]?.temporal?.hasTemporal && (
                  <small className="text-muted">
                    {layerStyles[layer.name]?.temporal?.availableTimes?.length || 0} time instances
                  </small>
                )}
              </div>
              <div className="d-flex gap-1">
                <Button 
                  size="sm" 
                  variant="outline-primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    getFeatureInfo(layer);
                  }}
                  title="View Features"
                  disabled={loading}
                >
                  {loading && selectedLayerForDetails?.name === layer.name ? (
                    <Spinner animation="border" size="sm" />
                  ) : (
                    <Eye size={14} />
                  )}
                </Button>
                <Button 
                  size="sm" 
                  variant="outline-secondary"
                  onClick={(e) => {
                    e.stopPropagation();
                    downloadLayerMetadata(layer);
                  }}
                  title="Download Metadata"
                >
                  <Download size={14} />
                </Button>
                <Button 
                  size="sm" 
                  variant="outline-info"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleShowLayerMetadata(layer);
                  }}
                  title="View Metadata"
                >
                  <FileText size={14} />
                </Button>
              </div>
            </Card.Body>
          </Card>
        </div>
      ))}
    </div>
  );
  const renderFeatureInfo = () => (
    <div>
      {selectedLayerForDetails ? (
        <Card>
          <Card.Header>
            <MapPin size={18} className="me-2" />
            Feature Information: {selectedLayerForDetails.title}
            <Badge bg="info" className="ms-2">
              {selectedLayerForDetails.name}
            </Badge>
          </Card.Header>
          <Card.Body>
            {selectedLayerForDetails.featureInfo ? (
              <div className="feature-info-container">
                <Alert variant="success" className="mb-3">
                  <strong>Features Found:</strong> {selectedLayerForDetails.featureInfo.features?.length || 0} features
                  {selectedLayerForDetails.featureInfo.totalFeatures && (
                    <span> (showing first 10 of {selectedLayerForDetails.featureInfo.totalFeatures} total)</span>
                  )}
                </Alert>
                
                {selectedLayerForDetails.featureInfo.features?.length > 0 ? (
                  <div>
                    <h6>Feature Details:</h6>
                    <pre style={{ maxHeight: '400px', overflow: 'auto', fontSize: '12px' }}>
                      {JSON.stringify(selectedLayerForDetails.featureInfo, null, 2)}
                    </pre>
                  </div>
                ) : (
                  <Alert variant="info">
                    No features found for this layer. The layer may be empty or the query parameters may need adjustment.
                  </Alert>
                )}
              </div>
            ) : selectedLayerForDetails.featureInfo === null ? (
              <Alert variant="warning">
                <strong>Feature Info Unavailable:</strong> Unable to retrieve feature information for this layer. 
                This could be due to network issues or the layer may not support feature queries.
              </Alert>
            ) : (
              <Alert variant="info">
                <strong>Click "View Features" to load feature information for this layer.</strong>
                <div className="mt-2">
                  <Button 
                    size="sm" 
                    variant="primary"
                    onClick={() => getFeatureInfo(selectedLayerForDetails)}
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Spinner animation="border" size="sm" className="me-2" />
                        Loading Features...
                      </>
                    ) : (
                      <>
                        <Eye size={14} className="me-1" />
                        View Features
                      </>
                    )}
                  </Button>
                </div>
              </Alert>
            )}
          </Card.Body>
        </Card>
      ) : (
        <Alert variant="info">
          <h5>Select a Layer to View Features</h5>
          <p>Click on any layer from the <strong>Map Previews</strong> tab to view its feature information here.</p>
          <p>You can also click the <Eye size={14} className="mx-1" /> button next to any layer thumbnail to load feature details.</p>
        </Alert>
      )}
    </div>
  );
  const renderLegendImages = () => (
    <div className="row">
      {layers.map((layer) => (
        <div key={layer.name} className="col-md-6 col-lg-4 mb-3">
          <Card>            <Card.Header className="text-center d-flex justify-content-between align-items-center">
              <small>{layer.title}</small>
              {layerStyles[layer.name]?.temporal?.hasTemporal && (
                <Badge bg="info" className="badge-sm">
                  <i className="fas fa-clock me-1"></i>
                  Temporal
                </Badge>
              )}
            </Card.Header>
            <div className="legend-container">
              {legendImages[layer.name] ? (
                <img 
                  src={legendImages[layer.name]}
                  alt={`Legend for ${layer.title}`}
                  className="legend-image"
                />
              ) : (
                <span className="text-muted">No legend available</span>
              )}
            </div>
            <Card.Body>
              <div className="d-flex justify-content-between align-items-center">
                <Button 
                  size="sm" 
                  variant="outline-primary" 
                  className="flex-grow-1"
                  onClick={() => downloadLayerMetadata(layer)}
                >
                  <Download size={14} className="me-1" />
                  Download Info
                </Button>
                {layerStyles[layer.name]?.temporal?.hasTemporal && (
                  <div className="ms-2">
                    <small className="text-muted">
                      {layerStyles[layer.name]?.temporal?.availableTimes?.length || 0} times
                    </small>
                  </div>
                )}
              </div>
            </Card.Body>
          </Card>
        </div>
      ))}
    </div>
  );
  const renderStyleDefinitions = () => {
    const layersWithStyles = layers.filter(layer => layerStyles[layer.name]);
    const totalStyles = layersWithStyles.reduce((total, layer) => 
      total + (layerStyles[layer.name]?.styles?.length || 0), 0
    );
    
    return (
      <div>
        {/* Styles Summary */}
        <Card className="mb-3">
          <Card.Header>
            <FileText size={18} className="me-2" />
            Style Summary
          </Card.Header>
          <Card.Body>
            <div className="row text-center">
              <div className="col-4">
                <h5 className="mb-1">{layers.length}</h5>
                <small className="text-muted">Total Layers</small>
              </div>
              <div className="col-4">
                <h5 className="mb-1">{layersWithStyles.length}</h5>
                <small className="text-muted">With Styles</small>
              </div>
              <div className="col-4">
                <h5 className="mb-1">{totalStyles}</h5>
                <small className="text-muted">Total Styles</small>
              </div>
            </div>
            
            {layersWithStyles.length === 0 && (
              <Alert variant="info" className="mt-3 mb-0">
                <strong>No style information available.</strong> 
                Style definitions may not be accessible or layers may use default styling.
              </Alert>
            )}
          </Card.Body>
        </Card>

        {/* Layer Styles Accordion */}
        <Accordion>
          {layers.map((layer, index) => (
            <Accordion.Item key={layer.name} eventKey={index.toString()}>              <Accordion.Header>
                <Layers size={16} className="me-2" />
                {layer.title}
                <div className="ms-auto d-flex align-items-center">
                  {layerStyles[layer.name]?.temporal?.hasTemporal && (
                    <Badge bg="info" className="me-2">
                      <i className="fas fa-clock me-1"></i>
                      Temporal
                    </Badge>
                  )}
                  {layerStyles[layer.name] && (
                    <Badge bg="success" className="me-2">
                      {Array.isArray(layerStyles[layer.name].styles) ? layerStyles[layer.name].styles.length : 0} style{Array.isArray(layerStyles[layer.name].styles) && layerStyles[layer.name].styles.length !== 1 ? 's' : ''}
                    </Badge>
                  )}
                </div>
              </Accordion.Header>
          <Accordion.Body>
            <div className="mb-3">
              <div className="metadata-field">
                <span className="metadata-label">Layer Name:</span>
                <span className="metadata-value">{layer.name}</span>
              </div>
              <div className="metadata-field">
                <span className="metadata-label">Type:</span>
                <span className="metadata-value">{layer.type || 'Unknown'}</span>
              </div>
              <div className="metadata-field">
                <span className="metadata-label">Abstract:</span>
                <span className="metadata-value">{layer.abstract || 'No description available'}</span>
              </div>
            </div>
            
            {layer.bbox && (
              <div className="mb-3">
                <strong>Bounding Box:</strong>
                <div className="bbox-display">
                  Min X: {layer.bbox.minx}<br/>
                  Min Y: {layer.bbox.miny}<br/>
                  Max X: {layer.bbox.maxx}<br/>
                  Max Y: {layer.bbox.maxy}<br/>
                  SRS: {layer.bbox.SRS || 'EPSG:4326'}
                </div>
              </div>
            )}            {layerStyles[layer.name] && (
              <div className="mb-3">
                <strong>Style Information:</strong>
                <div className="styles-info-container mt-2">
                  <div className="metadata-field">
                    <span className="metadata-label">Queryable:</span>
                    <span className="metadata-value">
                      <Badge bg={layerStyles[layer.name].queryable ? "success" : "secondary"}>
                        {layerStyles[layer.name].queryable ? "Yes" : "No"}
                      </Badge>
                    </span>
                  </div>
                  <div className="metadata-field">
                    <span className="metadata-label">Supported Formats:</span>
                    <span className="metadata-value">
                      {layerStyles[layer.name].supportedFormats?.join(', ') || 'Unknown'}
                    </span>
                  </div>
                  
                  {/* Temporal Information Display */}
                  <div className="metadata-field">
                    <span className="metadata-label">Temporal Support:</span>
                    <span className="metadata-value">
                      <Badge bg={layerStyles[layer.name].temporal?.hasTemporal ? "info" : "secondary"}>
                        {layerStyles[layer.name].temporal?.hasTemporal ? "Yes" : "No"}
                      </Badge>
                    </span>
                  </div>
                  
                  {layerStyles[layer.name].temporal?.hasTemporal && (
                    <div className="temporal-info-section mt-3">
                      <strong className="d-block mb-2">
                        <i className="fas fa-clock me-2"></i>
                        Temporal Information:
                      </strong>
                      <div className="temporal-details bg-light p-3 rounded">
                        {layerStyles[layer.name].temporal.temporalExtent && (
                          <div className="row mb-2">
                            <div className="col-sm-6">
                              <small className="text-muted">Start Date:</small>
                              <div className="fw-bold text-success">
                                {new Date(layerStyles[layer.name].temporal.temporalExtent.start).toLocaleDateString()}
                              </div>
                            </div>
                            <div className="col-sm-6">
                              <small className="text-muted">End Date:</small>
                              <div className="fw-bold text-danger">
                                {new Date(layerStyles[layer.name].temporal.temporalExtent.end).toLocaleDateString()}
                              </div>
                            </div>
                          </div>
                        )}
                        
                        {layerStyles[layer.name].temporal.defaultTime && (
                          <div className="mb-2">
                            <small className="text-muted">Default Time:</small>
                            <div className="fw-bold">
                              {new Date(layerStyles[layer.name].temporal.defaultTime).toLocaleDateString()}
                            </div>
                          </div>
                        )}
                        
                        {layerStyles[layer.name].temporal.units && (
                          <div className="mb-2">
                            <small className="text-muted">Time Units:</small>
                            <Badge bg="outline-info" className="ms-2">
                              {layerStyles[layer.name].temporal.units}
                            </Badge>
                          </div>
                        )}
                        
                        {layerStyles[layer.name].temporal.availableTimes && (
                          <div className="mb-2">
                            <small className="text-muted">Available Time Instances:</small>
                            <div className="fw-bold text-primary">
                              {layerStyles[layer.name].temporal.availableTimes.length} time points
                            </div>
                            <small className="text-muted">
                              (Span: {layerStyles[layer.name].temporal.temporalExtent ? 
                                Math.round((new Date(layerStyles[layer.name].temporal.temporalExtent.end).getTime() - 
                                          new Date(layerStyles[layer.name].temporal.temporalExtent.start).getTime()) / 
                                          (1000 * 60 * 60 * 24 * 365.25)) : 0} years)
                            </small>
                          </div>
                        )}
                        
                        <div className="mt-2">
                          <Badge bg="success" className="me-2">
                            <i className="fas fa-calendar-alt me-1"></i>
                            Temporal Layer
                          </Badge>
                          <small className="text-muted">
                            This layer supports time-based queries for historical data analysis.
                          </small>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {layerStyles[layer.name].styles && layerStyles[layer.name].styles.length > 0 && (
                    <div className="mt-3">
                      <strong className="d-block mb-2">Available Styles:</strong>
                      {layerStyles[layer.name].styles.map((style: any, styleIndex: number) => (
                        <div key={styleIndex} className="style-item mb-3 p-2 border rounded">
                          <div className="d-flex justify-content-between align-items-start mb-2">
                            <div>
                              <strong>{style.title || style.name}</strong>
                              {style.abstract && (
                                <p className="text-muted small mb-1">{style.abstract}</p>
                              )}
                            </div>
                            {style.legendURL && (
                              <img 
                                src={style.legendURL}
                                alt={`Legend for ${style.title || style.name}`}
                                style={{ 
                                  maxWidth: `${Math.min(parseInt(style.legendWidth) || 20, 100)}px`,
                                  maxHeight: `${Math.min(parseInt(style.legendHeight) || 20, 100)}px`,
                                  objectFit: 'contain'
                                }}
                                onError={(e) => {
                                  (e.target as HTMLImageElement).style.display = 'none';
                                }}
                              />
                            )}
                          </div>
                          <div className="style-details">
                            <small className="text-muted">
                              Style Name: {style.name} | 
                              Format: {style.legendFormat} | 
                              Size: {style.legendWidth}x{style.legendHeight}
                            </small>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  <details className="mt-3">
                    <summary className="cursor-pointer text-primary">
                      <small>View Raw JSON Data</small>
                    </summary>
                    <div className="bbox-display mt-2">
                      <pre style={{ fontSize: '10px', maxHeight: '200px', overflow: 'auto' }}>
                        {JSON.stringify(layerStyles[layer.name], null, 2)}
                      </pre>
                    </div>
                  </details>
                </div>
              </div>
            )}
            
            <div className="d-flex gap-2">
              <Button 
                size="sm" 
                variant="outline-primary"
                onClick={() => getFeatureInfo(layer)}
              >
                <FileText size={14} className="me-1" />
                Get Features
              </Button>
              <Button 
                size="sm" 
                variant="outline-secondary"
                onClick={() => downloadLayerMetadata(layer)}
              >
                <Download size={14} className="me-1" />
                Export
              </Button>
            </div>          </Accordion.Body>
        </Accordion.Item>
      ))}
    </Accordion>
      </div>
    );
  };

  const handleShowLayerMetadata = async (layer: LayerDetails) => {
    setLoading(true);
    try {
      const metadata = await fetchLayerMetadata(layer.name);
      setLayerMetadata(prev => ({ ...prev, [layer.name]: metadata }));
      setSelectedLayerForDetails(layer);
      setActiveTab('metadata');
    } catch (error) {
      setLayerMetadata(prev => ({ ...prev, [layer.name]: null }));
      setSelectedLayerForDetails(layer);
      setActiveTab('metadata');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className="service-details section">
        <Button
          className="action-button w-100"
          onClick={() => setShowModal(true)}
        >
          <Info size={18} className="me-2" />
          View Service Details
        </Button>
      </div>

      <Modal 
        show={showModal} 
        onHide={() => setShowModal(false)} 
        size="xl" 
        centered
      >
        <Modal.Header closeButton className="app-header-blue">
          <Modal.Title>
            <Info size={24} className="me-2 header-icon" />
            WMS Service Details
          </Modal.Title>
        </Modal.Header>        <Modal.Body style={{ maxHeight: '70vh', overflowY: 'auto' }}>
          {loading ? (
            <div className="loading-container">
              <Spinner animation="border" role="status">
                <span className="visually-hidden">Loading...</span>
              </Spinner>
              <div className="loading-text">Loading service information...</div>
            </div>
          ) : (
            <>              {error && (
                <Alert variant={networkError ? "danger" : "warning"} className={`mb-3 ${networkError ? 'network-error' : ''}`}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    {networkError && (
                      <div className="network-error-indicator-large"></div>
                    )}
                    <div>
                      {networkError && <strong>Network Connection Error</strong>}
                      <div>{error}</div>
                    </div>
                  </div>
                </Alert>
              )}
              <Tabs 
                activeKey={activeTab} 
                onSelect={(k) => setActiveTab(k || 'metadata')}
                className="mb-3"
            >
              <Tab eventKey="metadata" title={
                <span><Info size={16} className="me-1" />Metadata</span>
              }>
                {renderServiceMetadata()}
              </Tab>
              
              <Tab eventKey="thumbnails" title={
                <span><Image size={16} className="me-1" />Map Previews</span>
              }>
                {renderMapThumbnails()}
              </Tab>
              
              <Tab eventKey="features" title={
                <span><MapPin size={16} className="me-1" />Feature Info</span>
              }>
                {renderFeatureInfo()}
              </Tab>
              
              <Tab eventKey="legends" title={
                <span><Layers size={16} className="me-1" />Legends</span>
              }>
                {renderLegendImages()}
              </Tab>
                <Tab eventKey="styles" title={
                <span><FileText size={16} className="me-1" />Styles</span>
              }>
                {renderStyleDefinitions()}
              </Tab>
            </Tabs>
            
            {selectedLayerForDetails && activeTab === 'metadata' && (
              <Card className="mb-3">
                <Card.Header>
                  <FileText size={18} className="me-2" />
                  Layer Metadata: {selectedLayerForDetails.title}
                </Card.Header>
                <Card.Body>
                  {layerMetadata[selectedLayerForDetails.name] ? (
                    <div>
                      <div className="metadata-field"><span className="metadata-label">Name:</span> <span className="metadata-value">{layerMetadata[selectedLayerForDetails.name].name}</span></div>
                      <div className="metadata-field"><span className="metadata-label">Title:</span> <span className="metadata-value">{layerMetadata[selectedLayerForDetails.name].title}</span></div>
                      <div className="metadata-field"><span className="metadata-label">Abstract:</span> <span className="metadata-value">{layerMetadata[selectedLayerForDetails.name].abstract}</span></div>
                      {layerMetadata[selectedLayerForDetails.name].bbox && (
                        <div className="metadata-field"><span className="metadata-label">Bounding Box:</span> <span className="metadata-value">{JSON.stringify(layerMetadata[selectedLayerForDetails.name].bbox)}</span></div>
                      )}
                      <div className="metadata-field"><span className="metadata-label">CRS:</span> <span className="metadata-value">{layerMetadata[selectedLayerForDetails.name].crs}</span></div>
                      <div className="metadata-field"><span className="metadata-label">Queryable:</span> <span className="metadata-value">{layerMetadata[selectedLayerForDetails.name].queryable ? 'Yes' : 'No'}</span></div>
                      <div className="metadata-field"><span className="metadata-label">Opaque:</span> <span className="metadata-value">{layerMetadata[selectedLayerForDetails.name].opaque ? 'Yes' : 'No'}</span></div>
                      <div className="metadata-field"><span className="metadata-label">Cascaded:</span> <span className="metadata-value">{layerMetadata[selectedLayerForDetails.name].cascaded}</span></div>
                      {layerMetadata[selectedLayerForDetails.name].keywords && layerMetadata[selectedLayerForDetails.name].keywords.length > 0 && (
                        <div className="metadata-field"><span className="metadata-label">Keywords:</span> <span className="metadata-value">{layerMetadata[selectedLayerForDetails.name].keywords.join(', ')}</span></div>
                      )}
                      {/* Add more fields as needed */}
                    </div>
                  ) : (
                    <div>No metadata available for this layer.</div>
                  )}
                </Card.Body>
              </Card>
            )}
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default ServiceDetails;