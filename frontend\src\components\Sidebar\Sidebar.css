.sidebar {
  padding: 0;
  height: 100%;
  overflow-y: auto;
  background-color: #1e3a5f; /* Darker blue background to match design document */
  color: white;
  /* Remove fixed width to let container control sizing */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  font-family: Arial, sans-serif;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  /* Remove custom scrollbar styling that might interfere */
}

.sidebar.collapsed {
  width: 60px; /* Slightly wider when collapsed for better icon visibility */
  min-width: 60px;
  padding: 0;
  overflow: hidden;
}

.sidebar-toggle {
  background-color: transparent;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  transition: background 0.2s;
}

.sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-header {
  padding: 15px 20px;
  background-color: #1e4080; /* Darker blue for header */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-title {
  font-size: var(--font-size-lg, 1.125rem); /* Use responsive font size instead of fixed pixels */
  font-weight: bold;
  margin: 0;
  color: white;
  letter-spacing: 0.5px;
}

.sidebar-content {
  padding: 16px;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: white;
}

.section-title-container {
  margin-bottom: 15px;
}

.section-loading {
  display: flex;
  align-items: center;
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(13, 110, 253, 0.1);
  border-radius: 6px;
  border-left: 3px solid #0d6efd;
}

.section-error {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(220, 53, 69, 0.1);
  border-radius: 6px;
  border-left: 3px solid #dc3545;
}

.form-group {
  margin-bottom: 15px;
}

.date-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: white;
}

.search-button, .action-button {
  background-color: #1e4080;
  border: none;
  width: 100%;
  margin-top: 8px;
  transition: background-color 0.3s ease;
  color: white;
}

.search-button:hover, .action-button:hover {
  background-color: #0f2d5c;
  color: white;
}

.region-input {
  width: 100%;
  padding: 6px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  margin-top: 5px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.region-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.region-input:focus {
  border-color: rgba(255, 255, 255, 0.5);
  background-color: rgba(255, 255, 255, 0.15);
  outline: none;
}

.layer-checkbox {
  margin-bottom: 8px;
}

/* Suggested Date Ranges Styles */
.suggested-ranges {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.suggested-ranges-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.suggested-range-btn {
  transition: all 0.2s ease;
  border-width: 1px;
  font-weight: 500;
  white-space: nowrap;
}

.suggested-range-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,123,255,0.25);
}

.suggested-range-btn:active {
  transform: translateY(0);
}

/* More Layers Dropdown Styles */
.more-layers-section {
  margin-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 1rem;
}

.more-layers-toggle {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  color: white;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.more-layers-toggle:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.more-layers-content {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-top: none;
  border-radius: 0 0 6px 6px;
  padding: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
}

.more-layers-item {
  padding: 0.4rem 0.6rem;
  margin: 0.2rem 0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: block;
  font-size: 0.9rem;
  border-left: 3px solid transparent;
  color: white;
}

.more-layers-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-left-color: #ffffff;
  transform: translateX(2px);
}

.more-layers-item.temporal {
  border-left-color: #17a2b8;
  background-color: rgba(23, 162, 184, 0.2);
}

.more-layers-item.temporal:hover {
  background-color: rgba(23, 162, 184, 0.3);
  border-left-color: #17a2b8;
}

.layer-info-badge {
  font-size: 0.7rem;
  padding: 0.15rem 0.4rem;
  border-radius: 10px;
  margin-left: 0.5rem;
}

.toggle-icon {
  transition: transform 0.2s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

/* Query Button Styles - Simplified flat design */
.query-button {
  background: #1e4080 !important;
  border: none !important;
  border-radius: 0 !important;
  font-weight: 600 !important;
  transition: background-color 0.2s ease !important;
  box-shadow: none !important;
  color: white !important;
}

.query-button:hover {
  background: #0f2d5c !important;
  color: white !important;
}

.query-button:active {
  background: #0f2d5c !important;
}

/* Data Actions Improvements - Simplified */
.data-actions .action-button {
  margin-bottom: 0.5rem;
  transition: background-color 0.2s ease;
}

.data-actions .action-button:hover {
  background-color: #0f2d5c;
}

.info-section {
  animation: fadeInUp 0.3s ease-in-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Bootstrap form control overrides for dark theme */
.sidebar .form-label {
  color: white;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sidebar .form-control {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  color: white;
}

.sidebar .form-control::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.sidebar .form-control:focus {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: none;
  color: white;
}

.sidebar .form-select {
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.8);
  color: #333;
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: 4px;
  padding: 8px 12px;
}

.sidebar .form-select:focus {
  background-color: white;
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  color: #333;
}

.sidebar .form-select option {
  color: #333;
  background-color: white;
}

.sidebar .form-check-input {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.sidebar .form-check-input:checked {
  background-color: #1e4080;
  border-color: #1e4080;
}

.sidebar .form-check-label {
  color: white;
}

.sidebar .btn-outline-primary {
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.sidebar .btn-outline-primary:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
}

.sidebar .btn-primary {
  background-color: #1e4080;
  border-color: #1e4080;
}

.sidebar .btn-primary:hover {
  background-color: #0f2d5c;
  border-color: #0f2d5c;
}

.sidebar .text-muted {
  color: rgba(255, 255, 255, 0.7) !important;
}

.sidebar .badge {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Custom Switch Styling */
.sidebar .custom-switch .form-check-input {
  background-color: #6c757d; /* Gray color when off */
  border-color: #6c757d;
  width: 3rem;
  height: 1.5rem;
}

.sidebar .custom-switch .form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

.sidebar .custom-switch .form-check-input:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Nested Section Styling */
.nested-section {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  overflow: hidden;
}

.nested-section-header {
  background-color: rgba(30, 64, 128, 0.8);
  padding: 8px 12px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  justify-content: space-between;
}

.nested-section-header.clickable {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.nested-section-header.clickable:hover {
  background-color: rgba(30, 64, 128, 1);
}

.nested-section-icon {
  font-size: 0.9rem;
  margin-right: 8px;
}

.nested-section-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  flex-grow: 1;
}

.nested-section-toggle {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 8px;
  transition: transform 0.2s ease;
}

.nested-section-header.clickable:hover .nested-section-toggle {
  color: white;
}

.nested-section-body {
  padding: 12px;
}

/* Hover effect for nested sections */
.nested-section:hover {
  background-color: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
}

/* Dropdown Menu Styling - Simplified */
.sidebar .dropdown-menu {
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  z-index: 1050; /* High z-index to appear above cards */
  max-height: 200px;
  overflow-y: auto;
}

.sidebar .dropdown-item {
  color: #333;
  padding: 8px 16px;
  font-size: 0.9rem;
}

.sidebar .dropdown-item:hover {
  background-color: #1e4080;
  color: white;
}

.sidebar .dropdown-item:focus {
  background-color: #1e4080;
  color: white;
}

/* Sidebar Card Styling - Simplified flat design */
.sidebar-card {
  background-color: transparent;
  border: none;
  border-radius: 0;
  margin-bottom: 8px;
  overflow: visible; /* Allow dropdowns to extend outside card */
  position: relative; /* For proper z-index stacking */
}

.sidebar-card:hover {
  background-color: transparent;
}

.sidebar-card-header {
  background-color: #1e4080;
  padding: 12px 16px;
  border-bottom: none;
  border-radius: 0;
}

.sidebar-card-title {
  margin: 0;
  font-size: 14px;
  font-weight: 700;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sidebar-card-body {
  padding: 12px 16px;
  position: relative; /* For dropdown positioning */
  z-index: 10; /* Ensure dropdowns appear above other cards */
}

/* Custom Accordion Styling - Simplified flat design */
.sidebar .custom-accordion-section {
  overflow: hidden; /* Contain accordion content */
  margin-bottom: 0.8rem; /* Add consistent spacing between sections */
}

.sidebar .custom-accordion-header {
  background-color: #1e4080 !important;
  color: white !important;
  border: none !important;
  border-radius: 0 !important;
  transition: background-color 0.2s ease !important;
  font-size: var(--font-size-sm, 0.9rem) !important; /* Responsive font size */
}

.sidebar .custom-accordion-header:hover {
  background-color: #0f2d5c !important;
}

/* Data Layer Accordion Headers - Sidebar blue with semi-transparent white overlay */
.sidebar .data-layer-accordion-header {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

.sidebar .data-layer-accordion-header:hover {
  background-color: rgba(255, 255, 255, 0.3) !important;
}

/* When accordion is open, keep flat design */
.sidebar .custom-accordion-section:has(.custom-accordion-body) .custom-accordion-header {
  border-radius: 0px !important;
}

.sidebar .custom-accordion-body {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border: none !important;
  border-radius: 0 !important;
  color: white;
  overflow: hidden; /* Prevent content from overflowing */
  word-wrap: break-word; /* Break long words */
  box-sizing: border-box; /* Include padding in width calculation */
  font-size: var(--font-size-xs, 0.85rem) !important; /* Responsive font size */
}

/* Responsive adjustments - Remove fixed width since container controls it */
@media (max-width: 1200px) {
  .sidebar {
    /* Width controlled by parent container */
  }
}

/* Mobile Sidebar Overlay */
@media (max-width: 767px) {
  .sidebar {
    position: fixed;
    top: 56px; /* Below navbar */
    left: 0;
    width: 100vw;
    max-width: none;
    min-width: auto;
    height: calc(100vh - 56px);
    height: calc(100dvh - 56px); /* Dynamic viewport height */
    z-index: var(--z-modal, 1050);
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .sidebar-content {
    padding: 1rem;
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .sidebar-card {
    margin-bottom: 0.75rem;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .sidebar-card-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .sidebar-card-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0;
  }

  .sidebar-card-body {
    padding: 1rem;
  }

  .app-title {
    font-size: 1.1rem;
    font-weight: 600;
  }

  /* Touch-friendly form controls */
  .form-control,
  .form-select {
    min-height: 44px;
    font-size: 1rem;
    padding: 0.75rem;
    border-radius: 8px;
  }

  /* Touch-friendly buttons */
  .btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border-radius: 8px;
  }

  /* Touch-friendly checkboxes */
  .form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
  }

  .form-check-label {
    font-size: 1rem;
    line-height: 1.5;
    padding-left: 0.5rem;
  }
}

/* Mobile Backdrop */
.sidebar-backdrop {
  display: none;
}

@media (max-width: 767px) {
  .sidebar-backdrop {
    position: fixed;
    top: 56px;
    left: 0;
    width: 100vw;
    height: calc(100vh - 56px);
    height: calc(100dvh - 56px);
    background-color: rgba(0, 0, 0, 0.5);
    z-index: var(--z-modal-backdrop, 1040);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .sidebar-backdrop.show {
    display: block;
    opacity: 1;
    visibility: visible;
  }
}

/* Extra small screens */
@media (max-width: 479px) {
  .sidebar-content {
    padding: 0.75rem;
  }

  .sidebar-card {
    margin-bottom: 0.5rem;
    border-radius: 6px;
  }

  .sidebar-card-header {
    padding: 0.625rem 0.75rem;
  }

  .sidebar-card-title {
    font-size: 0.85rem;
  }

  .sidebar-card-body {
    padding: 0.75rem;
  }

  .app-title {
    font-size: 1rem;
  }

  /* Smaller form controls for very small screens */
  .form-control,
  .form-select {
    min-height: 40px;
    font-size: 0.9rem;
    padding: 0.625rem;
  }

  .btn {
    min-height: 40px;
    padding: 0.625rem 0.75rem;
    font-size: 0.9rem;
  }

  .form-check-input {
    width: 1.1rem;
    height: 1.1rem;
  }

  .form-check-label {
    font-size: 0.9rem;
  }
}

/* Landscape orientation adjustments */
@media (max-width: 767px) and (orientation: landscape) {
  .sidebar {
    width: 60vw;
    max-width: 400px;
  }

  .sidebar-content {
    padding: 0.75rem;
  }

  .sidebar-card {
    margin-bottom: 0.5rem;
  }

  .sidebar-card-header {
    padding: 0.5rem 0.75rem;
  }

  .sidebar-card-body {
    padding: 0.75rem;
  }
}

/* Layer item container with info badges - Compact spacing */
.layer-item-with-info {
  margin-bottom: 0.25rem;
}

.layer-checkbox-container {
  display: flex;
  align-items: center;
}

.layer-label-with-info {
  display: flex;
  align-items: center;
  color: white !important;
  margin-bottom: 0 !important;
}

.layer-info-badge {
  background-color: #1e4080 !important;
  border: none !important;
  transition: all 0.2s ease !important;
  border-radius: 50% !important;
  width: 16px !important;
  height: 16px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  font-size: 0.6rem !important;
  line-height: 1 !important;
}

.layer-info-badge:hover {
  background-color: #0f2d5c !important;
  transform: scale(1.15) !important;
  box-shadow: 0 2px 6px rgba(30, 64, 128, 0.4) !important;
}