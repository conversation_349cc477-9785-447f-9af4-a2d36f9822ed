import React from 'react';
import { <PERSON>, But<PERSON> } from 'react-bootstrap';
import { useTemporalMetadata, SAMPLE_TEMPORAL_METADATA } from './useTemporalMetadata';
import { formatDate } from '../../utils/temporalUtils';

interface TemporalDemoProps {
  metadataString?: string;
}

const TemporalDemo: React.FC<TemporalDemoProps> = ({ 
  metadataString = SAMPLE_TEMPORAL_METADATA
}) => {
  const { temporalInfo, currentTime, handleTimeChange } = useTemporalMetadata(metadataString);
  
  if (!temporalInfo) {
    return (
      <Card className="p-3 mb-3">
        <Card.Title>Temporal Demo</Card.Title>
        <Card.Body>
          <p className="text-muted">No temporal information available.</p>
        </Card.Body>
      </Card>
    );
  }
  
  return (
    <Card className="p-3 mb-3">
      <Card.Title>Temporal Layer Information</Card.Title>
      <Card.Body>
        <div className="mb-3">
          <h6>Current Time:</h6>
          <code className="p-2 bg-light d-block">
            {currentTime || 'Not set'}
          </code>
          <p className="mt-1">
            <small className="text-muted">Formatted: {currentTime ? formatDate(currentTime) : 'N/A'}</small>
          </p>
        </div>
        
        <h6>Available Temporal Information:</h6>
        <ul className="list-unstyled">
          <li><strong>Dimension:</strong> {temporalInfo.name}</li>
          {temporalInfo.units && <li><strong>Units:</strong> {temporalInfo.units}</li>}
          {temporalInfo.default && <li><strong>Default Time:</strong> {temporalInfo.default}</li>}
          {temporalInfo.values && (
            <li>
              <strong>Available Times:</strong> {temporalInfo.values.length} time points
              <Button 
                variant="link" 
                size="sm" 
                className="p-0 ms-2"
                onClick={() => console.log('All available times:', temporalInfo.values)}
              >
                View All
              </Button>
            </li>
          )}
        </ul>
        
        <div className="bg-light p-3 rounded mb-3">
          <h6>Usage Instructions:</h6>
          <p className="small mb-0">
            This temporal layer contains time series data spanning from 1991 to 1992. 
            Use the time slider in the navigation bar to view data for different time periods. 
            Clicking "Play" will animate through the time series automatically.
          </p>
        </div>
        
        <div className="d-grid">
          <Button 
            variant="primary"
            onClick={() => {
              if (temporalInfo.values && temporalInfo.values.length > 0) {
                // Demonstrate time change by picking a random time from available values
                const randomIndex = Math.floor(Math.random() * temporalInfo.values.length);
                handleTimeChange(temporalInfo.values[randomIndex]);
              }
            }}
          >
            Select Random Time
          </Button>
        </div>
      </Card.Body>
    </Card>
  );
};

export default TemporalDemo;
