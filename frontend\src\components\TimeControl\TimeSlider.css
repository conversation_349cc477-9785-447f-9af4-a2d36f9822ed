.time-slider-container {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-right: 10px;
  max-width: 300px;
  min-width: 280px;
}

.time-slider-header {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  font-weight: 600;
  color: #0a4273;
  margin-bottom: 6px;
}

.time-slider-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.time-navigation {
  display: flex;
  gap: 4px;
}

.time-nav-btn, .time-play-btn {
  padding: 2px 6px;
  background-color: #f8f9fa;
  border-color: #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-play-btn {
  background-color: #e9f2ff;
  color: #0a4273;
}

.time-display {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #f0f9ff;
  color: #0a4273;
  display: flex;
  align-items: center;
}

.current-time {
  display: flex;
  align-items: center;
  cursor: help;
}

.time-slider-wrapper {
  margin-bottom: 6px;
}

.time-slider {
  height: 4px;
}

.time-slider::-webkit-slider-thumb {
  background: #0a4273;
}

.time-slider::-moz-range-thumb {
  background: #0a4273;
}

.time-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.7rem;
  color: #6c757d;
  margin-top: 2px;
}

.time-speed-control {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 6px;
  font-size: 0.75rem;
}

.speed-select {
  font-size: 0.75rem;
  padding: 0 4px;
  height: auto;
  width: auto;
}

@media (max-width: 768px) {
  .time-slider-container {
    max-width: none;
    width: 100%;
    margin-right: 0;
    margin-bottom: 8px;
  }
}
