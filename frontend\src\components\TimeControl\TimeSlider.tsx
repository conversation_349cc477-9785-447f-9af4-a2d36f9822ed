import React, { useState, useEffect } from 'react';
import { Button, Form, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { Clock, Play, Pause, SkipForward, SkipBack, Calendar } from 'lucide-react';
import './TimeSlider.css';

export interface TemporalInfo {
  name: string;
  units?: string;
  default?: string;
  extent?: string;
  values?: string[];
}

interface TimeSliderProps {
  temporalInfo: TemporalInfo;
  onTimeChange: (time: string) => void;
  isVisible?: boolean;
}

const TimeSlider: React.FC<TimeSliderProps> = ({ 
  temporalInfo, 
  onTimeChange,
  isVisible = false
}) => {
  // Parse the extent string to get array of time values
  const [timeValues, setTimeValues] = useState<string[]>([]);
  const [currentTimeIndex, setCurrentTimeIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playSpeed, setPlaySpeed] = useState(1000); // milliseconds
  
  useEffect(() => {
    if (temporalInfo.values && temporalInfo.values.length > 0) {
      setTimeValues(temporalInfo.values);
    } else if (temporalInfo.extent) {
      // Parse comma-separated extent values
      const extentValues = temporalInfo.extent.split(',');
      setTimeValues(extentValues);
    } else {
      setTimeValues([]);
    }
  }, [temporalInfo]);

  useEffect(() => {
    // Find default time index
    if (temporalInfo.default && timeValues.length > 0) {
      const defaultIndex = timeValues.findIndex(time => time === temporalInfo.default);
      if (defaultIndex >= 0) {
        setCurrentTimeIndex(defaultIndex);
      }
    }
  }, [timeValues, temporalInfo.default]);

  // Apply time change when index changes
  useEffect(() => {
    if (timeValues.length > 0 && currentTimeIndex >= 0 && currentTimeIndex < timeValues.length) {
      onTimeChange(timeValues[currentTimeIndex]);
    }
  }, [currentTimeIndex, timeValues, onTimeChange]);

  // Handle play/pause animation
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    
    if (isPlaying && timeValues.length > 0) {
      interval = setInterval(() => {
        setCurrentTimeIndex(prevIndex => {
          // Loop back to start if we reach the end
          const nextIndex = prevIndex + 1 >= timeValues.length ? 0 : prevIndex + 1;
          return nextIndex;
        });
      }, playSpeed);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isPlaying, timeValues.length, playSpeed]);

  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newIndex = parseInt(event.target.value, 10);
    setCurrentTimeIndex(newIndex);
  };

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const handleNext = () => {
    setCurrentTimeIndex(prevIndex => 
      prevIndex + 1 >= timeValues.length ? timeValues.length - 1 : prevIndex + 1
    );
  };

  const handlePrevious = () => {
    setCurrentTimeIndex(prevIndex => 
      prevIndex - 1 < 0 ? 0 : prevIndex - 1
    );
  };

  const formatDate = (isoString: string) => {
    try {
      return new Date(isoString).toLocaleDateString();
    } catch {
      return isoString;
    }
  };

  if (!isVisible || timeValues.length === 0) {
    return null;
  }

  return (
    <div className="time-slider-container">
      <div className="time-slider-header">
        <Clock size={14} className="me-2" />
        <span>Temporal Data</span>
      </div>
      
      <div className="time-slider-controls">
        <div className="time-navigation">
          <Button 
            variant="light" 
            size="sm" 
            onClick={handlePrevious}
            disabled={currentTimeIndex === 0}
            className="time-nav-btn"
          >
            <SkipBack size={14} />
          </Button>
          
          <Button 
            variant="light" 
            size="sm" 
            onClick={togglePlay}
            className="time-play-btn"
          >
            {isPlaying ? <Pause size={14} /> : <Play size={14} />}
          </Button>
          
          <Button 
            variant="light" 
            size="sm" 
            onClick={handleNext}
            disabled={currentTimeIndex === timeValues.length - 1}
            className="time-nav-btn"
          >
            <SkipForward size={14} />
          </Button>
        </div>
        
        <div className="time-display">
          <OverlayTrigger
            placement="bottom"
            overlay={
              <Tooltip id="time-tooltip">
                {timeValues[currentTimeIndex]}
              </Tooltip>
            }
          >
            <span className="current-time">
              <Calendar size={12} className="me-1" />
              {formatDate(timeValues[currentTimeIndex])}
            </span>
          </OverlayTrigger>
        </div>
      </div>
      
      <div className="time-slider-wrapper">
        <Form.Range
          min={0}
          max={timeValues.length - 1}
          value={currentTimeIndex}
          onChange={handleSliderChange}
          className="time-slider"
        />
        <div className="time-labels">
          <small>{formatDate(timeValues[0])}</small>
          <small>{formatDate(timeValues[timeValues.length - 1])}</small>
        </div>
      </div>
      
      <div className="time-speed-control">
        <small>Speed:</small>
        <Form.Select 
          size="sm" 
          value={playSpeed}
          onChange={(e) => setPlaySpeed(parseInt(e.target.value, 10))}
          className="speed-select"
        >
          <option value="2000">Slow</option>
          <option value="1000">Normal</option>
          <option value="500">Fast</option>
          <option value="200">Very Fast</option>
        </Form.Select>
      </div>
    </div>
  );
};

export default TimeSlider;
