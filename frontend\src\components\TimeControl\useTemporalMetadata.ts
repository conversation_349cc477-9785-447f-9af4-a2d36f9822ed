import { useState, useEffect } from 'react';
import { parseTemporalMetadata } from '../../utils/temporalUtils';
import { TemporalInfo } from '../TimeControl/TimeSlider';

/**
 * Example hook to demonstrate how to parse and use temporal metadata
 * This would typically be integrated with your layer service
 */
export const useTemporalMetadata = (metadataString?: string) => {
  const [temporalInfo, setTemporalInfo] = useState<TemporalInfo | null>(null);
  const [currentTime, setCurrentTime] = useState<string | null>(null);
  
  useEffect(() => {
    if (metadataString) {
      const parsedInfo = parseTemporalMetadata(metadataString);
      setTemporalInfo(parsedInfo);
      
      // Set default time if available
      if (parsedInfo?.default) {
        setCurrentTime(parsedInfo.default);
      } else if (parsedInfo?.values && parsedInfo.values.length > 0) {
        setCurrentTime(parsedInfo.values[0]);
      }
    } else {
      setTemporalInfo(null);
      setCurrentTime(null);
    }
  }, [metadataString]);
  
  const handleTimeChange = (time: string) => {
    setCurrentTime(time);
    
    // This is where you would update your map layers with the new time
    console.log('Time changed to:', time);
    
    // Example: Update WMS layer params
    // updateLayerParams({ time: time });
  };
  
  return {
    temporalInfo,
    currentTime,
    handleTimeChange
  };
};

// Sample metadata string (you would get this from your GeoServer/WMS service)
export const SAMPLE_TEMPORAL_METADATA = `Temporal Information
Dimension: time
Units: ISO8601
Default Time: 1991-08-11T00:00:00Z
Extent: 1991-08-11T00:00:00.000Z,1991-08-21T00:00:00.000Z,1991-09-01T00:00:00.000Z,1991-09-11T00:00:00.000Z,1991-09-21T00:00:00.000Z,1991-10-01T00:00:00.000Z,1991-10-11T00:00:00.000Z,1991-10-21T00:00:00.000Z,1991-11-01T00:00:00.000Z,1991-11-11T00:00:00.000Z,1991-11-21T00:00:00.000Z,1991-12-01T00:00:00.000Z,1991-12-11T00:00:00.000Z,1991-12-21T00:00:00.000Z,1992-01-01T00:00:00.000Z`;
