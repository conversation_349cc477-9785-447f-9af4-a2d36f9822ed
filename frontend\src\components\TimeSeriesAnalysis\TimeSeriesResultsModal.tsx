import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Tab } from 'react-bootstrap';
import { Download, TrendingUp, Bar<PERSON>hart3, <PERSON><PERSON><PERSON>riangle, Calendar, Clock, CheckCircle } from 'lucide-react';
import { TimeSeriesResult, TimeSeriesService } from '../../services/timeSeriesService';
import TimeSeriesChart from './TimeSeriesChart';

interface TimeSeriesResultsModalProps {
  show: boolean;
  onHide: () => void;
  result: TimeSeriesResult | null;
  isLoading: boolean;
  error: string | null;
}

const TimeSeriesResultsModal: React.FC<TimeSeriesResultsModalProps> = ({
  show,
  onHide,
  result,
  isLoading,
  error
}) => {
  const [activeTab, setActiveTab] = useState('summary');
  const [visualizationData, setVisualizationData] = useState<any>(null);
  const [loadingVisualization, setLoadingVisualization] = useState(false);

  // Load visualization data when result changes
  useEffect(() => {
    if (result && (result.analysisType === 'ndvi_trend' || result.analysisType === 'vci_analysis')) {
      loadVisualizationData();
    }
  }, [result]);

  const loadVisualizationData = async () => {
    if (!result) return;

    try {
      setLoadingVisualization(true);
      const vizData = await TimeSeriesService.getVisualizationData(
        result.analysisType,
        result.dateRange.startDate,
        result.dateRange.endDate,
        ['sample_layer'] // This would come from the actual layers used
      );
      setVisualizationData(vizData);
    } catch (error) {
      console.error('Failed to load visualization data:', error);
    } finally {
      setLoadingVisualization(false);
    }
  };

  const formatAnalysisResult = () => {
    if (!result) return null;
    return TimeSeriesService.formatAnalysisResult(result);
  };

  const getAnalysisIcon = (analysisType: string) => {
    switch (analysisType) {
      case 'static_mosaic': return '📸';
      case 'ndvi_trend': return '📈';
      case 'vci_analysis': return '🌱';
      case 'anomaly_detection': return '🔍';
      case 'seasonal_patterns': return '🍂';
      default: return '📊';
    }
  };

  const getQualityColor = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'danger';
  };

  const renderSummaryTab = () => {
    if (!result) return null;

    const formatted = formatAnalysisResult();
    if (!formatted) return null;

    return (
      <div className="p-3">
        {/* Analysis Header */}
        <div className="d-flex align-items-center mb-4">
          <span className="me-3" style={{ fontSize: '2rem' }}>
            {getAnalysisIcon(result.analysisType)}
          </span>
          <div>
            <h5 className="mb-1">{formatted.title}</h5>
            <p className="text-muted mb-0">{formatted.summary}</p>
          </div>
        </div>

        {/* Metadata Cards */}
        <Row className="mb-4">
          <Col md={3}>
            <Card className="text-center border-0 bg-light">
              <Card.Body className="py-3">
                <Clock size={24} className="text-primary mb-2" />
                <div className="small text-muted">Processing Time</div>
                <div className="fw-bold">{result.metadata.processingTime}ms</div>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className="text-center border-0 bg-light">
              <Card.Body className="py-3">
                <BarChart3 size={24} className="text-info mb-2" />
                <div className="small text-muted">Data Points</div>
                <div className="fw-bold">{result.metadata.dataPoints}</div>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className="text-center border-0 bg-light">
              <Card.Body className="py-3">
                <CheckCircle size={24} className={`text-${getQualityColor(result.metadata.qualityScore)} mb-2`} />
                <div className="small text-muted">Quality Score</div>
                <div className="fw-bold">{result.metadata.qualityScore}%</div>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className="text-center border-0 bg-light">
              <Card.Body className="py-3">
                <Calendar size={24} className="text-secondary mb-2" />
                <div className="small text-muted">Date Range</div>
                <div className="fw-bold small">
                  {new Date(result.dateRange.startDate).toLocaleDateString()} - 
                  {new Date(result.dateRange.endDate).toLocaleDateString()}
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Key Findings */}
        <Card className="mb-4">
          <Card.Header className="bg-primary text-white">
            <h6 className="mb-0">
              <TrendingUp size={16} className="me-2" />
              Key Findings
            </h6>
          </Card.Header>
          <Card.Body>
            <ul className="mb-0">
              {formatted.keyFindings.map((finding, index) => (
                <li key={index} className="mb-1">{finding}</li>
              ))}
            </ul>
          </Card.Body>
        </Card>

        {/* Recommendations */}
        <Card>
          <Card.Header className="bg-success text-white">
            <h6 className="mb-0">
              <AlertTriangle size={16} className="me-2" />
              Recommendations
            </h6>
          </Card.Header>
          <Card.Body>
            <ul className="mb-0">
              {formatted.recommendations.map((recommendation, index) => (
                <li key={index} className="mb-1">{recommendation}</li>
              ))}
            </ul>
          </Card.Body>
        </Card>
      </div>
    );
  };

  const renderVisualizationTab = () => {
    if (!result) return null;

    if (result.analysisType === 'static_mosaic') {
      return (
        <div className="p-3 text-center">
          <div className="mb-3">
            <img 
              src={result.products.staticMosaic?.wmsUrl} 
              alt="Static Mosaic"
              className="img-fluid rounded border"
              style={{ maxHeight: '400px' }}
            />
          </div>
          <p className="text-muted">Static mosaic for the selected date range</p>
          {result.products.staticMosaic?.downloadUrl && (
            <Button variant="primary" href={result.products.staticMosaic.downloadUrl}>
              <Download size={16} className="me-2" />
              Download High Resolution
            </Button>
          )}
        </div>
      );
    }

    if (loadingVisualization) {
      return (
        <div className="p-5 text-center">
          <Spinner animation="border" className="mb-3" />
          <p className="text-muted">Loading visualization...</p>
        </div>
      );
    }

    if (!visualizationData) {
      return (
        <div className="p-3">
          <Alert variant="info">
            Visualization data is not available for this analysis type.
          </Alert>
        </div>
      );
    }

    // Render chart based on visualization data
    return (
      <div className="p-3">
        <div className="mb-3">
          <h6>Time Series Visualization</h6>
          <p className="text-muted small">
            Interactive chart showing {result.analysisType.replace('_', ' ')} over time
          </p>
        </div>
        
        {/* Time-series chart */}
        <TimeSeriesChart
          data={visualizationData}
          title={`${result.analysisType.replace('_', ' ').toUpperCase()} Analysis`}
          height={350}
        />
      </div>
    );
  };

  const renderDataTab = () => {
    if (!result) return null;

    return (
      <div className="p-3">
        <h6 className="mb-3">Raw Analysis Data</h6>
        
        {/* Analysis-specific data display */}
        {result.analysisType === 'ndvi_trend' && result.products.ndviTrend && (
          <Card className="mb-3">
            <Card.Header>NDVI Trend Statistics</Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <strong>Trend Direction:</strong> 
                  <Badge bg={result.products.ndviTrend.trendData.trend === 'increasing' ? 'success' : 
                           result.products.ndviTrend.trendData.trend === 'decreasing' ? 'danger' : 'secondary'} 
                         className="ms-2">
                    {result.products.ndviTrend.trendData.trend}
                  </Badge>
                </Col>
                <Col md={6}>
                  <strong>Correlation:</strong> {result.products.ndviTrend.trendData.correlation.toFixed(4)}
                </Col>
                <Col md={6}>
                  <strong>Mean NDVI:</strong> {result.products.ndviTrend.statistics.mean.toFixed(4)}
                </Col>
                <Col md={6}>
                  <strong>Standard Deviation:</strong> {result.products.ndviTrend.statistics.stdDev.toFixed(4)}
                </Col>
              </Row>
            </Card.Body>
          </Card>
        )}

        {result.analysisType === 'vci_analysis' && result.products.vciAnalysis && (
          <Card className="mb-3">
            <Card.Header>VCI Analysis Data</Card.Header>
            <Card.Body>
              <div className="mb-3">
                <strong>Drought Conditions Summary:</strong>
              </div>
              <div className="row">
                {['extreme', 'severe', 'moderate', 'mild', 'normal'].map(severity => {
                  const count = result.products.vciAnalysis!.vciData.droughtConditions.filter(
                    c => c.severity === severity
                  ).length;
                  return (
                    <div key={severity} className="col-md-2 text-center mb-2">
                      <div className="small text-muted text-capitalize">{severity}</div>
                      <div className="fw-bold">{count}</div>
                    </div>
                  );
                })}
              </div>
            </Card.Body>
          </Card>
        )}

        {/* Raw JSON data (collapsible) */}
        <Card>
          <Card.Header>
            <Button 
              variant="link" 
              className="p-0 text-decoration-none"
              onClick={() => setActiveTab(activeTab === 'raw-json' ? 'data' : 'raw-json')}
            >
              {activeTab === 'raw-json' ? 'Hide' : 'Show'} Raw JSON Data
            </Button>
          </Card.Header>
          {activeTab === 'raw-json' && (
            <Card.Body>
              <pre className="small bg-light p-3 rounded" style={{ maxHeight: '300px', overflow: 'auto' }}>
                {JSON.stringify(result, null, 2)}
              </pre>
            </Card.Body>
          )}
        </Card>
      </div>
    );
  };

  return (
    <Modal show={show} onHide={onHide} size="xl" centered>
      <Modal.Header closeButton style={{ backgroundColor: 'var(--bs-primary)', color: 'white' }}>
        <Modal.Title style={{ color: 'white' }}>
          Time-Series Analysis Results
        </Modal.Title>
      </Modal.Header>

      <Modal.Body className="p-0">
        {isLoading && (
          <div className="p-5 text-center">
            <Spinner animation="border" size="lg" className="mb-3" />
            <h5>Processing Time-Series Analysis</h5>
            <p className="text-muted">This may take a few minutes depending on the analysis type...</p>
          </div>
        )}

        {error && (
          <div className="p-3">
            <Alert variant="danger">
              <AlertTriangle size={16} className="me-2" />
              <strong>Analysis Failed:</strong> {error}
            </Alert>
          </div>
        )}

        {result && !isLoading && !error && (
          <Tabs
            activeKey={activeTab}
            onSelect={(k) => setActiveTab(k || 'summary')}
            className="border-bottom"
          >
            <Tab eventKey="summary" title="Summary">
              {renderSummaryTab()}
            </Tab>
            <Tab eventKey="visualization" title="Visualization">
              {renderVisualizationTab()}
            </Tab>
            <Tab eventKey="data" title="Data">
              {renderDataTab()}
            </Tab>
          </Tabs>
        )}
      </Modal.Body>

      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Close
        </Button>
        {result && (
          <Button variant="primary">
            <Download size={16} className="me-2" />
            Export Results
          </Button>
        )}
      </Modal.Footer>
    </Modal>
  );
};

export default TimeSeriesResultsModal;
