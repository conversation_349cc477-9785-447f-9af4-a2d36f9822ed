.toast-notification-container {
  max-width: 400px;
}

.toast-notification {
  margin-bottom: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: none;
}

.toast-notification .toast-header {
  border-bottom: none;
  padding: 12px 16px;
  font-weight: 600;
}

.toast-notification .toast-body {
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.4;
}

.toast-notification.toast-info .toast-header {
  background-color: #0dcaf0 !important;
}

.toast-notification.toast-success .toast-header {
  background-color: #198754 !important;
}

.toast-notification.toast-warning .toast-header {
  background-color: #fd7e14 !important;
}

.toast-notification.toast-error .toast-header {
  background-color: #dc3545 !important;
}

.toast-notification .btn-close {
  filter: invert(1);
}

/* Animation for toast entrance */
.toast-notification {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .toast-notification-container {
    position: fixed !important;
    top: 70px !important;
    right: 10px !important;
    left: 10px !important;
    max-width: none;
  }
  
  .toast-notification {
    width: 100%;
  }
}
