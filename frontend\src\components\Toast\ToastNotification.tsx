import React, { useEffect, useState } from 'react';
import { To<PERSON>, ToastContainer } from 'react-bootstrap';
import { AlertTriangle, Info, CheckCircle, XCircle } from 'lucide-react';
import './ToastNotification.css';

export interface ToastMessage {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  duration?: number; // in milliseconds, default 5000
  persistent?: boolean; // if true, won't auto-dismiss
}

interface ToastNotificationProps {
  toasts: ToastMessage[];
  onDismiss: (id: string) => void;
}

const ToastNotification: React.FC<ToastNotificationProps> = ({ toasts, onDismiss }) => {
  const [visibleToasts, setVisibleToasts] = useState<ToastMessage[]>([]);

  useEffect(() => {
    setVisibleToasts(toasts);
  }, [toasts]);

  const getToastIcon = (type: ToastMessage['type']) => {
    switch (type) {
      case 'info':
        return <Info size={20} className="text-info" />;
      case 'success':
        return <CheckCircle size={20} className="text-success" />;
      case 'warning':
        return <AlertTriangle size={20} className="text-warning" />;
      case 'error':
        return <XCircle size={20} className="text-danger" />;
      default:
        return <Info size={20} className="text-info" />;
    }
  };

  const getToastVariant = (type: ToastMessage['type']) => {
    switch (type) {
      case 'success':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'danger';
      default:
        return 'info';
    }
  };

  const handleDismiss = (id: string) => {
    setVisibleToasts(prev => prev.filter(toast => toast.id !== id));
    onDismiss(id);
  };

  return (
    <ToastContainer 
      position="top-end" 
      className="toast-notification-container"
      style={{ 
        position: 'fixed', 
        top: '80px', 
        right: '20px', 
        zIndex: 9999 
      }}
    >
      {visibleToasts.map((toast) => (
        <Toast
          key={toast.id}
          show={true}
          onClose={() => handleDismiss(toast.id)}
          delay={toast.persistent ? undefined : (toast.duration || 5000)}
          autohide={!toast.persistent}
          className={`toast-notification toast-${toast.type}`}
        >
          <Toast.Header className={`bg-${getToastVariant(toast.type)} text-white`}>
            <div className="d-flex align-items-center">
              {getToastIcon(toast.type)}
              <strong className="ms-2">{toast.title}</strong>
            </div>
          </Toast.Header>
          <Toast.Body>
            {toast.message}
          </Toast.Body>
        </Toast>
      ))}
    </ToastContainer>
  );
};

export default ToastNotification;
