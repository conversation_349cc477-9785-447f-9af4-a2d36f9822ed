/* Tools Panel Styles */
.tools-panel-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.tools-panel-backdrop.show {
  opacity: 1;
  visibility: visible;
}

.tools-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 500px;
  height: 100vh;
  background-color: #ffffff;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  z-index: 1050;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tools-panel.open {
  transform: translateX(0);
}

.tools-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  flex-shrink: 0;
}

.tools-panel-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
}

.tools-panel-close {
  border: none;
  padding: 0.25rem 0.5rem;
}

.tools-panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.tools-category {
  margin-bottom: 2rem;
}

.tools-category:last-child {
  margin-bottom: 1rem;
}

.tools-category-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.tool-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  height: 100%;
}

.tool-card:hover {
  border-color: #0066cc;
  box-shadow: 0 2px 8px rgba(0, 102, 204, 0.1);
  transform: translateY(-1px);
}

.tool-card.requires-privileges {
  border-left: 3px solid #ffc107;
}

.tool-card.requires-privileges:hover {
  border-left-color: #ff9500;
}

.tool-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.tool-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #0066cc;
}

.privilege-icon {
  color: #ffc107;
}

.tool-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.tool-description {
  font-size: 0.8rem;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
}

.tools-panel-footer {
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  flex-shrink: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tools-panel {
    width: 100vw;
    max-width: 400px;
  }
  
  .tools-panel-content {
    padding: 1rem;
  }
  
  .tool-card .col-md-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .tools-panel {
    width: 100vw;
  }
  
  .tools-panel-header {
    padding: 0.75rem 1rem;
  }
  
  .tools-panel-title {
    font-size: 1rem;
  }
}

/* Custom scrollbar for tools panel content */
.tools-panel-content::-webkit-scrollbar {
  width: 6px;
}

.tools-panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.tools-panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.tools-panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
