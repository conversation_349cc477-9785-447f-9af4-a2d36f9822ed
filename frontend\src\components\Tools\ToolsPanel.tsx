import React from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Col } from 'react-bootstrap';
import { 
  X, 
  Layers, 
  Activity, 
  Settings, 
  Users, 
  Database, 
  Shield, 
  Download, 
  Upload,
  Map,
  BarChart3,
  Palette,
  Globe
} from 'lucide-react';
import './ToolsPanel.css';

interface ToolsPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ToolOption {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: 'layer' | 'admin' | 'data' | 'system';
  requiresPrivileges?: boolean;
  onClick?: () => void;
}

const ToolsPanel: React.FC<ToolsPanelProps> = ({ isOpen, onClose }) => {
  const toolOptions: ToolOption[] = [
    // Layer Management
    {
      id: 'layer-categories',
      title: 'Define Layer Categories',
      description: 'Configure and manage layer categorization and groupings',
      icon: <Layers size={20} />,
      category: 'layer',
      requiresPrivileges: true,
      onClick: () => console.log('Define Layer Categories clicked')
    },
    {
      id: 'layer-styles',
      title: 'Layer Styling',
      description: 'Customize layer appearance and symbology',
      icon: <Palette size={20} />,
      category: 'layer',
      onClick: () => console.log('Layer Styling clicked')
    },
    {
      id: 'spatial-reference',
      title: 'Spatial Reference Systems',
      description: 'Manage coordinate reference systems and projections',
      icon: <Globe size={20} />,
      category: 'layer',
      requiresPrivileges: true,
      onClick: () => console.log('Spatial Reference Systems clicked')
    },
    
    // System Administration
    {
      id: 'app-health',
      title: 'Application Health',
      description: 'Monitor system performance and service status',
      icon: <Activity size={20} />,
      category: 'admin',
      requiresPrivileges: true,
      onClick: () => console.log('Application Health clicked')
    },
    {
      id: 'user-management',
      title: 'User Management',
      description: 'Manage user accounts and permissions',
      icon: <Users size={20} />,
      category: 'admin',
      requiresPrivileges: true,
      onClick: () => console.log('User Management clicked')
    },
    {
      id: 'security-settings',
      title: 'Security Settings',
      description: 'Configure authentication and access controls',
      icon: <Shield size={20} />,
      category: 'admin',
      requiresPrivileges: true,
      onClick: () => console.log('Security Settings clicked')
    },
    
    // Data Management
    {
      id: 'data-sources',
      title: 'Data Source Configuration',
      description: 'Configure GeoServer and database connections',
      icon: <Database size={20} />,
      category: 'data',
      requiresPrivileges: true,
      onClick: () => console.log('Data Source Configuration clicked')
    },
    {
      id: 'data-import',
      title: 'Data Import/Export',
      description: 'Import and export spatial data and configurations',
      icon: <Upload size={20} />,
      category: 'data',
      onClick: () => console.log('Data Import/Export clicked')
    },
    {
      id: 'backup-restore',
      title: 'Backup & Restore',
      description: 'Backup and restore system configurations',
      icon: <Download size={20} />,
      category: 'data',
      requiresPrivileges: true,
      onClick: () => console.log('Backup & Restore clicked')
    },
    
    // System Settings
    {
      id: 'general-settings',
      title: 'General Settings',
      description: 'Configure application preferences and defaults',
      icon: <Settings size={20} />,
      category: 'system',
      onClick: () => console.log('General Settings clicked')
    },
    {
      id: 'map-settings',
      title: 'Map Configuration',
      description: 'Configure default map view and basemap settings',
      icon: <Map size={20} />,
      category: 'system',
      onClick: () => console.log('Map Configuration clicked')
    },
    {
      id: 'analytics-config',
      title: 'Analytics Configuration',
      description: 'Configure reporting and analytics settings',
      icon: <BarChart3 size={20} />,
      category: 'system',
      onClick: () => console.log('Analytics Configuration clicked')
    }
  ];

  const getCategoryTitle = (category: string) => {
    switch (category) {
      case 'layer': return 'Layer Management';
      case 'admin': return 'System Administration';
      case 'data': return 'Data Management';
      case 'system': return 'System Settings';
      default: return 'Other';
    }
  };

  const groupedTools = toolOptions.reduce((groups, tool) => {
    if (!groups[tool.category]) {
      groups[tool.category] = [];
    }
    groups[tool.category].push(tool);
    return groups;
  }, {} as Record<string, ToolOption[]>);

  return (
    <>
      {/* Backdrop */}
      <div 
        className={`tools-panel-backdrop ${isOpen ? 'show' : ''}`}
        onClick={onClose}
      />
      
      {/* Panel */}
      <div className={`tools-panel ${isOpen ? 'open' : ''}`}>
        <div className="tools-panel-header app-header-blue">
          <h4 className="tools-panel-title">
            <Settings size={20} className="me-2 header-icon" />
            Application Tools
          </h4>
          <Button
            variant="outline-secondary"
            size="sm"
            onClick={onClose}
            className="tools-panel-close"
          >
            <X size={16} />
          </Button>
        </div>
        
        <div className="tools-panel-content">
          {Object.entries(groupedTools).map(([category, tools]) => (
            <div key={category} className="tools-category">
              <h6 className="tools-category-title">{getCategoryTitle(category)}</h6>
              <Row className="g-2">
                {tools.map((tool) => (
                  <Col key={tool.id} xs={12} md={6}>
                    <Card 
                      className={`tool-card ${tool.requiresPrivileges ? 'requires-privileges' : ''}`}
                      onClick={tool.onClick}
                    >
                      <Card.Body className="p-3">
                        <div className="tool-card-header">
                          <div className="tool-icon">{tool.icon}</div>
                          {tool.requiresPrivileges && (
                            <Shield size={14} className="privilege-icon" />
                          )}
                        </div>
                        <h6 className="tool-title">{tool.title}</h6>
                        <p className="tool-description">{tool.description}</p>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            </div>
          ))}
        </div>
        
        <div className="tools-panel-footer">
          <small className="text-muted">
            <Shield size={12} className="me-1" />
            Some features require administrative privileges
          </small>
        </div>
      </div>
    </>
  );
};

export default ToolsPanel;
