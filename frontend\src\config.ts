// Use environment variables or fallback to defaults
const getAPIBaseURL = (): string => {
  // In production (Docker), use relative path that gets proxied by <PERSON><PERSON><PERSON>
  if (import.meta.env.PROD) {
    return '/api';
  }
  
  // In development, use direct connection to backend
  return import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';
};

const getOWSBaseURL = (): string => {
  // In production (Docker), use relative path that gets proxied by <PERSON><PERSON><PERSON>
  if (import.meta.env.PROD) {
    return '/ows';
  }
  
  // In development, use direct connection to backend
  return import.meta.env.VITE_OWS_BASE_URL || 'http://localhost:3001/ows';
};

// UI Engine API configuration
export const API_CONFIG = {
    BASE_URL: getAPIBaseURL(),  // Environment-aware API URL
    OWS_BASE_URL: getOWSBaseURL(), // Environment-aware OWS URL for GeoServer proxy
    // Remove direct GeoServer URLs - all requests should go through UIEngine
    // GEOSERVER_URL: import.meta.env.VITE_GEOSERVER_URL || 'https://*************/geoserver',
    // GEOSERVER_WMS_URL: (import.meta.env.VITE_GEOSERVER_URL || 'https://*************/geoserver') + '/wms',
    // GEOSERVER_WFS_URL: (import.meta.env.VITE_GEOSERVER_URL || 'https://*************/geoserver') + '/wfs'
};

// Environment and network configuration
export const ENVIRONMENT_CONFIG = {
    mode: import.meta.env.MODE || 'development',
    network: import.meta.env.VITE_NETWORK_MODE || 'auto', // Back to auto mode with user choice
    fallbackEnabled: import.meta.env.VITE_ENABLE_FALLBACK !== 'false',
    healthCheckInterval: parseInt(import.meta.env.VITE_HEALTH_CHECK_INTERVAL || '30000'), // 30 seconds
    maxRetries: parseInt(import.meta.env.VITE_MAX_RETRIES || '3'),
    retryDelay: parseInt(import.meta.env.VITE_RETRY_DELAY || '2000'), // 2 seconds
};

// Feature flags for optional system components
export const FEATURE_FLAGS = {
    // Database involvement - DISABLED
    enableDatabase: false, // FORCED DISABLED

    // ROI/Spatial functionality - DISABLED  
    enableROI: false, // FORCED DISABLED

    // Reporting and Analytics - Force enabled for development
    enableReporting: true,   // Always enabled for now
};
