import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { ToastMessage } from '../components/Toast/ToastNotification';

interface ToastContextType {
  toasts: ToastMessage[];
  addToast: (toast: Omit<ToastMessage, 'id'>) => void;
  removeToast: (id: string) => void;
  clearAllToasts: () => void;
  // Convenience methods
  showInfo: (title: string, message: string, options?: Partial<ToastMessage>) => void;
  showSuccess: (title: string, message: string, options?: Partial<ToastMessage>) => void;
  showWarning: (title: string, message: string, options?: Partial<ToastMessage>) => void;
  showError: (title: string, message: string, options?: Partial<ToastMessage>) => void;
  // Specific method for clipping notifications
  showClippingFallback: (layerName: string, reason: string, method: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  const generateId = useCallback(() => {
    return `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  const addToast = useCallback((toast: Omit<ToastMessage, 'id'>) => {
    const newToast: ToastMessage = {
      ...toast,
      id: generateId(),
    };

    setToasts(prev => {
      // Prevent duplicate toasts with same title and message
      const isDuplicate = prev.some(existingToast =>
        existingToast.title === newToast.title &&
        existingToast.message === newToast.message
      );

      if (isDuplicate) {
        return prev; // Don't add duplicate
      }

      return [...prev, newToast];
    });

    // Auto-remove non-persistent toasts
    if (!newToast.persistent) {
      setTimeout(() => {
        setToasts(currentToasts =>
          currentToasts.filter(t => t.id !== newToast.id)
        );
      }, newToast.duration || 5000);
    }
  }, [generateId]);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  // Convenience methods
  const showInfo = useCallback((title: string, message: string, options?: Partial<ToastMessage>) => {
    addToast({ type: 'info', title, message, ...options });
  }, [addToast]);

  const showSuccess = useCallback((title: string, message: string, options?: Partial<ToastMessage>) => {
    addToast({ type: 'success', title, message, ...options });
  }, [addToast]);

  const showWarning = useCallback((title: string, message: string, options?: Partial<ToastMessage>) => {
    addToast({ type: 'warning', title, message, ...options });
  }, [addToast]);

  const showError = useCallback((title: string, message: string, options?: Partial<ToastMessage>) => {
    addToast({ type: 'error', title, message, ...options });
  }, [addToast]);

  // Specific method for clipping notifications with debouncing
  const showClippingFallback = useCallback((layerName: string, reason: string, method: string) => {
    // Determine notification type and messaging based on clipping method
    let title: string;
    let message: string;
    let type: 'info' | 'warning' | 'success';

    if (method === 'BBOX') {
      // BBOX is now last-resort fallback, show warning
      title = 'Last-Resort BBOX Clipping';
      message = `Layer "${layerName}": ${reason}`;
      type = 'warning';
    } else if (method === 'SLD' || method === 'WPS') {
      // SLD/WPS are exact clipping methods, show success info
      title = 'Exact Clipping Applied';
      message = `Layer "${layerName}": Using ${method} for precise polygon clipping`;
      type = 'success';
    } else {
      // Fallback for other methods
      title = `${method} Clipping Used`;
      message = `Layer "${layerName}": ${reason}`;
      type = 'info';
    }

    // Debounce rapid notifications for the same layer
    const debounceKey = `clipping-${layerName}`;

    // Clear any existing timeout for this layer
    if ((window as any)[debounceKey]) {
      clearTimeout((window as any)[debounceKey]);
    }

    // Set new timeout
    (window as any)[debounceKey] = setTimeout(() => {
      addToast({
        type,
        title,
        message,
        duration: type === 'warning' ? 7000 : 4000, // Longer duration for warnings
      });
      delete (window as any)[debounceKey];
    }, 100); // 100ms debounce
  }, [addToast]);

  const value: ToastContextType = {
    toasts,
    addToast,
    removeToast,
    clearAllToasts,
    showInfo,
    showSuccess,
    showWarning,
    showError,
    showClippingFallback,
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
    </ToastContext.Provider>
  );
};

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export default ToastContext;
