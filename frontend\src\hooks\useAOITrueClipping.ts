/**
 * AOI-True Clipping Hook
 * 
 * This hook provides a React interface to the AOI-True Clipping system,
 * managing AOI state, creation, deletion, and mode switching.
 */

import { useState, useCallback, useEffect } from 'react';
import { 
  getAOITrueClippingService, 
  AOICreateRequest, 
  AOICreateResponse 
} from '../services/aoiTrueClippingService';

export interface AOIState {
  aoiId: string | null;
  mode: 'administrative' | 'drawn' | 'pin' | null;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  } | null;
  centroid: {
    lat: number;
    lng: number;
  } | null;
  meta: {
    area: number;
    vertices: number;
    hasSimplified: boolean;
    mode: string;
  } | null;
  isLoading: boolean;
  error: string | null;
}

export interface UseAOITrueClippingReturn {
  // State
  aoiState: AOIState;
  
  // Actions
  createAOI: (request: AOICreateRequest) => Promise<void>;
  deleteAOI: () => Promise<void>;
  switchMode: (newAOIData: any) => Promise<void>;
  updateDateRange: (dateRange: { startDate: string; endDate: string }) => Promise<void>;
  
  // Utilities
  hasActiveAOI: () => boolean;
  getAOIId: () => string | null;
  refreshCapabilities: () => Promise<void>;
}

export function useAOITrueClipping(): UseAOITrueClippingReturn {
  const [aoiState, setAOIState] = useState<AOIState>({
    aoiId: null,
    mode: null,
    bounds: null,
    centroid: null,
    meta: null,
    isLoading: false,
    error: null
  });

  const aoiService = getAOITrueClippingService();

  // Initialize with existing AOI ID if available
  useEffect(() => {
    const existingAOIId = aoiService.getCurrentAOIId();
    if (existingAOIId) {
      setAOIState(prev => ({
        ...prev,
        aoiId: existingAOIId
      }));
    }
  }, [aoiService]);

  /**
   * Create a new AOI
   */
  const createAOI = useCallback(async (request: AOICreateRequest) => {
    setAOIState(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }));

    try {
      console.log('🎯 Creating AOI via hook:', { mode: request.mode });
      
      const response = await aoiService.createAOI(request);
      
      setAOIState(prev => ({
        ...prev,
        aoiId: response.aoiId,
        mode: request.mode,
        bounds: response.bounds,
        centroid: response.centroid,
        meta: response.meta,
        isLoading: false,
        error: null
      }));

      console.log('✅ AOI created successfully via hook:', response.aoiId);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create AOI';
      
      setAOIState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));

      console.error('❌ AOI creation failed via hook:', errorMessage);
      throw error;
    }
  }, [aoiService]);

  /**
   * Delete current AOI
   */
  const deleteAOI = useCallback(async () => {
    if (!aoiState.aoiId) {
      console.log('ℹ️ No AOI to delete');
      return;
    }

    setAOIState(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }));

    try {
      console.log('🗑️ Deleting AOI via hook:', aoiState.aoiId);
      
      await aoiService.deleteCurrentAOI();
      
      setAOIState({
        aoiId: null,
        mode: null,
        bounds: null,
        centroid: null,
        meta: null,
        isLoading: false,
        error: null
      });

      console.log('✅ AOI deleted successfully via hook');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete AOI';
      
      setAOIState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));

      console.error('❌ AOI deletion failed via hook:', errorMessage);
      // Don't throw - deletion failures shouldn't break the app
    }
  }, [aoiState.aoiId, aoiService]);

  /**
   * Switch AOI mode (ensures mutual exclusivity)
   */
  const switchMode = useCallback(async (newAOIData: any) => {
    setAOIState(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }));

    try {
      console.log('🔄 Switching AOI mode via hook:', { 
        from: aoiState.mode, 
        to: newAOIData.type || newAOIData.mode 
      });
      
      const response = await aoiService.switchAOIMode(newAOIData);
      
      setAOIState(prev => ({
        ...prev,
        aoiId: response.aoiId,
        mode: newAOIData.type || newAOIData.mode || 'administrative',
        bounds: response.bounds,
        centroid: response.centroid,
        meta: response.meta,
        isLoading: false,
        error: null
      }));

      console.log('✅ AOI mode switched successfully via hook:', response.aoiId);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to switch AOI mode';
      
      setAOIState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));

      console.error('❌ AOI mode switch failed via hook:', errorMessage);
      throw error;
    }
  }, [aoiState.mode, aoiService]);

  /**
   * Update date range for current AOI
   */
  const updateDateRange = useCallback(async (dateRange: { startDate: string; endDate: string }) => {
    try {
      console.log('📅 Updating AOI date range via hook:', dateRange);
      
      await aoiService.updateAOIDateRange(dateRange);
      
      console.log('✅ AOI date range updated via hook');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update date range';
      
      setAOIState(prev => ({
        ...prev,
        error: errorMessage
      }));

      console.error('❌ AOI date range update failed via hook:', errorMessage);
      throw error;
    }
  }, [aoiService]);

  /**
   * Check if there's an active AOI
   */
  const hasActiveAOI = useCallback(() => {
    return !!aoiState.aoiId;
  }, [aoiState.aoiId]);

  /**
   * Get current AOI ID
   */
  const getAOIId = useCallback(() => {
    return aoiState.aoiId;
  }, [aoiState.aoiId]);

  /**
   * Refresh capabilities cache
   */
  const refreshCapabilities = useCallback(async () => {
    try {
      console.log('🔄 Refreshing capabilities via hook...');
      await aoiService.refreshCapabilities();
      console.log('✅ Capabilities refreshed via hook');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh capabilities';
      console.error('❌ Capabilities refresh failed via hook:', errorMessage);
      throw error;
    }
  }, [aoiService]);

  return {
    // State
    aoiState,
    
    // Actions
    createAOI,
    deleteAOI,
    switchMode,
    updateDateRange,
    
    // Utilities
    hasActiveAOI,
    getAOIId,
    refreshCapabilities
  };
}
