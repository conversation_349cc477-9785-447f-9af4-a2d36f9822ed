import { useEffect, useState } from 'react';
import { fetchAvailableLayers } from '../services/geoserverService';
import { LayerDiscovery } from '../types/discovery';

export function useDiscoveryLayers() {
  const [layers, setLayers] = useState<LayerDiscovery[]>([]);
  const [selectedLayers, setselectedLayers] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadLayers = async () => {
      try {
        setIsLoading(true);
        setError(null);
        console.log('🔍 useDiscoveryLayers: Loading layers...');
        const data = await fetchAvailableLayers();
        console.log('🔍 useDiscoveryLayers: Loaded', data.length, 'layers');
        setLayers(data);
      } catch (error) {
        console.error('🚨 Failed to fetch discovery layers:', error);
        setError('Failed to load layers');
      } finally {
        setIsLoading(false);
      }
    };

    loadLayers();
  }, []);

  const handleLayerToggle = (layerName: string) => {
    console.log('🔄 Layer toggled for map loading:', layerName);
    setselectedLayers(prev =>
      prev.includes(layerName)
        ? prev.filter(name => name !== layerName)
        : [...prev, layerName]
    );
  };

  return { layers, selectedLayers, handleLayerToggle, isLoading, error };
}