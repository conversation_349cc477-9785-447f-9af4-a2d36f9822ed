// Abstraction layer for reporting and analytics
import { FEATURE_FLAGS } from '../config';

export interface ReportOptions {
  reportTypes: Array<{ value: string; label: string; description?: string }>;
  formats: Array<{ value: string; label: string; description?: string }>;
  periods: Array<{ value: string; label: string }>;
}

export interface AnalyticsData {
  summaryStats: {
    avgResponseTime: number;
    totalAlerts: number;
    totalDatasets: number;
    activeRules: number;
  };
  riskTrend: Array<{ date: string; riskLevel: number; alertCount: number; datasetCount: number }>;
  regionAnalysis: Array<{ region: string; riskScore: number; alertCount: number; populationAtRisk: number }>;
  timeframeSummary: { period: string; startDate: string; endDate: string };
}

// Fallback data generators
export const getFallbackReportOptions = (): ReportOptions => ({
  reportTypes: [
    { value: 'demo_summary', label: 'Demo Summary', description: 'Basic summary report (demo mode)' }
  ],
  formats: [
    { value: 'json', label: 'JSON', description: 'JSON format (demo mode)' },
    { value: 'csv', label: 'CSV', description: 'CSV format (demo mode)' }
  ],
  periods: [
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 90 Days' }
  ]
});

export const generateFallbackAnalytics = (period: string): AnalyticsData => {
  const now = new Date();
  const daysMap: Record<string, number> = { '24h': 1, '7d': 7, '30d': 30, '90d': 90 };
  const days = daysMap[period] || 7;
  const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  const endDate = now.toISOString().split('T')[0];
  return {
    summaryStats: { avgResponseTime: 0, totalAlerts: 0, totalDatasets: 0, activeRules: 0 },
    riskTrend: [],
    regionAnalysis: [],
    timeframeSummary: { period, startDate, endDate }
  };
};

// Real service calls
const realFetchReportOptions = async (): Promise<ReportOptions> => {
  const response = await fetch('/api/reports/options');
  const result = await response.json();
  return result.data as ReportOptions;
};

const realFetchAnalytics = async (period: string): Promise<{ data: AnalyticsData; fallback: boolean }> => {
  const response = await fetch(`/api/reports/analytics?period=${encodeURIComponent(period)}`);
  const result = await response.json();
  if (result.success) {
    return { data: result.data as AnalyticsData, fallback: false };
  }
  return { data: generateFallbackAnalytics(period), fallback: true };
};

// Abstraction exports
export const fetchReportOptions = async (): Promise<ReportOptions> => {
  if (!FEATURE_FLAGS.enableReporting) {
    console.log('🚫 Reporting disabled via feature flag; using fallback options');
    return getFallbackReportOptions();
  }
  try {
    return await realFetchReportOptions();
  } catch (error) {
    console.warn('Failed to fetch report options, using fallback', error);
    return getFallbackReportOptions();
  }
};

export const fetchAnalytics = async (period: string): Promise<{ data: AnalyticsData; fallback: boolean }> => {
  if (!FEATURE_FLAGS.enableReporting) {
    console.log('🚫 Reporting disabled via feature flag; using fallback analytics');
    return { data: generateFallbackAnalytics(period), fallback: true };
  }
  try {
    return await realFetchAnalytics(period);
  } catch (error) {
    console.warn('Failed to fetch analytics, using fallback', error);
    return { data: generateFallbackAnalytics(period), fallback: true };
  }
};
