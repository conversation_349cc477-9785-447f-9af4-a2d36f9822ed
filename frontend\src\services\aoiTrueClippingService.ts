/**
 * AOI-True Clipping Service for Frontend
 * 
 * This service provides a thin client interface to the UIEngine's
 * AOI-True Clipping system. It handles AOI creation, tile URL generation,
 * and cleanup while keeping all clipping logic on the server.
 */

import axios from 'axios';
import { API_CONFIG } from '../config';

export interface AOICreateRequest {
  mode: 'administrative' | 'drawn' | 'pin';
  geometry: GeoJSON.Geometry;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  metadata?: {
    name?: string;
    level?: 'province' | 'district' | 'municipality' | 'ward';
    code?: string;
  };
}

export interface AOICreateResponse {
  aoiId: string;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  centroid: {
    lat: number;
    lng: number;
  };
  meta: {
    area: number;
    vertices: number;
    hasSimplified: boolean;
    mode: string;
  };
}

export interface TileURLOptions {
  layer: string;
  aoiId?: string;
  time?: string;
  format?: string;
  srs?: string;
}

export class AOITrueClippingService {
  private baseUrl: string;
  private currentAOIId: string | null = null;

  constructor() {
    this.baseUrl = API_CONFIG.BASE_URL;
  }

  /**
   * Create a new AOI on the server
   */
  async createAOI(request: AOICreateRequest): Promise<AOICreateResponse> {
    console.log('🎯 Creating AOI on server:', { mode: request.mode, hasGeometry: !!request.geometry });

    try {
      const response = await axios.post(`${this.baseUrl}/aoi`, request);
      
      if (response.status !== 201) {
        throw new Error(`AOI creation failed: ${response.status}`);
      }

      const aoiData = response.data as AOICreateResponse;
      this.currentAOIId = aoiData.aoiId;

      console.log('✅ AOI created successfully:', {
        aoiId: aoiData.aoiId,
        area: aoiData.meta.area.toFixed(2) + ' km²',
        vertices: aoiData.meta.vertices,
        hasSimplified: aoiData.meta.hasSimplified
      });

      return aoiData;

    } catch (error) {
      console.error('❌ AOI creation failed:', error);
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.error || 'Failed to create AOI');
      }
      throw error;
    }
  }

  /**
   * Delete current AOI and cleanup
   */
  async deleteCurrentAOI(): Promise<void> {
    if (!this.currentAOIId) {
      console.log('ℹ️ No current AOI to delete');
      return;
    }

    try {
      console.log(`🗑️ Deleting AOI: ${this.currentAOIId}`);
      
      await axios.delete(`${this.baseUrl}/aoi/${this.currentAOIId}`);
      
      this.currentAOIId = null;
      console.log('✅ AOI deleted successfully');

    } catch (error) {
      console.error('❌ AOI deletion failed:', error);
      // Don't throw - deletion failures shouldn't break the app
      this.currentAOIId = null; // Clear anyway
    }
  }

  /**
   * Get current AOI ID
   */
  getCurrentAOIId(): string | null {
    return this.currentAOIId;
  }

  /**
   * Generate vector tile URL
   */
  generateVectorTileURL(z: number, x: number, y: number, options: TileURLOptions): string {
    const params = new URLSearchParams();
    params.set('layer', options.layer);
    
    if (options.aoiId || this.currentAOIId) {
      params.set('aoiId', options.aoiId || this.currentAOIId!);
    }
    
    if (options.time) {
      params.set('time', options.time);
    }
    
    if (options.format) {
      params.set('format', options.format);
    }
    
    if (options.srs) {
      params.set('srs', options.srs);
    }

    return `${this.baseUrl}/tiles/vector/${z}/${x}/${y}.png?${params.toString()}`;
  }

  /**
   * Generate raster tile URL
   */
  generateRasterTileURL(z: number, x: number, y: number, options: TileURLOptions): string {
    const params = new URLSearchParams();
    params.set('layer', options.layer);

    if (options.aoiId || this.currentAOIId) {
      params.set('aoiId', options.aoiId || this.currentAOIId!);
    }

    if (options.time) {
      params.set('time', options.time);
    }

    if (options.format) {
      params.set('format', options.format);
    }

    if (options.srs) {
      params.set('srs', options.srs);
    }

    return `${this.baseUrl}/tiles/raster/${z}/${x}/${y}.png?${params.toString()}`;
  }

  /**
   * Generate tile URL based on layer type
   */
  generateTileURL(
    layerType: 'vector' | 'raster',
    z: number,
    x: number,
    y: number,
    options: TileURLOptions
  ): string {
    if (layerType === 'vector') {
      return this.generateVectorTileURL(z, x, y, options);
    } else {
      return this.generateRasterTileURL(z, x, y, options);
    }
  }

  /**
   * Create AOI from existing AOI data (for compatibility)
   */
  async createAOIFromExisting(aoiData: any): Promise<AOICreateResponse> {
    // Convert existing AOI data format to new API format
    const request: AOICreateRequest = {
      mode: aoiData.type || 'administrative',
      geometry: aoiData.geometry || this.boundsToGeometry(aoiData.bounds),
      dateRange: aoiData.dateRange,
      metadata: {
        name: aoiData.name,
        level: aoiData.level,
        code: aoiData.code
      }
    };

    return this.createAOI(request);
  }

  /**
   * Convert bounds to geometry (fallback for legacy data)
   */
  private boundsToGeometry(bounds: any): GeoJSON.Geometry {
    if (!bounds) {
      throw new Error('No geometry or bounds provided for AOI');
    }

    // Create a simple rectangle from bounds
    return {
      type: 'Polygon',
      coordinates: [[
        [bounds.west, bounds.south],
        [bounds.east, bounds.south],
        [bounds.east, bounds.north],
        [bounds.west, bounds.north],
        [bounds.west, bounds.south]
      ]]
    };
  }

  /**
   * Switch AOI mode (ensures mutual exclusivity)
   */
  async switchAOIMode(newAOIData: any): Promise<AOICreateResponse> {
    // Delete current AOI first
    await this.deleteCurrentAOI();
    
    // Create new AOI
    return this.createAOIFromExisting(newAOIData);
  }

  /**
   * Update AOI with new date range
   */
  async updateAOIDateRange(dateRange: { startDate: string; endDate: string }): Promise<void> {
    // For now, we'll need to recreate the AOI with new date range
    // TODO: Implement PATCH endpoint for AOI updates
    console.log('ℹ️ Date range updated - tiles will use new time parameter:', dateRange);
  }

  /**
   * Get AOI service statistics
   */
  async getStats(): Promise<any> {
    try {
      const response = await axios.get(`${this.baseUrl}/api/aoi/stats`);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to get AOI stats:', error);
      return null;
    }
  }

  /**
   * Refresh capabilities cache (admin function)
   */
  async refreshCapabilities(): Promise<void> {
    try {
      console.log('🔄 Refreshing capabilities cache...');
      await axios.post(`${this.baseUrl}/aoi/admin/refresh-capabilities`);
      console.log('✅ Capabilities cache refreshed');
    } catch (error) {
      console.error('❌ Failed to refresh capabilities cache:', error);
      throw error;
    }
  }
}

// Global service instance
let globalAOITrueClippingService: AOITrueClippingService | null = null;

/**
 * Get global AOI-True Clipping service instance
 */
export function getAOITrueClippingService(): AOITrueClippingService {
  if (!globalAOITrueClippingService) {
    globalAOITrueClippingService = new AOITrueClippingService();
  }
  return globalAOITrueClippingService;
}

/**
 * Reset global service instance (for testing)
 */
export function resetAOITrueClippingService(): void {
  globalAOITrueClippingService = null;
}
