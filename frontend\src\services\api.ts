import axios from 'axios';
import { API_CONFIG } from '../config';

// Function to fetch flood risk data from UI Engine API
export const fetchFloodRiskData = async (region: any, timeframe: any) => {
  console.log('Fetching flood risk data for region:', region);
  console.log('Timeframe:', timeframe);
  
  try {
    const params = {
      typeName: 'flood:risk_areas',
      bbox: region ? `${region._southWest.lng},${region._southWest.lat},${region._northEast.lng},${region._northEast.lat}` : undefined,
      startDate: timeframe?.startDate,
      endDate: timeframe?.endDate
    };

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/features`, { params });
    
    const mappedData = response.data.features.map((feature: any) => ({
      id: feature.id,
      coordinates: feature.geometry.coordinates[0],
      risk: feature.properties.risk_level,
      date: feature.properties.date
    }));
    
    return mappedData;
  } catch (error) {
    console.error('Error fetching flood risk data:', error);
    return [];
  }
};

// Function to fetch layer data from UI Engine API
export const fetchLayerData = async (layerType: string, bounds: any) => {
  try {
    const params = {
      typeName: `flood:${layerType}`,
      bbox: bounds ? `${bounds._southWest.lng},${bounds._southWest.lat},${bounds._northEast.lng},${bounds._northEast.lat}` : undefined
    };

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/features`, { params });
    
    return response.data.features.map((feature: any) => {
      if (layerType === 'cadastre') {
        return {
          id: feature.id,
          bounds: [
            [feature.geometry.coordinates[0][0][1], feature.geometry.coordinates[0][0][0]],
            [feature.geometry.coordinates[0][2][1], feature.geometry.coordinates[0][2][0]]
          ],
          owner: feature.properties.owner
        };
      } else if (layerType === 'dwsVillage') {
        return {
          id: feature.id,
          center: [feature.geometry.coordinates[1], feature.geometry.coordinates[0]],
          radius: feature.properties.radius || 10000,
          name: feature.properties.name,
          population: feature.properties.population
        };
      }
      return feature;
    });
  } catch (error) {
    console.error(`Error fetching ${layerType} data:`, error);
    return [];
  }
};

// Function to generate downloadable data
export const generateDownloadData = async (selectedLayers: any, dateRange: any, region: any) => {
  try {
    const params = {
      layers: Object.entries(selectedLayers)
        .filter(([_, isSelected]) => isSelected)
        .map(([name]) => `flood:${name}`).join(','),
      startDate: dateRange?.startDate,
      endDate: dateRange?.endDate,
      bbox: region ? `${region._southWest.lng},${region._southWest.lat},${region._northEast.lng},${region._northEast.lat}` : undefined
    };

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/download`, { params });
    return response.data;
  } catch (error) {
    console.error('Error generating download:', error);
    return null;
  }
};

// Function to search for a location
export const searchLocation = async (query: string) => {
  try {
    const params = { query };
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/search`, { params });
    
    if (response.data.features?.length > 0) {
      const feature = response.data.features[0];
      return {
        name: feature.properties.name,
        coordinates: [feature.geometry.coordinates[1], feature.geometry.coordinates[0]],
        boundingBox: feature.properties.bbox ? [
          [feature.properties.bbox[1], feature.properties.bbox[0]],
          [feature.properties.bbox[3], feature.properties.bbox[2]]
        ] : null
      };
    }
    return null;
  } catch (error) {
    console.error('Error searching for location:', error);
    return null;
  }
};
