import axios from 'axios';
import { API_CONFIG } from '../config';

export interface BoundaryLayer {
  name: string;
  title: string;
  abstract?: string;
  queryable: boolean;
}

export interface LayerSchema {
  hierarchyFields: {
    level: number;
    field: string;
    displayName: string;
  }[];
  geometryField: string;
}

export interface Region {
  id: string;
  name: string;
  hierarchy: Record<string, string>;
  geometry?: any;
  properties?: Record<string, any>;
}

export interface RegionalAOI {
  method: 'regional';
  boundaryLayer: string;
  selectedRegions: Region[];
  combinedBounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  totalArea: number;
}

// Enhanced interfaces for interactive filtering
export interface BoundaryFilters {
  province?: string;
  district?: string;
  municipality?: string;
  ward?: string;
}

export interface BoundaryFilterResponse {
  features: GeoJSON.Feature[];
  totalCount: number;
  isPartial: boolean;
}

export interface FilterOption {
  value: string;
  label: string;
}

/**
 * Security: Sanitize CQL filter values to prevent injection
 */
export const sanitizeCQLValue = (value: string): string => {
  if (!value || typeof value !== 'string') return '';
  
  // Remove potentially dangerous characters and patterns
  return value
    .replace(/['"\\]/g, '') // Remove quotes and backslashes
    .replace(/[;&|]/g, '')  // Remove command separators  
    .replace(/[<>]/g, '')   // Remove comparison operators
    .replace(/\b(DROP|DELETE|INSERT|UPDATE|CREATE|ALTER|EXEC|EXECUTE)\b/gi, '') // Remove SQL keywords
    .trim()
    .slice(0, 100); // Limit length
};

/**
 * Build CQL filter string from boundary filters
 */
export const buildCQLFilter = (filters: BoundaryFilters): string => {
  const conditions: string[] = [];
  
  if (filters.province) {
    const sanitized = sanitizeCQLValue(filters.province);
    conditions.push(`adm1_en='${sanitized}'`);
  }
  
  if (filters.district) {
    const sanitized = sanitizeCQLValue(filters.district);
    conditions.push(`adm2_en='${sanitized}'`);
  }
  
  if (filters.municipality) {
    const sanitized = sanitizeCQLValue(filters.municipality);
    conditions.push(`adm3_en='${sanitized}'`);
  }
  
  if (filters.ward) {
    const sanitized = sanitizeCQLValue(filters.ward);
    conditions.push(`adm4_en='${sanitized}'`);
  }
  
  return conditions.length > 0 ? conditions.join(' AND ') : '';
};

/**
 * Get available values for a specific boundary level with parent filtering
 */
export const getAvailableBoundaryValues = async (
  level: string, 
  parentFilters: BoundaryFilters = {}
): Promise<FilterOption[]> => {
  const cacheKey = `boundary-values-${level}-${JSON.stringify(parentFilters)}`;
  const cached = boundaryCache.get(cacheKey);
  if (cached) {
    return cached;
  }

  try {
    // Determine the layer and field name based on level
    let layerName = 'geonode:south_africa_municipal_boundaries';
    let fieldName = 'adm1_en'; // province
    
    switch (level) {
      case 'provinces':
        layerName = 'geonode:south_africa_provincial_boundaries';
        fieldName = 'adm1_en';
        break;
      case 'districts':
        layerName = 'geonode:south_africa_municipal_boundaries';
        fieldName = 'adm2_en';
        break;
      case 'municipalities':
        layerName = 'geonode:south_africa_municipal_boundaries';
        fieldName = 'adm3_en';
        break;
      case 'wards':
        layerName = 'geonode:south_africa_ward_boundaries';
        fieldName = 'adm4_en';
        break;
    }

    // Build CQL filter for parent constraints
    const parentCQL = buildCQLFilter(parentFilters);
    
    const params: any = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: layerName,
      outputFormat: 'application/json',
      maxFeatures: 1000,
      propertyName: fieldName
    };

    if (parentCQL) {
      params.CQL_FILTER = parentCQL;
    }

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/wfs-proxy`, {
      params,
      timeout: 15000
    });

    if (response.data && response.data.features) {
      // Extract unique values
      const values = new Set<string>();
      response.data.features.forEach((feature: any) => {
        const value = feature.properties?.[fieldName];
        if (value && typeof value === 'string') {
          values.add(value.trim());
        }
      });

      const options: FilterOption[] = Array.from(values)
        .filter(v => v.length > 0)
        .sort()
        .map(value => ({
          value,
          label: value
        }));

      boundaryCache.set(cacheKey, options);
      return options;
    }

    return [];
  } catch (error) {
    console.error(`Failed to get boundary values for ${level}:`, error);
    return [];
  }
};

/**
 * Get filtered boundary features with optional spatial bounds
 */
export const getFilteredBoundaryFeatures = async (
  filters: BoundaryFilters,
  bbox?: string
): Promise<BoundaryFilterResponse> => {
  try {
    // Determine the most specific layer based on filters
    let layerName = 'geonode:south_africa_provincial_boundaries';
    
    if (filters.ward) {
      layerName = 'geonode:south_africa_ward_boundaries';
    } else if (filters.municipality) {
      layerName = 'geonode:south_africa_municipal_boundaries';
    } else if (filters.district) {
      layerName = 'geonode:south_africa_municipal_boundaries';
    }

    const cqlFilter = buildCQLFilter(filters);
    if (!cqlFilter) {
      return { features: [], totalCount: 0, isPartial: false };
    }

    const params: any = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: layerName,
      outputFormat: 'application/json',
      CQL_FILTER: cqlFilter,
      maxFeatures: 1000
    };

    if (bbox) {
      params.bbox = bbox;
      params.srsName = 'EPSG:4326';
    }

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/wfs-proxy`, {
      params,
      timeout: 20000
    });

    if (response.data && response.data.features) {
      const features = response.data.features;
      const totalCount = features.length;
      const isPartial = totalCount >= 1000; // Indicates potential truncation

      return {
        features,
        totalCount,
        isPartial
      };
    }

    return { features: [], totalCount: 0, isPartial: false };
  } catch (error) {
    console.error('Failed to get filtered boundary features:', error);
    throw error;
  }
};

/**
 * Debounced version of getFilteredBoundaryFeatures for performance
 */
let debounceTimeout: NodeJS.Timeout;
export const debouncedGetFilteredBoundaryFeatures = (
  filters: BoundaryFilters,
  callback: (result: BoundaryFilterResponse) => void,
  delay: number = 300
) => {
  clearTimeout(debounceTimeout);
  debounceTimeout = setTimeout(async () => {
    try {
      const result = await getFilteredBoundaryFeatures(filters);
      callback(result);
    } catch (error) {
      console.error('Debounced boundary filter failed:', error);
      callback({ features: [], totalCount: 0, isPartial: false });
    }
  }, delay);
};

/**
 * Cache for boundary data to avoid repeated network calls
 */
class BoundaryCache {
  private cache = new Map<string, any>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  set(key: string, data: any) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  get(key: string): any | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    // Check if cache is expired
    if (Date.now() - cached.timestamp > this.CACHE_DURATION) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  clear() {
    this.cache.clear();
  }
}

const boundaryCache = new BoundaryCache();

/**
 * Discover available boundary layers from WFS capabilities
 */
export const discoverBoundaryLayers = async (): Promise<BoundaryLayer[]> => {
  const cacheKey = 'boundary-layers';
  const cached = boundaryCache.get(cacheKey);
  if (cached) {
    return cached;
  }

  try {
   
    // Get WFS capabilities
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/wfs-proxy`, {
      params: {
        service: 'WFS',
        request: 'GetCapabilities',
        version: '1.0.0'
      },
      timeout: 10000
    });

    // Parse XML response to find boundary layers
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(response.data, 'text/xml');
    
    // Extract layer information
    const featureTypes = xmlDoc.getElementsByTagName('FeatureType');
    const boundaryLayers: BoundaryLayer[] = [];
    const allLayers: string[] = [];

    for (let i = 0; i < featureTypes.length; i++) {
      const featureType = featureTypes[i];
      const nameElement = featureType.getElementsByTagName('Name')[0];
      const titleElement = featureType.getElementsByTagName('Title')[0];
      const abstractElement = featureType.getElementsByTagName('Abstract')[0];

      if (nameElement && titleElement) {
        const name = nameElement.textContent || '';
        const title = titleElement.textContent || '';
        const abstract = abstractElement?.textContent || '';

        allLayers.push(name);

        // Filter for our specific boundary layers
        if (isBoundaryLayer(name, title, abstract)) {
          console.log(`Found target boundary layer: ${name} (${title})`);
          boundaryLayers.push({
            name,
            title,
            abstract,
            queryable: true
          });
        }
      }
    }

    console.log(`Found ${boundaryLayers.length} target boundary layers:`, boundaryLayers.map(l => l.name));
    boundaryCache.set(cacheKey, boundaryLayers);
    return boundaryLayers;

  } catch (error) {
    console.error('Failed to discover boundary layers:', error);
    throw new Error('Failed to discover boundary layers from WFS service');
  }
};

/**
 * Check if a layer is a boundary/administrative layer based on dynamic criteria
 */
const isBoundaryLayer = (name: string, title: string, abstract: string): boolean => {
  const searchText = `${name} ${title} ${abstract}`.toLowerCase();

  // Dynamic criteria for boundary/administrative layers
  return (
    // South Africa specific boundaries
    (searchText.includes('south africa') || searchText.includes('south_africa')) &&
    (searchText.includes('boundary') || searchText.includes('boundaries') ||
     searchText.includes('place') || searchText.includes('administrative') ||
     searchText.includes('municipal') || searchText.includes('provincial') ||
     searchText.includes('country'))
  ) ||
  // General administrative/boundary indicators
  searchText.includes('administrative') ||
  searchText.includes('boundary') ||
  searchText.includes('boundaries') ||
  (searchText.includes('settlement') && searchText.includes('informal')) ||
  searchText.includes('ecds');
};

/**
 * Get schema information for a specific boundary layer
 */
export const getLayerSchema = async (layerName: string): Promise<LayerSchema> => {
  console.log(`🔍 getLayerSchema called for: ${layerName}`);
  const cacheKey = `schema-${layerName}`;
  const cached = boundaryCache.get(cacheKey);
  if (cached) {
    console.log(`✅ Using cached schema for ${layerName}`);
    return cached;
  }

  try {
    console.log(`🌐 Making WFS DescribeFeatureType request for: ${layerName}`);
    // Get feature type description
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/wfs-proxy`, {
      params: {
        service: 'WFS',
        request: 'DescribeFeatureType',
        typeName: layerName,
        version: '1.0.0'
      },
      timeout: 10000
    });

    console.log(`✅ WFS response received for ${layerName}, status: ${response.status}`);

    // Parse XML schema
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(response.data, 'text/xml');
    
    // Extract field information
    const elements = xmlDoc.getElementsByTagName('xsd:element');
    const hierarchyFields: LayerSchema['hierarchyFields'] = [];
    let geometryField = 'geometry';

    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];
      const name = element.getAttribute('name') || '';
      const type = element.getAttribute('type') || '';

      // Identify geometry field - try multiple patterns
      if (type.includes('gml:') ||
          name.toLowerCase().includes('geom') ||
          name.toLowerCase() === 'geometry' ||
          name.toLowerCase() === 'the_geom' ||
          name.toLowerCase() === 'wkb_geometry') {
        geometryField = name;
        console.log(`🎯 Detected geometry field for layer ${layerName}: ${name} (type: ${type})`);
        continue;
      }

      // Identify administrative hierarchy fields
      if (isAdminField(name)) {
        const level = getAdminLevel(name);
        const displayName = getDisplayName(name);
        
        hierarchyFields.push({
          level,
          field: name,
          displayName
        });
      }
    }

    // Sort hierarchy fields by level
    hierarchyFields.sort((a, b) => a.level - b.level);

    const schema: LayerSchema = {
      hierarchyFields,
      geometryField
    };

    console.log(`Schema loaded for ${layerName}:`, schema);
    boundaryCache.set(cacheKey, schema);
    return schema;

  } catch (error) {
    console.error(`Failed to load schema for layer ${layerName}:`, error);
    throw new Error(`Failed to load schema for layer: ${layerName}`);
  }
};

/**
 * Check if a field represents administrative data for our specific boundary layers
 */
const isAdminField = (fieldName: string): boolean => {
  const adminPatterns = [
    // Standard administrative fields
    /^adm\d+_en$/i,     // adm1_en, adm2_en, etc.
    /^admin\d+$/i,      // admin1, admin2, etc.
    /province/i,
    /municipal/i,
    /district/i,
    /region/i,
    // Specific fields for our target layers
    /country/i,         // for south_africa_country_boundary
    /place.*name/i,     // for south_africa_place_names
    /settlement/i,      // for informal_settlements_2011
    /ecds/i,           // for national_ecds
    /name/i,           // generic name fields
    /type/i,           // type classification fields
    /category/i        // category fields
  ];

  return adminPatterns.some(pattern => pattern.test(fieldName));
};

/**
 * Determine administrative level from field name
 */
const getAdminLevel = (fieldName: string): number => {
  const field = fieldName.toLowerCase();

  // Extract numeric level from field name (e.g., adm1_en -> 0, adm2_en -> 1)
  const match = field.match(/(\d+)/);
  if (match) {
    return parseInt(match[1]) - 1; // Convert to 0-based index
  }

  // Specific hierarchy for our target layers
  if (field.includes('country')) return 0;           // Country level (highest)
  if (field.includes('province')) return 1;          // Province level
  if (field.includes('municipal')) return 2;         // Municipality level
  if (field.includes('district')) return 2;          // District level (same as municipality)
  if (field.includes('place')) return 3;             // Place names (local level)
  if (field.includes('settlement')) return 3;        // Settlements (local level)
  if (field.includes('ecds')) return 3;             // ECDS facilities (local level)
  if (field.includes('name') && !field.includes('place')) return 2; // Generic names (mid level)
  if (field.includes('type')) return 1;              // Type classifications (higher level)
  if (field.includes('category')) return 1;          // Categories (higher level)

  return 0; // Default to highest level
};

/**
 * Generate display name from field name
 */
const getDisplayName = (fieldName: string): string => {
  // Convert field names to readable labels
  const displayNames: Record<string, string> = {
    // Standard administrative fields
    'adm1_en': 'Province',
    'adm2_en': 'Municipality',
    'adm3_en': 'Ward',
    'province': 'Province',
    'municipal': 'Municipality',
    'district': 'District',
    // Specific fields for our target layers
    'country': 'Country',
    'place_name': 'Place Name',
    'placename': 'Place Name',
    'name': 'Name',
    'settlement_name': 'Settlement Name',
    'settlement_type': 'Settlement Type',
    'ecds_name': 'ECDS Name',
    'ecds_type': 'ECDS Type',
    'type': 'Type',
    'category': 'Category'
  };

  return displayNames[fieldName.toLowerCase()] ||
         fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

/**
 * Load regions for a specific hierarchy level
 */
export const loadRegions = async (
  layerName: string, 
  level: number, 
  parentField?: string, 
  parentValue?: string
): Promise<Region[]> => {
  const cacheKey = `regions-${layerName}-${level}-${parentValue || 'root'}`;
  const cached = boundaryCache.get(cacheKey);
  if (cached) {
    return cached;
  }

  try {
    console.log(`Loading regions for ${layerName}, level ${level}, parent: ${parentValue}`);
    
    // Build WFS GetFeature request
    const params: any = {
      service: 'WFS',
      request: 'GetFeature',
      typeName: layerName,
      version: '1.0.0',
      outputFormat: 'application/json',
      maxFeatures: 1000
    };

    // Add filter for parent region if specified
    if (parentField && parentValue) {
      params.CQL_FILTER = `${parentField}='${parentValue}'`;
    }

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/wfs-proxy`, {
      params,
      timeout: 15000
    });

    // Parse GeoJSON response
    const geoJson = response.data;
    const regions: Region[] = [];

    if (geoJson.features) {
      // Get unique regions at this level
      const uniqueRegions = new Map<string, any>();
      
      geoJson.features.forEach((feature: any) => {
        const properties = feature.properties;
        
        // Find the field for this level
        const schema = boundaryCache.get(`schema-${layerName}`);
        if (schema && schema.hierarchyFields[level]) {
          const levelField = schema.hierarchyFields[level].field;
          const regionName = properties[levelField];
          
          if (regionName && !uniqueRegions.has(regionName)) {
            // Build hierarchy object
            const hierarchy: Record<string, string> = {};
            schema.hierarchyFields.forEach((field: any) => {
              if (properties[field.field]) {
                hierarchy[field.field] = properties[field.field];
              }
            });

            uniqueRegions.set(regionName, {
              id: `${layerName}-${regionName.replace(/\s+/g, '_').toLowerCase()}`,
              name: regionName,
              hierarchy,
              geometry: feature.geometry,
              properties
            });
          }
        }
      });

      regions.push(...Array.from(uniqueRegions.values()));
    }

    console.log(`Loaded ${regions.length} regions for level ${level}`);
    boundaryCache.set(cacheKey, regions);
    return regions;

  } catch (error) {
    console.error(`Failed to load regions for ${layerName}:`, error);
    throw new Error(`Failed to load regions for layer: ${layerName}`);
  }
};

/**
 * Calculate combined bounds for selected regions
 */
export const calculateCombinedBounds = (regions: Region[]): RegionalAOI['combinedBounds'] => {
  if (regions.length === 0) {
    return { north: 0, south: 0, east: 0, west: 0 };
  }

  let minLng = Infinity;
  let maxLng = -Infinity;
  let minLat = Infinity;
  let maxLat = -Infinity;

  regions.forEach(region => {
    if (region.geometry && region.geometry.coordinates) {
      // Simple bounds calculation - can be enhanced for complex geometries
      const coords = region.geometry.coordinates;
      
      // Flatten coordinates array and find bounds
      const flattenCoords = (arr: any[]): number[][] => {
        const result: number[][] = [];
        const flatten = (item: any) => {
          if (Array.isArray(item) && typeof item[0] === 'number') {
            result.push(item);
          } else if (Array.isArray(item)) {
            item.forEach(flatten);
          }
        };
        flatten(arr);
        return result;
      };

      const allCoords = flattenCoords(coords);
      allCoords.forEach(([lng, lat]) => {
        minLng = Math.min(minLng, lng);
        maxLng = Math.max(maxLng, lng);
        minLat = Math.min(minLat, lat);
        maxLat = Math.max(maxLat, lat);
      });
    }
  });

  return {
    north: maxLat,
    south: minLat,
    east: maxLng,
    west: minLng
  };
};

/**
 * Clear boundary cache
 */
export const clearBoundaryCache = () => {
  boundaryCache.clear();
};
