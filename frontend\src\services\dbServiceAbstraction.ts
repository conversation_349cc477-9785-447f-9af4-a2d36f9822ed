// Abstraction layer for database (ROI) services
import { FEATURE_FLAGS } from '../config';
import ROIApiService, { ROI, SpatialAnalysis } from './roiService';

// Fallback in-memory storage
let inMemoryROIs: ROI[] = [];

// Real service instance
const realService = new ROIApiService();

// Check ROI availability
export const checkROIAvailability = async (): Promise<boolean> => {
  if (!FEATURE_FLAGS.enableDatabase || !FEATURE_FLAGS.enableROI) {
    console.log('🚫 Database/ROI features disabled via feature flag');
    return false;
  }
  return await realService.checkAvailability();
};

// List ROIs
export const listROIs = async (): Promise<ROI[]> => {
  if (!FEATURE_FLAGS.enableDatabase || !FEATURE_FLAGS.enableROI) {
    console.log('🚫 Database/ROI disabled; returning empty list');
    return [];
  }
  try {
    return await realService.getAllROIs();
  } catch (error) {
    console.warn('🛠️ ROI list fetch failed; using fallback:', error);
    return inMemoryROIs;
  }
};

// Get single ROI
export const getROI = async (id: number): Promise<ROI | null> => {
  if (!FEATURE_FLAGS.enableDatabase) {
    console.log('🚫 Database disabled; searching in-memory ROIs');
    return inMemoryROIs.find(r => r.id === id) || null;
  }
  try {
    return await realService.getROIById(id);
  } catch (error) {
    console.warn('🛠️ ROI fetch failed; returning null:', error);
    return null;
  }
};

// Create or update ROI
export const saveROI = async (roi: ROI): Promise<ROI> => {
  if (!FEATURE_FLAGS.enableDatabase || !FEATURE_FLAGS.enableROI) {
    console.log('🚫 Database/ROI disabled; saving to in-memory');
    if (roi.id) {
      inMemoryROIs = inMemoryROIs.map(r => r.id === roi.id ? roi : r);
    } else {
      roi.id = Date.now();
      inMemoryROIs.push(roi);
    }
    return roi;
  }
  if (roi.id) {
    return await realService.updateROI(roi.id, roi);
  } else {
    return await realService.createROI(roi);
  }
};

// Delete ROI
export const deleteROI = async (id: number): Promise<void> => {
  if (!FEATURE_FLAGS.enableDatabase || !FEATURE_FLAGS.enableROI) {
    console.log('🚫 Database/ROI disabled; removing from in-memory');
    inMemoryROIs = inMemoryROIs.filter(r => r.id !== id);
    return;
  }
  await realService.deleteROI(id);
};

// Spatial analysis
export const analyzeROI = async (analysis: SpatialAnalysis): Promise<any> => {
  if (!FEATURE_FLAGS.enableDatabase || !FEATURE_FLAGS.enableROI) {
    console.log('🚫 Database/ROI disabled; returning empty analysis');
    return null;
  }
  try {
    return await realService.performSpatialAnalysis(analysis);
  } catch (error) {
    console.warn('🛠️ Spatial analysis failed:', error);
    return null;
  }
};
