import { LayerDiscovery, LayerGeometry, LayerRenderOrder, LayerStyle } from '../types/discovery';
import { API_CONFIG } from '../config';

// Import dataset types
export interface CategoryInfo {
    key: string;
    title: string;
}

// Cache for enhanced layer metadata
interface LayerCache {
    layers: LayerDiscovery[];
    timestamp: number;
    categories: CategoryInfo[];
}

let layerCache: LayerCache | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export interface GeoNodeDiscoveryResponse {
    success: boolean;
    total: number;
    layers: LayerDiscovery[];
    categories: CategoryInfo[];
}

export interface CategorizedLayers {
    [categoryKey: string]: LayerDiscovery[];
}

/**
 * Determine layer geometry type based on layer metadata
 */
function determineLayerGeometry(layer: LayerDiscovery): LayerGeometry {
    const name = layer.name.toLowerCase();
    const title = layer.title.toLowerCase();
    const abstract = layer.abstract.toLowerCase();
    const keywords = layer.keywords.join(' ').toLowerCase();
    const searchText = `${name} ${title} ${abstract} ${keywords}`;

    // Specific known point layers (in case metadata is missing)
    const knownPointLayers = [
        'informal settlements 2011',
        'informal settlement 2006',
        'sassa local offices',
        'saps stations',
        'sassa pay points',
        'health services',
        'early childhood development center',
        'south africa place names'
    ];

    // Check if this is a known point layer
    const matchedPointLayer = knownPointLayers.find(pointLayer =>
        title.includes(pointLayer) ||
        name.includes(pointLayer.replace(/\s+/g, '_')) ||
        name.includes(pointLayer.replace(/\s+/g, ''))
    );

    if (matchedPointLayer) {
        console.log(`📍 Identified known point layer: "${layer.title}" matched "${matchedPointLayer}"`);
        return { type: 'point' };
    }

    // Check for Digital Surface Model (special raster - goes right after base layer)
    if (searchText.includes('digital surface model') ||
        searchText.includes('dsm') ||
        name.includes('dsm') ||
        title.includes('digital surface model')) {
        console.log(`🏔️ Identified Digital Surface Model: "${layer.title}"`);
        return { type: 'raster', field: 'dsm' };
    }

    // Check for raster/imagery indicators
    if (searchText.includes('raster') ||
        searchText.includes('imagery') ||
        searchText.includes('satellite') ||
        searchText.includes('dem') ||
        searchText.includes('elevation') ||
        searchText.includes('mosaic') ||
        name.includes('_raster') ||
        name.includes('_img')) {
        console.log(`🖼️ Identified raster layer: "${layer.title}"`);
        return { type: 'raster' };
    }

    // Check for point indicators (general patterns)
    if (searchText.includes('point') ||
        searchText.includes('marker') ||
        searchText.includes('location') ||
        searchText.includes('facility') ||
        searchText.includes('facilities') ||
        searchText.includes('station') ||
        searchText.includes('stations') ||
        searchText.includes('office') ||
        searchText.includes('offices') ||
        searchText.includes('center') ||
        searchText.includes('centre') ||
        searchText.includes('settlement') ||
        searchText.includes('settlements') ||
        searchText.includes('services') ||
        searchText.includes('place names') ||
        searchText.includes('placenames') ||
        name.includes('_pt') ||
        name.includes('_point')) {
        return { type: 'point' };
    }

    // All other layers are treated as raster layers (below points)
    console.log(`🖼️ Treating as raster layer (default): "${layer.title}"`);
    return { type: 'raster' };
}

/**
 * Determine layer render order based on geometry type and layer characteristics
 */
function determineRenderOrder(layer: LayerDiscovery, geometry: LayerGeometry): LayerRenderOrder {
    const name = layer.name.toLowerCase();
    const title = layer.title.toLowerCase();
    const searchText = `${name} ${title}`;

    // Basemap layers (lowest priority)
    if (searchText.includes('basemap') ||
        searchText.includes('base_map') ||
        searchText.includes('background')) {
        return { category: 'basemap', zIndex: 100, priority: 1 };
    }

    // Geometry-based ordering
    switch (geometry.type) {
        case 'raster':
            // Special handling for Digital Surface Model
            if (geometry.field === 'dsm') {
                // DSM goes right after base layer (very bottom)
                return { category: 'raster', zIndex: 150, priority: 1.5 };
            }
            // Other raster layers go above DSM but below points
            return { category: 'raster', zIndex: 200, priority: 2 };

        case 'point':
            // Points on top for visibility
            return { category: 'point', zIndex: 500, priority: 5 };

        default:
            // Treat unknown as raster
            return { category: 'raster', zIndex: 200, priority: 2 };
    }
}

/**
 * Fetch layers from the new GeoNode discovery endpoint
 */
export async function fetchLayersFromGeoNode(): Promise<GeoNodeDiscoveryResponse> {
    try {

        
        const response = await fetch(`${API_CONFIG.BASE_URL}/datasets/geonode/discovery`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message || 'Failed to fetch layers from GeoNode');
        }        
        return data;
    } catch (error: any) {
        console.error('Error fetching layers from GeoNode:', error);
        throw new Error(`Failed to fetch layers: ${error.message}`);
    }
}

/**
 * Fetch categories from GeoNode
 */
export async function fetchCategoriesFromGeoNode(): Promise<CategoryInfo[]> {
    try {
        console.log('Fetching categories from GeoNode...');
        
        const response = await fetch(`${API_CONFIG.BASE_URL}/datasets/geonode/categories`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.message || 'Failed to fetch categories from GeoNode');
        }

        console.log(`Successfully fetched ${data.categories.length} categories from GeoNode`);
        
        return data.categories;
    } catch (error: any) {
        console.error('Error fetching categories from GeoNode:', error);
        throw new Error(`Failed to fetch categories: ${error.message}`);
    }
}

/**
 * Enhance layer with geometry and render order information
 */
function enhanceLayerMetadata(layer: LayerDiscovery): LayerDiscovery {
    // Determine geometry type
    const geometry = determineLayerGeometry(layer);

    // Determine render order
    const renderOrder = determineRenderOrder(layer, geometry);

    // Extract styles information if available
    const styles: LayerStyle[] = [];
    if ((layer as any).styles && Array.isArray((layer as any).styles)) {
        (layer as any).styles.forEach((style: any) => {
            styles.push({
                name: style.name || style.Name || '',
                title: style.title || style.Title || style.name || style.Name,
                abstract: style.abstract || style.Abstract || '',
                legendUrl: style.legendUrl || style.LegendURL ||
                          `${API_CONFIG.BASE_URL}/ows/legend?layer=${encodeURIComponent(layer.name)}&style=${encodeURIComponent(style.name || style.Name)}&format=image/png`,
                isDefault: style.isDefault || style.name === layer.style || style.Name === layer.style,
                styleSheetUrl: style.styleSheetUrl || style.StyleSheetURL,
                styleUrl: style.styleUrl || style.StyleURL
            });
        });
    }

    // If no styles found, create a default one
    if (styles.length === 0) {
        styles.push({
            name: layer.style || 'default',
            title: 'Default Style',
            abstract: 'Default styling for this layer',
            legendUrl: layer.legendUrl,
            isDefault: true
        });
    }

    return {
        ...layer,
        geometry,
        renderOrder,
        styles,
        defaultStyle: layer.style || styles.find(s => s.isDefault)?.name || styles[0]?.name
    };
}

/**
 * Categorize layers based on their category property
 */
export function categorizeLayers(layers: LayerDiscovery[]): CategorizedLayers {
    const categorized: CategorizedLayers = {};

    layers.forEach(layer => {
        const categoryKey = layer.category || 'other';

        if (!categorized[categoryKey]) {
            categorized[categoryKey] = [];
        }

        categorized[categoryKey].push(layer);
    });

    return categorized;
}

/**
 * Sort layers by render order (for proper map stacking)
 */
export function sortLayersByRenderOrder(layers: LayerDiscovery[]): LayerDiscovery[] {
    return layers.sort((a, b) => {
        const priorityA = a.renderOrder?.priority || 0;
        const priorityB = b.renderOrder?.priority || 0;
        return priorityA - priorityB; // Lower priority renders first (bottom)
    });
}

/**
 * Get layers grouped by render category
 */
export function getLayersByRenderCategory(layers: LayerDiscovery[]): {
    basemap: LayerDiscovery[];
    raster: LayerDiscovery[];
    point: LayerDiscovery[];
} {
    const grouped = {
        basemap: [] as LayerDiscovery[],
        raster: [] as LayerDiscovery[],
        point: [] as LayerDiscovery[]
    };

    layers.forEach(layer => {
        const category = layer.renderOrder?.category || 'raster';
        // Map all categories to our simplified structure
        if (category === 'basemap') {
            grouped.basemap.push(layer);
        } else if (category === 'point') {
            grouped.point.push(layer);
        } else {
            // All other categories (raster, line, polygon, overlay) go to raster
            grouped.raster.push(layer);
        }
    });

    return grouped;
}

/**
 * Clear the layer cache (useful for forcing refresh)
 */
export function clearLayerCache(): void {
    layerCache = null;
    console.log('🗑️ Layer cache cleared');
}

/**
 * Map category keys to user-friendly accordion section titles
 */
export function mapCategoryToAccordionSection(categoryKey: string): string {
    const categoryMapping: { [key: string]: string } = {
        // Common GeoNode categories
        'biota': 'Biological Data',
        'boundaries': 'Administrative Boundaries',
        'climatologyMeteorologyAtmosphere': 'Climate & Weather',
        'economy': 'Economic Data',
        'elevation': 'Elevation & Terrain',
        'environment': 'Environmental Data',
        'farming': 'Agriculture & Farming',
        'geoscientificInformation': 'Geological Data',
        'health': 'Health Data',
        'imageryBaseMapsEarthCover': 'Satellite & Imagery',
        'intelligenceMilitary': 'Military & Intelligence',
        'inlandWaters': 'Water Resources',
        'location': 'Location Services',
        'oceans': 'Marine & Coastal',
        'planningCadastre': 'Planning & Cadastre',
        'society': 'Social Data',
        'structure': 'Infrastructure',
        'transportation': 'Transportation',
        'utilitiesCommunication': 'Utilities & Communication',
        
        // Flood-specific categories
        'flood_risk': 'Flood Risk',
        'satellite': 'Satellite Data',
        'historical': 'Historical Data',
        'climatology': 'Climate Data',
        'settlements': 'Human Settlements',
        'service_points': 'Service Points',
        'admin': 'Administrative Layers',
        'basemaps': 'Basemaps & Elevation',
        'other': 'Other Datasets'
    };
    
    return categoryMapping[categoryKey] || formatCategoryTitle(categoryKey);
}

/**
 * Format category key into a readable title
 */
function formatCategoryTitle(categoryKey: string): string {
    return categoryKey
        .split(/[_-]/)
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}

/**
 * Create accordion sections from categories
 */
export function createAccordionSections(categories: CategoryInfo[]): Array<{key: string, title: string, content: null}> {
    return categories.map(category => ({
        key: category.key,
        title: category.title || mapCategoryToAccordionSection(category.key),
        content: null
    }));
}

/**
 * Enhanced layer discovery that combines GeoNode data with existing functionality
 */
export async function discoverLayers(): Promise<{
    layers: LayerDiscovery[];
    categories: CategoryInfo[];
    categorized: CategorizedLayers;
    accordionSections: Array<{key: string, title: string, content: null}>;
}> {
    try {
        // Check cache first
        const now = Date.now();
        if (layerCache && (now - layerCache.timestamp) < CACHE_DURATION) {
            console.log('📦 Using cached layer discovery data');
            const categorized = categorizeLayers(layerCache.layers);
            const accordionSections = createAccordionSections(layerCache.categories);

            return {
                layers: layerCache.layers,
                categories: layerCache.categories,
                categorized,
                accordionSections
            };
        }

        console.log('🔄 Fetching fresh layer discovery data...');
        const geoNodeData = await fetchLayersFromGeoNode();

        // Debug remote layer detection
        const remoteLayers = geoNodeData.layers.filter(l => (l as any).isRemote);
        console.log(`📊 Discovery: Found ${geoNodeData.layers.length} total layers (${remoteLayers.length} remote, ${geoNodeData.layers.length - remoteLayers.length} local)`);

        if (remoteLayers.length > 0) {
            console.log('🌐 Remote layers:', remoteLayers.map(l => l.name));
        }

        // Enhance layers with geometry and render order information
        console.log('🔍 Enhancing layers with geometry and render order metadata...');
        const enhancedLayers = geoNodeData.layers.map(layer => enhanceLayerMetadata(layer));

        // Sort layers by render order for proper map stacking
        const sortedLayers = sortLayersByRenderOrder(enhancedLayers);

        // Log geometry detection summary
        const geometryStats = {
            point: sortedLayers.filter(l => l.geometry?.type === 'point').length,
            raster: sortedLayers.filter(l => l.geometry?.type === 'raster').length,
            dsm: sortedLayers.filter(l => l.geometry?.type === 'raster' && l.geometry?.field === 'dsm').length
        };

        console.log('📊 Geometry detection summary:', geometryStats);

        // Log render order information
        console.log('📋 Layer render order:');
        sortedLayers.forEach((layer, index) => {
            console.log(`  ${index + 1}. ${layer.name} (${layer.geometry?.type || 'unknown'}) - Priority: ${layer.renderOrder?.priority || 0}`);
        });

        // Categorize the enhanced layers
        const categorized = categorizeLayers(sortedLayers);

        // Create accordion sections
        const accordionSections = createAccordionSections(geoNodeData.categories);

        // Cache the enhanced results
        layerCache = {
            layers: sortedLayers,
            categories: geoNodeData.categories,
            timestamp: Date.now()
        };

        console.log(`✅ Layer discovery complete: ${sortedLayers.length} layers enhanced and cached`);

        return {
            layers: sortedLayers,
            categories: geoNodeData.categories,
            categorized,
            accordionSections
        };

    } catch (error: any) {
        console.error('Enhanced layer discovery failed:', error);
        
        // Fallback to empty data structure
        return {
            layers: [],
            categories: [],
            categorized: {},
            accordionSections: [
                { key: 'satellite', title: 'Satellite Data', content: null },
                { key: 'flood_risk', title: 'Flood Risk', content: null },
                { key: 'historical', title: 'Historical Data', content: null },
                { key: 'climatology', title: 'Climate Data', content: null },
                { key: 'settlements', title: 'Human Settlements', content: null },
                { key: 'admin', title: 'Administrative Layers', content: null },
                { key: 'service_points', title: 'Service Points', content: null },
                { key: 'other', title: 'Other Datasets', content: null },
                { key: 'basemaps', title: 'Basemaps & Elevation', content: null }
            ]
        };
    }
}

/**
 * Legacy compatibility function - maps to the new discovery system
 */
export async function fetchAvailableLayers(): Promise<LayerDiscovery[]> {
    try {
        const discovery = await discoverLayers();
        return discovery.layers;
    } catch (error) {
        console.error('Legacy fetchAvailableLayers failed:', error);
        return [];
    }
}
