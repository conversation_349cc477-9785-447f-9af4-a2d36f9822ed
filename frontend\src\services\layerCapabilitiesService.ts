/**
 * Layer Capabilities Service for Frontend
 * 
 * This service fetches and caches layer capabilities from the UIEngine
 * to determine layer types and supported features for the AOI-True Clipping system.
 */

import axios from 'axios';
import { API_CONFIG } from '../config';

export interface LayerCapabilities {
  name: string;
  title: string;
  type: 'vector' | 'raster';
  supports: {
    cql: boolean;
    bbox: boolean;
    wms_sld_clip: boolean;
    wps_crop: boolean;
    server_time: boolean;
  };
  geometryField?: string;
  temporal?: {
    hasData: boolean;
    extent?: string;
    defaultTime?: string;
    units?: string;
  };
  bbox?: {
    minx: number;
    miny: number;
    maxx: number;
    maxy: number;
    crs: string;
  };
  lastUpdated: Date;
}

export class LayerCapabilitiesService {
  private cache = new Map<string, LayerCapabilities>();
  private lastFetch: Date | null = null;
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  /**
   * Get capabilities for a specific layer
   */
  async getLayerCapabilities(layerName: string): Promise<LayerCapabilities | null> {
    // Check cache first
    if (this.cache.has(layerName) && !this.isCacheExpired()) {
      return this.cache.get(layerName) || null;
    }

    // Fetch from server if not cached or cache expired
    await this.fetchAllCapabilities();
    
    return this.cache.get(layerName) || null;
  }

  /**
   * Get all cached capabilities
   */
  async getAllCapabilities(): Promise<LayerCapabilities[]> {
    if (this.cache.size === 0 || this.isCacheExpired()) {
      await this.fetchAllCapabilities();
    }
    
    return Array.from(this.cache.values());
  }

  /**
   * Determine if a layer is vector or raster
   */
  async getLayerType(layerName: string): Promise<'vector' | 'raster' | null> {
    // First check if cache is empty and try a quick initialization
    if (this.cache.size === 0) {
      console.log(`📋 Cache is empty (${this.cache.size} layers), attempting quick initialization...`);
      try {
        // Try a quick fetch with shorter timeout
        await this.quickFetchCapabilities();
      } catch (error) {
        console.warn(`⚠️ Quick capabilities fetch failed, using fallback detection`);
      }
    }

    const capabilities = await this.getLayerCapabilities(layerName);

    if (!capabilities) {
      console.warn(`⚠️ No capabilities found for layer: ${layerName}`);
      console.log(`📋 Available layers in cache:`, Array.from(this.cache.keys()));

      // Use fallback heuristic detection
      const fallbackType = this.determineLayerTypeFromName(layerName);
      console.log(`🔍 Using fallback detection: ${layerName} → ${fallbackType}`);
      return fallbackType;
    }

    return capabilities.type;
  }

  /**
   * Quick capabilities fetch with shorter timeout for immediate needs
   */
  private async quickFetchCapabilities(): Promise<void> {
    try {
      console.log('⚡ Quick capabilities fetch...');
      const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/capabilities`, {
        timeout: 3000 // 3 second timeout for quick fetch
      });

      if (Array.isArray(response.data) && response.data.length > 0) {
        console.log(`⚡ Quick fetch successful: ${response.data.length} layers`);
        this.processCapabilitiesData(response.data);
      }
    } catch (error) {
      console.warn('⚡ Quick fetch failed:', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  /**
   * Fallback method to determine layer type from name patterns
   */
  private determineLayerTypeFromName(layerName: string): 'vector' | 'raster' {
    const name = layerName.toLowerCase();
    console.log(`🔍 Fallback detection for: "${name}"`);

    // Vector patterns
    const vectorPatterns = [
      'boundaries', 'administrative', 'roads', 'points', 'lines', 'polygons',
      'settlements', 'stations', 'offices', 'services', 'centers', 'wards',
      'municipal', 'provincial', 'district'
    ];

    for (const pattern of vectorPatterns) {
      if (name.includes(pattern)) {
        console.log(`✅ Matched vector pattern: "${pattern}" → vector`);
        return 'vector';
      }
    }

    // Raster patterns - enhanced with specific patterns
    const rasterPatterns = [
      'imagery', 'satellite', 'mosaic', 'dem', 'elevation', 'dsm', 'dtm',
      'landsat', 'sentinel', 'modis', 'ndvi', 'evi', 'moisture', 'temperature',
      'sa_mosaic', 'final2', 'cbers', 'eumetsat', 'nasa_power', 'flood',
      'africa_mosaic', 'optmised'
    ];

    for (const pattern of rasterPatterns) {
      if (name.includes(pattern)) {
        console.log(`✅ Matched raster pattern: "${pattern}" → raster`);
        return 'raster';
      }
    }

    // Default to raster for unknown layers
    console.log(`⚠️ No pattern matched for "${name}", defaulting to raster`);
    return 'raster';
  }

  /**
   * Check if a layer supports CQL filtering
   */
  async supportsCQL(layerName: string): Promise<boolean> {
    const capabilities = await this.getLayerCapabilities(layerName);
    return capabilities?.supports.cql || false;
  }

  /**
   * Check if a layer supports temporal filtering
   */
  async supportsTemporal(layerName: string): Promise<boolean> {
    const capabilities = await this.getLayerCapabilities(layerName);
    return capabilities?.supports.server_time || false;
  }

  /**
   * Get geometry field for vector layers
   */
  async getGeometryField(layerName: string): Promise<string | null> {
    const capabilities = await this.getLayerCapabilities(layerName);
    return capabilities?.geometryField || null;
  }

  /**
   * Get temporal information for a layer
   */
  async getTemporalInfo(layerName: string): Promise<LayerCapabilities['temporal'] | null> {
    const capabilities = await this.getLayerCapabilities(layerName);
    return capabilities?.temporal || null;
  }

  /**
   * Get layer bounds
   */
  async getLayerBounds(layerName: string): Promise<LayerCapabilities['bbox'] | null> {
    const capabilities = await this.getLayerCapabilities(layerName);
    return capabilities?.bbox || null;
  }

  /**
   * Refresh capabilities cache
   */
  async refreshCapabilities(): Promise<void> {
    console.log('🔄 Refreshing layer capabilities cache...');
    this.cache.clear();
    this.lastFetch = null;
    await this.fetchAllCapabilities();
    console.log('✅ Layer capabilities cache refreshed');
  }

  /**
   * Check if UIEngine is responding and trigger cache initialization if needed
   */
  async initializeIfNeeded(): Promise<void> {
    if (this.cache.size === 0 && !this.lastFetch) {
      console.log('🚀 Initializing capabilities cache...');
      try {
        await this.quickFetchCapabilities();
      } catch (error) {
        console.warn('⚠️ Initial cache population failed, will use fallback detection');
      }
    }
  }

  /**
   * Get cache status for debugging
   */
  getCacheStatus(): { size: number; lastFetch: Date | null; isExpired: boolean } {
    return {
      size: this.cache.size,
      lastFetch: this.lastFetch,
      isExpired: this.isCacheExpired()
    };
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalLayers: number;
    vectorLayers: number;
    rasterLayers: number;
    temporalLayers: number;
    lastFetch: Date | null;
    cacheAge: number; // minutes
  } {
    const layers = Array.from(this.cache.values());
    const cacheAge = this.lastFetch 
      ? (Date.now() - this.lastFetch.getTime()) / (1000 * 60)
      : 0;

    return {
      totalLayers: layers.length,
      vectorLayers: layers.filter(l => l.type === 'vector').length,
      rasterLayers: layers.filter(l => l.type === 'raster').length,
      temporalLayers: layers.filter(l => l.temporal?.hasData).length,
      lastFetch: this.lastFetch,
      cacheAge
    };
  }

  /**
   * Fetch all capabilities from server
   */
  private async fetchAllCapabilities(): Promise<void> {
    try {
      console.log('🌐 Fetching layer capabilities from server...');

      const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/capabilities`, {
        timeout: 10000 // 10 second timeout
      });

      if (!Array.isArray(response.data)) {
        console.warn('⚠️ Unexpected capabilities response format:', typeof response.data);
        return;
      }

      console.log(`📋 Received ${response.data.length} layers from server`);
      this.processCapabilitiesData(response.data);

    } catch (error) {
      console.error('❌ Failed to fetch layer capabilities:', error);

      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          console.error('❌ Request timeout - UIEngine may be slow or overloaded');
        } else if (error.code === 'ECONNREFUSED') {
          console.error('❌ Connection refused - UIEngine may not be running');
        } else if (error.response?.status === 404) {
          console.error('❌ Capabilities endpoint not found');
        } else if (error.response?.status >= 500) {
          console.error('❌ Server error - UIEngine capabilities cache may not be initialized');
        }
      }

      // Don't throw - allow fallback mechanisms to work
      console.warn('⚠️ Continuing without capabilities cache - using fallback detection');
    }
  }

  /**
   * Process capabilities data from server response
   */
  private processCapabilitiesData(data: any[]): void {
    console.log(`📋 Processing ${data.length} layers...`);

    // Handle both old discovery format and new enhanced capabilities format
    for (const layer of data) {
      let capabilities: LayerCapabilities | null = null;

      // Check if this is already in enhanced capabilities format
      if (layer.supports && typeof layer.supports === 'object') {
        // This is enhanced capabilities format - use directly
        capabilities = {
          name: layer.name,
          title: layer.title,
          type: layer.type,
          supports: layer.supports,
          geometryField: layer.geometryField,
          temporal: layer.temporal,
          bbox: layer.bbox,
          lastUpdated: new Date(layer.lastUpdated || Date.now())
        };
        console.log(`✅ Using enhanced format for: ${capabilities.name} (${capabilities.type})`);
      } else {
        // This is old discovery format - convert it
        capabilities = this.convertDiscoveryToCapabilities(layer);
        if (capabilities) {
          console.log(`✅ Converted old format for: ${capabilities.name} (${capabilities.type})`);
        }
      }

      if (capabilities) {
        this.cache.set(capabilities.name, capabilities);
      } else {
        console.warn(`⚠️ Failed to process layer: ${layer.name}`);
      }
    }

    this.lastFetch = new Date();
    console.log(`✅ Loaded capabilities for ${this.cache.size} layers`);
  }

  /**
   * Convert discovery data to capabilities format
   */
  private convertDiscoveryToCapabilities(layer: any): LayerCapabilities | null {
    if (!layer.name) {
      return null;
    }

    // Determine layer type based on available information
    const type: 'vector' | 'raster' = this.determineLayerType(layer);

    // Extract temporal information
    let temporal: LayerCapabilities['temporal'] | undefined;
    if (layer.temporal || layer.dimensions?.length > 0) {
      temporal = {
        hasData: true,
        extent: layer.temporal?.extent,
        defaultTime: layer.temporal?.defaultTime,
        units: layer.temporal?.units
      };
    }

    // Extract bbox information
    let bbox: LayerCapabilities['bbox'] | undefined;
    if (layer.bbox) {
      bbox = {
        minx: layer.bbox.minx || layer.bbox[0],
        miny: layer.bbox.miny || layer.bbox[1],
        maxx: layer.bbox.maxx || layer.bbox[2],
        maxy: layer.bbox.maxy || layer.bbox[3],
        crs: layer.bbox.crs || layer.bbox.SRS || 'EPSG:4326'
      };
    }

    return {
      name: layer.name,
      title: layer.title || layer.name,
      type,
      supports: {
        cql: type === 'vector', // CQL only for vector layers
        bbox: true, // BBOX always supported
        wms_sld_clip: type === 'raster', // SLD clipping for raster layers
        wps_crop: type === 'raster', // WPS crop for raster layers (if WPS available)
        server_time: !!temporal // Time filtering if temporal data available
      },
      geometryField: type === 'vector' ? (layer.geometryField || 'the_geom') : undefined,
      temporal,
      bbox,
      lastUpdated: new Date()
    };
  }

  /**
   * Determine layer type from discovery data
   */
  private determineLayerType(layer: any): 'vector' | 'raster' {
    // Check if layer has WFS support (indicates vector)
    if (layer.supports?.WFS || layer.queryable) {
      return 'vector';
    }

    // Check layer name patterns for known vector layers
    const vectorPatterns = [
      'boundaries', 'administrative', 'roads', 'points', 'lines', 'polygons',
      'settlements', 'stations', 'offices', 'services', 'centers'
    ];

    const layerName = layer.name.toLowerCase();
    const layerTitle = (layer.title || '').toLowerCase();

    for (const pattern of vectorPatterns) {
      if (layerName.includes(pattern) || layerTitle.includes(pattern)) {
        return 'vector';
      }
    }

    // Check for raster patterns
    const rasterPatterns = [
      'imagery', 'satellite', 'mosaic', 'dem', 'elevation', 'dsm', 'dtm',
      'landsat', 'sentinel', 'modis', 'ndvi', 'evi', 'moisture', 'temperature'
    ];

    for (const pattern of rasterPatterns) {
      if (layerName.includes(pattern) || layerTitle.includes(pattern)) {
        return 'raster';
      }
    }

    // Default to raster if uncertain
    return 'raster';
  }

  /**
   * Check if cache is expired
   */
  private isCacheExpired(): boolean {
    if (!this.lastFetch) {
      return true;
    }
    
    return (Date.now() - this.lastFetch.getTime()) > this.cacheTimeout;
  }
}

// Global service instance
let globalLayerCapabilitiesService: LayerCapabilitiesService | null = null;

/**
 * Get global layer capabilities service instance
 */
export function getLayerCapabilitiesService(): LayerCapabilitiesService {
  if (!globalLayerCapabilitiesService) {
    globalLayerCapabilitiesService = new LayerCapabilitiesService();
  }
  return globalLayerCapabilitiesService;
}

/**
 * Reset global service instance (for testing)
 */
export function resetLayerCapabilitiesService(): void {
  globalLayerCapabilitiesService = null;
}
