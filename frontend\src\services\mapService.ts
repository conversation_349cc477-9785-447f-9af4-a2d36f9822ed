import axios from 'axios';
// import { API_BASE } from './geoserverService';
const API_BASE = "";
// Function to fetch flood risk data from UI Engine
export const fetchFloodRiskData = async (region: any, timeframe: any) => {
  console.log('Fetching flood risk data for region:', region);
  console.log('Timeframe:', timeframe);
  
  try {
    const params = {
      typeName: 'flood:risk_areas',
      bbox: region ? `${region._southWest.lng},${region._southWest.lat},${region._northEast.lng},${region._northEast.lat}` : undefined,
      startDate: timeframe?.startDate,
      endDate: timeframe?.endDate
    };

    console.log('Request params for flood risk:', params);
    const response = await axios.get(`${API_BASE}/features`, { params });
    console.log('WFS response for flood risk - feature count:', response.data.features?.length || 0);
    
    const mappedData = response.data.features.map((feature: any) => ({
      id: feature.id,
      coordinates: feature.geometry.coordinates[0],
      risk: feature.properties.risk_level,
      date: feature.properties.date
    }));
    
    console.log('Mapped flood risk data:', mappedData);
    return mappedData;
  } catch (error) {
    console.error('Error fetching flood risk data:', error);
    return [];
  }
};

// Function to fetch layer data from UI Engine
export const fetchLayerData = async (layerType: string, bounds: any) => {
  console.log(`Fetching ${layerType} data within bounds:`, bounds);
  
  try {
    const params = {
      typeName: `flood:${layerType}`,
      bbox: bounds ? `${bounds._southWest.lng},${bounds._southWest.lat},${bounds._northEast.lng},${bounds._northEast.lat}` : undefined,
    };

    console.log(`Request params for ${layerType}:`, params);
    const response = await axios.get(`${API_BASE}/features`, { params });
    console.log(`WFS response for ${layerType} - feature count:`, response.data.features?.length || 0);
    
    const mappedData = response.data.features.map((feature: any) => {
      if (layerType === 'cadastre') {
        return {
          id: feature.id,
          bounds: [
            [feature.geometry.coordinates[0][0][1], feature.geometry.coordinates[0][0][0]],
            [feature.geometry.coordinates[0][2][1], feature.geometry.coordinates[0][2][0]]
          ],
          owner: feature.properties.owner
        };
      } else if (layerType === 'dwsVillage') {
        return {
          id: feature.id,
          center: [feature.geometry.coordinates[1], feature.geometry.coordinates[0]],
          radius: feature.properties.radius || 10000,
          name: feature.properties.name,
          population: feature.properties.population
        };
      }
      return feature;
    });
    
    console.log(`Mapped ${layerType} data:`, mappedData);
    return mappedData;
  } catch (error) {
    console.error(`Error fetching ${layerType} data:`, error);
    return [];
  }
};

// Function to generate downloadable data from GeoServer
export const generateDownloadData = async (selectedLayers: any, dateRange: any, region: any) => {
  console.log('Generating download for:', selectedLayers);
  console.log('Date range:', dateRange);
  console.log('Region:', region);
  
  try {
    // Create a list of layer names to download (use actual layer names)
    const layerNames = Object.entries(selectedLayers)
      .filter(([_, isSelected]) => isSelected)
      .map(([name]) => name); // Use actual layer names without hardcoded prefixes

    // Build the download URL
    const params = new URLSearchParams({
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: layerNames.join(','),
      outputFormat: 'SHAPE-ZIP',
      format_options: 'filename:geospatial-data.zip'
    });
    
    if (dateRange) {
      params.append('CQL_FILTER', `date>='${dateRange.startDate}' AND date<='${dateRange.endDate}'`);
    }
    
    const downloadUrl = `${API_BASE}/download?${params.toString()}`;
    
    return {
      url: downloadUrl,
      size: 'Variable',
      format: 'ZIP (contains Shapefile data)'
    };
  } catch (error) {
    console.error('Error generating download:', error);
    return null;
  }
};

// Function to search for a location using GeoServer
export const searchLocation = async (query: string) => {
  console.log('Searching for location:', query);
  
  try {
    const params = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      typeName: 'flood:locations',
      outputFormat: 'application/json',
      CQL_FILTER: `name ILIKE '%${query}%'`
    };

    const response = await axios.get(`${API_BASE}/search`, { params });
    
    if (response.data.features.length > 0) {
      const feature = response.data.features[0];
      return {
        name: feature.properties.name,
        coordinates: [feature.geometry.coordinates[1], feature.geometry.coordinates[0]],
        boundingBox: feature.properties.bbox ? [
          [feature.properties.bbox[1], feature.properties.bbox[0]],
          [feature.properties.bbox[3], feature.properties.bbox[2]]
        ] : null
      };
    }
    return null;
  } catch (error) {
    console.error('Error searching for location:', error);
    return null;
  }
};

