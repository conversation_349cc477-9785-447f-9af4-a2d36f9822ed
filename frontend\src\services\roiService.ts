// ROI API Service - Frontend integration for PostGIS ROI functionality

export interface ROI {
  id?: number;
  name: string;
  description?: string;
  geometry: any; // GeoJSON geometry
  created_by?: string;
  status?: 'active' | 'archived' | 'processing';
  metadata?: any;
  area_sqkm?: number;
  created_at?: string;
  updated_at?: string;
}

export interface SpatialAnalysis {
  roi_id?: number;
  analysis_type?: 'flood_extent' | 'water_level' | 'damage_assessment' | 'temporal_change';
  start_date?: string;
  end_date?: string;
  results?: any;
  geometry?: any;
}

class ROIApiService {
  private baseUrl = '/api/roi';
  private isAvailable: boolean | null = null;

  // Check if PostGIS/ROI features are available
  async checkAvailability(): Promise<boolean> {
    if (this.isAvailable !== null) {
      return this.isAvailable;
    }

    try {
      const response = await fetch('/api/health');
      const health = await response.json();
      this.isAvailable = health.features?.spatial === true;
      return this.isAvailable;
    } catch (error) {
      console.warn('Could not check ROI service availability:', error);
      this.isAvailable = false;
      return false;
    }
  }
  // Wrapper for API calls that checks availability first
  private async ensureAvailable(): Promise<void> {
    const available = await this.checkAvailability();
    if (!available) {
      throw new Error('PostGIS spatial features are not available. Please check your database configuration. Using client-side fallback calculations instead.');
    }
  }

  // Client-side fallback for basic ROI calculations
  private calculateClientSideROI(roi: ROI): ROI {
    // Simple client-side area calculation for basic polygons
    if (roi.geometry && roi.geometry.type === 'Polygon') {
      const coordinates = roi.geometry.coordinates[0];
      let area = 0;
      
      // Simple polygon area calculation using shoelace formula
      for (let i = 0; i < coordinates.length - 1; i++) {
        const [x1, y1] = coordinates[i];
        const [x2, y2] = coordinates[i + 1];
        area += (x1 * y2 - x2 * y1);
      }
      area = Math.abs(area) / 2;
      
      // Convert to approximate square kilometers (very rough approximation)
      const approxSqKm = area * 12364; // Rough conversion factor for degrees to km²
      
      return {
        ...roi,
        id: Date.now(), // Use timestamp as fallback ID
        area_sqkm: approxSqKm,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 'active'
      };
    }
    
    return {
      ...roi,
      id: Date.now(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      status: 'active'
    };
  }

  // In-memory storage for ROIs when database is unavailable
  private memoryROIs: ROI[] = [];  // Create new ROI from user-drawn polygon
  async createROI(roi: ROI): Promise<ROI> {
    try {
      await this.ensureAvailable();
      
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(roi),
      });

      if (!response.ok) {
        const error = await response.json();
        if (error.fallback) {
          throw new Error('Spatial features are not available. PostGIS database is not configured.');
        }
        throw new Error(error.message || 'Failed to create ROI');
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.warn('Database ROI creation failed, using client-side fallback:', error);
      
      // Use client-side fallback
      const fallbackROI = this.calculateClientSideROI(roi);
      this.memoryROIs.push(fallbackROI);
      
      console.log('✅ ROI created with client-side calculations (limited accuracy)');
      return fallbackROI;
    }
  }  // Get all ROIs
  async getAllROIs(): Promise<ROI[]> {
    try {
      await this.ensureAvailable();
      
      const response = await fetch(this.baseUrl);
      
      if (!response.ok) {
        if (response.status === 503) {
          throw new Error('Spatial features are not available. PostGIS database is not configured.');
        }
        throw new Error('Failed to fetch ROIs');
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.warn('Database ROI fetch failed, using in-memory ROIs:', error);
      
      // Return in-memory ROIs as fallback
      return this.memoryROIs;
    }
  }

  // Get ROI by ID
  async getROIById(id: number): Promise<ROI> {
    const response = await fetch(`${this.baseUrl}/${id}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch ROI');
    }

    const result = await response.json();
    return result.data;
  }

  // Update ROI
  async updateROI(id: number, updates: Partial<ROI>): Promise<ROI> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update ROI');
    }

    const result = await response.json();
    return result.data;
  }

  // Delete ROI
  async deleteROI(id: number): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error('Failed to delete ROI');
    }
  }

  // Find intersecting ROIs
  async findIntersectingROIs(geometry: any, bufferMeters: number = 0): Promise<ROI[]> {
    const response = await fetch(`${this.baseUrl}/intersect`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        geometry,
        buffer_meters: bufferMeters,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to find intersecting ROIs');
    }

    const result = await response.json();
    return result.data;
  }

  // Perform spatial analysis
  async performSpatialAnalysis(query: {
    roi_id?: number;
    geometry?: any;
    analysis_type?: string;
    start_date?: string;
    end_date?: string;
    buffer_meters?: number;
  }): Promise<SpatialAnalysis> {
    const response = await fetch(`${this.baseUrl}/analysis`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(query),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to perform spatial analysis');
    }

    const result = await response.json();
    return result.data;
  }

  // Get flood events within ROI
  async getFloodEventsInROI(roiId: number, startDate?: string, endDate?: string): Promise<any[]> {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    const response = await fetch(`${this.baseUrl}/${roiId}/flood-events?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch flood events');
    }

    const result = await response.json();
    return result.data;
  }

  // Get water measurements within ROI
  async getWaterMeasurementsInROI(roiId: number, startDate?: string, endDate?: string): Promise<any[]> {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    const response = await fetch(`${this.baseUrl}/${roiId}/water-measurements?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch water measurements');
    }

    const result = await response.json();
    return result.data;
  }
}

export const roiApiService = new ROIApiService();

// Utility functions for working with ROI data
export const roiUtils = {
  // Convert Leaflet polygon to GeoJSON
  leafletToGeoJSON(leafletPolygon: any): any {
    // Implementation depends on your Leaflet setup
    return {
      type: 'Polygon',
      coordinates: [leafletPolygon.getLatLngs()[0].map((latlng: any) => [latlng.lng, latlng.lat])]
    };
  },

  // Calculate polygon area (approximate)
  calculatePolygonArea(coordinates: number[][]): number {
    // Simple area calculation for display purposes
    // For accurate area, use the backend PostGIS calculation
    let area = 0;
    const n = coordinates.length;
    
    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n;
      area += coordinates[i][0] * coordinates[j][1];
      area -= coordinates[j][0] * coordinates[i][1];
    }
    
    return Math.abs(area) / 2;
  },

  // Format ROI for display
  formatROIForDisplay(roi: ROI): string {
    const area = roi.area_sqkm ? `${roi.area_sqkm.toFixed(2)} km²` : 'Unknown area';
    const created = roi.created_at ? new Date(roi.created_at).toLocaleDateString() : 'Unknown date';
    
    return `${roi.name} (${area}, created ${created})`;
  }
};

export default ROIApiService;
