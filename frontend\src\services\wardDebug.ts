// Debug file for ward loading issues
import { API_CONFIG } from "../config";
import { sanitizeCQLValue } from "./unifiedBoundaryService";

// Utility function to print parameter values
export function debugWardLoading(municipalityId: string): void {
  console.log("======= DEBUGGING WARD LOADING =======");
  console.log("Municipality ID:", municipalityId);
  
  // Check if municipality ID exists
  if (!municipalityId) {
    console.error("ERROR: Municipality ID is empty or undefined");
    return;
  }
  
  // Determine municipality type
  const isMetro = !municipalityId.startsWith("DC");
  console.log("Municipality type:", isMetro ? "Metropolitan" : "Local");
  
  // Construct the CQL filter that would be used
  let cqlFilter = "";
  if (isMetro) {
    cqlFilter = `cat_b='${sanitizeCQLValue(municipalityId)}'`;
    console.log("Metro CQL filter would be:", cqlFilter);
  } else {
    cqlFilter = `cat_b='${sanitizeCQLValue(municipalityId)}' OR municipali ILIKE '%${sanitizeCQLValue(municipalityId)}%'`;
    console.log("Local municipality CQL filter would be:", cqlFilter);
  }
  
  // Log the API URL that would be used
  const params = {
    service: "WFS",
    version: "1.0.0",
    request: "GetFeature",
    typeName: "geonode:wards_2020",
    outputFormat: "application/json",
    maxFeatures: 100,
    CQL_FILTER: cqlFilter
  };
  
  console.log("API base URL:", API_CONFIG.OWS_BASE_URL);
  
  // Construct parameters string for logging
  const paramsString = Object.entries(params)
    .map(([key, value]) => `${key}=${value}`)
    .join("&");
  
  console.log("Full request URL would be:", `${API_CONFIG.OWS_BASE_URL}?${paramsString}`);
  console.log("======= END DEBUG INFO =======");
}

// Function to check if the ward layer exists
export function debugLayerExistence(): void {
  console.log("======= CHECKING LAYER EXISTENCE =======");
  console.log("Checking if wards_2020 layer exists in the WFS capabilities...");
  
  const capabilitiesUrl = `${API_CONFIG.OWS_BASE_URL}?service=WFS&request=GetCapabilities`;
  console.log("GetCapabilities URL:", capabilitiesUrl);
  
  // This would actually be an API call in a real implementation
  console.log("To test this, you can manually open this URL in a browser:");
  console.log(capabilitiesUrl);
  console.log("======= END LAYER CHECK =======");
}
