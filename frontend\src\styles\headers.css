/* Global Header Styles for Consistent Blue Theme */

/* Primary Blue Color Variables */
:root {
  --primary-blue: #007bff;
  --primary-blue-dark: #0056b3;
  --primary-blue-darker: #004085;
  --header-text-white: #ffffff;
  --header-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

/* Base Header Styling */
.app-header-blue {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%) !important;
  color: var(--header-text-white) !important;
  border-bottom: 1px solid var(--primary-blue-darker) !important;
  box-shadow: var(--header-shadow) !important;
}

.app-header-blue * {
  color: var(--header-text-white) !important;
}

/* Modal Headers */
.modal-header.app-header-blue {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%) !important;
  border-bottom: 1px solid var(--primary-blue-darker) !important;
}

.modal-header.app-header-blue .modal-title {
  color: var(--header-text-white) !important;
  font-weight: 600;
}

.modal-header.app-header-blue .btn-close {
  filter: brightness(0) invert(1) !important;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.modal-header.app-header-blue .btn-close:hover {
  opacity: 1;
}

.modal-header.app-header-blue .btn-close:focus {
  box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25) !important;
}

/* Panel Headers */
.panel-header.app-header-blue {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%) !important;
  color: var(--header-text-white) !important;
  border-bottom: 1px solid var(--primary-blue-darker) !important;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-header.app-header-blue .panel-title {
  color: var(--header-text-white) !important;
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.panel-header.app-header-blue .panel-close-btn {
  background: none !important;
  border: none !important;
  color: var(--header-text-white) !important;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  opacity: 0.8;
}

.panel-header.app-header-blue .panel-close-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  opacity: 1;
  transform: scale(1.05);
}

.panel-header.app-header-blue .panel-close-btn:focus {
  box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25) !important;
  outline: none;
}

/* Card Headers */
.card-header.app-header-blue {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%) !important;
  color: var(--header-text-white) !important;
  border-bottom: 1px solid var(--primary-blue-darker) !important;
}

.card-header.app-header-blue .card-title {
  color: var(--header-text-white) !important;
  margin: 0;
  font-weight: 600;
}

.card-header.app-header-blue .btn {
  color: var(--header-text-white) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  transition: all 0.2s ease;
}

.card-header.app-header-blue .btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  transform: scale(1.05);
}

.card-header.app-header-blue .btn-outline-secondary {
  border-color: rgba(255, 255, 255, 0.3) !important;
  color: var(--header-text-white) !important;
}

.card-header.app-header-blue .btn-outline-secondary:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  color: var(--header-text-white) !important;
}

/* Expanded Legend Panel Header */
.expanded-legend-header.app-header-blue {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%) !important;
  color: var(--header-text-white) !important;
  border-bottom: 1px solid var(--primary-blue-darker) !important;
}

.expanded-legend-header.app-header-blue .expanded-legend-title {
  color: var(--header-text-white) !important;
  font-weight: 600;
}

.expanded-legend-header.app-header-blue .expanded-legend-close {
  color: var(--header-text-white) !important;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.expanded-legend-header.app-header-blue .expanded-legend-close:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: var(--header-text-white) !important;
  opacity: 1;
  transform: scale(1.05);
}

/* Tools Panel Header */
.tools-panel-header.app-header-blue {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%) !important;
  color: var(--header-text-white) !important;
  border-bottom: 1px solid var(--primary-blue-darker) !important;
}

.tools-panel-header.app-header-blue .tools-panel-title {
  color: var(--header-text-white) !important;
  font-weight: 600;
}

.tools-panel-header.app-header-blue .tools-panel-close {
  color: var(--header-text-white) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  transition: all 0.2s ease;
}

.tools-panel-header.app-header-blue .tools-panel-close:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  color: var(--header-text-white) !important;
  transform: scale(1.05);
}

/* Legend Panel Title */
.legend-title.app-header-blue {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%) !important;
  color: var(--header-text-white) !important;
  padding: 0.75rem 1rem;
  margin: -15px -15px 15px -15px;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  text-align: center;
  border-bottom: 1px solid var(--primary-blue-darker);
  box-shadow: var(--header-shadow);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .panel-header.app-header-blue {
    padding: 0.75rem 1rem;
  }
  
  .panel-header.app-header-blue .panel-title {
    font-size: 1rem;
  }
  
  .legend-title.app-header-blue {
    padding: 0.5rem 0.75rem;
    margin: -12px -12px 12px -12px;
    font-size: 0.9rem;
  }
}

/* Icon styling in headers */
.app-header-blue .header-icon {
  color: var(--header-text-white) !important;
  opacity: 0.9;
}

.app-header-blue .header-icon:hover {
  opacity: 1;
}

/* Badge styling in blue headers */
.app-header-blue .badge {
  background: rgba(255, 255, 255, 0.2) !important;
  color: var(--header-text-white) !important;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Animation for header appearance */
.app-header-blue {
  animation: headerFadeIn 0.3s ease-out;
}

@keyframes headerFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
