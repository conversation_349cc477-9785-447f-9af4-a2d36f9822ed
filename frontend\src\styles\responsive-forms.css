/* Responsive Form Controls */

/* Base Form Improvements */
.form-control,
.form-select {
  min-height: 44px;
  padding: 0.75rem;
  font-size: 1rem;
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: all 0.2s ease;
  background-color: #fff;
  line-height: 1.5;
}

.form-control:focus,
.form-select:focus {
  border-color: var(--primary-blue, #007bff);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  outline: none;
}

.form-control:disabled,
.form-select:disabled {
  background-color: #f8f9fa;
  opacity: 0.6;
  cursor: not-allowed;
}

/* Input Groups */
.input-group {
  margin-bottom: 1rem;
}

.input-group .form-control,
.input-group .form-select {
  border-radius: 0;
}

.input-group .form-control:first-child,
.input-group .form-select:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.input-group .form-control:last-child,
.input-group .form-select:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.input-group-text {
  min-height: 44px;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border: 2px solid #e9ecef;
  font-size: 1rem;
  display: flex;
  align-items: center;
}

/* Checkboxes and Radio Buttons */
.form-check {
  margin-bottom: 0.75rem;
  padding-left: 2rem;
}

.form-check-input {
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0.125rem;
  margin-left: -2rem;
  cursor: pointer;
  border: 2px solid #dee2e6;
  transition: all 0.2s ease;
}

.form-check-input:checked {
  background-color: var(--primary-blue, #007bff);
  border-color: var(--primary-blue, #007bff);
}

.form-check-input:focus {
  border-color: var(--primary-blue, #007bff);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check-label {
  font-size: 1rem;
  line-height: 1.5;
  cursor: pointer;
  padding-left: 0.5rem;
  user-select: none;
}

/* Buttons */
.btn {
  min-height: 44px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  user-select: none;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:active {
  transform: translateY(0);
}

.btn:focus {
  outline: 2px solid var(--primary-blue, #007bff);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Button Variants */
.btn-primary {
  background-color: var(--primary-blue, #007bff);
  border-color: var(--primary-blue, #007bff);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-blue-dark, #0056b3);
  border-color: var(--primary-blue-dark, #0056b3);
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.btn-outline-primary {
  background-color: transparent;
  border-color: var(--primary-blue, #007bff);
  color: var(--primary-blue, #007bff);
}

.btn-outline-primary:hover {
  background-color: var(--primary-blue, #007bff);
  color: white;
}

/* Dropdowns */
.dropdown-toggle {
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dropdown-menu {
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  margin-top: 0.25rem;
  max-height: 300px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.dropdown-item {
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  transition: background-color 0.2s ease;
  cursor: pointer;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: #f8f9fa;
  color: var(--primary-blue, #007bff);
}

.dropdown-item.active {
  background-color: var(--primary-blue, #007bff);
  color: white;
}

/* Form Labels */
.form-label {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #495057;
}

.form-text {
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

/* Date Inputs */
input[type="date"],
input[type="datetime-local"],
input[type="time"] {
  min-height: 44px;
  padding: 0.75rem;
  font-size: 1rem;
  border-radius: 8px;
  border: 2px solid #e9ecef;
}

/* Range Inputs */
input[type="range"] {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #e9ecef;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--primary-blue, #007bff);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input[type="range"]::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--primary-blue, #007bff);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Mobile Responsive Styles */
@media (max-width: 767px) {
  .form-control,
  .form-select,
  .btn,
  .input-group-text {
    min-height: 48px;
    font-size: 1rem;
    padding: 0.875rem;
  }

  .form-check-input {
    width: 1.375rem;
    height: 1.375rem;
  }

  .form-check-label {
    font-size: 1rem;
  }

  .btn {
    padding: 0.875rem 1.25rem;
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .btn:last-child {
    margin-bottom: 0;
  }

  .dropdown-menu {
    max-height: 250px;
  }

  .dropdown-item {
    padding: 1rem;
    font-size: 1rem;
  }

  /* Stack form groups vertically */
  .row .col-auto,
  .row .col-sm-auto,
  .row .col-md-auto {
    width: 100%;
    margin-bottom: 0.75rem;
  }

  .row .col-auto:last-child,
  .row .col-sm-auto:last-child,
  .row .col-md-auto:last-child {
    margin-bottom: 0;
  }
}

@media (max-width: 479px) {
  .form-control,
  .form-select,
  .btn,
  .input-group-text {
    min-height: 44px;
    font-size: 0.9rem;
    padding: 0.75rem;
  }

  .form-check-input {
    width: 1.25rem;
    height: 1.25rem;
  }

  .form-check-label {
    font-size: 0.9rem;
  }

  .btn {
    padding: 0.75rem 1rem;
  }

  .dropdown-item {
    padding: 0.875rem;
    font-size: 0.9rem;
  }
}

/* Touch Improvements */
@media (hover: none) and (pointer: coarse) {
  .btn:hover {
    transform: none;
    box-shadow: none;
  }

  .dropdown-item:hover {
    background-color: transparent;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .form-control,
  .form-select,
  .btn {
    border-width: 3px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .form-control,
  .form-select,
  .btn,
  .dropdown-item {
    transition: none;
  }

  .btn:hover {
    transform: none;
  }
}
