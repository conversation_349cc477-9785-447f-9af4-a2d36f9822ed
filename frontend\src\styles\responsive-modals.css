/* Responsive Modal Styles */

/* Base Modal Improvements */
.modal {
  --modal-z-index: 1055;
  z-index: var(--modal-z-index) !important;
}

.modal-backdrop {
  --backdrop-z-index: 1050;
  z-index: var(--backdrop-z-index) !important;
  backdrop-filter: blur(2px);
}

.modal-dialog {
  margin: 1rem;
  max-width: 90vw;
  width: auto;
}

.modal-content {
  border-radius: 12px;
  border: none;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.modal-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, var(--primary-blue, #007bff) 0%, var(--primary-blue-dark, #0056b3) 100%);
  color: white;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.modal-body {
  padding: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background-color: #f8f9fa;
}

/* Close Button Improvements */
.modal-header .btn-close,
.modal-header .close {
  color: white;
  opacity: 0.8;
  font-size: 1.25rem;
  padding: 0.5rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-header .btn-close:hover,
.modal-header .close:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

/* Touch-friendly Buttons */
.modal .btn {
  min-height: 44px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.modal .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modal .btn:active {
  transform: translateY(0);
}

/* Form Controls */
.modal .form-control,
.modal .form-select {
  min-height: 44px;
  padding: 0.75rem;
  font-size: 1rem;
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.modal .form-control:focus,
.modal .form-select:focus {
  border-color: var(--primary-blue, #007bff);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.modal .form-check-input {
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0.125rem;
}

.modal .form-check-label {
  font-size: 1rem;
  line-height: 1.5;
  padding-left: 0.5rem;
  cursor: pointer;
}

/* Responsive Breakpoints */
@media (max-width: 767px) {
  .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100vw - 1rem);
    max-height: calc(100vh - 1rem);
  }

  .modal-content {
    border-radius: 8px;
    max-height: calc(100vh - 1rem);
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    padding: 1rem 1.25rem;
    flex-shrink: 0;
  }

  .modal-title {
    font-size: 1.1rem;
  }

  .modal-body {
    padding: 1.25rem;
    max-height: none;
    flex: 1;
    overflow-y: auto;
  }

  .modal-footer {
    padding: 1rem 1.25rem;
    flex-shrink: 0;
  }

  .modal .btn {
    min-height: 48px;
    font-size: 1rem;
    padding: 0.875rem 1.25rem;
  }

  .modal .form-control,
  .modal .form-select {
    min-height: 48px;
    font-size: 1rem;
  }
}

@media (max-width: 479px) {
  .modal-dialog {
    margin: 0.25rem;
    max-width: calc(100vw - 0.5rem);
    max-height: calc(100vh - 0.5rem);
  }

  .modal-header {
    padding: 0.875rem 1rem;
  }

  .modal-title {
    font-size: 1rem;
  }

  .modal-body {
    padding: 1rem;
  }

  .modal-footer {
    padding: 0.875rem 1rem;
  }

  .modal .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .modal .btn:last-child {
    margin-bottom: 0;
  }
}

/* Landscape Orientation */
@media (max-width: 767px) and (orientation: landscape) {
  .modal-dialog {
    max-height: calc(100vh - 0.5rem);
  }

  .modal-body {
    max-height: calc(100vh - 200px);
  }
}

/* Full Screen Modal for Very Small Screens */
@media (max-width: 479px) and (max-height: 600px) {
  .modal-dialog {
    margin: 0;
    max-width: 100vw;
    max-height: 100vh;
    height: 100vh;
  }

  .modal-content {
    border-radius: 0;
    height: 100%;
  }
}

/* Scrollbar Styling */
.modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation Improvements */
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}

.modal.show .modal-dialog {
  transform: none;
}

/* Accessibility Improvements */
.modal:focus {
  outline: none;
}

.modal .btn:focus,
.modal .form-control:focus,
.modal .form-select:focus {
  outline: 2px solid var(--primary-blue, #007bff);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .modal-content {
    border: 2px solid currentColor;
  }

  .modal .btn {
    border: 2px solid currentColor;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }

  .modal .btn {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .modal {
    position: static !important;
    z-index: auto !important;
  }

  .modal-backdrop {
    display: none !important;
  }

  .modal-dialog {
    margin: 0 !important;
    max-width: none !important;
  }

  .modal-content {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }

  .modal-footer {
    display: none !important;
  }
}
