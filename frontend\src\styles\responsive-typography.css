/* Responsive Typography System */

/* CSS Custom Properties for Typography */
:root {
  /* Base Font Sizes (fluid scaling) */
  --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --font-size-xxl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
  --font-size-xxxl: clamp(2rem, 1.7rem + 1.5vw, 3rem);

  /* Heading Sizes */
  --h1-size: var(--font-size-xxxl);
  --h2-size: var(--font-size-xxl);
  --h3-size: var(--font-size-xl);
  --h4-size: var(--font-size-lg);
  --h5-size: var(--font-size-base);
  --h6-size: var(--font-size-sm);

  /* Line Heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Letter Spacing */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;

  /* Spacing Scale (responsive) */
  --space-xs: clamp(0.25rem, 0.2rem + 0.25vw, 0.375rem);
  --space-sm: clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem);
  --space-md: clamp(1rem, 0.8rem + 1vw, 1.5rem);
  --space-lg: clamp(1.5rem, 1.2rem + 1.5vw, 2.25rem);
  --space-xl: clamp(2rem, 1.6rem + 2vw, 3rem);
  --space-xxl: clamp(3rem, 2.4rem + 3vw, 4.5rem);
}

/* Base Typography */
html {
  font-size: 16px; /* Base font size */
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  color: #212529;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Headings */
h1, .h1 {
  font-size: var(--h1-size);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  margin-bottom: var(--space-lg);
  margin-top: 0;
}

h2, .h2 {
  font-size: var(--h2-size);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  margin-bottom: var(--space-md);
  margin-top: 0;
}

h3, .h3 {
  font-size: var(--h3-size);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-md);
  margin-top: 0;
}

h4, .h4 {
  font-size: var(--h4-size);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-sm);
  margin-top: 0;
}

h5, .h5 {
  font-size: var(--h5-size);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-sm);
  margin-top: 0;
}

h6, .h6 {
  font-size: var(--h6-size);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-xs);
  margin-top: 0;
}

/* Paragraphs and Text */
p {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-md);
  margin-top: 0;
}

.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-xxl {
  font-size: var(--font-size-xxl);
}

/* Font Weights */
.font-light {
  font-weight: var(--font-weight-light);
}

.font-normal {
  font-weight: var(--font-weight-normal);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

/* Line Heights */
.leading-tight {
  line-height: var(--line-height-tight);
}

.leading-normal {
  line-height: var(--line-height-normal);
}

.leading-relaxed {
  line-height: var(--line-height-relaxed);
}

.leading-loose {
  line-height: var(--line-height-loose);
}

/* Letter Spacing */
.tracking-tight {
  letter-spacing: var(--letter-spacing-tight);
}

.tracking-normal {
  letter-spacing: var(--letter-spacing-normal);
}

.tracking-wide {
  letter-spacing: var(--letter-spacing-wide);
}

.tracking-wider {
  letter-spacing: var(--letter-spacing-wider);
}

/* Responsive Spacing Utilities */
.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }
.m-xxl { margin: var(--space-xxl); }

.mt-xs { margin-top: var(--space-xs); }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }
.mt-xxl { margin-top: var(--space-xxl); }

.mb-xs { margin-bottom: var(--space-xs); }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }
.mb-xxl { margin-bottom: var(--space-xxl); }

.ml-xs { margin-left: var(--space-xs); }
.ml-sm { margin-left: var(--space-sm); }
.ml-md { margin-left: var(--space-md); }
.ml-lg { margin-left: var(--space-lg); }
.ml-xl { margin-left: var(--space-xl); }
.ml-xxl { margin-left: var(--space-xxl); }

.mr-xs { margin-right: var(--space-xs); }
.mr-sm { margin-right: var(--space-sm); }
.mr-md { margin-right: var(--space-md); }
.mr-lg { margin-right: var(--space-lg); }
.mr-xl { margin-right: var(--space-xl); }
.mr-xxl { margin-right: var(--space-xxl); }

.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }
.p-xxl { padding: var(--space-xxl); }

.pt-xs { padding-top: var(--space-xs); }
.pt-sm { padding-top: var(--space-sm); }
.pt-md { padding-top: var(--space-md); }
.pt-lg { padding-top: var(--space-lg); }
.pt-xl { padding-top: var(--space-xl); }
.pt-xxl { padding-top: var(--space-xxl); }

.pb-xs { padding-bottom: var(--space-xs); }
.pb-sm { padding-bottom: var(--space-sm); }
.pb-md { padding-bottom: var(--space-md); }
.pb-lg { padding-bottom: var(--space-lg); }
.pb-xl { padding-bottom: var(--space-xl); }
.pb-xxl { padding-bottom: var(--space-xxl); }

.pl-xs { padding-left: var(--space-xs); }
.pl-sm { padding-left: var(--space-sm); }
.pl-md { padding-left: var(--space-md); }
.pl-lg { padding-left: var(--space-lg); }
.pl-xl { padding-left: var(--space-xl); }
.pl-xxl { padding-left: var(--space-xxl); }

.pr-xs { padding-right: var(--space-xs); }
.pr-sm { padding-right: var(--space-sm); }
.pr-md { padding-right: var(--space-md); }
.pr-lg { padding-right: var(--space-lg); }
.pr-xl { padding-right: var(--space-xl); }
.pr-xxl { padding-right: var(--space-xxl); }

/* Text Alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* Text Colors */
.text-primary { color: var(--primary-blue, #007bff); }
.text-secondary { color: #6c757d; }
.text-success { color: #28a745; }
.text-danger { color: #dc3545; }
.text-warning { color: #ffc107; }
.text-info { color: #17a2b8; }
.text-light { color: #f8f9fa; }
.text-dark { color: #343a40; }
.text-muted { color: #6c757d; }
.text-white { color: #fff; }

/* Mobile Typography Adjustments */
@media (max-width: 767px) {
  :root {
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 0.75rem;
    --space-lg: 1rem;
    --space-xl: 1.25rem;
    --space-xxl: 1.5rem;
  }

  h1, .h1 { margin-bottom: var(--space-md); }
  h2, .h2 { margin-bottom: var(--space-sm); }
  h3, .h3 { margin-bottom: var(--space-sm); }
  h4, .h4 { margin-bottom: var(--space-xs); }
  h5, .h5 { margin-bottom: var(--space-xs); }
  h6, .h6 { margin-bottom: var(--space-xs); }
  
  p { margin-bottom: var(--space-sm); }
}

@media (max-width: 479px) {
  :root {
    --space-xs: 0.2rem;
    --space-sm: 0.4rem;
    --space-md: 0.6rem;
    --space-lg: 0.8rem;
    --space-xl: 1rem;
    --space-xxl: 1.2rem;
  }
}

/* Reading Optimization */
.text-readable {
  max-width: 65ch;
  line-height: var(--line-height-relaxed);
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  body {
    color: #000;
    background-color: #fff;
  }
  
  .text-muted {
    color: #333;
  }
}

/* Print Styles */
@media print {
  body {
    font-size: 12pt;
    line-height: 1.4;
    color: #000;
  }
  
  h1, .h1 { font-size: 18pt; }
  h2, .h2 { font-size: 16pt; }
  h3, .h3 { font-size: 14pt; }
  h4, .h4 { font-size: 12pt; }
  h5, .h5 { font-size: 11pt; }
  h6, .h6 { font-size: 10pt; }
}
