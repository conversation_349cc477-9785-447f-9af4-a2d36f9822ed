/* Responsive Design Foundation */

/* CSS Custom Properties for Responsive Design */
:root {
  /* Breakpoints */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;

  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-xxl: 3rem;

  /* Fluid Typography */
  --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --font-size-xxl: clamp(1.5rem, 1.3rem + 1vw, 2rem);

  /* Container Sizes */
  --container-xs: 100%;
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-xxl: 1320px;

  /* Sidebar Dimensions */
  --sidebar-width-desktop: 25vw;
  --sidebar-min-width: 320px;
  --sidebar-max-width: 500px;
  --sidebar-collapsed-width: 60px;

  /* Z-index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Base Reset and Responsive Improvements */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
  overflow-x: hidden;
}

/* Responsive Container Queries */
@container (max-width: 768px) {
  .responsive-container {
    padding: var(--space-sm);
  }
}

/* Utility Classes for Responsive Design */
.container-responsive {
  width: 100%;
  max-width: var(--container-xxl);
  margin: 0 auto;
  padding: 0 var(--space-md);
}

.flex-responsive {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.grid-responsive {
  display: grid;
  gap: var(--space-md);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Touch-friendly Interactive Elements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-touch {
  padding: var(--space-sm) var(--space-md);
  min-height: 44px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--font-size-base);
}

.btn-touch:hover,
.btn-touch:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive Text */
.text-responsive {
  font-size: var(--font-size-base);
  line-height: 1.6;
}

.text-responsive-sm {
  font-size: var(--font-size-sm);
}

.text-responsive-lg {
  font-size: var(--font-size-lg);
}

/* Responsive Spacing */
.p-responsive {
  padding: var(--space-md);
}

.m-responsive {
  margin: var(--space-md);
}

.gap-responsive {
  gap: var(--space-md);
}

/* Mobile-first Media Queries */
@media (max-width: 479px) {
  :root {
    --space-md: 0.75rem;
    --space-lg: 1rem;
    --space-xl: 1.25rem;
  }
  
  .container-responsive {
    padding: 0 var(--space-sm);
  }
  
  .flex-responsive {
    gap: var(--space-sm);
  }
  
  .grid-responsive {
    grid-template-columns: 1fr;
    gap: var(--space-sm);
  }
}

@media (min-width: 480px) and (max-width: 767px) {
  .grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (min-width: 992px) {
  .grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

/* Responsive Visibility Utilities */
.d-mobile-only {
  display: block;
}

.d-desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .d-mobile-only {
    display: none;
  }
  
  .d-desktop-only {
    display: block;
  }
}

/* Responsive Flexbox Utilities */
.flex-column-mobile {
  flex-direction: column;
}

@media (min-width: 768px) {
  .flex-column-mobile {
    flex-direction: row;
  }
}

/* Scrollable Areas */
.scrollable-y {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.scrollable-x {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Focus Management for Accessibility */
.focus-visible {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .btn-touch {
    border: 2px solid currentColor;
  }
}

/* Print Styles */
@media print {
  .d-print-none {
    display: none !important;
  }
  
  .sidebar-container,
  .navbar {
    display: none !important;
  }
  
  .map-container {
    width: 100% !important;
    height: 100vh !important;
  }
}
