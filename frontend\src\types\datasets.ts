// TypeScript interfaces for the datasets API response

export interface Main {
    links:     Links;
    total:     number;
    page:      number;
    page_size: number;
    datasets:  Dataset[];
}

export interface Dataset {
    rating:                       string;
    purpose:                      null;
    srid:                         string;
    has_time:                     boolean;
    charset:                      Charset;
    maintenance_frequency:        null;
    processed:                    boolean;
    temporal_extent_end:          null;
    is_approved:                  boolean;
    abstract:                     string;
    time_regex:                   null;
    temporal_extent_start:        null;
    ll_bbox_polygon:              BboxPolygon;
    keywords:                     Keyword[];
    popular_count:                string;
    processor:                    any[];
    advertised:                   boolean;
    raw_constraints_other:        Raw;
    polymorphic_ctype_id:         string;
    uuid:                         string;
    license:                      License;
    resource_provider:            any[];
    extent:                       Extent;
    doi:                          null;
    last_updated:                 Date;
    custodian:                    any[];
    link:                         string;
    metadata_uploaded_preserve:   boolean;
    owner:                        Owner;
    spatial_representation_type:  null;
    featured:                     boolean;
    is_mosaic:                    boolean;
    raw_purpose:                  Raw;
    perms:                        string[];
    links:                        Link[];
    raw_abstract:                 string;
    resource_user:                any[];
    metadata_author:              Owner[];
    distributor:                  any[];
    regions:                      any[];
    thumbnail_url:                string;
    bbox_polygon:                 BboxPolygon;
    publisher:                    any[];
    metadata_only:                boolean;
    state:                        State;
    pk:                           string;
    elevation_regex:              null;
    has_elevation:                boolean;
    store:                        string;
    subtype:                      Subtype;
    sourcetype:                   Sourcetype;
    group:                        null;
    download_urls:                DownloadURL[];
    principal_investigator:       any[];
    styles:                       Style[];
    is_published:                 boolean;
    attribution:                  null;
    share_count:                  string;
    featureinfo_custom_template:  string;
    ptype:                        Ptype;
    download_url:                 string;
    edition:                      null;
    workspace:                    Workspace;
    tkeywords:                    any[];
    title:                        string;
    date:                         Date;
    data_quality_statement:       null;
    detail_url:                   string;
    originator:                   any[];
    alternate:                    string;
    created:                      Date;
    embed_url:                    string;
    category:                     Category;
    restriction_code_type:        null;
    resource_type:                ResourceType;
    language:                     Language;
    supplemental_information:     SupplementalInformation;
    favorite:                     boolean;
    raw_data_quality_statement:   Raw;
    name:                         string;
    default_style:                Style;
    poc:                          Owner[];
    constraints_other:            null;
    raw_supplemental_information: SupplementalInformation;
    is_copyable:                  boolean;
    date_type:                    DateType;
}

export interface BboxPolygon {
    type:        Type;
    coordinates: Array<Array<number[]>>;
}

export enum Type {
    Polygon = "Polygon",
}

export interface Category {
    identifier:     string;
    gn_description: string;
}

export enum Charset {
    UTF8 = "UTF-8",
}

export enum DateType {
    Publication = "publication",
}

export interface Style {
    pk:        number;
    name:      string;
    workspace: Workspace;
    sld_title: SldTitle;
    sld_url:   string;
}

export enum SldTitle {
    BlueLine = "Blue Line",
    DefaultPolygon = "Default Polygon",
    OpaqueRaster = "Opaque Raster",
    RedSquarePoint = "Red Square Point",
}

export enum Workspace {
    Geonode = "geonode",
}

export interface DownloadURL {
    url:       string;
    ajax_safe: boolean;
    default:   boolean;
}

export interface Extent {
    coords: number[];
    srid:   Srid;
}

export enum Srid {
    Epsg4326 = "EPSG:4326",
}

export interface Keyword {
    name: string;
    slug: Slug;
}

export enum Slug {
    Data = "data",
    HTML = "html",
    Image = "image",
    Metadata = "metadata",
    OgcWcs = "OGC:WCS",
    OgcWfs = "OGC:WFS",
    OgcWms = "OGC:WMS",
}

export enum Language {
    Eng = "eng",
}

export interface License {
    identifier: Identifier;
}

export enum Identifier {
    NotSpecified = "not_specified",
}

export interface Link {
    extension: string;
    link_type: Slug;
    name:      string;
    mime:      MIME;
    url:       string;
    extras?:   Extras;
}

export interface Extras {
    type:    string;
    content: Content;
}

export interface Content {
    title:        string;
    description:  null;
    type:         string;
    download_url: string;
}

export enum MIME {
    ApplicationPDF = "application/pdf",
    CSV = "csv",
    Empty = "",
    Excel = "excel",
    Gml2 = "gml2",
    ImageJPEG = "image/jpeg",
    ImagePNG = "image/png",
    ImageTiff = "image/tiff",
    JSON = "json",
    ShapeZip = "SHAPE-ZIP",
    TextHTML = "text/html",
    TextXML = "text/xml",
    TextXMLSubtypeGML311 = "text/xml; subtype=gml/3.1.1",
}

export interface Owner {
    pk:           number;
    username:     Username;
    first_name:   FirstName;
    last_name:    LastName;
    avatar:       string;
    is_superuser: boolean;
    is_staff:     boolean;
    email:        Email;
    link:         string;
    perms?:       Perm[];
}

export enum Email {
    JmashalaneSansaOrgZa = "<EMAIL>",
}

export enum FirstName {
    Morwapula = "Morwapula",
}

export enum LastName {
    Mashalane = "Mashalane",
}

export enum Perm {
    AddDataset = "add_dataset",
    AddResource = "add_resource",
    ChangeResourcebaseMetadata = "change_resourcebase_metadata",
    DeleteDataset = "delete_dataset",
    DownloadResourcebase = "download_resourcebase",
    ViewDataset = "view_dataset",
    ViewMap = "view_map",
    ViewMaplayer = "view_maplayer",
    ViewResourcebase = "view_resourcebase",
}

export enum Username {
    Admin = "admin",
}

export enum Ptype {
    GxpWmscsource = "gxp_wmscsource",
}

export enum Raw {
    None = "None",
}

export enum SupplementalInformation {
    NoInformationProvided = "No information provided",
}

export enum ResourceType {
    Dataset = "dataset",
}

export enum Sourcetype {
    Local = "LOCAL",
}

export enum State {
    Processed = "PROCESSED",
}

export enum Subtype {
    Raster = "raster",
    Vector = "vector",
}

export interface Links {
    next:     string;
    previous: null;
}
