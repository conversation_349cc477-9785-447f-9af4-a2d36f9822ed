// AOI Clipping Debugger - Comprehensive testing utility
// Call from browser console: window.aoiDebugger.runFullTest()

import { getSelectedBoundaryGeometry } from '../services/unifiedBoundaryService';
import { API_CONFIG } from '../config';

interface GeometryTestResult {
  success: boolean;
  hasGeometry: boolean;
  hasFeature: boolean;
  hasBounds: boolean;
  geometryType?: string;
  coordinateCount?: number;
  error?: string;
  rawResponse?: any;
}

interface CQLTestResult {
  success: boolean;
  wktGenerated: boolean;
  cqlFilterCreated: boolean;
  serverResponse?: any;
  requestUrl?: string;
  error?: string;
  responseStatus?: number;
}

interface WKTTestResult {
  success: boolean;
  wktString?: string;
  geometryType?: string;
  coordinateCount?: number;
  error?: string;
}

class AOIClippingDebugger {
  private testProvince = 'Western Cape';
  private testDistrict = 'City of Cape Town';
  private testLayer = 'geonode:south_africa_municipal_boundaries';

  /**
   * Test 1: Verify getSelectedBoundaryGeometry() returns proper geometry data
   */
  async testBoundaryGeometry(filters: {
    province?: string;
    district?: string;
    municipality?: string;
    ward?: string;
  }): Promise<GeometryTestResult> {
    console.log('🔍 Testing boundary geometry retrieval...');
    console.log('📋 Filters:', filters);

    try {
      const result = await getSelectedBoundaryGeometry(filters);
      
      const testResult: GeometryTestResult = {
        success: true,
        hasGeometry: !!result.geometry,
        hasFeature: !!result.feature,
        hasBounds: !!result.bounds,
        geometryType: result.geometry?.type,
        coordinateCount: this.countCoordinates(result.geometry || undefined),
        rawResponse: result
      };

      console.log('✅ Geometry test results:', testResult);
      
      if (result.geometry) {
        console.log('📐 Geometry details:', {
          type: result.geometry.type,
          coordinates: (result.geometry as any).coordinates ? 'Present' : 'Missing',
          coordinateCount: this.countCoordinates(result.geometry)
        });
      } else {
        console.warn('⚠️ No geometry returned - this will prevent precise clipping');
      }

      if (result.bounds) {
        console.log('📦 Bounds:', result.bounds);
      } else {
        console.warn('⚠️ No bounds returned - fallback clipping may not work');
      }

      return testResult;
    } catch (error) {
      console.error('❌ Geometry test failed:', error);
      return {
        success: false,
        hasGeometry: false,
        hasFeature: false,
        hasBounds: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test 2: Verify WKT conversion handles different geometry types
   */
  testWKTConversion(geometry: GeoJSON.Geometry): WKTTestResult {
    console.log('🔄 Testing WKT conversion...');
    console.log('📐 Input geometry:', geometry);

    try {
      const wktString = this.convertGeoJSONToWKT(geometry);
      
      const result: WKTTestResult = {
        success: true,
        wktString,
        geometryType: geometry.type,
        coordinateCount: this.countCoordinates(geometry)
      };

      console.log('✅ WKT conversion successful:', result);
      console.log('📝 Generated WKT:', wktString.substring(0, 200) + '...');

      return result;
    } catch (error) {
      console.error('❌ WKT conversion failed:', error);
      return {
        success: false,
        geometryType: geometry.type,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test 3: Verify server accepts CQL_FILTER parameter format
   */
  async testCQLFilter(layerName: string, geometry: GeoJSON.Geometry): Promise<CQLTestResult> {
    console.log('🌐 Testing CQL filter with server...');
    console.log('🗺️ Layer:', layerName);
    console.log('📐 Geometry type:', geometry.type);

    try {
      // Generate WKT
      const wktString = this.convertGeoJSONToWKT(geometry);
      console.log('📝 Generated WKT (preview):', wktString.substring(0, 100) + '...');

      // Create CQL filter (use generic geometry field name to avoid assuming the_geom)
      const cqlFilter = `INTERSECTS(geometry, ${wktString})`;
      console.log('🔍 CQL Filter (preview):', cqlFilter.substring(0, 100) + '...');

      // Build test URL
      const testParams = new URLSearchParams({
        SERVICE: 'WMS',
        VERSION: '1.1.1',
        REQUEST: 'GetMap',
        LAYERS: layerName,
        STYLES: '',
        FORMAT: 'image/png',
        TRANSPARENT: 'true',
        SRS: 'EPSG:4326',
        BBOX: '-180,-90,180,90',
        WIDTH: '256',
        HEIGHT: '256',
        CQL_FILTER: cqlFilter
      });

      const testUrl = `${API_CONFIG.BASE_URL}/ows/wms-proxy?${testParams.toString()}`;
      console.log('🔗 Test URL (truncated):', testUrl.substring(0, 200) + '...');

      // Make test request
      const response = await fetch(testUrl);
      
      const result: CQLTestResult = {
        success: response.ok,
        wktGenerated: true,
        cqlFilterCreated: true,
        responseStatus: response.status,
        requestUrl: testUrl
      };

      if (response.ok) {
        console.log('✅ CQL filter accepted by server');
        result.serverResponse = 'Success - Image returned';
      } else {
        console.error('❌ CQL filter rejected by server');
        const errorText = await response.text();
        result.serverResponse = errorText;
        result.error = `HTTP ${response.status}: ${errorText}`;
      }

      console.log('📊 CQL test results:', result);
      return result;
    } catch (error) {
      console.error('❌ CQL test failed:', error);
      return {
        success: false,
        wktGenerated: false,
        cqlFilterCreated: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test 4: Run comprehensive edge case tests
   */
  async testEdgeCases(): Promise<void> {
    console.log('🧪 Running edge case tests...');

    // Test 1: Empty geometry
    console.log('\n📍 Test 1: Empty coordinates');
    try {
      const emptyPolygon: GeoJSON.Polygon = {
        type: 'Polygon',
        coordinates: [[]]
      };
      this.testWKTConversion(emptyPolygon);
    } catch (error) {
      console.log('✅ Empty coordinates properly rejected:', error);
    }

    // Test 2: Invalid coordinates
    console.log('\n📍 Test 2: Invalid coordinates');
    try {
      const invalidPolygon: GeoJSON.Polygon = {
        type: 'Polygon',
        coordinates: [[[NaN, NaN], [0, 0], [1, 1], [NaN, NaN]]]
      };
      this.testWKTConversion(invalidPolygon);
    } catch (error) {
      console.log('✅ Invalid coordinates properly rejected:', error);
    }

    // Test 3: Very large geometry
    console.log('\n📍 Test 3: Large geometry stress test');
    try {
      const largeCoords: [number, number][] = [];
      for (let i = 0; i < 1000; i++) {
        largeCoords.push([i * 0.001, i * 0.001]);
      }
      largeCoords.push(largeCoords[0]); // Close polygon

      const largePolygon: GeoJSON.Polygon = {
        type: 'Polygon',
        coordinates: [largeCoords]
      };

      const wktResult = this.testWKTConversion(largePolygon);
      console.log('📏 Large geometry WKT length:', wktResult.wktString?.length);
    } catch (error) {
      console.log('⚠️ Large geometry test failed:', error);
    }

    // Test 4: MultiPolygon with multiple parts
    console.log('\n📍 Test 4: MultiPolygon test');
    try {
      const multiPolygon: GeoJSON.MultiPolygon = {
        type: 'MultiPolygon',
        coordinates: [
          [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
          [[[2, 2], [3, 2], [3, 3], [2, 3], [2, 2]]]
        ]
      };
      this.testWKTConversion(multiPolygon);
    } catch (error) {
      console.log('❌ MultiPolygon test failed:', error);
    }
  }

  /**
   * Master test function - runs all tests
   */
  async runFullTest(): Promise<void> {
    console.log('🚀 Starting comprehensive AOI clipping debug test...');
    console.log('================================================');

    // Test with default filters
    const filters = {
      province: this.testProvince,
      district: this.testDistrict
    };

    try {
      // Test 1: Boundary geometry retrieval
      console.log('\n🔍 TEST 1: Boundary Geometry Retrieval');
      console.log('======================================');
      const geometryResult = await this.testBoundaryGeometry(filters);

      if (geometryResult.success && geometryResult.hasGeometry) {
        // Test 2: WKT conversion
        console.log('\n🔄 TEST 2: WKT Conversion');
        console.log('========================');
        const wktResult = this.testWKTConversion(geometryResult.rawResponse.geometry);

        if (wktResult.success) {
          // Test 3: CQL filter server test
          console.log('\n🌐 TEST 3: CQL Filter Server Test');
          console.log('=================================');
          await this.testCQLFilter(this.testLayer, geometryResult.rawResponse.geometry);
        }
      }

      // Test 4: Edge cases
      console.log('\n🧪 TEST 4: Edge Case Testing');
      console.log('============================');
      await this.testEdgeCases();

      console.log('\n✅ Full test suite completed!');
      console.log('Check the results above to identify any issues.');
    } catch (error) {
      console.error('❌ Full test suite failed:', error);
    }
  }

  /**
   * Quick test for current AOI state
   */
  async testCurrentAOI(): Promise<void> {
    // Try to get AOI data from global state if available
    const aoiData = (window as any).currentAOIData;
    
    if (!aoiData) {
      console.log('❌ No current AOI data found. Please select a region first.');
      console.log('💡 You can run window.aoiDebugger.runFullTest() for a complete test.');
      return;
    }

    console.log('🎯 Testing current AOI data...');
    console.log('Current AOI:', aoiData);

    if (aoiData.geometry) {
      const wktResult = this.testWKTConversion(aoiData.geometry);
      if (wktResult.success && wktResult.wktString) {
        await this.testCQLFilter(this.testLayer, aoiData.geometry);
      }
    } else {
      console.log('⚠️ Current AOI has no geometry - only bounds-based clipping available');
    }
  }

  // Helper methods
  private convertGeoJSONToWKT(geometry: GeoJSON.Geometry): string {
    if (geometry.type === 'Polygon') {
      const coordinates = geometry.coordinates[0];
      if (!coordinates || coordinates.length === 0) {
        throw new Error('Polygon has no coordinates');
      }
      
      const coordPairs = coordinates.map((coord: number[]) => {
        if (!Array.isArray(coord) || coord.length < 2) {
          throw new Error('Invalid coordinate pair');
        }
        if (isNaN(coord[0]) || isNaN(coord[1])) {
          throw new Error('Coordinate contains NaN values');
        }
        return `${coord[0]} ${coord[1]}`;
      }).join(', ');
      
      return `POLYGON((${coordPairs}))`;
    } else if (geometry.type === 'MultiPolygon') {
      const polygons = geometry.coordinates.map((polygon: number[][][]) => {
        const coordPairs = polygon[0].map((coord: number[]) => {
          if (!Array.isArray(coord) || coord.length < 2) {
            throw new Error('Invalid coordinate pair in MultiPolygon');
          }
          if (isNaN(coord[0]) || isNaN(coord[1])) {
            throw new Error('MultiPolygon coordinate contains NaN values');
          }
          return `${coord[0]} ${coord[1]}`;
        }).join(', ');
        return `(${coordPairs})`;
      }).join(', ');
      
      return `MULTIPOLYGON((${polygons}))`;
    }
    
    throw new Error(`Unsupported geometry type for WKT conversion: ${geometry.type}`);
  }

  private countCoordinates(geometry?: GeoJSON.Geometry): number {
    if (!geometry) return 0;
    
    if (geometry.type === 'Polygon') {
      return geometry.coordinates[0]?.length || 0;
    } else if (geometry.type === 'MultiPolygon') {
      return geometry.coordinates.reduce((total, polygon) => {
        return total + (polygon[0]?.length || 0);
      }, 0);
    }
    
    return 0;
  }
}

// Make debugger available globally
const aoiDebugger = new AOIClippingDebugger();
(window as any).aoiDebugger = aoiDebugger;

// Export for module use
export default aoiDebugger;
