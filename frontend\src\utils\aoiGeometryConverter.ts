/**
 * AOI Geometry Converter Utility
 * 
 * Converts between different AOI geometry formats used in the application
 * and the format expected by the new AOI-hole basemap overlay components.
 */

// Types for the new AOI components
type Ring = [number, number][];                // [lng, lat]
type Polygon = Ring[];                         // [outer, hole1, ...]
type MultiPolygon = Polygon[];                 // multiple polygons

/**
 * Convert GeoJSON geometry to the format expected by AOI-hole components
 * @param geometry - GeoJSON geometry object
 * @returns MultiPolygon or Polygon in [lng, lat] format
 */
export function convertGeoJSONToAOIFormat(geometry: GeoJSON.Geometry): MultiPolygon | Polygon {
  if (!geometry) {
    throw new Error('No geometry provided');
  }

  switch (geometry.type) {
    case 'Polygon':
      // Convert from GeoJSON Polygon to Polygon format
      const polygonCoords = geometry.coordinates as number[][][];
      const polygon: Polygon = polygonCoords.map(ring => 
        ring.map(coord => [coord[0], coord[1]] as [number, number])
      );
      return polygon;

    case 'MultiPolygon':
      // Convert from GeoJSON MultiPolygon to MultiPolygon format
      const multiPolygonCoords = geometry.coordinates as number[][][][];
      const multiPolygon: MultiPolygon = multiPolygonCoords.map(polygonCoords =>
        polygonCoords.map(ring =>
          ring.map(coord => [coord[0], coord[1]] as [number, number])
        )
      );
      return multiPolygon;

    default:
      throw new Error(`Unsupported geometry type: ${geometry.type}`);
  }
}

/**
 * Convert AOI bounds to Leaflet LatLngBounds format
 * @param bounds - AOI bounds object
 * @returns Leaflet LatLngBounds literal
 */
export function convertBoundsToLeafletFormat(bounds: {
  north: number;
  south: number;
  east: number;
  west: number;
}): L.LatLngBoundsLiteral {
  return [
    [bounds.south, bounds.west], // southwest corner
    [bounds.north, bounds.east]  // northeast corner
  ];
}

/**
 * Extract basemap configuration from current map setup
 * @param selectedBasemap - Current basemap selection
 * @returns Basemap configuration for AOI-hole overlay
 */
export function getBasemapConfig(selectedBasemap: string = 'osm:osm') {
  if (selectedBasemap === 'osm:osm') {
    return {
      tileUrl: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
      subdomains: ['a', 'b', 'c'],
      attribution: "&copy; OpenStreetMap contributors"
    };
  } else {
    // For WMS basemaps, we'll use a simplified tile URL approach
    // Note: This is a fallback - for complex WMS layers, you might need custom tile handling
    const API_CONFIG = { BASE_URL: process.env.VITE_API_BASE_URL || 'http://localhost:3001' };
    return {
      tileUrl: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", // Fallback to OSM for WMS basemaps
      subdomains: ['a', 'b', 'c'],
      attribution: "GeoServer Basemap (OSM Fallback)"
    };
  }
}

/**
 * Check if AOI data has valid geometry for the hole overlay
 * @param aoiData - AOI data object
 * @returns boolean indicating if geometry is valid
 */
export function hasValidAOIGeometry(aoiData?: any): boolean {
  return !!(aoiData?.geometry && (
    aoiData.geometry.type === 'Polygon' || 
    aoiData.geometry.type === 'MultiPolygon'
  ));
}

/**
 * Extract geometry from various AOI data formats
 * @param aoiData - AOI data in various formats
 * @returns GeoJSON geometry or null
 */
export function extractGeometryFromAOI(aoiData?: any): GeoJSON.Geometry | null {
  if (!aoiData) return null;

  // Direct geometry property
  if (aoiData.geometry) {
    return aoiData.geometry;
  }

  // From feature property
  if (aoiData.feature?.geometry) {
    return aoiData.feature.geometry;
  }

  // From coordinates (for drawn polygons)
  if (aoiData.coordinates && Array.isArray(aoiData.coordinates)) {
    // Assume it's a polygon coordinate array
    return {
      type: 'Polygon',
      coordinates: [aoiData.coordinates]
    };
  }

  return null;
}
