/**
 * Utility functions for calculating areas from coordinates
 */

/**
 * Calculate area in square kilometers from a bounding box (BBOX)
 * Uses the Haversine formula to account for earth's curvature
 * 
 * @param west - Western longitude boundary
 * @param south - Southern latitude boundary
 * @param east - Eastern longitude boundary
 * @param north - Northern latitude boundary
 * @returns Area in square kilometers
 */
export function calculateAreaFromBbox(west: number, south: number, east: number, north: number): number {
    // Convert to radians
    const westRad = (west * Math.PI) / 180;
    const southRad = (south * Math.PI) / 180;
    const eastRad = (east * Math.PI) / 180;
    const northRad = (north * Math.PI) / 180;
    
    // Earth's radius in kilometers
    const earthRadius = 6371;
    
    // Width calculation (distance between west and east at the middle latitude)
    const midLat = (southRad + northRad) / 2;
    const width = Math.abs(eastRad - westRad) * earthRadius * Math.cos(midLat);
    
    // Height calculation (distance between north and south)
    const height = Math.abs(northRad - southRad) * earthRadius;
    
    // Area = width * height
    return width * height;
}

/**
 * Calculate area in square kilometers from a polygon
 * Uses the Shoelace formula (<PERSON><PERSON><PERSON>'s area formula)
 * 
 * @param coordinates - Array of [longitude, latitude] coordinates
 * @returns Area in square kilometers
 */
export function calculateAreaFromPolygon(coordinates: [number, number][]): number {
    if (!coordinates || coordinates.length < 3) {
        console.error('Invalid polygon: at least 3 coordinates required');
        return 0;
    }
    
    // Earth's radius in kilometers
    const earthRadius = 6371;
    
    // Convert to radians and calculate
    let total = 0;
    for (let i = 0; i < coordinates.length; i++) {
        const j = (i + 1) % coordinates.length;
        
        // Convert to radians
        const lon1 = (coordinates[i][0] * Math.PI) / 180;
        const lat1 = (coordinates[i][1] * Math.PI) / 180;
        const lon2 = (coordinates[j][0] * Math.PI) / 180;
        const lat2 = (coordinates[j][1] * Math.PI) / 180;
        
        // Calculate the contribution of this edge to the area
        total += (lon2 - lon1) * (2 + Math.sin(lat1) + Math.sin(lat2));
    }
    
    // Get the absolute value of the result
    const area = Math.abs(total * earthRadius * earthRadius / 2);
    
    return area;
}

/**
 * Calculate area from a GeoJSON polygon
 * For simple cases, assumes the first ring is the exterior ring
 * 
 * @param geojson - GeoJSON Polygon object or coordinates array
 * @returns Area in square kilometers
 */
export function calculateAreaFromGeoJSON(geojson: any): number {
    if (!geojson) return 0;
    
    try {
        let coordinates;
        
        // Check if we have a full GeoJSON object or just coordinates
        if (geojson.type === 'Polygon' && geojson.coordinates) {
            // Use the first (exterior) ring
            coordinates = geojson.coordinates[0];
        } else if (Array.isArray(geojson) && geojson.length > 0 && Array.isArray(geojson[0])) {
            // Assume it's already a coordinates array
            if (Array.isArray(geojson[0][0])) {
                // This is likely a polygon with multiple rings
                coordinates = geojson[0];
            } else {
                // This is likely a simple ring
                coordinates = geojson;
            }
        } else {
            console.error('Unsupported GeoJSON format for area calculation');
            return 0;
        }
        
        return calculateAreaFromPolygon(coordinates as [number, number][]);
    } catch (error) {
        console.error('Error calculating area from GeoJSON:', error);
        return 0;
    }
}

/**
 * Detect the data type and calculate area accordingly
 * 
 * @param data - Either a bbox array, GeoJSON, or coordinate array
 * @returns Area in square kilometers
 */
export function calculateArea(data: any): number {
    if (!data) return 0;
    
    try {
        // Check if it's a bbox array [west, south, east, north]
        if (Array.isArray(data) && data.length === 4 && 
            typeof data[0] === 'number' && 
            typeof data[1] === 'number' &&
            typeof data[2] === 'number' && 
            typeof data[3] === 'number') {
            return calculateAreaFromBbox(data[0], data[1], data[2], data[3]);
        }
        
        // Check if it's a bbox object {west, south, east, north}
        if (data.west !== undefined && data.south !== undefined && 
            data.east !== undefined && data.north !== undefined) {
            return calculateAreaFromBbox(data.west, data.south, data.east, data.north);
        }
        
        // Check if it's a GeoJSON object or coordinate array
        return calculateAreaFromGeoJSON(data);
    } catch (error) {
        console.error('Error calculating area:', error);
        return 0;
    }
}
