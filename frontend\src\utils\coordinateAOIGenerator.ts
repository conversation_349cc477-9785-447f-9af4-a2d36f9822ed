/**
 * Utility functions for generating AOI geometry from pin coordinates
 */

export interface PinAOIConfig {
  coordinates: { lat: number; lng: number };
  shape: 'square' | 'circle';
  size: number; // in km²
}

export interface PinAOIData {
  type: 'pin-based';
  coordinates: { lat: number; lng: number };
  shape: 'square' | 'circle';
  size: number;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  area: number;
  geometry: GeoJSON.Geometry;
  feature: GeoJSON.Feature;
  name: string;
}

/**
 * Generate AOI geometry from pin coordinates and area configuration
 */
export const generateAOIFromPin = (config: PinAOIConfig): PinAOIData => {
  const { coordinates, shape, size } = config;
  
  // Calculate radius based on area and shape
  const radius = shape === 'circle' 
    ? Math.sqrt(size / Math.PI) // radius for circle
    : Math.sqrt(size) / 2; // half side length for square
  
  // Convert km to degrees (rough approximation: 1 degree ≈ 111 km)
  const latOffset = radius / 111;
  const lngOffset = radius / (111 * Math.cos(coordinates.lat * Math.PI / 180));
  
  let geometry: GeoJSON.Geometry;
  
  if (shape === 'square') {
    // Generate square polygon
    geometry = {
      type: 'Polygon',
      coordinates: [[
        [coordinates.lng - lngOffset, coordinates.lat + latOffset], // NW
        [coordinates.lng + lngOffset, coordinates.lat + latOffset], // NE
        [coordinates.lng + lngOffset, coordinates.lat - latOffset], // SE
        [coordinates.lng - lngOffset, coordinates.lat - latOffset], // SW
        [coordinates.lng - lngOffset, coordinates.lat + latOffset]  // Close polygon
      ]]
    };
  } else {
    // Generate circle as polygon with multiple points
    const points = 32;
    const coords: number[][] = [];
    for (let i = 0; i <= points; i++) {
      const angle = (i * 2 * Math.PI) / points;
      const lat = coordinates.lat + (latOffset * Math.cos(angle));
      const lng = coordinates.lng + (lngOffset * Math.sin(angle));
      coords.push([lng, lat]);
    }
    geometry = {
      type: 'Polygon',
      coordinates: [coords]
    };
  }
  
  // Calculate bounds
  const bounds = {
    north: coordinates.lat + latOffset,
    south: coordinates.lat - latOffset,
    east: coordinates.lng + lngOffset,
    west: coordinates.lng - lngOffset
  };
  
  // Create feature
  const feature: GeoJSON.Feature = {
    type: 'Feature',
    properties: {
      name: `Pin-based AOI (${shape}, ${size} km²)`,
      type: 'pin-aoi',
      shape,
      size,
      center: coordinates
    },
    geometry
  };
  
  // Generate descriptive name
  const name = `Pin AOI: ${coordinates.lat.toFixed(4)}, ${coordinates.lng.toFixed(4)} (${size} km²)`;
  
  return {
    type: 'pin-based',
    coordinates,
    shape,
    size,
    bounds,
    area: size,
    geometry,
    feature,
    name
  };
};

/**
 * Predefined area sizes for pin-based AOI
 */
export const PIN_AREA_SIZES = [
  { size: 1, label: '1 km²', description: 'Very small area (1x1 km)' },
  { size: 5, label: '5 km²', description: 'Small area (2.2x2.2 km)' },
  { size: 10, label: '10 km²', description: 'Medium area (3.2x3.2 km)' },
  { size: 25, label: '25 km²', description: 'Large area (5x5 km)' },
  { size: 50, label: '50 km²', description: 'Very large area (7x7 km)' },
  { size: 100, label: '100 km²', description: 'Extra large area (10x10 km)' }
];

/**
 * Drawing AOI Data Interface
 */
export interface DrawingAOIData {
  type: 'drawn';
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  area: number; // in km²
  geometry: GeoJSON.Geometry;
  feature: GeoJSON.Feature;
  name: string;
}

/**
 * Generate AOI data from drawn polygon GeoJSON
 */
export const generateAOIFromDrawing = (geoJSON: GeoJSON.Feature): DrawingAOIData => {
  const geometry = geoJSON.geometry;

  if (geometry.type !== 'Polygon') {
    throw new Error('Only polygon geometries are supported for drawing AOI');
  }

  // Calculate bounds from polygon coordinates
  const coordinates = geometry.coordinates[0]; // First ring of polygon
  let minLat = Infinity, maxLat = -Infinity;
  let minLng = Infinity, maxLng = -Infinity;

  coordinates.forEach((coord: number[]) => {
    const [lng, lat] = coord;
    minLat = Math.min(minLat, lat);
    maxLat = Math.max(maxLat, lat);
    minLng = Math.min(minLng, lng);
    maxLng = Math.max(maxLng, lng);
  });

  const bounds = {
    north: maxLat,
    south: minLat,
    east: maxLng,
    west: minLng
  };

  // Calculate approximate area in km²
  // Simple bounding box area calculation (rough approximation)
  const latDiff = maxLat - minLat;
  const lngDiff = maxLng - minLng;
  const avgLat = (maxLat + minLat) / 2;

  // Convert degrees to km (rough approximation)
  const latKm = latDiff * 111; // 1 degree lat ≈ 111 km
  const lngKm = lngDiff * 111 * Math.cos(avgLat * Math.PI / 180); // Adjust for longitude
  const area = latKm * lngKm;

  // Create feature with proper properties
  const feature: GeoJSON.Feature = {
    type: 'Feature',
    properties: {
      name: `Drawn AOI (${area.toFixed(1)} km²)`,
      type: 'drawn-aoi',
      area: area
    },
    geometry
  };

  // Generate descriptive name
  const name = `Drawn AOI: ${area.toFixed(1)} km²`;

  return {
    type: 'drawn',
    bounds,
    area,
    geometry,
    feature,
    name
  };
};
