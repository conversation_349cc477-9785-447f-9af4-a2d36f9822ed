/**
 * CQL Security Sanitizer - Prevents SQL injection and validates CQL filters
 * Used for securing AOI clipping operations with WMS CQL_FILTER parameters
 */

export interface CQLValidationResult {
  isValid: boolean;
  sanitizedCQL?: string;
  errors: string[];
  warnings: string[];
  blockedPatterns: string[];
  metadata: {
    originalLength: number;
    sanitizedLength?: number;
    spatialFunctionsUsed: string[];
    validationTime: number;
  };
}

export interface CQLFilterOptions {
  geometry: string; // WKT string
  spatialFunction: 'INTERSECTS' | 'CONTAINS' | 'WITHIN' | 'TOUCHES' | 'CROSSES' | 'OVERLAPS';
  propertyName?: string;
  additionalFilters?: string[];
}

/**
 * Dangerous SQL patterns that should be blocked
 */
const DANGEROUS_PATTERNS = [
  // SQL injection patterns
  /(\b(DROP|DELETE|INSERT|UPDATE|ALTER|CREATE|TRUNCATE|EXEC|EXECUTE)\b)/gi,
  /(--|\*\/|\/\*)/g, // SQL comments
  /(\b(UNION|SELECT|FROM|WHERE|HAVING|GROUP\s+BY|ORDER\s+BY)\b)/gi,
  /(;|\|\||&&)/g, // Statement terminators and logical operators
  /(\b(SCRIPT|JAVASCRIPT|VBSCRIPT|ONLOAD|ONERROR)\b)/gi, // XSS patterns
  
  // Dangerous characters and sequences
  /[<>'"]/g, // HTML/XML injection
  /(\$\{|\#\{)/g, // Expression language injection
  /(\b(EVAL|EXEC|SYSTEM|CMD)\b)/gi, // Code execution
  /(\\x[0-9a-fA-F]{2})/g, // Hex encoding
  /(%[0-9a-fA-F]{2})/g, // URL encoding that might hide malicious content
];

/**
 * Allowed CQL spatial functions
 */
const ALLOWED_SPATIAL_FUNCTIONS = [
  'INTERSECTS',
  'CONTAINS', 
  'WITHIN',
  'TOUCHES',
  'CROSSES',
  'OVERLAPS',
  'DISJOINT',
  'EQUALS',
  'DWITHIN',
  'BEYOND'
];

/**
 * Allowed CQL operators and keywords
 */
const ALLOWED_CQL_KEYWORDS = [
  'AND', 'OR', 'NOT',
  'LIKE', 'ILIKE',
  'IS', 'NULL',
  'BETWEEN',
  'IN',
  '=', '!=', '<>', '<', '>', '<=', '>='
];

/**
 * WKT geometry type patterns for validation
 */
const WKT_PATTERNS = [
  /^POINT\s*\(/i,
  /^LINESTRING\s*\(/i,
  /^POLYGON\s*\(/i,
  /^MULTIPOINT\s*\(/i,
  /^MULTILINESTRING\s*\(/i,
  /^MULTIPOLYGON\s*\(/i,
  /^GEOMETRYCOLLECTION\s*\(/i
];

/**
 * Sanitize and validate CQL filter string
 * @param cqlFilter The CQL filter string to validate
 * @param options Validation options
 * @returns CQLValidationResult with validation status and sanitized filter
 */
export function sanitizeAndValidateCQL(
  cqlFilter: string,
  options: { maxLength?: number; allowedProperties?: string[] } = {}
): CQLValidationResult {
  const startTime = Date.now();
  const { maxLength = 8000, allowedProperties = [] } = options;
  
  const result: CQLValidationResult = {
    isValid: false,
    errors: [],
    warnings: [],
    blockedPatterns: [],
    metadata: {
      originalLength: cqlFilter.length,
      spatialFunctionsUsed: [],
      validationTime: 0
    }
  };

  try {
    // Basic validation
    if (!cqlFilter || typeof cqlFilter !== 'string') {
      result.errors.push('CQL filter must be a non-empty string');
      return result;
    }

    if (cqlFilter.length > maxLength) {
      result.errors.push(`CQL filter too long: ${cqlFilter.length} chars (max: ${maxLength})`);
      return result;
    }

    // Check for dangerous patterns
    const blockedPatterns: string[] = [];
    for (const pattern of DANGEROUS_PATTERNS) {
      const matches = cqlFilter.match(pattern);
      if (matches) {
        blockedPatterns.push(...matches);
      }
    }

    if (blockedPatterns.length > 0) {
      result.errors.push('Dangerous patterns detected in CQL filter');
      result.blockedPatterns = [...new Set(blockedPatterns)]; // Remove duplicates
      return result;
    }

    // Validate spatial functions
    const spatialFunctions = extractSpatialFunctions(cqlFilter);
    const invalidFunctions = spatialFunctions.filter(
      func => !ALLOWED_SPATIAL_FUNCTIONS.includes(func.toUpperCase())
    );

    if (invalidFunctions.length > 0) {
      result.errors.push(`Invalid spatial functions: ${invalidFunctions.join(', ')}`);
      return result;
    }

    result.metadata.spatialFunctionsUsed = spatialFunctions;

    // Validate WKT geometries within the CQL
    const wktValidation = validateWKTInCQL(cqlFilter);
    if (!wktValidation.isValid) {
      result.errors.push(...wktValidation.errors);
      return result;
    }

    // Validate property names if provided
    if (allowedProperties.length > 0) {
      const propertyValidation = validatePropertyNames(cqlFilter, allowedProperties);
      if (!propertyValidation.isValid) {
        result.errors.push(...propertyValidation.errors);
        result.warnings.push(...propertyValidation.warnings);
      }
    }

    // Sanitize the CQL filter
    const sanitizedCQL = sanitizeCQLFilter(cqlFilter);
    
    result.isValid = true;
    result.sanitizedCQL = sanitizedCQL;
    result.metadata.sanitizedLength = sanitizedCQL.length;

    if (sanitizedCQL !== cqlFilter) {
      result.warnings.push('CQL filter was modified during sanitization');
    }

  } catch (error) {
    result.errors.push(`CQL validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    result.metadata.validationTime = Date.now() - startTime;
  }

  return result;
}

/**
 * Create a safe CQL filter for spatial operations
 * @param options CQL filter options
 * @returns Validated and sanitized CQL filter string
 */
export function createSafeCQLFilter(options: CQLFilterOptions): string {
  const { geometry, spatialFunction, propertyName = 'the_geom', additionalFilters = [] } = options;

  // Validate spatial function
  if (!ALLOWED_SPATIAL_FUNCTIONS.includes(spatialFunction)) {
    throw new Error(`Invalid spatial function: ${spatialFunction}`);
  }

  // Validate WKT geometry
  if (!isValidWKT(geometry)) {
    throw new Error('Invalid WKT geometry provided');
  }

  // Sanitize property name
  const safePropertyName = sanitizePropertyName(propertyName);
  
  // Create base spatial filter
  const spatialFilter = `${spatialFunction}(${safePropertyName}, '${geometry}')`;

  // Add additional filters if provided
  const allFilters = [spatialFilter];
  
  for (const filter of additionalFilters) {
    const validation = sanitizeAndValidateCQL(filter, { maxLength: 1000 });
    if (!validation.isValid) {
      throw new Error(`Invalid additional filter: ${validation.errors.join(', ')}`);
    }
    allFilters.push(validation.sanitizedCQL!);
  }

  // Combine filters with AND
  const finalFilter = allFilters.join(' AND ');

  // Final validation
  const finalValidation = sanitizeAndValidateCQL(finalFilter);
  if (!finalValidation.isValid) {
    throw new Error(`Generated CQL filter failed validation: ${finalValidation.errors.join(', ')}`);
  }

  return finalValidation.sanitizedCQL!;
}

/**
 * Extract spatial function names from CQL filter
 */
function extractSpatialFunctions(cqlFilter: string): string[] {
  const functions: string[] = [];
  const pattern = /(\w+)\s*\(/g;
  let match;

  while ((match = pattern.exec(cqlFilter)) !== null) {
    const funcName = match[1].toUpperCase();
    if (ALLOWED_SPATIAL_FUNCTIONS.includes(funcName)) {
      functions.push(funcName);
    }
  }

  return functions;
}

/**
 * Validate WKT geometries within CQL filter
 */
function validateWKTInCQL(cqlFilter: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Extract potential WKT strings (content within single quotes)
  const wktMatches = cqlFilter.match(/'([^']+)'/g);
  
  if (wktMatches) {
    for (const match of wktMatches) {
      const wktString = match.slice(1, -1); // Remove quotes
      if (!isValidWKT(wktString)) {
        errors.push(`Invalid WKT geometry: ${wktString.substring(0, 50)}...`);
      }
    }
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validate WKT geometry string
 */
function isValidWKT(wkt: string): boolean {
  if (!wkt || typeof wkt !== 'string') {
    return false;
  }

  // Check if it matches any WKT pattern
  const matchesPattern = WKT_PATTERNS.some(pattern => pattern.test(wkt.trim()));
  if (!matchesPattern) {
    return false;
  }

  // Basic structure validation (balanced parentheses)
  let openParens = 0;
  for (const char of wkt) {
    if (char === '(') openParens++;
    if (char === ')') openParens--;
    if (openParens < 0) return false;
  }

  return openParens === 0;
}

/**
 * Validate property names in CQL filter
 */
function validatePropertyNames(
  cqlFilter: string, 
  allowedProperties: string[]
): { isValid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Extract property names (simplified pattern matching)
  const propertyPattern = /(\w+)\s*[=<>!]/g;
  let match;

  while ((match = propertyPattern.exec(cqlFilter)) !== null) {
    const propertyName = match[1];
    if (!allowedProperties.includes(propertyName)) {
      warnings.push(`Unknown property name: ${propertyName}`);
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * Sanitize CQL filter by removing/escaping dangerous content
 */
function sanitizeCQLFilter(cqlFilter: string): string {
  let sanitized = cqlFilter;

  // Remove any remaining dangerous characters
  sanitized = sanitized.replace(/[<>"']/g, '');
  
  // Normalize whitespace
  sanitized = sanitized.replace(/\s+/g, ' ').trim();

  return sanitized;
}

/**
 * Sanitize property name
 */
function sanitizePropertyName(propertyName: string): string {
  // Only allow alphanumeric characters, underscores, and dots
  return propertyName.replace(/[^a-zA-Z0-9_.]/g, '');
}

/**
 * Check if a CQL filter contains only safe spatial operations
 * @param cqlFilter CQL filter string to check
 * @returns boolean indicating if the filter is safe for spatial operations
 */
export function isSafeSpatialCQL(cqlFilter: string): boolean {
  const validation = sanitizeAndValidateCQL(cqlFilter, { maxLength: 10000 });
  return validation.isValid && validation.metadata.spatialFunctionsUsed.length > 0;
}

/**
 * Extract geometry WKT strings from CQL filter
 * @param cqlFilter CQL filter string
 * @returns Array of WKT geometry strings found in the filter
 */
export function extractWKTFromCQL(cqlFilter: string): string[] {
  const wktStrings: string[] = [];
  const matches = cqlFilter.match(/'([^']+)'/g);

  if (matches) {
    for (const match of matches) {
      const wktString = match.slice(1, -1); // Remove quotes
      if (isValidWKT(wktString)) {
        wktStrings.push(wktString);
      }
    }
  }

  return wktStrings;
}

/**
 * Create a safe property filter for non-spatial attributes
 * @param propertyName Name of the property to filter
 * @param operator Comparison operator
 * @param value Value to compare against
 * @returns Safe CQL property filter
 */
export function createSafePropertyFilter(
  propertyName: string,
  operator: '=' | '!=' | '<' | '>' | '<=' | '>=' | 'LIKE' | 'ILIKE',
  value: string | number
): string {
  const safeProperty = sanitizePropertyName(propertyName);

  if (!safeProperty) {
    throw new Error('Invalid property name provided');
  }

  // Validate operator
  const allowedOperators = ['=', '!=', '<', '>', '<=', '>=', 'LIKE', 'ILIKE'];
  if (!allowedOperators.includes(operator)) {
    throw new Error(`Invalid operator: ${operator}`);
  }

  // Sanitize value based on type
  let safeValue: string;
  if (typeof value === 'number') {
    if (!isFinite(value)) {
      throw new Error('Invalid numeric value provided');
    }
    safeValue = value.toString();
  } else {
    // String value - escape single quotes and validate
    const stringValue = String(value);
    if (stringValue.length > 1000) {
      throw new Error('String value too long (max 1000 characters)');
    }

    // Check for dangerous patterns in string values
    for (const pattern of DANGEROUS_PATTERNS) {
      if (pattern.test(stringValue)) {
        throw new Error('Dangerous pattern detected in filter value');
      }
    }

    safeValue = `'${stringValue.replace(/'/g, "''")}'`; // Escape single quotes
  }

  const filter = `${safeProperty} ${operator} ${safeValue}`;

  // Final validation
  const validation = sanitizeAndValidateCQL(filter, { maxLength: 2000 });
  if (!validation.isValid) {
    throw new Error(`Generated property filter failed validation: ${validation.errors.join(', ')}`);
  }

  return validation.sanitizedCQL!;
}

/**
 * Combine multiple CQL filters safely
 * @param filters Array of CQL filter strings
 * @param operator Logical operator to combine filters ('AND' or 'OR')
 * @returns Combined and validated CQL filter
 */
export function combineCQLFilters(
  filters: string[],
  operator: 'AND' | 'OR' = 'AND'
): string {
  if (!filters || filters.length === 0) {
    throw new Error('No filters provided to combine');
  }

  if (filters.length === 1) {
    const validation = sanitizeAndValidateCQL(filters[0]);
    if (!validation.isValid) {
      throw new Error(`Filter validation failed: ${validation.errors.join(', ')}`);
    }
    return validation.sanitizedCQL!;
  }

  // Validate each filter individually
  const validatedFilters: string[] = [];
  for (let i = 0; i < filters.length; i++) {
    const filter = filters[i];
    const validation = sanitizeAndValidateCQL(filter, { maxLength: 5000 });

    if (!validation.isValid) {
      throw new Error(`Filter ${i + 1} validation failed: ${validation.errors.join(', ')}`);
    }

    validatedFilters.push(`(${validation.sanitizedCQL})`);
  }

  // Combine with specified operator
  const combinedFilter = validatedFilters.join(` ${operator} `);

  // Final validation of combined filter
  const finalValidation = sanitizeAndValidateCQL(combinedFilter, { maxLength: 15000 });
  if (!finalValidation.isValid) {
    throw new Error(`Combined filter validation failed: ${finalValidation.errors.join(', ')}`);
  }

  return finalValidation.sanitizedCQL!;
}

/**
 * Debug function to analyze CQL filter security
 * @param cqlFilter CQL filter to analyze
 * @returns Detailed security analysis
 */
export function analyzeCQLSecurity(cqlFilter: string): {
  securityLevel: 'SAFE' | 'WARNING' | 'DANGEROUS';
  analysis: {
    hasSQL: boolean;
    hasXSS: boolean;
    hasInjection: boolean;
    spatialFunctionsCount: number;
    propertyFiltersCount: number;
    complexityScore: number;
  };
  recommendations: string[];
} {
  const validation = sanitizeAndValidateCQL(cqlFilter, { maxLength: 20000 });
  const recommendations: string[] = [];

  const analysis = {
    hasSQL: false,
    hasXSS: false,
    hasInjection: false,
    spatialFunctionsCount: validation.metadata.spatialFunctionsUsed.length,
    propertyFiltersCount: 0,
    complexityScore: 0
  };

  // Check for SQL patterns
  const sqlPatterns = [/\b(SELECT|FROM|WHERE|UNION|DROP|DELETE|INSERT|UPDATE)\b/gi];
  analysis.hasSQL = sqlPatterns.some(pattern => pattern.test(cqlFilter));

  // Check for XSS patterns
  const xssPatterns = [/<script|javascript:|onload=|onerror=/gi];
  analysis.hasXSS = xssPatterns.some(pattern => pattern.test(cqlFilter));

  // Check for injection patterns
  analysis.hasInjection = validation.blockedPatterns.length > 0;

  // Calculate complexity score
  analysis.complexityScore = cqlFilter.length / 100 +
                           analysis.spatialFunctionsCount * 2 +
                           (cqlFilter.match(/AND|OR/gi) || []).length;

  // Determine security level
  let securityLevel: 'SAFE' | 'WARNING' | 'DANGEROUS' = 'SAFE';

  if (analysis.hasSQL || analysis.hasXSS || analysis.hasInjection) {
    securityLevel = 'DANGEROUS';
    recommendations.push('Contains dangerous patterns that could indicate injection attempts');
  } else if (analysis.complexityScore > 20 || cqlFilter.length > 5000) {
    securityLevel = 'WARNING';
    recommendations.push('High complexity filter - consider simplification');
  }

  if (analysis.spatialFunctionsCount === 0) {
    recommendations.push('No spatial functions detected - ensure this is intentional');
  }

  if (validation.warnings.length > 0) {
    recommendations.push(...validation.warnings);
  }

  return {
    securityLevel,
    analysis,
    recommendations
  };
}
