/**
 * Layer Testing Utility
 * 
 * This utility helps test AOI clipping with known working layers
 */

import { generateAOIScreenshot } from '../services/geoserverService';
import { fetchAvailableLayers } from '../services/geoserverService';

/**
 * Test AOI screenshot with available layers
 */
export const testAOIWithAvailableLayers = async (bounds: any) => {
  console.log('🧪 Testing AOI screenshot with available layers...');
  
  try {
    // Get available layers
    const availableLayers = await fetchAvailableLayers();
    console.log('📋 Available layers:', availableLayers.map(l => l.name));
    
    // Filter for layers that are likely to work well for testing
    const testLayers = availableLayers
      .filter(layer => {
        const name = layer.name.toLowerCase();
        // Prefer layers that are likely to have data and work well
        return (
          name.includes('flood') ||
          name.includes('damage') ||
          name.includes('mosaic') ||
          name.includes('risk') ||
          name.includes('africa')
        );
      })
      .slice(0, 2) // Take first 2 matching layers
      .map(l => l.name);
    
    if (testLayers.length === 0) {
      // Fallback to first few available layers
      testLayers.push(...availableLayers.slice(0, 2).map(l => l.name));
    }
    
    console.log('🎯 Selected test layers:', testLayers);
    
    // Test with these layers
    const screenshotUrl = await generateAOIScreenshot({
      bounds,
      selectedLayers: testLayers,
      selectedBasemap: 'osm:osm',
      dimensions: { width: 400, height: 200 },
      format: 'png'
    });
    
    console.log('✅ Test screenshot generated successfully:', !!screenshotUrl);
    return { success: true, url: screenshotUrl, layers: testLayers };
    
  } catch (error) {
    console.error('❌ Test screenshot failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

/**
 * Get recommended layers for testing
 */
export const getRecommendedTestLayers = async (): Promise<string[]> => {
  try {
    const availableLayers = await fetchAvailableLayers();
    
    // Priority order for testing
    const priorities = [
      'flood_risk_layer',
      'damage_analysis', 
      'africa_mosaic',
      'flood_plain',
      'mosaic'
    ];
    
    const recommended: string[] = [];
    
    // Find layers matching priorities
    for (const priority of priorities) {
      const matching = availableLayers.find(layer => 
        layer.name.toLowerCase().includes(priority.toLowerCase())
      );
      if (matching && !recommended.includes(matching.name)) {
        recommended.push(matching.name);
      }
    }
    
    // If we don't have enough, add more
    if (recommended.length < 2) {
      const additional = availableLayers
        .filter(layer => !recommended.includes(layer.name))
        .slice(0, 2 - recommended.length)
        .map(l => l.name);
      recommended.push(...additional);
    }
    
    return recommended.slice(0, 3); // Max 3 layers for testing
    
  } catch (error) {
    console.error('Failed to get recommended layers:', error);
    return [];
  }
};
