/**
 * Secure AOI Utilities - Integration example for Phase 1 security improvements
 * 
 * This file demonstrates how to use the new progressive WKT converter and CQL sanitizer
 * to eliminate HTTP 431 errors and prevent CQL injection vulnerabilities.
 */

// Dynamic import for WKT converter to avoid bundling conflicts
// import { convertGeoJSONToWKTProgressive, WKTConversionResult } from './wktConverter';
import { 
  sanitizeAndValidateCQL, 
  createSafeCQLFilter, 
  CQLValidationResult,
  CQLFilterOptions 
} from './cqlSanitizer';

export interface SecureAOIResult {
  success: boolean;
  cqlFilter?: string;
  wktResult?: any; // WKTConversionResult - using any to avoid import
  validationResult?: CQLValidationResult;
  method: 'CQL_FILTER' | 'BBOX' | 'FAILED';
  warnings: string[];
  errors: string[];
}

/**
 * Create a secure AOI filter with progressive WKT conversion and CQL validation
 * This replaces the existing unsafe CQL filter generation in AOI clipping operations
 * 
 * @param geometry GeoJSON geometry for AOI
 * @param layerName Name of the layer to filter
 * @param geometryField Geometry field name (defaults to 'the_geom')
 * @param maxWKTLength Maximum WKT length before simplification
 * @returns SecureAOIResult with validated CQL filter or fallback options
 */
export async function createSecureAOIFilter(
  geometry: GeoJSON.Geometry,
  layerName: string,
  geometryField: string = 'the_geom',
  maxWKTLength: number = 6000
): Promise<SecureAOIResult> {
  const warnings: string[] = [];
  const errors: string[] = [];

  try {
    // Step 1: Progressive WKT conversion to handle HTTP 431 errors
    // Use explicit dynamic import to avoid Vite static analysis
    const modulePath = './wktConverter';
    const { convertGeoJSONToWKTProgressive } = await import(/* @vite-ignore */ modulePath);
    const wktResult = convertGeoJSONToWKTProgressive(geometry, maxWKTLength);
    
    // Add warnings from WKT conversion
    warnings.push(...wktResult.warnings);
    
    // Log WKT conversion details
    if (wktResult.metadata.method !== 'original') {
      warnings.push(
        `WKT simplified using ${wktResult.metadata.method} ` +
        `(level ${wktResult.metadata.simplificationLevel})`
      );
    }

    // Step 2: Create safe CQL filter with validation
    const cqlOptions: CQLFilterOptions = {
      geometry: wktResult.wkt,
      spatialFunction: 'INTERSECTS',
      propertyName: geometryField
    };

    const safeCQLFilter = createSafeCQLFilter(cqlOptions);

    // Step 3: Additional validation of the generated CQL
    const validationResult = sanitizeAndValidateCQL(safeCQLFilter, {
      maxLength: maxWKTLength + 1000, // Allow some overhead for CQL syntax
      allowedProperties: [geometryField]
    });

    if (!validationResult.isValid) {
      errors.push(`CQL validation failed: ${validationResult.errors.join(', ')}`);
      return {
        success: false,
        method: 'FAILED',
        warnings,
        errors,
        wktResult,
        validationResult
      };
    }

    // Add validation warnings
    warnings.push(...validationResult.warnings);

    return {
      success: true,
      cqlFilter: validationResult.sanitizedCQL,
      wktResult,
      validationResult,
      method: 'CQL_FILTER',
      warnings,
      errors
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    errors.push(`Secure AOI filter creation failed: ${errorMessage}`);
    
    return {
      success: false,
      method: 'FAILED',
      warnings,
      errors
    };
  }
}

/**
 * Enhanced version of the existing applyAOIClipping function with security improvements
 * This can be used to replace the current implementation in aoiClippingService.ts
 */
export async function applySecureAOIClipping(options: {
  geometry?: GeoJSON.Geometry;
  bounds?: { north: number; south: number; east: number; west: number };
  layerName: string;
  geometryField?: string;
  maxWKTLength?: number;
  enableFallback?: boolean;
}): Promise<SecureAOIResult> {
  const {
    geometry,
    bounds,
    layerName,
    geometryField = 'the_geom',
    maxWKTLength = 6000,
    enableFallback = true
  } = options;

  const warnings: string[] = [];
  const errors: string[] = [];

  // Method 1: Secure CQL_FILTER with precise geometry (preferred)
  if (geometry) {
    try {
      const result = await createSecureAOIFilter(geometry, layerName, geometryField, maxWKTLength);

      if (result.success) {
        return result;
      }

      // Add errors and warnings from failed attempt
      errors.push(...result.errors);
      warnings.push(...result.warnings);
      
      if (!enableFallback) {
        return {
          success: false,
          method: 'FAILED',
          warnings,
          errors
        };
      }
      
      warnings.push('CQL filter creation failed, falling back to BBOX method');
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(`Secure CQL clipping failed: ${errorMessage}`);
      
      if (!enableFallback) {
        return {
          success: false,
          method: 'FAILED',
          warnings,
          errors
        };
      }
    }
  }

  // Method 2: BBOX clipping (fallback or when only bounds available)
  if (bounds && enableFallback) {
    warnings.push('Using BBOX clipping as fallback method');
    
    // Validate bounds
    if (!isValidBounds(bounds)) {
      errors.push('Invalid bounds provided for BBOX clipping');
      return {
        success: false,
        method: 'FAILED',
        warnings,
        errors
      };
    }
    
    return {
      success: true,
      method: 'BBOX',
      warnings,
      errors
    };
  }

  // No valid clipping method available
  errors.push('No valid geometry or bounds provided for AOI clipping');
  return {
    success: false,
    method: 'FAILED',
    warnings,
    errors
  };
}

/**
 * Validate bounds object
 */
function isValidBounds(bounds: { north: number; south: number; east: number; west: number }): boolean {
  return (
    typeof bounds.north === 'number' && isFinite(bounds.north) &&
    typeof bounds.south === 'number' && isFinite(bounds.south) &&
    typeof bounds.east === 'number' && isFinite(bounds.east) &&
    typeof bounds.west === 'number' && isFinite(bounds.west) &&
    bounds.north > bounds.south &&
    bounds.east > bounds.west &&
    bounds.north <= 90 && bounds.south >= -90 &&
    bounds.east <= 180 && bounds.west >= -180
  );
}

/**
 * Validate an existing CQL filter for security issues
 * This can be used to audit existing CQL filters in the codebase
 */
export function auditExistingCQLFilter(cqlFilter: string): {
  isSecure: boolean;
  securityLevel: 'SAFE' | 'WARNING' | 'DANGEROUS';
  issues: string[];
  recommendations: string[];
} {
  const validation = sanitizeAndValidateCQL(cqlFilter);
  
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  if (!validation.isValid) {
    issues.push(...validation.errors);
    recommendations.push('Replace this CQL filter with a secure version using createSafeCQLFilter()');
  }
  
  if (validation.blockedPatterns.length > 0) {
    issues.push(`Dangerous patterns detected: ${validation.blockedPatterns.join(', ')}`);
    recommendations.push('Remove dangerous patterns and use parameterized CQL generation');
  }
  
  if (validation.warnings.length > 0) {
    issues.push(...validation.warnings);
  }
  
  // Determine security level
  let securityLevel: 'SAFE' | 'WARNING' | 'DANGEROUS' = 'SAFE';
  
  if (validation.blockedPatterns.length > 0 || !validation.isValid) {
    securityLevel = 'DANGEROUS';
  } else if (validation.warnings.length > 0) {
    securityLevel = 'WARNING';
  }
  
  return {
    isSecure: validation.isValid && validation.blockedPatterns.length === 0,
    securityLevel,
    issues,
    recommendations
  };
}

/**
 * Migration helper to update existing CQL filter usage
 * This provides a drop-in replacement for existing convertGeoJSONToWKT + CQL generation
 */
export async function migrateToSecureCQL(
  geometry: GeoJSON.Geometry,
  geometryField: string = 'the_geom'
): Promise<{ cqlFilter: string; migrationNotes: string[] }> {
  const result = await createSecureAOIFilter(geometry, 'migration', geometryField);

  if (!result.success) {
    throw new Error(`Migration failed: ${result.errors.join(', ')}`);
  }
  
  const migrationNotes = [
    'Migrated to secure CQL generation with progressive WKT conversion',
    'Added CQL injection protection and validation',
    ...result.warnings
  ];
  
  return {
    cqlFilter: result.cqlFilter!,
    migrationNotes
  };
}
