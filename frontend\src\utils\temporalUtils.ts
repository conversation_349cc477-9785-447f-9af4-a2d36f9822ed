import { TemporalInfo } from '../components/TimeControl/TimeSlider';

/**
 * Parses temporal metadata string into a structured TemporalInfo object
 * 
 * @param metadata - String containing temporal metadata
 * @returns TemporalInfo object with parsed values
 */
export const parseTemporalMetadata = (metadata: string): TemporalInfo | null => {
  if (!metadata) return null;
  
  const lines = metadata.split('\n');
  const temporalInfo: Partial<TemporalInfo> = {};
  
  for (const line of lines) {
    if (line.startsWith('Dimension:')) {
      temporalInfo.name = line.replace('Dimension:', '').trim();
    } else if (line.startsWith('Units:')) {
      temporalInfo.units = line.replace('Units:', '').trim();
    } else if (line.startsWith('Default Time:')) {
      temporalInfo.default = line.replace('Default Time:', '').trim();
    } else if (line.startsWith('Extent:')) {
      temporalInfo.extent = line.replace('Extent:', '').trim();
    }
  }
  
  // Parse extent into values array if available
  if (temporalInfo.extent) {
    temporalInfo.values = temporalInfo.extent.split(',').map(val => val.trim());
  }
  
  // Validate that we have the minimum required properties
  if (temporalInfo.name) {
    return temporalInfo as TemporalInfo;
  }
  
  return null;
};

/**
 * Formats an ISO date string to a readable format
 * 
 * @param isoDateString - ISO 8601 date string
 * @returns Formatted date string
 */
export const formatDate = (isoDateString: string): string => {
  try {
    const date = new Date(isoDateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    return isoDateString;
  }
};

/**
 * Creates a range of dates between two ISO date strings
 * 
 * @param startDate - Start date in ISO format
 * @param endDate - End date in ISO format
 * @param steps - Number of steps between start and end
 * @returns Array of ISO date strings
 */
export const createDateRange = (startDate: string, endDate: string, steps: number = 10): string[] => {
  const start = new Date(startDate).getTime();
  const end = new Date(endDate).getTime();
  const stepSize = (end - start) / (steps - 1);
  
  const range: string[] = [];
  for (let i = 0; i < steps; i++) {
    const time = start + i * stepSize;
    range.push(new Date(time).toISOString());
  }
  
  return range;
};
