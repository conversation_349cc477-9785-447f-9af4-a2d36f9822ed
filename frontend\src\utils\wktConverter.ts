/**
 * Utility functions for converting between GeoJSON and WKT (Well-Known Text) formats
 * Used for AOI clipping with CQL filters in WMS requests
 */

import * as turf from '@turf/turf';

export interface GeoJSONGeometry {
  type: string;
  coordinates: any;
}

export interface WKTConversionResult {
  wkt: string;
  metadata: {
    simplificationLevel: number;
    originalLength: number;
    finalLength: number;
    method: 'original' | 'simplified' | 'bbox_fallback';
    toleranceUsed?: number;
    coordinatesReduced?: {
      from: number;
      to: number;
    };
  };
  warnings: string[];
}

/**

/**
 * Simplify coordinates by reducing precision and removing redundant points
 * @param coordinates Array of coordinate pairs
 * @param tolerance Simplification tolerance (default: 0.001 degrees ≈ 100m)
 * @param aggressive Whether to use aggressive simplification for very complex geometries
 * @returns Simplified coordinates array
 */
function simplifyCoordinates(coordinates: number[][], tolerance: number = 0.001, aggressive: boolean = false): number[][] {
  if (coordinates.length <= 3) return coordinates; // Keep minimal polygons

  // Use more aggressive simplification for very complex geometries
  const effectiveTolerance = aggressive ? tolerance * 5 : tolerance;
  const minPoints = aggressive ? 5 : 10;
  const maxPoints = aggressive ? 50 : 200; // Limit maximum points for very complex shapes

  const simplified: number[][] = [coordinates[0]]; // Always keep first point

  // If we have too many points, use a step-based approach for aggressive simplification
  if (aggressive && coordinates.length > 500) {
    const step = Math.ceil(coordinates.length / maxPoints);
    for (let i = step; i < coordinates.length - 1; i += step) {
      simplified.push([
        Math.round(coordinates[i][0] * 1000) / 1000, // Round to 3 decimal places for aggressive
        Math.round(coordinates[i][1] * 1000) / 1000
      ]);
    }
  } else {
    // Normal Douglas-Peucker-style simplification
    for (let i = 1; i < coordinates.length - 1; i++) {
      const prev = simplified[simplified.length - 1];
      const curr = coordinates[i];
      const next = coordinates[i + 1];

      // Calculate distance from current point to line between prev and next
      const distance = pointToLineDistance(curr, prev, next);

      // Keep point if it's significant or if we're getting too sparse
      if (distance > effectiveTolerance || simplified.length < minPoints) {
        const precision = aggressive ? 1000 : 10000; // Less precision for aggressive
        simplified.push([
          Math.round(curr[0] * precision) / precision,
          Math.round(curr[1] * precision) / precision
        ]);
      }

      // Stop if we've reached max points for aggressive mode
      if (aggressive && simplified.length >= maxPoints) {
        break;
      }
    }
  }

  // Always keep last point (should be same as first for closed polygons)
  simplified.push(coordinates[coordinates.length - 1]);

  console.log(`🔧 Simplified coordinates (${aggressive ? 'aggressive' : 'normal'}): ${coordinates.length} → ${simplified.length} points`);
  return simplified;
}

/**
 * Calculate perpendicular distance from point to line
 */
function pointToLineDistance(point: number[], lineStart: number[], lineEnd: number[]): number {
  const [px, py] = point;
  const [x1, y1] = lineStart;
  const [x2, y2] = lineEnd;

  const A = px - x1;
  const B = py - y1;
  const C = x2 - x1;
  const D = y2 - y1;

  const dot = A * C + B * D;
  const lenSq = C * C + D * D;

  if (lenSq === 0) return Math.sqrt(A * A + B * B);

  const param = dot / lenSq;

  let xx, yy;
  if (param < 0) {
    xx = x1;
    yy = y1;
  } else if (param > 1) {
    xx = x2;
    yy = y2;
  } else {
    xx = x1 + param * C;
    yy = y1 + param * D;
  }

  const dx = px - xx;
  const dy = py - yy;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Converts a GeoJSON geometry to WKT format with automatic simplification for URL length limits
 * @param geometry GeoJSON geometry object (supports both our interface and standard GeoJSON.Geometry)
 * @param simplify Whether to simplify geometry to reduce URL length (default: true)
 * @param maxLength Maximum WKT length before aggressive simplification (default: 6000)
 * @returns WKT string representation
 */
export function convertGeoJSONToWKT(geometry: GeoJSONGeometry | GeoJSON.Geometry, simplify: boolean = true, maxLength: number = 6000): string {
  // Try normal simplification first
  const normalWKT = convertGeoJSONToWKTInternal(geometry, simplify, false);

  if (!simplify || normalWKT.length <= maxLength) {
    return normalWKT;
  }

  console.log(`⚠️ WKT too long (${normalWKT.length} chars), trying aggressive simplification...`);

  // Try aggressive simplification
  const aggressiveWKT = convertGeoJSONToWKTInternal(geometry, true, true);

  if (aggressiveWKT.length <= maxLength) {
    console.log(`✅ Aggressive simplification successful: ${normalWKT.length} → ${aggressiveWKT.length} chars`);
    return aggressiveWKT;
  }

  console.warn(`❌ Even aggressive simplification too long (${aggressiveWKT.length} chars), returning anyway`);
  return aggressiveWKT;
}

/**
 * Internal WKT conversion function
 * @param geometry GeoJSON geometry object
 * @param simplify Whether to simplify geometry
 * @param aggressive Whether to use aggressive simplification
 * @returns WKT string representation
 */
function convertGeoJSONToWKTInternal(geometry: GeoJSONGeometry | GeoJSON.Geometry, simplify: boolean = true, aggressive: boolean = false): string {
  if (!geometry || !geometry.type) {
    throw new Error('Invalid GeoJSON geometry provided');
  }

  // Handle GeometryCollection
  if (geometry.type === 'GeometryCollection') {
    throw new Error('GeometryCollection is not supported for WKT conversion');
  }

  // Cast to our interface after type validation
  const geom = geometry as GeoJSONGeometry;

  if (!geom.coordinates) {
    throw new Error('Invalid GeoJSON geometry provided - missing coordinates');
  }

  // Apply simplification if requested
  let processedGeom = geom;
  if (simplify) {
    processedGeom = { ...geom };

    switch (geom.type) {
      case 'Polygon':
        processedGeom.coordinates = geom.coordinates.map((ring: number[][]) => simplifyCoordinates(ring, 0.001, aggressive));
        break;
      case 'MultiPolygon':
        processedGeom.coordinates = geom.coordinates.map((polygon: number[][][]) =>
          polygon.map((ring: number[][]) => simplifyCoordinates(ring, 0.001, aggressive))
        );
        break;
      case 'LineString':
        processedGeom.coordinates = simplifyCoordinates(geom.coordinates, 0.001, aggressive);
        break;
      case 'MultiLineString':
        processedGeom.coordinates = geom.coordinates.map((line: number[][]) => simplifyCoordinates(line, 0.001, aggressive));
        break;
      // Points don't need simplification
    }
  }

  switch (processedGeom.type) {
    case 'Point':
      return convertPointToWKT(processedGeom.coordinates);

    case 'Polygon':
      return convertPolygonToWKT(processedGeom.coordinates);

    case 'MultiPolygon':
      return convertMultiPolygonToWKT(processedGeom.coordinates);

    case 'LineString':
      return convertLineStringToWKT(processedGeom.coordinates);

    case 'MultiLineString':
      return convertMultiLineStringToWKT(processedGeom.coordinates);

    default:
      throw new Error(`Unsupported geometry type: ${processedGeom.type}`);
  }
}

/**
 * Convert Point coordinates to WKT
 */
function convertPointToWKT(coordinates: number[]): string {
  if (!Array.isArray(coordinates) || coordinates.length < 2) {
    throw new Error('Invalid Point coordinates');
  }
  
  const [lng, lat] = coordinates;
  return `POINT(${lng} ${lat})`;
}

/**
 * Convert Polygon coordinates to WKT
 */
function convertPolygonToWKT(coordinates: number[][][]): string {
  if (!Array.isArray(coordinates) || coordinates.length === 0) {
    throw new Error('Invalid Polygon coordinates');
  }

  // Helper to ensure a ring is closed and valid
  const closeAndValidateRing = (ring: number[][]): number[][] => {
    if (!Array.isArray(ring) || ring.length < 4) {
      throw new Error('Invalid polygon ring - must have at least 4 coordinates');
    }

    // Remove any invalid points
    const cleaned = ring.filter((coord) =>
      Array.isArray(coord) && coord.length >= 2 && isFinite(coord[0]) && isFinite(coord[1])
    );

    if (cleaned.length < 4) {
      throw new Error('Invalid polygon ring after cleaning - not enough valid coordinates');
    }

    const first = cleaned[0];
    const last = cleaned[cleaned.length - 1];
    const isClosed = first[0] === last[0] && first[1] === last[1];
    return isClosed ? cleaned : [...cleaned, [first[0], first[1]]];
  };

  const rings = coordinates.map((ring) => {
    const closed = closeAndValidateRing(ring);
    const ringWKT = closed.map((coord) => `${coord[0]} ${coord[1]}`).join(', ');
    return `(${ringWKT})`;
  });

  return `POLYGON(${rings.join(', ')})`;
}

/**
 * Convert MultiPolygon coordinates to WKT
 */
function convertMultiPolygonToWKT(coordinates: number[][][][]): string {
  if (!Array.isArray(coordinates) || coordinates.length === 0) {
    throw new Error('Invalid MultiPolygon coordinates');
  }

  // Reuse polygon ring closure/validation for each polygon's rings
  const polygons = coordinates.map((polygonCoords) => {
    if (!Array.isArray(polygonCoords) || polygonCoords.length === 0) {
      throw new Error('Invalid polygon in MultiPolygon');
    }

    const rings = polygonCoords.map((ring) => {
      // Close and validate the ring similar to polygon
      const cleaned = ring.filter((coord) => Array.isArray(coord) && coord.length >= 2 && isFinite(coord[0]) && isFinite(coord[1]));
      if (cleaned.length < 4) {
        throw new Error('Invalid ring in MultiPolygon - not enough valid coordinates');
      }
      const first = cleaned[0];
      const last = cleaned[cleaned.length - 1];
      const isClosed = first[0] === last[0] && first[1] === last[1];
      const closed = isClosed ? cleaned : [...cleaned, [first[0], first[1]]];
      const ringWKT = closed.map((coord) => `${coord[0]} ${coord[1]}`).join(', ');
      return `(${ringWKT})`;
    });

    return `(${rings.join(', ')})`;
  });

  return `MULTIPOLYGON(${polygons.join(', ')})`;
}

/**
 * Convert LineString coordinates to WKT
 */
function convertLineStringToWKT(coordinates: number[][]): string {
  if (!Array.isArray(coordinates) || coordinates.length < 2) {
    throw new Error('Invalid LineString coordinates');
  }

  const lineWKT = coordinates.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
  return `LINESTRING(${lineWKT})`;
}

/**
 * Convert MultiLineString coordinates to WKT
 */
function convertMultiLineStringToWKT(coordinates: number[][][]): string {
  if (!Array.isArray(coordinates) || coordinates.length === 0) {
    throw new Error('Invalid MultiLineString coordinates');
  }

  const lines = coordinates.map(lineCoords => {
    const lineWKT = lineCoords.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
    return `(${lineWKT})`;
  });

  return `MULTILINESTRING(${lines.join(', ')})`;
}

/**
 * Validates that a GeoJSON geometry has valid coordinates
 */
export function validateGeoJSONGeometry(geometry: any): boolean {
  try {
    convertGeoJSONToWKT(geometry);
    return true;
  } catch (error) {
    console.error('Invalid GeoJSON geometry:', error);
    return false;
  }
}

/**
 * Creates a WKT representation from bounds for BBOX clipping
 */
export function createBoundsWKT(bounds: { north: number; south: number; east: number; west: number }): string {
  const { north, south, east, west } = bounds;
  
  // Create a polygon from bounds
  const coordinates = [
    [west, south],  // Bottom-left
    [east, south],  // Bottom-right
    [east, north],  // Top-right
    [west, north],  // Top-left
    [west, south]   // Close the polygon
  ];
  
  const ringWKT = coordinates.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
  return `POLYGON((${ringWKT}))`;
}

/**
 * Progressive WKT converter that handles HTTP 431 errors by using staged simplification
 * @param geometry GeoJSON geometry object
 * @param maxLength Maximum WKT length before simplification (default: 6000)
 * @returns WKTConversionResult with WKT string and detailed metadata
 */
export function convertGeoJSONToWKTProgressive(
  geometry: GeoJSONGeometry | GeoJSON.Geometry,
  maxLength: number = 6000
): WKTConversionResult {
  const warnings: string[] = [];

  if (!geometry || !geometry.type) {
    throw new Error('Invalid GeoJSON geometry provided');
  }

  // Handle GeometryCollection
  if (geometry.type === 'GeometryCollection') {
    throw new Error('GeometryCollection is not supported for WKT conversion');
  }

  const geom = geometry as GeoJSONGeometry;
  if (!geom.coordinates) {
    throw new Error('Invalid GeoJSON geometry provided - missing coordinates');
  }

  // Stage 1: Try original geometry
  try {
    const originalWKT = convertGeoJSONToWKTInternal(geom, false, false);
    if (originalWKT.length <= maxLength) {
      return {
        wkt: originalWKT,
        metadata: {
          simplificationLevel: 0,
          originalLength: originalWKT.length,
          finalLength: originalWKT.length,
          method: 'original'
        },
        warnings: []
      };
    }
    warnings.push(`Original WKT too long (${originalWKT.length} chars), attempting simplification`);
  } catch (error) {
    warnings.push(`Original conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  // Stage 2-6: Progressive simplification with different tolerance levels
  const toleranceLevels = [0.001, 0.005, 0.01, 0.05, 0.1];

  for (let i = 0; i < toleranceLevels.length; i++) {
    const tolerance = toleranceLevels[i];
    try {
      const simplifiedGeom = simplifyGeometryWithTurf(geom, tolerance);
      const simplifiedWKT = convertGeoJSONToWKTInternal(simplifiedGeom, false, false);

      if (simplifiedWKT.length <= maxLength) {
        const originalCoordCount = countCoordinates(geom);
        const finalCoordCount = countCoordinates(simplifiedGeom);

        return {
          wkt: simplifiedWKT,
          metadata: {
            simplificationLevel: i + 1,
            originalLength: -1, // Unknown since original failed
            finalLength: simplifiedWKT.length,
            method: 'simplified',
            toleranceUsed: tolerance,
            coordinatesReduced: {
              from: originalCoordCount,
              to: finalCoordCount
            }
          },
          warnings
        };
      }
      warnings.push(`Simplification level ${i + 1} (tolerance: ${tolerance}) still too long (${simplifiedWKT.length} chars)`);
    } catch (error) {
      warnings.push(`Simplification level ${i + 1} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Stage 7: Fallback to bounding box
  try {
    const bounds = calculateGeometryBounds(geom);
    const bboxWKT = createBoundsWKT(bounds);

    warnings.push('All simplification attempts failed, falling back to bounding box polygon');

    return {
      wkt: bboxWKT,
      metadata: {
        simplificationLevel: 6,
        originalLength: -1,
        finalLength: bboxWKT.length,
        method: 'bbox_fallback'
      },
      warnings
    };
  } catch (error) {
    throw new Error(`All WKT conversion attempts failed, including bounding box fallback: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Simplify geometry using Turf.js for more accurate simplification
 */
function simplifyGeometryWithTurf(geometry: GeoJSONGeometry, tolerance: number): GeoJSONGeometry {
  try {
    // Convert to GeoJSON Feature for Turf
    const feature: GeoJSON.Feature = {
      type: 'Feature',
      properties: {},
      geometry: geometry as GeoJSON.Geometry
    };

    // Use Turf simplify with high quality setting
    const simplified = turf.simplify(feature, {
      tolerance: tolerance,
      highQuality: true,
      mutate: false
    });

    return simplified.geometry as GeoJSONGeometry;
  } catch (error) {
    // Fallback to manual simplification if Turf fails
    return simplifyGeometryManually(geometry, tolerance);
  }
}

/**
 * Manual geometry simplification fallback
 */
function simplifyGeometryManually(geometry: GeoJSONGeometry, tolerance: number): GeoJSONGeometry {
  const processedGeom = { ...geometry };

  switch (geometry.type) {
    case 'Polygon':
      processedGeom.coordinates = geometry.coordinates.map((ring: number[][]) =>
        simplifyCoordinates(ring, tolerance, tolerance > 0.01)
      );
      break;
    case 'MultiPolygon':
      processedGeom.coordinates = geometry.coordinates.map((polygon: number[][][]) =>
        polygon.map((ring: number[][]) => simplifyCoordinates(ring, tolerance, tolerance > 0.01))
      );
      break;
    case 'LineString':
      processedGeom.coordinates = simplifyCoordinates(geometry.coordinates, tolerance, tolerance > 0.01);
      break;
    case 'MultiLineString':
      processedGeom.coordinates = geometry.coordinates.map((line: number[][]) =>
        simplifyCoordinates(line, tolerance, tolerance > 0.01)
      );
      break;
    // Points don't need simplification
  }

  return processedGeom;
}

/**
 * Count total coordinates in a geometry
 */
function countCoordinates(geometry: GeoJSONGeometry): number {
  const countArray = (arr: any): number => {
    if (Array.isArray(arr)) {
      if (typeof arr[0] === 'number') {
        return 1; // This is a coordinate pair
      }
      return arr.reduce((sum, item) => sum + countArray(item), 0);
    }
    return 0;
  };

  return countArray(geometry.coordinates);
}

/**
 * Calculate bounds from geometry
 */
function calculateGeometryBounds(geometry: GeoJSONGeometry): { north: number; south: number; east: number; west: number } {
  try {
    const feature: GeoJSON.Feature = {
      type: 'Feature',
      properties: {},
      geometry: geometry as GeoJSON.Geometry
    };

    const bbox = turf.bbox(feature);
    return {
      west: bbox[0],
      south: bbox[1],
      east: bbox[2],
      north: bbox[3]
    };
  } catch (error) {
    // Fallback to manual bounds calculation
    return calculateBoundsManually(geometry);
  }
}

/**
 * Manual bounds calculation fallback
 */
function calculateBoundsManually(geometry: GeoJSONGeometry): { north: number; south: number; east: number; west: number } {
  let minLng = Infinity, maxLng = -Infinity;
  let minLat = Infinity, maxLat = -Infinity;

  const processCoordinate = (coord: number[]) => {
    const [lng, lat] = coord;
    if (lng < minLng) minLng = lng;
    if (lng > maxLng) maxLng = lng;
    if (lat < minLat) minLat = lat;
    if (lat > maxLat) maxLat = lat;
  };

  const processCoordinates = (coords: any) => {
    if (Array.isArray(coords)) {
      if (typeof coords[0] === 'number') {
        processCoordinate(coords);
      } else {
        coords.forEach(processCoordinates);
      }
    }
  };

  processCoordinates(geometry.coordinates);

  return {
    west: minLng,
    south: minLat,
    east: maxLng,
    north: maxLat
  };
}

/**
 * Debug function to log WKT conversion details
 */
export function debugWKTConversion(geometry: GeoJSONGeometry): {
  originalType: string;
  coordinatesLength: number;
  wkt: string;
  wktLength: number;
} {
  const wkt = convertGeoJSONToWKT(geometry);

  return {
    originalType: geometry.type,
    coordinatesLength: JSON.stringify(geometry.coordinates).length,
    wkt: wkt,
    wktLength: wkt.length
  };
}
