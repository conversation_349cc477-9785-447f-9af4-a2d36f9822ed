import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  build: {
    chunkSizeWarningLimit: 1000 // Increase limit to reduce warnings
  },
  server: {
    proxy: {
      '/geoserver': {
        target: 'https://*************',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/geoserver/, '/geoserver')
      },
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false
      },
      '/ows': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/ows/, '/api/ows')
      }
    }
  }
});
