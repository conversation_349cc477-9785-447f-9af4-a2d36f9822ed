# Frontend build stage
FROM node:18.20.2-alpine as frontend-build
WORKDIR /app
COPY frontend/package*.json ./
RUN npm install
COPY frontend/ .
RUN npm run build && [ -d dist ] && echo "Frontend build successful" || (echo "Frontend build failed: dist directory not found" && exit 1)

# Nginx production stage
FROM nginx:alpine

# Create ssl and logs directories with correct permissions
RUN mkdir -p /etc/nginx/ssl /var/log/nginx \
    && chmod 755 /etc/nginx/ssl /var/log/nginx

# Remove default nginx index page
RUN rm -rf /usr/share/nginx/html/*

# Copy frontend build files
COPY --from=frontend-build /app/dist /usr/share/nginx/html

# Copy the custom nginx configuration
COPY nginx/nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
# EXPOSE 443 # Uncomment for HTTPS
CMD ["nginx", "-g", "daemon off;"]
