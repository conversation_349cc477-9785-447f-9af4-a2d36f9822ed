// Node.js script to test REST endpoints using axios
// Usage: node axios-test.js

const axios = require('axios');
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'; // Ignore self-signed SSL certs for testing

const API_BASE = 'https://*************';
const AUTH_HEADER = 'Basic Tmdvbmk6QGZyaWNAMSYyMDI1IzI='; 

async function fetchCategories() {
  try {
    const response = await axios.get(`${API_BASE}/api/v2/categories?format=json`, {
      headers: {
        'Authorization': AUTH_HEADER,
        'Accept': 'application/json',
      },
      maxRedirects: 5, // Follow up to 5 redirects
    });
    console.log('Status:', response.status);
    console.log('Categories:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Headers:', error.response.headers);
      console.log('Data:', error.response.data);
    } else {
      console.error('Request error:', error.message);
    }
  }
}

fetchCategories();
