const https = require('https');

const agent = new https.Agent({
  rejectUnauthorized: false
});

fetch('https://10.150.16.184/api/v2/categories?format=json', {
  method: 'GET',
  headers: {
    'Authorization': 'Basic Tmdvbmk6QGZyaWNAMSYyMDI1IzI=',
    'Accept': 'application/json'
  },
  dispatcher: agent // This works with Node.js v18+ fetch
})
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('Fetched data:', data);
  })
  .catch(error => {
    console.error('Error fetching data:', error);
  });