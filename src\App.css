/* Main app container */
.app-wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.app-container {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

/* Sidebar styles */
.sidebar-container {
  height: 100%;
  padding: 0;
  background-color: #f8f9fa;
  overflow-y: auto;
  border-right: 1px solid #dee2e6;
}

/* Map container styles */
.map-container {
  height: 100%;
  padding: 0;
}

/* Ensure the map takes full height */
.leaflet-container {
  height: 100%;
  width: 100%;
}

/* Legend styles */
.map-legend {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background: white;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-color {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

/* Button styles */
.action-button {
  width: 100%;
  background-color: #0a4273;
  border: none;
  margin-bottom: 10px;
  padding: 8px 0;
  transition: background-color 0.3s ease;
}

.action-button:hover {
  background-color: #063057;
}

/* Input styles */
.date-input {
  margin-bottom: 10px;
}

/* Section styles */
.section-title {
  font-weight: 600;
  margin-bottom: 15px;
  margin-top: 20px;
}

.section-content {
  margin-bottom: 20px;
}

/* Checkbox styles */
.layer-checkbox {
  margin-bottom: 8px;
}

.layer-label {
  margin-left: 8px;
}

/* Search input */
.search-input {
  width: 100%;
  margin-bottom: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }
  
  .sidebar-container, .map-container {
    height: auto;
  }
  
  .sidebar-container {
    max-height: 50vh;
    overflow-y: scroll;
  }
  
  .map-container {
    height: 50vh;
  }
  
  .leaflet-container {
    height: 50vh;
  }
}