import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import NavBar from './components/NavBar/NavBar';
import Sidebar from './components/Sidebar/Sidebar';
import MapComponent from './components/Map/MapComponent';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

function App() {
  const [selectedLayers, setSelectedLayers] = React.useState({
    sentinel: false,
    floodRisk: true,
    cbers: true,
    cadastre: true,
    dwsVillage: true,
    nasaPower: false,
    eumetsat: false,
    streamflow: false,
    historicalFlood: false
  });

  const [dateRange, setDateRange] = React.useState({
    startDate: '2025/05/20',
    endDate: '2025/05/20'
  });

  const [searchQuery, setSearchQuery] = React.useState('');
  const [drawnItems, setDrawnItems] = React.useState<any>(null);

  const handleLayerChange = (layerName: string, checked: boolean) => {
    setSelectedLayers(prev => ({
      ...prev,
      [layerName]: checked
    }));
  };

  const handleDateChange = (type: 'startDate' | 'endDate', value: string) => {
    setDateRange(prev => ({
      ...prev,
      [type]: value
    }));
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    console.log('Searching for:', query);
  };

  const handleDrawComplete = (layers: any) => {
    setDrawnItems(layers);
    console.log('Drawn items:', layers);
  };

  const handlePreviewData = () => {
    console.log('Preview data for layers:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
  };

  const handleDownloadData = () => {
    console.log('Downloading data for layers:', selectedLayers);
    console.log('Date range:', dateRange);
    console.log('Region:', drawnItems);
  };

  return (
    <div className="app-wrapper">
      <NavBar />
      <Container fluid className="app-container">
        <Row className="h-100">
          <Col xs={12} md={4} lg={3} className="sidebar-container">
            <Sidebar 
              selectedLayers={selectedLayers}
              onLayerChange={handleLayerChange}
              dateRange={dateRange}
              onDateChange={handleDateChange}
              onSearch={handleSearch}
              onPreviewData={handlePreviewData}
              onDownloadData={handleDownloadData}
            />
          </Col>
          <Col xs={12} md={8} lg={9} className="map-container">
            <MapComponent 
              selectedLayers={selectedLayers}
              onDrawComplete={handleDrawComplete}
            />
          </Col>
        </Row>
      </Container>
    </div>
  );
}

export default App;