.map-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
}

.map {
  height: 100%;
  width: 100%;
}

/* Override leaflet draw styles */
.leaflet-draw-toolbar a {
  background-color: #fff;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.leaflet-draw-toolbar a:hover {
  background-color: #f4f4f4;
}

.leaflet-control-zoom a {
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .map-wrapper, .map {
    height: 50vh;
  }
}