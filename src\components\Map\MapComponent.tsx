import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, FeatureGroup, WMSTileLayer } from 'react-leaflet';
import { EditControl } from 'react-leaflet-draw';
import { fetchAvailableLayers, WMSLayer } from '../../services/geoserverService';
import MapLayers from './MapLayers';
import MapLegend from './MapLegend';
import './MapComponent.css';

interface MapComponentProps {
  selectedLayers: {
    sentinel: boolean;
    floodRisk: boolean;
    cbers: boolean;
    cadastre: boolean;
    dwsVillage: boolean;
    nasaPower: boolean;
    eumetsat: boolean;
    streamflow: boolean;
    historicalFlood: boolean;
  };
  onDrawComplete: (layers: any) => void;
}

const MapComponent: React.FC<MapComponentProps> = ({ 
  selectedLayers, 
  onDrawComplete 
}) => {
  const [center] = useState<[number, number]>([-29.0, 24.0]);
  const [zoom] = useState<number>(6);
  const [wmsLayers, setWmsLayers] = useState<WMSLayer[]>([]);

  useEffect(() => {
    const loadWMSLayers = async () => {
      const layers = await fetchAvailableLayers();
      setWmsLayers(layers);
    };

    loadWMSLayers();
  }, []);

  const handleCreated = (e: any) => {
    const { layerType, layer } = e;
    if (layerType === 'polygon') {
      const geoJSON = layer.toGeoJSON();
      onDrawComplete(geoJSON);
    }
  };

  return (
    <div className="map-wrapper">
      <MapContainer 
        center={center} 
        zoom={zoom} 
        scrollWheelZoom={true} 
        className="map"
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        {wmsLayers.map((layer) => (
          <WMSTileLayer
            key={layer.name}
            url={`${process.env.VITE_GEOSERVER_URL}/wms`}
            layers={layer.name}
            format="image/png"
            transparent={true}
            version="1.1.1"
          />
        ))}

        <FeatureGroup>
          <EditControl
            position="topright"
            onCreated={handleCreated}
            draw={{
              rectangle: false,
              circle: false,
              circlemarker: false,
              marker: false,
              polyline: false,
              polygon: {
                allowIntersection: false,
                drawError: {
                  color: '#e1e100',
                  message: '<strong>Error:</strong> Shape edges cannot cross!'
                },
                shapeOptions: {
                  color: '#0a4273',
                  weight: 2
                }
              }
            }}
          />
        </FeatureGroup>
        
        <MapLayers selectedLayers={selectedLayers} />
        <MapLegend />
      </MapContainer>
    </div>
  );
};

export default MapComponent;