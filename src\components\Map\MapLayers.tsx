import React from 'react';
import { LayerGroup, Rectangle, Circle, Polygon } from 'react-leaflet';

interface MapLayersProps {
  selectedLayers: {
    sentinel: boolean;
    floodRisk: boolean;
    cbers: boolean;
    cadastre: boolean;
    dwsVillage: boolean;
    nasaPower: boolean;
    eumetsat: boolean;
    streamflow: boolean;
    historicalFlood: boolean;
  };
}

// Mock data for demonstration
const floodRiskAreas = [
  {
    coordinates: [
      [-28.5, 22.5],
      [-28.5, 23.5],
      [-29.5, 23.5],
      [-29.5, 22.5],
    ],
    risk: 'high',
  },
  {
    coordinates: [
      [-30.5, 25.5],
      [-30.5, 26.5],
      [-31.5, 26.5],
      [-31.5, 25.5],
    ],
    risk: 'moderate',
  },
  {
    coordinates: [
      [-27.5, 27.5],
      [-27.5, 28.5],
      [-28.5, 28.5],
      [-28.5, 27.5],
    ],
    risk: 'low',
  },
];

const cadastreData = [
  { bounds: [[-26.5, 24.5], [-25.5, 25.5]] },
  { bounds: [[-27.5, 26.5], [-26.5, 27.5]] },
];

const dwsVillages = [
  { center: [-30, 24], radius: 10000 },
  { center: [-29, 27], radius: 15000 },
  { center: [-27, 25], radius: 12000 },
];

const MapLayers: React.FC<MapLayersProps> = ({ selectedLayers }) => {
  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high':
        return 'red';
      case 'moderate':
        return 'yellow';
      case 'low':
        return 'green';
      default:
        return 'blue';
    }
  };

  return (
    <>
      {selectedLayers.floodRisk && (
        <LayerGroup>
          {floodRiskAreas.map((area, index) => (
            <Polygon
              key={`flood-risk-${index}`}
              positions={area.coordinates}
              pathOptions={{
                color: getRiskColor(area.risk),
                fillColor: getRiskColor(area.risk),
                fillOpacity: 0.4,
              }}
            />
          ))}
        </LayerGroup>
      )}

      {selectedLayers.cadastre && (
        <LayerGroup>
          {cadastreData.map((cadastre, index) => (
            <Rectangle
              key={`cadastre-${index}`}
              bounds={cadastre.bounds}
              pathOptions={{
                color: 'purple',
                weight: 1,
                fillOpacity: 0.2,
              }}
            />
          ))}
        </LayerGroup>
      )}

      {selectedLayers.dwsVillage && (
        <LayerGroup>
          {dwsVillages.map((village, index) => (
            <Circle
              key={`village-${index}`}
              center={village.center}
              radius={village.radius}
              pathOptions={{
                color: 'blue',
                fillColor: 'blue',
                fillOpacity: 0.3,
              }}
            />
          ))}
        </LayerGroup>
      )}
    </>
  );
};

export default MapLayers;