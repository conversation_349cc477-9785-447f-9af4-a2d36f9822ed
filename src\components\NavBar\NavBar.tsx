import React from 'react';
import { Navbar, Nav, Button, Container } from 'react-bootstrap';
import { Home, Package, HelpCircle, FileText, LogOut } from 'lucide-react';
import './NavBar.css';

const NavBar: React.FC = () => {
  return (
    <Navbar className="navbar" variant="dark" expand="lg">
      <Container fluid>
        <Navbar.Brand href="#home" className="brand-text">
          SANSA
        </Navbar.Brand>
        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            <Nav.Link href="#home">
              <Home size={18} className="me-1" />
              Home
            </Nav.Link>
            <Nav.Link href="#products">
              <Package size={18} className="me-1" />
              Products
            </Nav.Link>
            <Nav.Link href="#help">
              <HelpCircle size={18} className="me-1" />
              Help
            </Nav.Link>
            <Nav.Link href="#documentation">
              <FileText size={18} className="me-1" />
              Documentation
            </Nav.Link>
          </Nav>
          <Button className="logout-button">
            <LogOut size={18} className="me-1" />
            Logout
          </Button>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default NavBar;