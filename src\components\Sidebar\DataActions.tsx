import React from 'react';
import { Button } from 'react-bootstrap';

interface DataActionsProps {
  onPreviewData: () => void;
  onDownloadData: () => void;
}

const DataActions: React.FC<DataActionsProps> = ({ onPreviewData, onDownloadData }) => {
  return (
    <div className="data-actions section">
      <h2 className="section-title">Data Actions</h2>
      <Button 
        className="action-button" 
        onClick={onPreviewData}
      >
        Preview Data
      </Button>
      <Button 
        className="action-button" 
        onClick={onDownloadData}
      >
        Download Selected Data
      </Button>
    </div>
  );
};

export default DataActions;