import React from 'react';
import { Form } from 'react-bootstrap';

interface DateSelectorProps {
  dateRange: {
    startDate: string;
    endDate: string;
  };
  onDateChange: (type: 'startDate' | 'endDate', value: string) => void;
}

const DateSelector: React.FC<DateSelectorProps> = ({ dateRange, onDateChange }) => {
  return (
    <div className="date-selector section">
      <h2 className="section-title">Date Range</h2>
      <Form.Group>
        <Form.Label className="date-label">Start Date:</Form.Label>
        <Form.Control
          type="date"
          value={dateRange.startDate.split('/').join('-')}
          onChange={(e) => {
            const date = e.target.value.split('-').join('/');
            onDateChange('startDate', date);
          }}
          className="date-input"
        />
      </Form.Group>
      <Form.Group>
        <Form.Label className="date-label">End Date:</Form.Label>
        <Form.Control
          type="date"
          value={dateRange.endDate.split('/').join('-')}
          onChange={(e) => {
            const date = e.target.value.split('-').join('/');
            onDateChange('endDate', date);
          }}
          className="date-input"
        />
      </Form.Group>
    </div>
  );
};

export default DateSelector;