import React, { useState } from 'react';
import { Button, Form } from 'react-bootstrap';

interface RegionSelectorProps {
  onSearch: (query: string) => void;
}

const RegionSelector: React.FC<RegionSelectorProps> = ({ onSearch }) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchQuery);
  };

  return (
    <div className="region-selector section">
      <h2 className="section-title">Region of Interest</h2>
      <Button className="action-button">Draw Polygon</Button>
      <Form onSubmit={handleSubmit}>
        <Form.Control
          type="text"
          placeholder="Enter location or coordinates"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="region-input"
        />
        <Button type="submit" className="search-button">
          Search
        </Button>
      </Form>
    </div>
  );
};

export default RegionSelector;