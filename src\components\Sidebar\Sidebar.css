.sidebar {
  padding: 15px;
  height: 100%;
  overflow-y: auto;
  background-color: white;
}

.sidebar-header {
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.app-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: #0a4273;
}

.sidebar-content {
  padding-top: 15px;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.form-group {
  margin-bottom: 15px;
}

.date-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.search-button, .action-button {
  background-color: #0a4273;
  border: none;
  width: 100%;
  margin-top: 8px;
  transition: background-color 0.3s ease;
}

.search-button:hover, .action-button:hover {
  background-color: #063057;
}

.region-input {
  width: 100%;
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  margin-top: 5px;
}

.layer-checkbox {
  margin-bottom: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    height: auto;
    padding: 10px;
  }
  
  .app-title {
    font-size: 1.3rem;
  }
}