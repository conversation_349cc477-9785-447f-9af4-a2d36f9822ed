import React from 'react';
import { Form, Button } from 'react-bootstrap';
import DateSelector from './DateSelector';
import RegionSelector from './RegionSelector';
import DataLayers from './DataLayers';
import DataActions from './DataActions';
import './Sidebar.css';

interface SidebarProps {
  selectedLayers: {
    sentinel: boolean;
    floodRisk: boolean;
    cbers: boolean;
    cadastre: boolean;
    dwsVillage: boolean;
    nasaPower: boolean;
    eumetsat: boolean;
    streamflow: boolean;
    historicalFlood: boolean;
  };
  onLayerChange: (layerName: string, checked: boolean) => void;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  onDateChange: (type: 'startDate' | 'endDate', value: string) => void;
  onSearch: (query: string) => void;
  onPreviewData: () => void;
  onDownloadData: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  selectedLayers,
  onLayerChange,
  dateRange,
  onDateChange,
  onSearch,
  onPreviewData,
  onDownloadData
}) => {
  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h1 className="app-title">Flood Monitoring System</h1>
      </div>
      
      <div className="sidebar-content">
        <DateSelector 
          dateRange={dateRange} 
          onDateChange={onDateChange} 
        />
        
        <RegionSelector onSearch={onSearch} />
        
        <DataLayers 
          selectedLayers={selectedLayers} 
          onLayerChange={onLayerChange} 
        />
        
        <DataActions 
          onPreviewData={onPreviewData} 
          onDownloadData={onDownloadData} 
        />
      </div>
    </div>
  );
};

export default Sidebar;