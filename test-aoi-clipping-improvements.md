# AOI Clipping Improvements - Test Guide

## Overview

This document provides testing instructions for the AOI clipping improvements implemented to address the issues with overly restrictive raster layer handling and missing server-side clipping capabilities.

## Changes Implemented

### 1. Frontend: Updated aoiClippingService.ts
- ✅ Added `supportsCQL?: boolean` parameter to `AOIClippingOptions` interface
- ✅ Added `fallbackReason?: string` to `ClippingResult` interface
- ✅ Replaced hardcoded `isRasterLayerType()` check with capability-driven decisions
- ✅ Enhanced error messages with descriptive fallback reasons
- ✅ Improved logging for better debugging

### 2. Frontend: Updated MapComponent.tsx
- ✅ Added layer capabilities service integration
- ✅ Modified clipping logic to fetch capabilities before applying clipping
- ✅ Added toast notification for BBOX fallback usage
- ✅ Enhanced error handling with fallback reason display

### 3. UIEngine: Added /ows/clip endpoint
- ✅ Created new POST endpoint `/ows/clip` in `server/routes/ows.ts`
- ✅ Implemented intelligent clipping strategy based on layer capabilities
- ✅ Added support for vector layers with CQL_FILTER
- ✅ Added support for raster layers with BBOX fallback
- ✅ Included proper error handling and validation
- ✅ Added Swagger documentation

### 4. Frontend: Added Toast Notification System
- ✅ Created `ToastNotification` component with Bootstrap styling
- ✅ Created `ToastContext` and `useToast` hook for state management
- ✅ Integrated toast system into main App component
- ✅ Added specific `showClippingFallback` method for clipping notifications
- ✅ Connected toast notifications to clipping fallback scenarios

## Testing Instructions

### Test 1: Capability-Driven CQL Support

1. **Start the application**:
   ```bash
   # Terminal 1 - UIEngine
   cd uiengine
   npm run dev

   # Terminal 2 - Frontend
   cd frontend
   npm run dev
   ```

2. **Test with supportsCQL=true**:
   - Select an AOI (administrative boundary or drawn polygon)
   - Toggle on a vector layer (e.g., administrative boundaries)
   - Check browser console for: `🔍 Layer capabilities for "layername": { supportsCQL: true }`
   - Verify CQL_FILTER is applied: `✅ CQL_FILTER clipping applied`

3. **Test with supportsCQL=false**:
   - Select an AOI
   - Toggle on a raster layer (e.g., satellite imagery)
   - Check browser console for: `🗺️ Skipping CQL for "layername": Layer capabilities indicate CQL not supported`
   - Verify BBOX fallback: `📦 BBOX clipping applied`
   - **Expected**: Toast notification appears explaining BBOX usage

### Test 2: Heuristic Fallback

1. **Test when capabilities are unavailable**:
   - Disconnect from network or simulate capability fetch failure
   - Select AOI and toggle layers
   - Verify heuristic fallback works: raster layers use BBOX, vector layers attempt CQL

### Test 3: Toast Notifications

1. **Test BBOX fallback notification**:
   - Select AOI and toggle a raster layer
   - **Expected**: Orange warning toast appears with:
     - Title: "BBOX Clipping Used"
     - Message: "Layer 'layername': Raster layer - precise clipping not yet implemented, using BBOX"
     - Auto-dismisses after 7 seconds

2. **Test multiple notifications**:
   - Toggle multiple raster layers quickly
   - **Expected**: Multiple toast notifications stack properly
   - **Expected**: Each notification is dismissible individually

### Test 4: UIEngine /ows/clip Endpoint

1. **Test the new endpoint directly**:
   ```bash
   curl -X POST http://localhost:3001/api/ows/clip \
     -H "Content-Type: application/json" \
     -d '{
       "layers": ["test_vector_layer", "test_raster_layer"],
       "aoi_wkt": "POLYGON((27 -26, 28 -26, 28 -27, 27 -27, 27 -26))",
       "crs": "EPSG:4326"
     }'
   ```

2. **Expected Response**:
   ```json
   {
     "success": true,
     "layers": {
       "test_vector_layer": {
         "method": "CQL_FILTER",
         "url": "https://*************/geoserver/geonode/ows",
         "parameters": { "CQL_FILTER": "INTERSECTS(the_geom, POLYGON(...))" },
         "fallbackReason": null
       },
       "test_raster_layer": {
         "method": "BBOX",
         "url": "https://*************/geoserver/geonode/ows",
         "parameters": { "BBOX": "27,-27,28,-26" },
         "fallbackReason": "Raster layer - precise clipping not yet implemented, using BBOX"
       }
     }
   }
   ```

### Test 5: Error Handling

1. **Test invalid requests**:
   ```bash
   # Missing layers
   curl -X POST http://localhost:3001/api/ows/clip \
     -H "Content-Type: application/json" \
     -d '{"aoi_wkt": "POLYGON((27 -26, 28 -26, 28 -27, 27 -27, 27 -26))"}'
   ```
   **Expected**: 400 error with descriptive message

2. **Test invalid WKT**:
   ```bash
   curl -X POST http://localhost:3001/api/ows/clip \
     -H "Content-Type: application/json" \
     -d '{"layers": ["test"], "aoi_wkt": "INVALID_WKT"}'
   ```
   **Expected**: 400 error with WKT conversion failure message

## Validation Checklist

- [ ] Frontend compiles without errors
- [ ] UIEngine compiles without errors
- [ ] Toast notifications appear for BBOX fallback
- [ ] Layer capabilities are fetched before clipping decisions
- [ ] CQL_FILTER is used for vector layers when supported
- [ ] BBOX fallback works for raster layers
- [ ] /ows/clip endpoint responds correctly
- [ ] Error handling works for invalid requests
- [ ] Console logging provides clear debugging information
- [ ] Toast notifications are dismissible and stack properly

## Expected Outcome

After successful implementation:

1. **Precise Clipping**: Vector layers use CQL_FILTER when capabilities indicate support
2. **Intelligent Fallback**: Raster layers gracefully fall back to BBOX with user notification
3. **User Awareness**: Toast notifications inform users when BBOX is used instead of precise clipping
4. **Server-side Support**: New /ows/clip endpoint provides intelligent clipping strategy
5. **Better Debugging**: Enhanced logging and error messages for troubleshooting

## Troubleshooting

### Common Issues

1. **Toast notifications not appearing**:
   - Check that ToastProvider wraps the App component
   - Verify useToast hook is called within ToastProvider context

2. **Capabilities not being fetched**:
   - Check network requests in browser dev tools
   - Verify layer capabilities service is properly imported

3. **CQL_FILTER not working**:
   - Check that supportsCQL flag is being passed correctly
   - Verify WKT conversion is working properly

4. **BBOX fallback not working**:
   - Check that bounds are available in AOI data
   - Verify fallback logic in aoiClippingService.ts
