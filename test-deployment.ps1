# Simple deployment test (PowerShell) - For testing before Linux deployment

Write-Host "🧪 SANSA Deployment Test (Windows/PowerShell)" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""

# Check current directory
Write-Host "📁 Current Directory: $(Get-Location)" -ForegroundColor Cyan

# Check if Docker is available
Write-Host "🔍 Checking Docker..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker is available: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker not found or not running" -ForegroundColor Red
    exit 1
}

# Check if Docker Compose is available
Write-Host "🔍 Checking Docker Compose..." -ForegroundColor Yellow
try {
    $dockerComposeVersion = docker-compose --version
    Write-Host "✅ Docker Compose is available: $dockerComposeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose not found" -ForegroundColor Red
    exit 1
}

# Check if docker-compose.yml exists
if (Test-Path "docker-compose.yml") {
    Write-Host "✅ docker-compose.yml found" -ForegroundColor Green
} else {
    Write-Host "❌ docker-compose.yml not found" -ForegroundColor Red
    exit 1
}

# Test docker-compose config
Write-Host "🔍 Testing Docker Compose configuration..." -ForegroundColor Yellow
try {
    docker-compose config > $null
    Write-Host "✅ Docker Compose configuration is valid" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose configuration has errors" -ForegroundColor Red
    Write-Host "Run 'docker-compose config' to see details" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "🎉 All checks passed! Ready for deployment." -ForegroundColor Green
Write-Host ""
Write-Host "To deploy on Linux server:" -ForegroundColor Cyan
Write-Host "1. Transfer files to Linux server" -ForegroundColor White
Write-Host "2. chmod +x deploy-linux.sh" -ForegroundColor White
Write-Host "3. ./deploy-linux.sh" -ForegroundColor White
Write-Host ""
Write-Host "To deploy locally for testing:" -ForegroundColor Cyan
Write-Host "docker-compose up -d --build" -ForegroundColor White
