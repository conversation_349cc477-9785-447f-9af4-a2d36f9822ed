#!/bin/bash

# Simple test script to verify bash execution
echo "=== BASH TEST SCRIPT ==="
echo "Current directory: $(pwd)"
echo "User: $(whoami)"
echo "Date: $(date)"

# Test basic functionality
echo "Testing basic commands..."

# Check if docker is available
if command -v docker &> /dev/null; then
    echo "✅ Docker is available"
    docker --version
else
    echo "❌ Docker not found"
fi

# Check if docker-compose is available
if command -v docker-compose &> /dev/null; then
    echo "✅ Docker Compose is available"
    docker-compose --version
else
    echo "❌ Docker Compose not found"
fi

echo "=== TEST COMPLETE ==="
