#!/usr/bin/env node

/**
 * Test script to validate temporal layers and debug the soil_moisture issue
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';
const GEOSERVER_URL = 'https://*************/geoserver';

async function testTemporalValidation() {
  console.log('🧪 Testing Temporal Layer Validation');
  console.log('=====================================\n');

  // Test layers including the problematic soil_moisture
  const testLayers = [
    'soil_moisture',
    'geonode:soil_moisture', 
    'flood_risk',
    'geonode:flood_risk',
    'africa_mosaic_optmised',
    'geonode:africa_mosaic_optmised'
  ];

  console.log(`Testing ${testLayers.length} layers:`, testLayers);
  console.log('');

  // Test 1: Individual layer validation
  console.log('📋 Test 1: Individual Layer Validation');
  console.log('---------------------------------------');
  
  for (const layer of testLayers) {
    try {
      console.log(`\n🔍 Testing layer: ${layer}`);
      
      const response = await axios.get(`${API_BASE_URL}/time-series/validate-layer/${encodeURIComponent(layer)}`, {
        timeout: 30000
      });
      
      if (response.data.success) {
        const validation = response.data.data;
        console.log(`✅ ${layer}:`, {
          isValid: validation.isValid,
          hasTemporal: validation.hasTemporal,
          hasGeometry: validation.hasGeometry,
          geometryField: validation.geometryField,
          reason: validation.reason
        });
      } else {
        console.log(`❌ ${layer}: API error -`, response.data.error);
      }
      
    } catch (error) {
      console.log(`❌ ${layer}: Request failed -`, error.message);
    }
  }

  // Test 2: Batch validation
  console.log('\n\n📦 Test 2: Batch Layer Validation');
  console.log('----------------------------------');
  
  try {
    const response = await axios.post(`${API_BASE_URL}/time-series/validate-layers`, {
      layers: testLayers
    }, {
      timeout: 60000
    });
    
    if (response.data.success) {
      const { validLayers, invalidLayers } = response.data.data;
      
      console.log(`\n✅ Valid temporal layers (${validLayers.length}):`);
      validLayers.forEach(layer => console.log(`  - ${layer}`));
      
      console.log(`\n❌ Invalid temporal layers (${invalidLayers.length}):`);
      invalidLayers.forEach(invalid => console.log(`  - ${invalid.layer}: ${invalid.reason}`));
      
    } else {
      console.log('❌ Batch validation failed:', response.data.error);
    }
    
  } catch (error) {
    console.log('❌ Batch validation request failed:', error.message);
  }

  // Test 3: Direct GeoServer access test
  console.log('\n\n🌐 Test 3: Direct GeoServer Access');
  console.log('-----------------------------------');
  
  const directTestLayers = ['soil_moisture', 'geonode:soil_moisture'];
  
  for (const layer of directTestLayers) {
    console.log(`\n🔍 Direct test for: ${layer}`);
    
    // Test WFS GetFeature
    try {
      const wfsUrl = `${GEOSERVER_URL}/geonode/wfs?` + new URLSearchParams({
        service: 'WFS',
        version: '1.0.0',
        request: 'GetFeature',
        typeName: layer,
        maxFeatures: '1',
        outputFormat: 'application/json'
      });
      
      console.log(`📡 WFS URL: ${wfsUrl}`);
      
      const wfsResponse = await axios.get(wfsUrl, {
        timeout: 15000,
        httpsAgent: new (require('https').Agent)({
          rejectUnauthorized: false
        })
      });
      
      if (wfsResponse.data && wfsResponse.data.features) {
        console.log(`✅ WFS Success: ${wfsResponse.data.features.length} features`);
        if (wfsResponse.data.features.length > 0) {
          const properties = wfsResponse.data.features[0].properties;
          console.log(`📊 Sample properties:`, Object.keys(properties).slice(0, 10));
          
          // Check for temporal fields
          const temporalFields = Object.keys(properties).filter(key =>
            key.toLowerCase().includes('time') ||
            key.toLowerCase().includes('date') ||
            key.toLowerCase().includes('temporal')
          );
          console.log(`⏰ Temporal fields found:`, temporalFields);
        }
      } else {
        console.log(`❌ WFS: No features returned`);
      }
      
    } catch (error) {
      console.log(`❌ WFS failed:`, error.message);
    }
    
    // Test WFS DescribeFeatureType
    try {
      const describeUrl = `${GEOSERVER_URL}/geonode/wfs?` + new URLSearchParams({
        service: 'WFS',
        version: '1.0.0',
        request: 'DescribeFeatureType',
        typeName: layer
      });
      
      const describeResponse = await axios.get(describeUrl, {
        timeout: 15000,
        httpsAgent: new (require('https').Agent)({
          rejectUnauthorized: false
        })
      });
      
      if (describeResponse.data) {
        console.log(`✅ DescribeFeatureType Success`);
        
        // Look for geometry fields in the schema
        const schemaText = describeResponse.data;
        const geometryMatches = schemaText.match(/name="([^"]*geom[^"]*)".*?type="([^"]*)"/gi);
        if (geometryMatches) {
          console.log(`🗺️ Geometry fields found:`, geometryMatches);
        } else {
          console.log(`❌ No geometry fields detected in schema`);
        }
      }
      
    } catch (error) {
      console.log(`❌ DescribeFeatureType failed:`, error.message);
    }
  }

  // Test 4: WMS Capabilities check
  console.log('\n\n🗺️ Test 4: WMS Capabilities Check');
  console.log('-----------------------------------');
  
  try {
    const capabilitiesUrl = `${GEOSERVER_URL}/geonode/wms?SERVICE=WMS&REQUEST=GetCapabilities&VERSION=1.1.1`;
    
    const capResponse = await axios.get(capabilitiesUrl, {
      timeout: 15000,
      httpsAgent: new (require('https').Agent)({
        rejectUnauthorized: false
      })
    });
    
    if (capResponse.data) {
      console.log(`✅ WMS Capabilities retrieved`);
      
      // Look for soil_moisture in capabilities
      const capText = capResponse.data;
      const soilMoistureMatches = capText.match(/<Name>.*soil_moisture.*<\/Name>/gi);
      if (soilMoistureMatches) {
        console.log(`🌱 Soil moisture layers found in capabilities:`, soilMoistureMatches);
      } else {
        console.log(`❌ No soil_moisture layers found in WMS capabilities`);
      }
      
      // Look for temporal dimensions
      const dimensionMatches = capText.match(/<Dimension[^>]*name="time"[^>]*>([^<]*)<\/Dimension>/gi);
      if (dimensionMatches) {
        console.log(`⏰ Temporal dimensions found:`, dimensionMatches.length);
      } else {
        console.log(`❌ No temporal dimensions found in WMS capabilities`);
      }
    }
    
  } catch (error) {
    console.log(`❌ WMS Capabilities failed:`, error.message);
  }

  console.log('\n🏁 Temporal validation testing complete!');
  console.log('\nRecommendations:');
  console.log('1. Check if soil_moisture layer exists in GeoServer');
  console.log('2. Verify layer has proper geometry field (the_geom vs geometry)');
  console.log('3. Confirm layer has temporal dimensions configured');
  console.log('4. Test CQL_FILTER compatibility with the layer');
}

// Run the test
if (require.main === module) {
  testTemporalValidation().catch(console.error);
}

module.exports = { testTemporalValidation };
