<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Decoupling Test - SANSA Flood Mapping</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f8fafc;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
        }
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #374151;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
        }
        .status.pass { background: #d1fae5; color: #065f46; }
        .status.fail { background: #fee2e2; color: #991b1b; }
        .status.warning { background: #fef3c7; color: #92400e; }
        .status.loading { background: #dbeafe; color: #1e40af; }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .test-item {
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            background: #f9fafb;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        .success { background: #d1fae5; color: #065f46; }
        .error { background: #fee2e2; color: #991b1b; }
        .info { background: #dbeafe; color: #1e40af; }
        .warning { background: #fef3c7; color: #92400e; }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2563eb; }
        button:disabled { background: #9ca3af; cursor: not-allowed; }
        
        .controls {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f3f4f6;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Database Decoupling Validation Test</h1>
            <p>Testing SANSA Flood Mapping application with database unavailable</p>
        </div>

        <div class="controls">
            <button onclick="runAllTests()">🚀 Run All Tests</button>
            <button onclick="testUIEngineHealth()">🏥 Test UI Engine Health</button>
            <button onclick="testAnalytics()">📊 Test Analytics Fallback</button>
            <button onclick="testROI()">🗺️ Test ROI Fallback</button>
            <button onclick="clearResults()">🧹 Clear Results</button>
        </div>

        <div class="test-section">            <h3>Core UI Engine Health Check <span id="uiengine-status" class="status loading">TESTING</span></h3>
            <div id="uiengine-result" class="test-result info">Click "Test UI Engine Health" to check if core features work without database...</div>
        </div>

        <div class="test-section">
            <h3>Feature Availability Matrix</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>🗺️ Core WMS Features</h4>
                    <div id="wms-result" class="test-result info">Should always work regardless of database state</div>
                </div>
                <div class="test-item">
                    <h4>🚨 Alert System</h4>
                    <div id="alert-result" class="test-result info">Should fallback to in-memory mode</div>
                </div>
                <div class="test-item">
                    <h4>📊 Analytics Dashboard</h4>
                    <div id="analytics-result" class="test-result info">Should show demo data in offline mode</div>
                </div>
                <div class="test-item">
                    <h4>🔍 ROI Analysis</h4>
                    <div id="roi-result" class="test-result info">Should use client-side calculations</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Database Benefit Documentation</h3>
            <div id="benefits-result" class="test-result info">
                <h4>🔥 Features that benefit from database connection:</h4>
                <ul>
                    <li><strong>📊 Advanced Analytics</strong>: Historical trends vs. demo data</li>
                    <li><strong>🚨 Persistent Alerts</strong>: Saved rules & history vs. session-only</li>
                    <li><strong>🗺️ Advanced Spatial Analysis</strong>: PostGIS precision vs. client-side approximations</li>
                    <li><strong>📈 Historical Data</strong>: Long-term trends vs. current session snapshots</li>
                    <li><strong>💾 Data Persistence</strong>: Saved configurations vs. session-only storage</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>Test Results Log</h3>
            <div id="test-log" style="background: #111827; color: #f9fafb; padding: 15px; border-radius: 6px; font-family: monospace; min-height: 200px; overflow-y: auto;">
                Waiting for tests to run...<br>
            </div>
        </div>
    </div>

    <script>
        let testLog = document.getElementById('test-log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#60a5fa',
                success: '#34d399', 
                error: '#f87171',
                warning: '#fbbf24'
            };
            testLog.innerHTML += `<span style="color: ${colors[type] || colors.info}">[${timestamp}] ${message}</span><br>`;
            testLog.scrollTop = testLog.scrollHeight;
        }

        function clearResults() {
            testLog.innerHTML = 'Test log cleared...<br>';
            document.querySelectorAll('.test-result').forEach(el => {
                el.className = 'test-result info';
                el.innerHTML = 'Ready for testing...';
            });            document.getElementById('uiengine-status').className = 'status loading';
            document.getElementById('uiengine-status').textContent = 'READY';
        }        async function testUIEngineHealth() {
            log('🏥 Testing UI Engine health endpoint...', 'info');
            document.getElementById('uiengine-status').className = 'status loading';
            document.getElementById('uiengine-status').textContent = 'TESTING';
            
            try {
                const response = await fetch('/api/health');
                const health = await response.json();
                
                log(`✅ UI Engine responded: ${JSON.stringify(health, null, 2)}`, 'success');
                
                // Check core features
                const coreAvailable = health.features?.core === true;
                const dbConnected = health.database === 'connected';
                const dataMode = health.dataMode;
                
                if (coreAvailable) {                    document.getElementById('uiengine-status').className = 'status pass';
                    document.getElementById('uiengine-status').textContent = 'HEALTHY';
                    document.getElementById('backend-result').className = 'test-result success';
                    document.getElementById('backend-result').innerHTML = `
                        ✅ Backend is healthy<br>
                        📊 Data Mode: ${dataMode}<br>
                        🗄️ Database: ${health.database}<br>
                        🔥 Core Features: Available<br>
                        🗺️ Spatial Features: ${health.features.spatial ? 'Available' : 'Fallback Mode'}<br>
                        🚨 Alerts: ${health.features.alerts ? 'Available' : 'Unavailable'}<br>
                        📊 Analytics: ${health.features.analytics ? 'Available' : 'Unavailable'}
                    `;
                    
                    // Update feature status
                    updateFeatureStatus(health);
                    
                    if (!dbConnected) {
                        log('⚠️ Database disconnected - testing fallback modes', 'warning');
                    }
                } else {
                    throw new Error('Core features not available');
                }
                
            } catch (error) {
                log(`❌ Backend health check failed: ${error.message}`, 'error');
                document.getElementById('backend-status').className = 'status fail';
                document.getElementById('backend-status').textContent = 'FAILED';
                document.getElementById('backend-result').className = 'test-result error';
                document.getElementById('backend-result').innerHTML = `❌ Backend health check failed: ${error.message}`;
            }
        }

        function updateFeatureStatus(health) {
            // WMS Features
            document.getElementById('wms-result').className = 'test-result success';
            document.getElementById('wms-result').innerHTML = '✅ Core WMS features always available (database-independent)';
            
            // Alert System
            if (health.features.alerts) {
                const mode = health.database === 'connected' ? 'Full Database Mode' : 'In-Memory Fallback Mode';
                document.getElementById('alert-result').className = 'test-result success';
                document.getElementById('alert-result').innerHTML = `✅ Alert system available (${mode})`;
            } else {
                document.getElementById('alert-result').className = 'test-result error';
                document.getElementById('alert-result').innerHTML = '❌ Alert system unavailable';
            }
            
            // Analytics
            if (health.features.analytics) {
                const mode = health.database === 'connected' ? 'Real Data' : 'Demo Data Mode';
                document.getElementById('analytics-result').className = 'test-result success';
                document.getElementById('analytics-result').innerHTML = `✅ Analytics available (${mode})`;
            } else {
                document.getElementById('analytics-result').className = 'test-result error';
                document.getElementById('analytics-result').innerHTML = '❌ Analytics unavailable';
            }
            
            // ROI Analysis
            if (health.features.spatial) {
                document.getElementById('roi-result').className = 'test-result success';
                document.getElementById('roi-result').innerHTML = '✅ ROI analysis available (PostGIS precision)';
            } else {
                document.getElementById('roi-result').className = 'test-result warning';
                document.getElementById('roi-result').innerHTML = '⚠️ ROI analysis available (Client-side approximations)';
            }
        }

        async function testAnalytics() {
            log('📊 Testing analytics fallback...', 'info');
            
            try {
                const response = await fetch('/api/reports/analytics?period=7d');
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ Analytics endpoint responded: ${JSON.stringify(result, null, 2)}`, 'success');
                    
                    if (result.success) {
                        log('✅ Analytics data available', 'success');
                    } else if (result.fallback) {
                        log('⚠️ Analytics using fallback/demo data', 'warning');
                    }
                } else {
                    log('⚠️ Analytics endpoint unavailable, frontend should use demo data', 'warning');
                }
            } catch (error) {
                log(`⚠️ Analytics API error (expected in offline mode): ${error.message}`, 'warning');
                log('✅ Frontend should automatically show demo analytics', 'success');
            }
        }

        async function testROI() {
            log('🗺️ Testing ROI service fallback...', 'info');
            
            try {
                const response = await fetch('/api/roi');
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ ROI endpoint responded: ${JSON.stringify(result, null, 2)}`, 'success');
                } else if (response.status === 503) {
                    const result = await response.json();
                    log(`⚠️ ROI service in fallback mode: ${result.message}`, 'warning');
                    log('✅ Client-side ROI calculations should be available', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`⚠️ ROI API error: ${error.message}`, 'warning');
                log('✅ Frontend should use client-side calculations', 'success');
            }
        }

        async function runAllTests() {
            log('🚀 Starting comprehensive database decoupling tests...', 'info');
            clearResults();
            
            await testBackendHealth();
            await new Promise(resolve => setTimeout(resolve, 1000)); // Small delay
            await testAnalytics();
            await new Promise(resolve => setTimeout(resolve, 1000)); // Small delay
            await testROI();
            
            log('🎉 All tests completed! Check individual results above.', 'success');
            log('💡 If database is disconnected, all fallback modes should be working', 'info');
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            log('🔧 Database Decoupling Test Page Loaded', 'info');
            log('📋 This page tests if the application works without database access', 'info');
            log('🚀 Click "Run All Tests" to begin validation...', 'info');
        });
    </script>
</body>
</html>
