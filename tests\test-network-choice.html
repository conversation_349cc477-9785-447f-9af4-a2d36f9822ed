<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Choice Modal Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { background-color: #007bff; color: white; border: none; padding: 8px 16px; margin: 5px; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log { background-color: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>🔧 Network Choice Modal Test</h1>
    <p>This page tests the new network choice functionality.</p>

    <div class="test-section">
        <h2>📋 Test Status</h2>
        <div id="status-display"></div>
    </div>

    <div class="test-section">
        <h2>🧪 Test Actions</h2>
        <button onclick="testUIEngineConnection()">Test UI Engine Connection</button>
        <button onclick="testMainApp()">Open Main Application</button>
        <button onclick="testNetworkChoice()">Test Network Choice Logic</button>
    </div>

    <div class="test-section">
        <h2>🔍 Expected Behavior</h2>
        <div class="log">
When UI Engine is DOWN (current state):
1. Application should detect connection failure
2. Network Choice Modal should appear
3. User can choose:
   - "Use Demo Data" (recommended)
   - "Retry Connection" 
   - "Continue with Live Data" (advanced)

When UI Engine is UP:
1. Application should detect healthy connection
2. No modal should appear
3. Live data mode should be used automatically
        </div>
    </div>

    <div id="results"></div>

    <script>
        let testResults = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testResults.push({ message: logEntry, type });
            updateResults();
            console.log(logEntry);
        }

        function updateResults() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = testResults.map(result => 
                `<div class="log ${result.type}">${result.message}</div>`
            ).join('');
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status-display');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }        async function testUIEngineConnection() {
            log('🔌 Testing UI Engine Connection...', 'info');
            
            try {
                const response = await fetch('http://localhost:3001/health', {
                    method: 'GET',
                    timeout: 3000
                });
                
                if (response.ok) {
                    const data = await response.json();                    log('✅ UI Engine is ONLINE', 'success');
                    log('📊 UI Engine response: ' + JSON.stringify(data, null, 2), 'info');
                    updateStatus('✅ UI Engine Online - Live data mode should be used', 'success');
                } else {                    log('⚠️ UI Engine responded with error: ' + response.status, 'warning');
                    updateStatus('⚠️ UI Engine Error - Network choice modal should appear', 'warning');
                }
            } catch (error) {                log('❌ UI Engine is OFFLINE: ' + error.message, 'error');
                updateStatus('❌ UI Engine Offline - Network choice modal should appear', 'error');
            }
        }

        function testMainApp() {
            log('🌐 Opening main application...', 'info');
            window.open('http://localhost:5173', '_blank');
        }

        function testNetworkChoice() {
            log('🧪 Testing Network Choice Logic...', 'info');
            
            // Simulate the network choice process
            log('1. Application starts and checks network health', 'info');
            log('2. UI Engine connection test fails (current state)', 'info');
            log('3. Network status becomes "awaiting-user-choice"', 'info');
            log('4. NetworkChoiceModal should appear with options:', 'info');
            log('   - Use Demo Data (green button)', 'info');
            log('   - Retry Connection (blue button)', 'info');
            log('   - Continue with Live Data (gray button)', 'info');
            log('5. User selection updates network health service', 'info');
            log('6. Application proceeds with chosen mode', 'info');
            
            log('✅ Network choice logic test completed', 'success');
        }

        // Auto-run initial tests
        window.addEventListener('load', () => {
            log('🌟 Network Choice Modal Test Page Loaded', 'success');
            setTimeout(testUIEngineConnection, 1000);
        });
    </script>
</body>
</html>
