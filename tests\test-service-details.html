<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Details Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        img { max-width: 200px; max-height: 150px; margin: 5px; border: 1px solid #ddd; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <h1>WMS Service Details Test</h1>
    
    <div id="metadata-test" class="test-section loading">
        <h3>1. Service Metadata Test</h3>
        <p>Testing /api/ows/metadata endpoint...</p>
        <div id="metadata-result"></div>
    </div>
    
    <div id="capabilities-test" class="test-section loading">
        <h3>2. Capabilities Test</h3>
        <p>Testing /api/ows/capabilities endpoint...</p>
        <div id="capabilities-result"></div>
    </div>
    
    <div id="thumbnail-test" class="test-section loading">
        <h3>3. Map Thumbnail Test</h3>
        <p>Testing WMS proxy for thumbnail generation...</p>
        <div id="thumbnail-result"></div>
    </div>
    
    <div id="legend-test" class="test-section loading">
        <h3>4. Legend Test</h3>
        <p>Testing legend endpoint...</p>
        <div id="legend-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/ows';

        // Test 1: Service Metadata
        async function testMetadata() {
            try {
                const response = await fetch(`${API_BASE}/metadata`);
                const data = await response.json();
                
                document.getElementById('metadata-test').className = 'test-section success';
                document.getElementById('metadata-result').innerHTML = `
                    <h4>✅ Service Metadata Retrieved Successfully</h4>
                    <p><strong>Title:</strong> ${data.title}</p>
                    <p><strong>Abstract:</strong> ${data.abstract}</p>
                    <p><strong>Organization:</strong> ${data.contact?.organization}</p>
                    <p><strong>Contact:</strong> ${data.contact?.person} (${data.contact?.email})</p>
                    <details>
                        <summary>View Raw Response</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;
            } catch (error) {
                document.getElementById('metadata-test').className = 'test-section error';
                document.getElementById('metadata-result').innerHTML = `
                    <h4>❌ Service Metadata Failed</h4>
                    <p>Error: ${error.message}</p>
                `;
            }
        }

        // Test 2: Capabilities
        async function testCapabilities() {
            try {
                const response = await fetch(`${API_BASE}/capabilities`);
                const data = await response.json();
                
                document.getElementById('capabilities-test').className = 'test-section success';
                document.getElementById('capabilities-result').innerHTML = `
                    <h4>✅ Capabilities Retrieved Successfully</h4>
                    <p><strong>Total Layers:</strong> ${data.length}</p>
                    <p><strong>Sample Layers:</strong></p>
                    <ul>
                        ${data.slice(0, 5).map(layer => `
                            <li><strong>${layer.name}</strong> - ${layer.title}</li>
                        `).join('')}
                    </ul>
                    <details>
                        <summary>View All Layers (${data.length})</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;
            } catch (error) {
                document.getElementById('capabilities-test').className = 'test-section error';
                document.getElementById('capabilities-result').innerHTML = `
                    <h4>❌ Capabilities Failed</h4>
                    <p>Error: ${error.message}</p>
                `;
            }
        }

        // Test 3: Map Thumbnails
        async function testThumbnails() {
            try {
                const layers = ['geonode:africa_mosaic_optmised', 'geonode:flood_risk_layer_1m'];
                let thumbnailHtml = '<h4>✅ Map Thumbnails Generated Successfully</h4>';
                
                for (const layer of layers) {
                    const thumbnailUrl = `${API_BASE}/wms-proxy?service=WMS&version=1.1.1&request=GetMap&layers=${layer}&width=200&height=150&format=image/jpeg&styles=&transparent=false&bbox=16.3,-34.8,32.9,-22.1&srs=EPSG:4326`;
                    thumbnailHtml += `
                        <div style="margin: 10px 0;">
                            <p><strong>Layer:</strong> ${layer}</p>
                            <img src="${thumbnailUrl}" alt="Thumbnail for ${layer}" onerror="this.style.border='2px solid red'; this.alt='Failed to load'">
                        </div>
                    `;
                }
                
                document.getElementById('thumbnail-test').className = 'test-section success';
                document.getElementById('thumbnail-result').innerHTML = thumbnailHtml;
            } catch (error) {
                document.getElementById('thumbnail-test').className = 'test-section error';
                document.getElementById('thumbnail-result').innerHTML = `
                    <h4>❌ Map Thumbnails Failed</h4>
                    <p>Error: ${error.message}</p>
                `;
            }
        }

        // Test 4: Legend
        async function testLegend() {
            try {
                const layers = ['geonode:africa_mosaic_optmised', 'geonode:flood_risk_layer_1m'];
                let legendHtml = '<h4>✅ Legend Images Generated Successfully</h4>';
                
                for (const layer of layers) {
                    const legendUrl = `${API_BASE}/legend?layer=${layer}&format=image/png`;
                    legendHtml += `
                        <div style="margin: 10px 0;">
                            <p><strong>Layer:</strong> ${layer}</p>
                            <img src="${legendUrl}" alt="Legend for ${layer}" onerror="this.style.border='2px solid red'; this.alt='Failed to load'">
                        </div>
                    `;
                }
                
                document.getElementById('legend-test').className = 'test-section success';
                document.getElementById('legend-result').innerHTML = legendHtml;
            } catch (error) {
                document.getElementById('legend-test').className = 'test-section error';
                document.getElementById('legend-result').innerHTML = `
                    <h4>❌ Legend Generation Failed</h4>
                    <p>Error: ${error.message}</p>
                `;
            }
        }

        // Run all tests
        async function runAllTests() {
            await testMetadata();
            await testCapabilities();
            await testThumbnails();
            await testLegend();
        }

        // Start tests when page loads
        window.addEventListener('load', runAllTests);
    </script>
</body>
</html>
