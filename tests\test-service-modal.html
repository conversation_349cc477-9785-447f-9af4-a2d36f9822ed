<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Details Modal Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .button { 
            background: #007bff; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            cursor: pointer; 
            margin: 10px 0;
        }
        .result { 
            background: #f8f9fa; 
            padding: 15px; 
            margin: 10px 0; 
            border-left: 4px solid #007bff; 
        }
        .error { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <h1>Service Details Modal Test</h1>
    <p>This page tests the backend API endpoints that the Service Details modal uses.</p>
    
    <button class="button" onclick="testCapabilities()">Test Capabilities</button>
    <button class="button" onclick="testMetadata()">Test Metadata</button>
    <button class="button" onclick="testLegend()">Test Legend</button>
    <button class="button" onclick="testWMSProxy()">Test WMS Proxy</button>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:3001/api/ows';
        
        function addResult(title, content, isError = false) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isError ? 'error' : ''}`;
            resultDiv.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testCapabilities() {
            try {
                console.log('Testing capabilities endpoint...');
                const response = await fetch(`${API_BASE}/capabilities`);
                const data = await response.json();
                addResult('Capabilities Test', `Status: ${response.status}\nLayers found: ${data.length}\nFirst layer: ${JSON.stringify(data[0], null, 2)}`);
            } catch (error) {
                addResult('Capabilities Test - Error', error.message, true);
            }
        }
        
        async function testMetadata() {
            try {
                console.log('Testing metadata endpoint...');
                const response = await fetch(`${API_BASE}/metadata`);
                const data = await response.json();
                addResult('Metadata Test', `Status: ${response.status}\nMetadata: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                addResult('Metadata Test - Error', error.message, true);
            }
        }
        
        async function testLegend() {
            try {
                console.log('Testing legend endpoint...');
                const response = await fetch(`${API_BASE}/legend?layer=geonode:flood_risk_layer_1m&format=image/png`);
                if (response.ok) {
                    const blob = await response.blob();
                    addResult('Legend Test', `Status: ${response.status}\nContent-Type: ${response.headers.get('content-type')}\nSize: ${blob.size} bytes`);
                } else {
                    addResult('Legend Test - Error', `Status: ${response.status}\nResponse: ${await response.text()}`, true);
                }
            } catch (error) {
                addResult('Legend Test - Error', error.message, true);
            }
        }
        
        async function testWMSProxy() {
            try {
                console.log('Testing WMS proxy endpoint...');
                const params = new URLSearchParams({
                    service: 'WMS',
                    version: '1.1.1',
                    request: 'GetMap',
                    layers: 'geonode:flood_risk_layer_1m',
                    width: '200',
                    height: '150',
                    format: 'image/jpeg',
                    styles: '',
                    transparent: 'false',
                    bbox: '16.3,-34.8,32.9,-22.1',
                    srs: 'EPSG:4326'
                });
                
                const response = await fetch(`${API_BASE}/wms-proxy?${params.toString()}`);
                if (response.ok) {
                    const blob = await response.blob();
                    addResult('WMS Proxy Test', `Status: ${response.status}\nContent-Type: ${response.headers.get('content-type')}\nSize: ${blob.size} bytes`);
                } else {
                    addResult('WMS Proxy Test - Error', `Status: ${response.status}\nResponse: ${await response.text()}`, true);
                }
            } catch (error) {
                addResult('WMS Proxy Test - Error', error.message, true);
            }
        }
    </script>
</body>
</html>
