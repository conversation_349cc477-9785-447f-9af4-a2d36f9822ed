<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>White Screen Fix Validation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 20px;
        }
        .test-controls {
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 White Screen Fix Validation Test</h1>
        <p>This test validates that the React application loads correctly even with network issues.</p>
        
        <div class="test-controls">
            <button onclick="runBasicLoadTest()">Test Basic Load</button>
            <button onclick="runNetworkFailureTest()">Simulate Network Failure</button>
            <button onclick="runSlowNetworkTest()">Simulate Slow Network</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div id="test-results"></div>
        
        <h3>Application Preview:</h3>
        <iframe id="app-frame" src="http://localhost:5174/"></iframe>
        
        <div class="log-output" id="console-log">
            <strong>Console Output:</strong><br>
            Monitoring application startup...
        </div>
    </div>

    <script>
        let testResults = [];
        
        function addTestResult(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push({ type, message, timestamp });
            updateTestResults();
            logToConsole(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
        }
        
        function updateTestResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => 
                `<div class="test-result ${result.type}">
                    <strong>[${result.timestamp}]</strong> ${result.message}
                </div>`
            ).join('');
        }
        
        function logToConsole(message) {
            const consoleLog = document.getElementById('console-log');
            consoleLog.innerHTML += '<br>' + message;
            consoleLog.scrollTop = consoleLog.scrollHeight;
        }
        
        function clearResults() {
            testResults = [];
            updateTestResults();
            document.getElementById('console-log').innerHTML = '<strong>Console Output:</strong><br>Console cleared...';
        }
        
        function runBasicLoadTest() {
            addTestResult('info', 'Starting basic load test...');
            
            const iframe = document.getElementById('app-frame');
            const testFrame = document.createElement('iframe');
            testFrame.style.display = 'none';
            testFrame.src = 'http://localhost:5174/';
            
            testFrame.onload = function() {
                setTimeout(() => {
                    try {
                        const frameDoc = testFrame.contentDocument || testFrame.contentWindow.document;
                        const body = frameDoc.body;
                        
                        if (body && body.innerHTML.trim()) {
                            if (body.innerHTML.includes('white') || body.innerHTML.includes('blank')) {
                                addTestResult('warning', 'Application loaded but may have white screen content');
                            } else {
                                addTestResult('success', 'Application loaded successfully with content');
                            }
                        } else {
                            addTestResult('error', 'Application loaded but body is empty');
                        }
                    } catch (e) {
                        addTestResult('warning', 'Cannot access iframe content due to CORS, but frame loaded');
                    }
                    document.body.removeChild(testFrame);
                }, 2000);
            };
            
            testFrame.onerror = function() {
                addTestResult('error', 'Failed to load application');
                document.body.removeChild(testFrame);
            };
            
            document.body.appendChild(testFrame);
        }
        
        function runNetworkFailureTest() {
            addTestResult('info', 'Simulating network failure scenario...');
            addTestResult('warning', 'Note: This test simulates conditions similar to network issues');
            
            // Test if app continues to work when resources fail
            fetch('http://localhost:5174/non-existent-resource')
                .then(() => {
                    addTestResult('warning', 'Unexpected: Non-existent resource returned success');
                })
                .catch(() => {
                    addTestResult('success', 'Network failure simulation: App should still be functional');
                    // Check if main app is still accessible
                    fetch('http://localhost:5174/')
                        .then(response => {
                            if (response.ok) {
                                addTestResult('success', 'Main application remains accessible during network issues');
                            } else {
                                addTestResult('error', 'Main application not accessible');
                            }
                        })
                        .catch(() => {
                            addTestResult('error', 'Cannot access main application');
                        });
                });
        }
        
        function runSlowNetworkTest() {
            addTestResult('info', 'Testing application responsiveness...');
            
            const startTime = performance.now();
            fetch('http://localhost:5174/')
                .then(response => {
                    const loadTime = performance.now() - startTime;
                    if (loadTime < 1000) {
                        addTestResult('success', `Application loads quickly: ${Math.round(loadTime)}ms`);
                    } else if (loadTime < 3000) {
                        addTestResult('warning', `Application load time acceptable: ${Math.round(loadTime)}ms`);
                    } else {
                        addTestResult('error', `Application load time too slow: ${Math.round(loadTime)}ms`);
                    }
                })
                .catch(() => {
                    addTestResult('error', 'Failed to test application load time');
                });
        }
        
        // Auto-run basic test on load
        setTimeout(() => {
            addTestResult('info', 'Page loaded, starting automatic validation...');
            runBasicLoadTest();
        }, 1000);
        
        // Monitor iframe for changes
        const iframe = document.getElementById('app-frame');
        iframe.onload = function() {
            addTestResult('success', 'Application iframe loaded successfully');
        };
        
        iframe.onerror = function() {
            addTestResult('error', 'Application iframe failed to load');
        };
    </script>
</body>
</html>
