# Core Application Environment Variables
NODE_ENV=development
PORT=3001

# OWS/GeoServer Configuration (Always available)
GEOSERVER_URL=https://*************/geoserver
CORS_ORIGIN=*

# PostGIS Configuration (Optional - set to 'false' to disable)
ENABLE_POSTGIS=true
DB_HOST=database
DB_PORT=5432
POSTGRES_USER=sansa_user
POSTGRES_PASSWORD=secure_password_123
POSTGRES_DB=sansa_flood_db

# Optional Features
JWT_SECRET=your_jwt_secret_here
LOG_LEVEL=info

# Phase 3: Real-time Alerting System Environment Variables

# JWT Configuration
JWT_EXPIRY=24h

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=SANSA Flood Alert <<EMAIL>>

# WebSocket Configuration
WEBSOCKET_PORT=3001

# Alert Engine Configuration
ALERT_ENGINE_INTERVAL_MINUTES=1
ALERT_ENGINE_MAX_CONCURRENT=5
ALERT_ENGINE_RETRY_ATTEMPTS=3
ALERT_ENGINE_RETRY_DELAY=5000

# GeoServer Configuration for Alert Data Fetching
GEOSERVER_BASE_URL=http://*************:8080/geoserver
GEOSERVER_USERNAME=admin
GEOSERVER_PASSWORD=geoserver

# Frontend URL for Alert Links
FRONTEND_URL=http://localhost:3000

# SMS Configuration (Optional - Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token  
TWILIO_PHONE_NUMBER=+**********

# Notification Configuration
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_WEBSOCKET_NOTIFICATIONS=true

# Performance Configuration
MAX_ALERT_EVENTS_PER_PAGE=50
ALERT_HISTORY_RETENTION_DAYS=90

# Redis Configuration (for caching and session management - optional)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Database Alert Tables Configuration
ENABLE_ALERT_SYSTEM=true
AUTO_CREATE_ALERT_TABLES=true
