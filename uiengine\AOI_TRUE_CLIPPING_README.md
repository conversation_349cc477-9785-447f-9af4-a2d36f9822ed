# AOI-True Clipping System

## Overview

The AOI-True Clipping System provides exact multipolygon clipping for both map previews and downloads, ensuring perfect consistency between what users see and what they download. All clipping logic is centralized in the UIEngine backend, with the frontend acting as a thin client.

## Key Features

- **Exact Multipolygon Clipping**: Supports complex geometries including holes and multipolygons
- **Preview-Download Consistency**: Identical clipping strategies for tiles and downloads
- **Tiered Clipping Strategies**: Automatic fallback from advanced to basic clipping methods
- **Centralized Logic**: All clipping handled server-side for consistency
- **Performance Optimized**: Caching, metrics, and performance monitoring
- **Mutual Exclusivity**: Only one AOI active at a time with proper lifecycle management

## Architecture

### Backend Services (UIEngine)

1. **Capabilities Cache Service** (`src/services/capabilitiesCache.ts`)
   - Probes GeoServer on startup
   - Caches layer metadata (type, supported features, geometry fields)
   - Auto-detects clipping capabilities per layer

2. **AOI Service** (`src/services/aoiService.ts`)
   - Centralized AOI lifecycle management
   - Geometry validation and canonicalization
   - WKT simplification for large geometries
   - Mutual exclusivity enforcement

3. **Tile Service** (`src/services/tileService.ts`)
   - Vector tiles: CQL_FILTER with INTERSECTS or BBOX fallback
   - Raster tiles: SLD clipping → WPS crop → server-side masking
   - Automatic strategy selection based on capabilities

### API Endpoints

#### AOI Management
- `POST /api/aoi` - Create AOI with geometry validation
- `GET /api/aoi/:aoiId` - Retrieve AOI data
- `DELETE /api/aoi/:aoiId` - Delete AOI and cleanup

#### Tile Serving
- `GET /api/tiles/vector/{z}/{x}/{y}.png` - Vector tiles with AOI clipping
- `GET /api/tiles/raster/{z}/{x}/{y}.png` - Raster tiles with AOI clipping

#### Downloads
- `POST /api/downloads/aoi-true` - Multi-layer download with exact clipping

#### Debug & Monitoring
- `GET /api/debug/health` - System health check
- `GET /api/debug/metrics` - Performance metrics
- `GET /api/debug/capabilities` - Capabilities cache status

### Frontend Integration

1. **AOI-True Clipping Service** (`frontend/src/services/aoiTrueClippingService.ts`)
   - Thin client interface to backend
   - AOI creation and lifecycle management
   - Tile URL generation

2. **Layer Capabilities Service** (`frontend/src/services/layerCapabilitiesService.ts`)
   - Fetches layer metadata from backend
   - Determines layer types for proper tile URL generation

3. **AOI-True WMS Tile Layer** (`frontend/src/components/Map/AOITrueWMSTileLayer.tsx`)
   - Drop-in replacement for standard WMSTileLayer
   - Automatically uses server-side clipping

4. **React Hook** (`frontend/src/hooks/useAOITrueClipping.ts`)
   - React state management for AOI operations
   - Centralized AOI state across components

## Clipping Strategies

### Vector Layers
1. **CQL_FILTER with INTERSECTS** (Primary)
   - Exact geometry intersection
   - Supports complex multipolygons with holes
   - Requires geometry field detection

2. **BBOX Filtering** (Fallback)
   - Bounding box clipping
   - Always supported
   - Less precise but reliable

### Raster Layers
1. **SLD Clipping** (Tier A)
   - Uses `ras:CropCoverage` render transform
   - Exact geometry clipping at WMS level
   - Best performance and quality

2. **WPS Cropping** (Tier B)
   - Uses `gs:CropCoverage` WPS process
   - Creates cached cropped coverage
   - Good for repeated access

3. **Server-side Masking** (Tier C)
   - Canvas-based alpha masking
   - Fallback when other methods fail
   - Ensures something always works

## Configuration

### Environment Variables

```bash
# GeoServer Configuration
GEOSERVER_URL=https://your-geoserver.com/geoserver
GEOSERVER_USERNAME=admin
GEOSERVER_PASSWORD=password

# AOI Configuration
AOI_MAX_VERTICES=10000
AOI_MAX_AREA=1000000
WKT_LENGTH_THRESHOLD=6000
AOI_SIMPLIFICATION_TOLERANCE=0.001
AOI_CACHE_TIMEOUT=60

# Tile Configuration
TILE_SIZE=256
MAX_ZOOM=19
MIN_ZOOM=2
TILE_CACHE_TIMEOUT=300
MASKING_TIMEOUT=30

# Capabilities Configuration
CAPABILITIES_CACHE_TIMEOUT=60
ALLOWED_WORKSPACES=geonode,public
```

### Service Initialization

The system initializes automatically on server startup:

```typescript
// In server.ts
await initializeCapabilitiesCache(capabilitiesConfig);
initializeAOIService(aoiConfig);
initializeTileService(tileConfig);
```

## Usage Examples

### Frontend Integration

```typescript
import { useAOITrueClipping } from '../hooks/useAOITrueClipping';
import AOITrueWMSTileLayer from '../components/Map/AOITrueWMSTileLayer';

function MapComponent() {
  const { aoiState, createAOI, deleteAOI } = useAOITrueClipping();

  const handleDrawnPolygon = async (geometry) => {
    await createAOI({
      mode: 'drawn',
      geometry,
      metadata: { name: 'User Drawn Area' }
    });
  };

  return (
    <MapContainer>
      {layers.map(layer => (
        <AOITrueWMSTileLayer
          key={layer.name}
          layerName={layer.name}
          aoiId={aoiState.aoiId}
          time={selectedDate}
        />
      ))}
    </MapContainer>
  );
}
```

### Direct API Usage

```bash
# Create AOI
curl -X POST http://localhost:3001/api/aoi \
  -H "Content-Type: application/json" \
  -d '{
    "mode": "drawn",
    "geometry": {
      "type": "Polygon",
      "coordinates": [[[18,-34],[19,-34],[19,-33],[18,-33],[18,-34]]]
    }
  }'

# Get vector tile with clipping
curl "http://localhost:3001/api/tiles/vector/10/512/512.png?layer=geonode:test_layer&aoiId=your-aoi-id"

# Download with clipping
curl -X POST http://localhost:3001/api/downloads/aoi-true \
  -H "Content-Type: application/json" \
  -d '{
    "layers": ["geonode:layer1", "geonode:layer2"],
    "aoiId": "your-aoi-id",
    "format": "shapefile"
  }' \
  --output download.zip
```

## Performance Considerations

### Caching Strategy
- **Capabilities**: Cached for 60 minutes (configurable)
- **Tiles**: Cached for 5 minutes with ETag support
- **AOI Data**: In-memory cache with configurable timeout

### WKT Optimization
- Large geometries automatically simplified
- Configurable vertex and length thresholds
- Fallback to BBOX when WKT too complex

### Monitoring
- Real-time performance metrics
- Processing time tracking
- Cache hit rate monitoring
- Error rate tracking

## Testing

### Running Tests

```bash
# Install dependencies
npm install

# Run all tests
npm test

# Run specific test suites
npm test -- --testNamePattern="AOI-True Clipping"
npm test -- --testNamePattern="Integration"

# Run with coverage
npm test -- --coverage
```

### Test Coverage

- **Unit Tests**: Individual service methods
- **Integration Tests**: End-to-end workflows
- **Performance Tests**: Response time validation
- **Edge Cases**: Complex geometries, error handling

## Troubleshooting

### Common Issues

1. **Tiles not clipping properly**
   - Check capabilities cache: `GET /api/debug/capabilities`
   - Verify layer type detection
   - Check AOI geometry validity

2. **Performance issues**
   - Monitor metrics: `GET /api/debug/metrics`
   - Check WKT length and simplification
   - Verify cache hit rates

3. **Download failures**
   - Check layer capabilities
   - Verify AOI exists
   - Monitor processing time

### Debug Endpoints

- `/api/debug/health` - Overall system health
- `/api/debug/metrics` - Performance metrics
- `/api/debug/capabilities` - Layer capabilities
- `/api/debug/aoi` - AOI service status

## Migration from Legacy System

1. **Replace WMSTileLayer components** with AOITrueWMSTileLayer
2. **Update AOI state management** to use useAOITrueClipping hook
3. **Migrate download endpoints** to use /api/downloads/aoi-true
4. **Update layer discovery** to use capabilities service

## Future Enhancements

- **WPS Cropping Implementation**: Complete Tier B raster clipping
- **Canvas Masking**: Implement Tier C with proper alpha masking
- **Streaming Downloads**: Large dataset streaming support
- **Advanced Caching**: Redis-based distributed caching
- **Real-time Updates**: WebSocket-based cache invalidation
