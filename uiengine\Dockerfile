FROM node:18-alpine

# Set environment variables to avoid permission issues
ENV HOME=/tmp
ENV npm_config_cache=/tmp/.npm
ENV npm_config_prefix=/tmp/.npm-global
ENV npm_config_update_notifier=false

WORKDIR /app

# Create necessary directories and set permissions
RUN mkdir -p /tmp/.npm /tmp/.npm-global /app/cache /app/logs && \
    chmod -R 777 /tmp/.npm /tmp/.npm-global && \
    chown -R node:node /app

# Copy package files first for better caching
COPY --chown=node:node package*.json ./

# Switch to node user for npm operations
USER node

# Install dependencies with faster settings
RUN npm config set registry https://registry.npmjs.org/ && \
    npm config set fetch-timeout 300000 && \
    npm config set fetch-retry-mintimeout 20000 && \
    npm config set fetch-retry-maxtimeout 120000 && \
    npm ci --omit=dev --verbose

# Copy source code
COPY --chown=node:node . .

# Install dev dependencies and build
RUN npm ci && npm run build

# Clean up dev dependencies
RUN npm prune --production

EXPOSE 3001

CMD ["npm", "start"]
