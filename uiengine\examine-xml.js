/**
 * Examine the actual XML structure from GeoServer
 */
const { secureGet } = require('./src/utils/secureRequest');
const { parseStringPromise } = require('xml2js');
const dotenv = require('dotenv');

dotenv.config();

async function examineXmlStructure() {
  try {
    console.log('Examining GeoServer WMS XML structure...');
    
    const geoserverUrl = process.env.GEOSERVER_URL || 'https://10.150.16.184/geoserver';
    const wmsUrl = `${geoserverUrl}/wms?service=WMS&version=1.3.0&request=GetCapabilities`;
    
    const response = await secureGet(wmsUrl);
    const parsed = await parseStringPromise(response.data);
    
    console.log('Full structure keys:', Object.keys(parsed));
    
    if (parsed['WMS_Capabilities']) {
      console.log('WMS_Capabilities keys:', Object.keys(parsed['WMS_Capabilities']));
      
      if (parsed['WMS_Capabilities']['Capability']) {
        console.log('Capability structure:', Object.keys(parsed['WMS_Capabilities']['Capability'][0]));
        
        const capability = parsed['WMS_Capabilities']['Capability'][0];
        if (capability['Layer']) {
          console.log('Layer structure:', Object.keys(capability['Layer'][0]));
          
          const rootLayer = capability['Layer'][0];
          console.log('Root layer keys:', Object.keys(rootLayer));
          
          if (rootLayer['Layer']) {
            console.log(`Found ${rootLayer['Layer'].length} sub-layers`);
            rootLayer['Layer'].forEach((layer, index) => {
              console.log(`Layer ${index}:`, {
                name: layer.Name?.[0],
                title: layer.Title?.[0]
              });
            });
          } else {
            console.log('No sub-layers found in Layer structure');
          }
        }
      }
    }
    
  } catch (error) {
    console.error('Error examining XML structure:', error.message);
  }
}

examineXmlStructure();
