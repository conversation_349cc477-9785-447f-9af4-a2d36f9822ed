<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SANSA UIEngine Comprehensive Test Suite</title>
  <style>
    :root {
      --primary-color: #2196F3;
      --success-color: #4CAF50;
      --warning-color: #FF9800;
      --error-color: #f44336;
      --background-color: #f5f5f5;
      --card-background: #ffffff;
      --text-color: #333;
      --border-color: #ddd;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: var(--background-color);
      color: var(--text-color);
      line-height: 1.6;
    }

    .header {
      text-align: center;
      margin-bottom: 30px;
      padding: 20px;
      background: linear-gradient(135deg, var(--primary-color), #1976D2);
      color: white;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .header h1 {
      margin: 0;
      font-size: 2.5em;
      font-weight: 300;
    }

    .header p {
      margin: 10px 0 0 0;
      opacity: 0.9;
      font-size: 1.1em;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .test-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .test-case {
      background: var(--card-background);
      border: 1px solid var(--border-color);
      border-radius: 10px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: transform 0.2s, box-shadow 0.2s;
    }

    .test-case:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .test-case h3 {
      margin: 0 0 10px 0;
      color: var(--primary-color);
      font-size: 1.3em;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .test-case .icon {
      font-size: 1.5em;
    }

    .test-case p {
      margin: 0 0 15px 0;
      color: #666;
    }

    .button-group {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      margin-bottom: 15px;
    }

    button {
      background: linear-gradient(135deg, var(--success-color), #45a049);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s;
      min-width: 120px;
    }

    button:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    button:active {
      transform: translateY(0);
    }

    button.secondary {
      background: linear-gradient(135deg, var(--primary-color), #1976D2);
    }

    button.warning {
      background: linear-gradient(135deg, var(--warning-color), #f57c00);
    }

    button.danger {
      background: linear-gradient(135deg, var(--error-color), #d32f2f);
    }

    button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }

    .result {
      margin-top: 15px;
    }

    .log {
      background: #1e1e1e;
      color: #f8f8f2;
      padding: 15px;
      border-radius: 6px;
      max-height: 300px;
      overflow-y: auto;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.4;
      white-space: pre-wrap;
      border: 1px solid #333;
    }

    .log:empty::before {
      content: "No output yet...";
      color: #666;
      font-style: italic;
    }

    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .status-success { background-color: var(--success-color); }
    .status-error { background-color: var(--error-color); }
    .status-warning { background-color: var(--warning-color); }
    .status-info { background-color: var(--primary-color); }

    .config-panel {
      background: var(--card-background);
      border: 1px solid var(--border-color);
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 30px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .config-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
    }

    .config-item {
      display: flex;
      flex-direction: column;
    }

    .config-item label {
      font-weight: 500;
      margin-bottom: 5px;
      color: var(--primary-color);
    }

    .config-item input, .config-item select {
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .config-item input:focus, .config-item select:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
    }

    .tabs {
      display: flex;
      background: var(--card-background);
      border-radius: 10px 10px 0 0;
      border: 1px solid var(--border-color);
      border-bottom: none;
      overflow: hidden;
    }

    .tab {
      padding: 15px 25px;
      cursor: pointer;
      background: #f8f9fa;
      border-right: 1px solid var(--border-color);
      transition: all 0.2s;
      font-weight: 500;
    }

    .tab:last-child {
      border-right: none;
    }

    .tab.active {
      background: var(--card-background);
      color: var(--primary-color);
      border-bottom: 2px solid var(--primary-color);
    }

    .tab:hover:not(.active) {
      background: #e9ecef;
    }

    .tab-content {
      background: var(--card-background);
      border: 1px solid var(--border-color);
      border-radius: 0 0 10px 10px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .hidden {
      display: none;
    }

    .progress-bar {
      width: 100%;
      height: 4px;
      background: #e0e0e0;
      border-radius: 2px;
      overflow: hidden;
      margin: 10px 0;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--primary-color), var(--success-color));
      width: 0%;
      transition: width 0.3s ease;
    }

    .summary-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }

    .stat-card {
      background: var(--card-background);
      padding: 15px;
      border-radius: 8px;
      text-align: center;
      border: 1px solid var(--border-color);
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .stat-value {
      font-size: 2em;
      font-weight: bold;
      color: var(--primary-color);
    }

    .stat-label {
      color: #666;
      font-size: 0.9em;
      margin-top: 5px;
    }

    @media (max-width: 768px) {
      .test-grid {
        grid-template-columns: 1fr;
      }

      .config-grid {
        grid-template-columns: 1fr;
      }

      .tabs {
        flex-direction: column;
      }

      .tab {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🌍 SANSA UIEngine Test Suite</h1>
      <p>Comprehensive testing and debugging tool for the SANSA Flood Monitoring System</p>
    </div>

    <!-- Configuration Panel -->
    <div class="config-panel">
      <h3>🔧 Configuration</h3>
      <div class="config-grid">
        <div class="config-item">
          <label for="geoserver-url">GeoServer URL:</label>
          <input type="text" id="geoserver-url" value="https://*************/geoserver">
        </div>
        <div class="config-item">
          <label for="uiengine-url">UIEngine URL:</label>
          <input type="text" id="uiengine-url" value="http://localhost:3001">
        </div>
        <div class="config-item">
          <label for="test-layer">Test Layer:</label>
          <select id="test-layer">
            <option value="geonode:africa_mosaic_optmised">Africa Mosaic</option>
            <option value="geonode:saps_stations">SAPS Stations</option>
            <option value="geonode:south_africa_provincial_boundaries">Provincial Boundaries</option>
            <option value="geonode:sa_wards2020">SA Wards 2020</option>
          </select>
        </div>
        <div class="config-item">
          <label for="output-format">Output Format:</label>
          <select id="output-format">
            <option value="geojson">GeoJSON</option>
            <option value="kml">KML</option>
            <option value="shapefile">Shapefile</option>
            <option value="csv">CSV</option>
            <option value="tiff">GeoTIFF</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Summary Statistics -->
    <div class="summary-stats">
      <div class="stat-card">
        <div class="stat-value" id="tests-passed">0</div>
        <div class="stat-label">Tests Passed</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="tests-failed">0</div>
        <div class="stat-label">Tests Failed</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="total-tests">0</div>
        <div class="stat-label">Total Tests</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="success-rate">0%</div>
        <div class="stat-label">Success Rate</div>
      </div>
    </div>

    <!-- Test Tabs -->
    <div class="tabs">
      <div class="tab active" data-tab="connectivity">🌐 Connectivity</div>
      <div class="tab" data-tab="data-services">📊 Data Services</div>
      <div class="tab" data-tab="downloads">📥 Downloads</div>
      <div class="tab" data-tab="advanced">⚙️ Advanced</div>
    </div>

    <!-- Connectivity Tests -->
    <div class="tab-content" id="connectivity-content">
      <div class="test-grid">
        <div class="test-case">
          <h3><span class="icon">🔗</span>System Health Check</h3>
          <p>Checks the overall health and status of all UIEngine components.</p>
          <div class="button-group">
            <button id="health-check">Check Health</button>
            <button id="health-detailed" class="secondary">Detailed Check</button>
          </div>
          <div class="result">
            <div id="health-check-log" class="log"></div>
          </div>
        </div>

        <div class="test-case">
          <h3><span class="icon">🌐</span>Direct GeoServer Connection</h3>
          <p>Tests direct connection to GeoServer WMS GetCapabilities endpoint.</p>
          <div class="button-group">
            <button id="direct-capabilities">Test Capabilities</button>
            <button id="direct-layers" class="secondary">List Layers</button>
          </div>
          <div class="result">
            <div id="direct-capabilities-log" class="log"></div>
          </div>
        </div>

        <div class="test-case">
          <h3><span class="icon">🔄</span>UIEngine Proxy Connection</h3>
          <p>Tests connection through the UIEngine proxy to verify routing.</p>
          <div class="button-group">
            <button id="proxy-capabilities">Test Proxy</button>
            <button id="proxy-layers" class="secondary">Proxy Layers</button>
          </div>
          <div class="result">
            <div id="proxy-capabilities-log" class="log"></div>
          </div>
        </div>

        <div class="test-case">
          <h3><span class="icon">🗺️</span>Map Tile Rendering</h3>
          <p>Tests WMS GetMap requests for tile rendering capabilities.</p>
          <div class="button-group">
            <button id="direct-map">Direct Map</button>
            <button id="proxy-map" class="secondary">Proxy Map</button>
          </div>
          <div class="result">
            <div id="map-test-log" class="log"></div>
            <img id="map-test-image" style="max-width: 100%; display: none; margin-top: 10px; border-radius: 4px;" />
          </div>
        </div>
      </div>
    </div>

    <!-- Data Services Tests -->
    <div class="tab-content hidden" id="data-services-content">
      <div class="test-grid">
        <div class="test-case">
          <h3><span class="icon">📋</span>Dataset Discovery</h3>
          <p>Tests dataset discovery and metadata retrieval from GeoNode.</p>
          <div class="button-group">
            <button id="dataset-discovery">Discover Datasets</button>
            <button id="dataset-categories" class="secondary">Get Categories</button>
            <button id="dataset-recent" class="secondary">Recent Datasets</button>
          </div>
          <div class="result">
            <div id="dataset-discovery-log" class="log"></div>
          </div>
        </div>

        <div class="test-case">
          <h3><span class="icon">🔍</span>Layer Capabilities</h3>
          <p>Tests layer-specific capabilities and metadata retrieval.</p>
          <div class="button-group">
            <button id="layer-capabilities">Test Layer Info</button>
            <button id="layer-styles" class="secondary">Get Styles</button>
            <button id="layer-temporal" class="secondary">Check Temporal</button>
          </div>
          <div class="result">
            <div id="layer-capabilities-log" class="log"></div>
          </div>
        </div>

        <div class="test-case">
          <h3><span class="icon">📍</span>Feature Info Queries</h3>
          <p>Tests GetFeatureInfo requests for queryable layers.</p>
          <div class="button-group">
            <button id="feature-info">Query Features</button>
            <button id="feature-info-json" class="secondary">JSON Format</button>
            <button id="feature-info-html" class="secondary">HTML Format</button>
          </div>
          <div class="result">
            <div id="feature-info-log" class="log"></div>
          </div>
        </div>

        <div class="test-case">
          <h3><span class="icon">🎯</span>Spatial Filtering</h3>
          <p>Tests CQL filters and spatial intersection queries.</p>
          <div class="button-group">
            <button id="spatial-filter">Test CQL Filter</button>
            <button id="bbox-filter" class="secondary">BBOX Filter</button>
            <button id="geometry-filter" class="secondary">Geometry Filter</button>
          </div>
          <div class="result">
            <div id="spatial-filter-log" class="log"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Downloads Tests -->
    <div class="tab-content hidden" id="downloads-content">
      <div class="test-grid">
        <div class="test-case">
          <h3><span class="icon">📥</span>Single Layer Download</h3>
          <p>Tests downloading individual layers in various formats.</p>
          <div class="button-group">
            <button id="single-download">Download Layer</button>
            <button id="single-geojson" class="secondary">GeoJSON</button>
            <button id="single-shapefile" class="secondary">Shapefile</button>
          </div>
          <div class="result">
            <div id="single-download-log" class="log"></div>
          </div>
        </div>

        <div class="test-case">
          <h3><span class="icon">📦</span>Multi-Layer Download</h3>
          <p>Tests the new multi-layer download with AOI clipping.</p>
          <div class="button-group">
            <button id="multi-download">Multi-Layer Download</button>
            <button id="multi-aoi" class="secondary">With AOI</button>
            <button id="multi-temporal" class="secondary">With Dates</button>
          </div>
          <div class="result">
            <div id="multi-download-log" class="log"></div>
          </div>
        </div>

        <div class="test-case">
          <h3><span class="icon">✂️</span>AOI Clipping</h3>
          <p>Tests Area of Interest clipping with various geometries.</p>
          <div class="button-group">
            <button id="aoi-polygon">Polygon AOI</button>
            <button id="aoi-bbox" class="secondary">BBOX AOI</button>
            <button id="aoi-complex" class="secondary">Complex AOI</button>
          </div>
          <div class="result">
            <div id="aoi-clipping-log" class="log"></div>
          </div>
        </div>

        <div class="test-case">
          <h3><span class="icon">📊</span>Download Validation</h3>
          <p>Validates downloaded files for correctness and completeness.</p>
          <div class="button-group">
            <button id="validate-download">Validate Files</button>
            <button id="check-sizes" class="secondary">Check Sizes</button>
            <button id="verify-content" class="secondary">Verify Content</button>
          </div>
          <div class="result">
            <div id="download-validation-log" class="log"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Advanced Tests -->
    <div class="tab-content hidden" id="advanced-content">
      <div class="test-grid">
        <div class="test-case">
          <h3><span class="icon">🚨</span>Alert System</h3>
          <p>Tests alert rules, events, and notification system.</p>
          <div class="button-group">
            <button id="alert-rules">Test Alert Rules</button>
            <button id="alert-events" class="secondary">Alert Events</button>
            <button id="alert-stats" class="secondary">Alert Stats</button>
          </div>
          <div class="result">
            <div id="alert-system-log" class="log"></div>
          </div>
        </div>

        <div class="test-case">
          <h3><span class="icon">📈</span>Performance Testing</h3>
          <p>Tests system performance under various load conditions.</p>
          <div class="button-group">
            <button id="perf-test">Performance Test</button>
            <button id="load-test" class="secondary">Load Test</button>
            <button id="stress-test" class="warning">Stress Test</button>
          </div>
          <div class="result">
            <div id="performance-log" class="log"></div>
          </div>
        </div>

        <div class="test-case">
          <h3><span class="icon">🔧</span>Debug Tools</h3>
          <p>Advanced debugging and diagnostic tools.</p>
          <div class="button-group">
            <button id="debug-info">Debug Info</button>
            <button id="clear-cache" class="warning">Clear Cache</button>
            <button id="reset-all" class="danger">Reset All</button>
          </div>
          <div class="result">
            <div id="debug-tools-log" class="log"></div>
          </div>
        </div>

        <div class="test-case">
          <h3><span class="icon">📋</span>API Documentation</h3>
          <p>Access API documentation and test endpoints directly.</p>
          <div class="button-group">
            <button id="open-swagger">Open Swagger UI</button>
            <button id="api-health" class="secondary">API Health</button>
            <button id="test-endpoints" class="secondary">Test All Endpoints</button>
          </div>
          <div class="result">
            <div id="api-docs-log" class="log"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Global Controls -->
    <div style="margin-top: 30px; text-align: center;">
      <div class="button-group" style="justify-content: center;">
        <button id="run-all-tests" class="secondary" style="min-width: 200px;">🚀 Run All Tests</button>
        <button id="clear-all-logs" class="warning">🗑️ Clear All Logs</button>
        <button id="export-results" class="secondary">📄 Export Results</button>
      </div>
      <div class="progress-bar" style="margin-top: 20px;">
        <div class="progress-fill" id="overall-progress"></div>
      </div>
    </div>
  </div>

  <script>
    // Configuration
    let config = {
      geoserverUrl: 'https://*************/geoserver',
      uiEngineUrl: 'http://localhost:3001',
      testLayer: 'geonode:africa_mosaic_optmised',
      outputFormat: 'geojson'
    };

    // Test statistics
    let stats = {
      passed: 0,
      failed: 0,
      total: 0
    };

    // Utility functions
    function log(id, message, type = 'info') {
      const logElement = document.getElementById(`${id}-log`);
      const time = new Date().toLocaleTimeString();
      const statusIcon = getStatusIcon(type);
      logElement.innerHTML += `[${time}] ${statusIcon} ${message}\n`;
      logElement.scrollTop = logElement.scrollHeight;
    }

    function getStatusIcon(type) {
      const icons = {
        'info': '📝',
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'loading': '⏳'
      };
      return icons[type] || '📝';
    }

    function clearLog(id) {
      const logElement = document.getElementById(`${id}-log`);
      logElement.innerHTML = '';
    }

    function updateStats() {
      document.getElementById('tests-passed').textContent = stats.passed;
      document.getElementById('tests-failed').textContent = stats.failed;
      document.getElementById('total-tests').textContent = stats.total;
      const successRate = stats.total > 0 ? Math.round((stats.passed / stats.total) * 100) : 0;
      document.getElementById('success-rate').textContent = `${successRate}%`;
    }

    function updateProgress(percentage) {
      document.getElementById('overall-progress').style.width = `${percentage}%`;
    }

    async function runTest(testName, testFunction) {
      stats.total++;
      updateStats();

      try {
        log(testName, `Starting ${testName} test...`, 'loading');
        await testFunction();
        stats.passed++;
        log(testName, `✅ ${testName} test completed successfully`, 'success');
      } catch (error) {
        stats.failed++;
        log(testName, `❌ ${testName} test failed: ${error.message}`, 'error');
        console.error(`${testName} test error:`, error);
      }

      updateStats();
    }

    // Configuration handlers
    function updateConfig() {
      config.geoserverUrl = document.getElementById('geoserver-url').value;
      config.uiEngineUrl = document.getElementById('uiengine-url').value;
      config.testLayer = document.getElementById('test-layer').value;
      config.outputFormat = document.getElementById('output-format').value;
    }

    // Tab switching
    function initTabs() {
      const tabs = document.querySelectorAll('.tab');
      const contents = document.querySelectorAll('.tab-content');

      tabs.forEach(tab => {
        tab.addEventListener('click', () => {
          // Remove active class from all tabs and contents
          tabs.forEach(t => t.classList.remove('active'));
          contents.forEach(c => c.classList.add('hidden'));

          // Add active class to clicked tab and corresponding content
          tab.classList.add('active');
          const targetContent = document.getElementById(`${tab.dataset.tab}-content`);
          if (targetContent) {
            targetContent.classList.remove('hidden');
          }
        });
      });
    }

    // Test Functions
    async function testHealthCheck() {
      updateConfig();
      const url = `${config.uiEngineUrl}/health`;
      log('health-check', `Requesting: ${url}`);

      const response = await fetch(url);
      log('health-check', `Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        throw new Error(`HTTP error: ${response.status}`);
      }

      const data = await response.json();
      log('health-check', `Service: ${data.service}`, 'success');
      log('health-check', `Database: ${data.database}`, data.database === 'connected' ? 'success' : 'warning');
      log('health-check', `PostGIS: ${data.postgis}`, data.postgis === 'available' ? 'success' : 'warning');
      log('health-check', `GeoServer: ${data.geoserver}`, 'info');
      log('health-check', `Features: ${JSON.stringify(data.features, null, 2)}`, 'info');
    }

    async function testMultiLayerDownload() {
      updateConfig();
      const layers = 'geonode:saps_stations,geonode:south_africa_provincial_boundaries';
      const geometry = JSON.stringify({
        type: 'Polygon',
        coordinates: [[[25, -30], [30, -30], [30, -25], [25, -25], [25, -30]]]
      });

      const params = new URLSearchParams({
        layers: layers,
        format: config.outputFormat,
        geometry: geometry,
        aoiName: 'Test_AOI',
        includeMetadata: 'true'
      });

      const url = `${config.uiEngineUrl}/api/ows/multi-layer-download?${params}`;
      log('multi-download', `Requesting multi-layer download...`, 'loading');
      log('multi-download', `Layers: ${layers}`, 'info');
      log('multi-download', `Format: ${config.outputFormat}`, 'info');

      const response = await fetch(url);
      log('multi-download', `Response status: ${response.status} ${response.statusText}`,
          response.ok ? 'success' : 'error');

      if (!response.ok) {
        const errorText = await response.text();
        log('multi-download', `Error response: ${errorText}`, 'error');
        throw new Error(`HTTP error: ${response.status}`);
      }

      const contentType = response.headers.get('content-type');
      const contentLength = response.headers.get('content-length');
      log('multi-download', `Content-Type: ${contentType}`, 'info');
      log('multi-download', `Content-Length: ${contentLength} bytes`, 'info');

      if (contentType.includes('application/zip')) {
        const blob = await response.blob();
        const downloadUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `test_multi_layer_download.zip`;
        link.textContent = '📥 Download Test File';
        link.style.display = 'block';
        link.style.marginTop = '10px';
        link.style.color = '#2196F3';
        link.style.textDecoration = 'none';
        link.style.padding = '8px 12px';
        link.style.border = '1px solid #2196F3';
        link.style.borderRadius = '4px';
        link.style.backgroundColor = '#f0f8ff';

        const logElement = document.getElementById('multi-download-log');
        logElement.appendChild(link);

        log('multi-download', `Download ready: ${blob.size} bytes`, 'success');
      } else {
        throw new Error(`Expected ZIP file but received: ${contentType}`);
      }
    }

    // Event Listeners
    function initEventListeners() {
      // Health check
      document.getElementById('health-check').addEventListener('click', () =>
        runTest('health-check', testHealthCheck));

      // Multi-layer download
      document.getElementById('multi-download').addEventListener('click', () =>
        runTest('multi-download', testMultiLayerDownload));

      // Swagger UI
      document.getElementById('open-swagger').addEventListener('click', () => {
        const swaggerUrl = `${config.uiEngineUrl}/api-docs`;
        window.open(swaggerUrl, '_blank');
        log('api-docs', `Opened Swagger UI: ${swaggerUrl}`, 'success');
      });

      // Clear all logs
      document.getElementById('clear-all-logs').addEventListener('click', () => {
        const logs = document.querySelectorAll('.log');
        logs.forEach(log => log.innerHTML = '');
        stats.passed = 0;
        stats.failed = 0;
        stats.total = 0;
        updateStats();
        updateProgress(0);
      });

      // Export results
      document.getElementById('export-results').addEventListener('click', () => {
        const results = {
          timestamp: new Date().toISOString(),
          config: config,
          stats: stats,
          logs: {}
        };

        const logs = document.querySelectorAll('.log');
        logs.forEach(log => {
          const id = log.id.replace('-log', '');
          results.logs[id] = log.textContent;
        });

        const blob = new Blob([JSON.stringify(results, null, 2)],
          { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `sansa_test_results_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
      });

      // Configuration updates
      document.getElementById('geoserver-url').addEventListener('change', updateConfig);
      document.getElementById('uiengine-url').addEventListener('change', updateConfig);
      document.getElementById('test-layer').addEventListener('change', updateConfig);
      document.getElementById('output-format').addEventListener('change', updateConfig);
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      initTabs();
      initEventListeners();
      updateConfig();
      updateStats();

      // Auto-run health check on load
      setTimeout(() => {
        runTest('health-check', testHealthCheck);
      }, 1000);
    });
  </script>
</body>
</html>
