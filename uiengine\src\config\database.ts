// Database configuration with optional PostGIS support
// This file is only loaded when PostGIS is available

let knex: any;
let db: any;

try {
  knex = require('knex');
} catch (error) {
  console.error('Knex not available - PostGIS features will be disabled');
  throw error;
}

import dotenv from 'dotenv';

dotenv.config();

const dbConfig = {
  client: 'pg',
  connection: {
    host: process.env.DB_HOST || 'database',
    port: parseInt(process.env.DB_PORT || '5432'),
    user: process.env.POSTGRES_USER || 'sansa_user',
    password: process.env.POSTGRES_PASSWORD || 'secure_password_123',
    database: process.env.POSTGRES_DB || 'sansa_flood_db',
  },
  pool: {
    min: 2,
    max: 10,
    acquireTimeoutMillis: 60000,
    idleTimeoutMillis: 600000,
  },
  acquireConnectionTimeout: 60000,
  debug: process.env.NODE_ENV === 'development',
};

// Create and export the database connection
db = knex(dbConfig);

// Test database connection
export const testConnection = async (): Promise<boolean> => {
  try {
    await db.raw('SELECT 1');
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
};

// Check PostGIS installation
export const checkPostGIS = async (): Promise<boolean> => {
  try {
    const result = await db.raw('SELECT PostGIS_Version()');
    console.log('✅ PostGIS available:', result.rows[0].postgis_version);
    return true;
  } catch (error) {
    console.error('❌ PostGIS not available:', error);
    return false;
  }
};

// Graceful shutdown
export const closeConnection = async (): Promise<void> => {
  try {
    await db.destroy();
    console.log('🔌 Database connection closed');
  } catch (error) {
    console.error('❌ Error closing database connection:', error);
  }
};

export { db };
export default db;
