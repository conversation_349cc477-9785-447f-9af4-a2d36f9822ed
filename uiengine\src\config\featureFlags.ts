/**
 * Feature Flags Configuration
 * 
 * Provides fail-safe feature flags for SLD/WPS rollback and monitoring.
 * Flags default to ON (true) for production use, with environment overrides.
 */

export interface FeatureFlags {
  ENABLE_SLD_CLIPPING: boolean;
  ENABLE_WPS_CLIPPING: boolean;
  ENABLE_CLIPPING_MONITORING: boolean;
  ENABLE_DEBUG_ENDPOINTS: boolean;
}

/**
 * Parse environment variable as boolean with default fallback
 */
function parseEnvBoolean(envVar: string | undefined, defaultValue: boolean): boolean {
  if (envVar === undefined) return defaultValue;
  return envVar.toLowerCase() === 'true' || envVar === '1';
}

/**
 * Get current feature flags from environment variables
 * Defaults to enabled (fail-safe) unless explicitly disabled
 */
export function getFeatureFlags(): FeatureFlags {
  return {
    // SLD clipping for raster layers (default: enabled)
    ENABLE_SLD_CLIPPING: parseEnvBoolean(process.env.ENABLE_SLD_CLIPPING, true),
    
    // WPS cropping for raster layers (default: enabled)
    ENABLE_WPS_CLIPPING: parseEnvBoolean(process.env.ENABLE_WPS_CLIPPING, true),
    
    // Clipping method monitoring and statistics (default: enabled)
    ENABLE_CLIPPING_MONITORING: parseEnvBoolean(process.env.ENABLE_CLIPPING_MONITORING, true),
    
    // Debug endpoints for monitoring (default: enabled in dev, disabled in prod)
    ENABLE_DEBUG_ENDPOINTS: parseEnvBoolean(
      process.env.ENABLE_DEBUG_ENDPOINTS, 
      process.env.NODE_ENV !== 'production'
    )
  };
}

/**
 * Global feature flags instance
 */
let cachedFlags: FeatureFlags | null = null;

/**
 * Get cached feature flags (refreshed on server restart)
 */
export function getFlags(): FeatureFlags {
  if (!cachedFlags) {
    cachedFlags = getFeatureFlags();
    console.log('🚩 Feature flags loaded:', cachedFlags);
  }
  return cachedFlags;
}

/**
 * Refresh feature flags from environment (for runtime updates)
 */
export function refreshFlags(): FeatureFlags {
  cachedFlags = getFeatureFlags();
  console.log('🔄 Feature flags refreshed:', cachedFlags);
  return cachedFlags;
}

/**
 * Check if SLD clipping is enabled
 */
export function isSLDEnabled(): boolean {
  return getFlags().ENABLE_SLD_CLIPPING;
}

/**
 * Check if WPS clipping is enabled
 */
export function isWPSEnabled(): boolean {
  return getFlags().ENABLE_WPS_CLIPPING;
}

/**
 * Check if clipping monitoring is enabled
 */
export function isMonitoringEnabled(): boolean {
  return getFlags().ENABLE_CLIPPING_MONITORING;
}

/**
 * Check if debug endpoints are enabled
 */
export function areDebugEndpointsEnabled(): boolean {
  return getFlags().ENABLE_DEBUG_ENDPOINTS;
}
