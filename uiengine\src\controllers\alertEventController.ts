import { Request, Response, NextFunction } from 'express';
import { AlertEventService } from '../services/alertEventService';
import { AlertEventsQuery, AcknowledgeAlertRequest } from '../types/alertRule';

export class AlertEventController {
  private alertEventService: AlertEventService;

  constructor() {
    this.alertEventService = new AlertEventService();
  }

  // GET /api/alert-events
  public getAlertEvents = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user?.id; // From auth middleware
      const {
        alertRuleId,
        acknowledged,
        startDate,
        endDate,
        page = 1,
        limit = 20
      } = req.query;

      const queryParams: AlertEventsQuery = {
        userId,
        alertRuleId: alertRuleId ? parseInt(alertRuleId as string) : undefined,
        acknowledged: acknowledged ? acknowledged === 'true' : undefined,
        startDate: startDate as string,
        endDate: endDate as string,
        page: parseInt(page as string),
        limit: parseInt(limit as string)
      };

      const result = await this.alertEventService.getAlertEvents(queryParams);

      res.json({
        success: true,
        data: result.events,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / result.limit)
        }
      });
    } catch (error) {
      console.error('Error fetching alert events:', error);
      next(error);
    }
  };

  // GET /api/alert-events/:id
  public getAlertEvent = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      const alertEvent = await this.alertEventService.getAlertEventById(parseInt(id), userId);

      if (!alertEvent) {
        res.status(404).json({
          success: false,
          message: 'Alert event not found'
        });
        return;
      }

      res.json({
        success: true,
        data: alertEvent
      });
    } catch (error) {
      console.error('Error fetching alert event:', error);
      next(error);
    }
  };

  // PATCH /api/alert-events/:id/acknowledge
  public acknowledgeAlert = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      const acknowledgeRequest: AcknowledgeAlertRequest = {
        userId
      };

      const alertEvent = await this.alertEventService.acknowledgeAlert(
        parseInt(id),
        acknowledgeRequest
      );

      if (!alertEvent) {
        res.status(404).json({
          success: false,
          message: 'Alert event not found or already acknowledged'
        });
        return;
      }

      res.json({
        success: true,
        data: alertEvent,
        message: 'Alert acknowledged successfully'
      });
    } catch (error) {
      console.error('Error acknowledging alert:', error);
      next(error);
    }
  };

  // GET /api/alert-events/stats
  public getAlertStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user?.id;
      const { startDate, endDate } = req.query;

      const stats = await this.alertEventService.getAlertStats({
        userId,
        startDate: startDate as string,
        endDate: endDate as string
      });

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Error fetching alert stats:', error);
      next(error);
    }
  };

  // GET /api/alert-events/unacknowledged/count
  public getUnacknowledgedCount = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user?.id;

      const count = await this.alertEventService.getUnacknowledgedCount(userId);

      res.json({
        success: true,
        data: { count }
      });
    } catch (error) {
      console.error('Error fetching unacknowledged count:', error);
      next(error);
    }
  };

  // POST /api/alert-events/bulk-acknowledge
  public bulkAcknowledgeAlerts = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user?.id;
      const { alertEventIds } = req.body;

      if (!Array.isArray(alertEventIds) || alertEventIds.length === 0) {
        res.status(400).json({
          success: false,
          message: 'alertEventIds must be a non-empty array'
        });
        return;
      }

      const acknowledgedCount = await this.alertEventService.bulkAcknowledgeAlerts(
        alertEventIds.map(id => parseInt(id)),
        userId
      );

      res.json({
        success: true,
        data: { acknowledgedCount },
        message: `${acknowledgedCount} alerts acknowledged successfully`
      });
    } catch (error) {
      console.error('Error bulk acknowledging alerts:', error);
      next(error);
    }
  };
}
