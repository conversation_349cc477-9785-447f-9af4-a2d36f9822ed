import { Request, Response, NextFunction } from 'express';
import { AlertRuleService } from '../services/alertRuleService';
import { CreateAlertRuleRequest, UpdateAlertRuleRequest, AlertEventsQuery } from '../types/alertRule';
// import { logger } from '../utils/logger'; // TODO: Enable for production

// Extend Request type to include user (from future auth middleware)
interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    username: string;
    role: string;
  };
}

export class AlertRuleController {
  private alertRuleService: AlertRuleService;

  constructor() {
    this.alertRuleService = new AlertRuleService();
  }
  // GET /api/alert-rules
  public getAlertRules = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id; // From auth middleware (future implementation)
      const { page = 1, limit = 20, isActive } = req.query;

      const result = await this.alertRuleService.getAlertRules({
        userId,
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        isActive: isActive ? isActive === 'true' : undefined
      });

      res.json({
        success: true,
        data: result.rules,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / result.limit)
        }
      });
    } catch (error) {
      console.error('Error fetching alert rules:', error);
      next(error);
    }
  };
  // GET /api/alert-rules/:id
  public getAlertRule = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const alertRule = await this.alertRuleService.getAlertRuleById(parseInt(id), userId);

      if (!alertRule) {
        res.status(404).json({
          success: false,
          message: 'Alert rule not found'
        });
        return;
      }

      res.json({
        success: true,
        data: alertRule
      });
    } catch (error) {
      console.error('Error fetching alert rule:', error);
      next(error);
    }
  };

  // POST /api/alert-rules
  public createAlertRule = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id || 1; // Default to user 1 for development
      const createRequest: CreateAlertRuleRequest = req.body;

      // Validate required fields
      if (!createRequest.name || !createRequest.datasetId || 
          createRequest.thresholdValue === undefined || !createRequest.thresholdOperator) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields: name, datasetId, thresholdValue, thresholdOperator'
        });
        return;
      }

      // Validate threshold operator
      const validOperators = ['>', '<', '>=', '<=', '=', '!='];
      if (!validOperators.includes(createRequest.thresholdOperator)) {
        res.status(400).json({
          success: false,
          message: 'Invalid threshold operator. Must be one of: >, <, >=, <=, =, !='
        });
        return;
      }      const alertRule = await this.alertRuleService.createAlertRule({
        ...createRequest,
        userId
      });

      console.log(`Alert rule created: ${alertRule.id} by user ${userId}`);

      res.status(201).json({
        success: true,
        data: alertRule,
        message: 'Alert rule created successfully'
      });
    } catch (error) {
      console.error('Error creating alert rule:', error);
      next(error);
    }
  };

  // PUT /api/alert-rules/:id
  public updateAlertRule = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      const updateRequest: UpdateAlertRuleRequest = req.body;

      // Validate threshold operator if provided
      if (updateRequest.thresholdOperator) {
        const validOperators = ['>', '<', '>=', '<=', '=', '!='];
        if (!validOperators.includes(updateRequest.thresholdOperator)) {
          res.status(400).json({
            success: false,
            message: 'Invalid threshold operator. Must be one of: >, <, >=, <=, =, !='
          });
          return;
        }
      }

      const alertRule = await this.alertRuleService.updateAlertRule(
        parseInt(id),
        updateRequest,
        userId
      );

      if (!alertRule) {
        res.status(404).json({
          success: false,
          message: 'Alert rule not found or access denied'
        });
        return;
      }      console.log(`Alert rule updated: ${id} by user ${userId}`);

      res.json({
        success: true,
        data: alertRule,
        message: 'Alert rule updated successfully'
      });
    } catch (error) {
      console.error('Error updating alert rule:', error);
      next(error);
    }
  };

  // DELETE /api/alert-rules/:id
  public deleteAlertRule = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const deleted = await this.alertRuleService.deleteAlertRule(parseInt(id), userId);

      if (!deleted) {
        res.status(404).json({
          success: false,
          message: 'Alert rule not found or access denied'
        });
        return;
      }      console.log(`Alert rule deleted: ${id} by user ${userId}`);

      res.json({
        success: true,
        message: 'Alert rule deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting alert rule:', error);
      next(error);
    }
  };

  // POST /api/alert-rules/:id/toggle
  public toggleAlertRule = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const alertRule = await this.alertRuleService.toggleAlertRule(parseInt(id), userId);

      if (!alertRule) {
        res.status(404).json({
          success: false,
          message: 'Alert rule not found or access denied'
        });
        return;
      }      console.log(`Alert rule toggled: ${id} (active: ${alertRule.isActive}) by user ${userId}`);

      res.json({
        success: true,
        data: alertRule,
        message: `Alert rule ${alertRule.isActive ? 'activated' : 'deactivated'} successfully`
      });
    } catch (error) {
      console.error('Error toggling alert rule:', error);
      next(error);
    }
  };

  // GET /api/alert-rules/:id/test
  public testAlertRule = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const testResult = await this.alertRuleService.testAlertRule(parseInt(id), userId);

      if (!testResult) {
        res.status(404).json({
          success: false,
          message: 'Alert rule not found or access denied'
        });
        return;
      }

      res.json({
        success: true,
        data: testResult,
        message: 'Alert rule test completed'
      });    } catch (error) {
      console.error('Error testing alert rule:', error);
      next(error);
    }
  };
}
