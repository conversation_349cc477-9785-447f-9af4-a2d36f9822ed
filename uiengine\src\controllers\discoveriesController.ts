import { Request, Response } from 'express';
import { discoveriesService } from '../services/discoveriesService';

// GET /discoveries/datasets
export const getDatasets = async (_req: Request, res: Response) => {
  try {
    const datasets = await discoveriesService.getDatasets();
    res.json(datasets);
  } catch (err) {
    res.status(500).json({ error: "Failed to fetch datasets" });
  }
};

// GET /discoveries/datasets/:id
export const getDatasetById = async (req: Request, res: Response) => {
  try {
    const dataset = await discoveriesService.getDatasetById(req.params.id);
    res.json(dataset);
  } catch (err) {
    res.status(500).json({ error: "Failed to fetch dataset" });
  }
};

// GET /discoveries/categories
export const getCategories = async (_req: Request, res: Response) => {
  try {
    const categories = await discoveriesService.getCategories();
    res.json(categories);
  } catch (err) {
    res.status(500).json({ error: "Failed to fetch categories" });
  }
};