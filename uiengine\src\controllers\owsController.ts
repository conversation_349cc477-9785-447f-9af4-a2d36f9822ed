import { Request, Response } from 'express';
import { getFullDiscovery } from '../services/geoServerService';
import { getCapabilitiesCache } from '../services/capabilitiesCache';
import axios from 'axios';
import { parseStringPromise } from 'xml2js';
import https from 'https';

const GEOSERVER_BASE = process.env.GEOSERVER_URL;
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

export const getCapabilities = async (req: Request, res: Response) => {
  try {
    console.log('Getting layer capabilities...');

    // Try to use the enhanced capabilities cache first
    const capabilitiesCache = getCapabilitiesCache();
    if (capabilitiesCache) {
      console.log('📋 Checking enhanced capabilities cache...');

      // Check if cache needs refresh
      if (capabilitiesCache.needsRefresh()) {
        console.log('🔄 Cache needs refresh, refreshing capabilities...');
        await capabilitiesCache.refreshCapabilities();
      }

      const capabilities = capabilitiesCache.getAllCapabilities();
      console.log(`📋 Enhanced cache contains ${capabilities.length} layers`);

      if (capabilities.length > 0) {
        // Convert capabilities format to match frontend expectations
        const data = capabilities.map(cap => ({
          name: cap.name,
          title: cap.title,
          type: cap.type,
          bbox: cap.bbox,
          temporal: cap.temporal,
          supports: cap.supports,
          geometryField: cap.geometryField,
          queryable: cap.supports.cql,
          lastUpdated: cap.lastUpdated
        }));

        console.log(`✅ Retrieved ${data.length} layers from enhanced cache`);
        console.log(`📋 Sample layers:`, data.slice(0, 3).map(l => `${l.name} (${l.type})`));
        res.status(200).json(data);
        return;
      } else {
        console.warn('⚠️ Enhanced capabilities cache is empty, falling back to getFullDiscovery');
      }
    } else {
      console.warn('⚠️ Enhanced capabilities cache not available');
    }

    // Fallback to old discovery method if cache not available
    console.log('⚠️ Enhanced capabilities cache not available, falling back to getFullDiscovery');
    const data = await getFullDiscovery();
    console.log(`Successfully retrieved ${data.length} layers from fallback`);
    res.status(200).json(data);
  } catch (error) {
    console.error('Discovery error:', error);

    // Provide more detailed error information
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    res.status(500).json({
      error: 'Failed to retrieve layer capabilities',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export const getLayerMetadata = async (req: Request, res: Response) => {
  try {
    const { layerName } = req.params;

    if (!layerName) {
      return res.status(400).json({ error: 'Layer name is required' });
    }

    console.log(`🔍 Fetching metadata for layer: ${layerName}`);

    // Get DescribeLayer information
    const describeLayerUrl = `${GEOSERVER_BASE}/ows?SERVICE=WMS&REQUEST=DescribeLayer&LAYERS=${encodeURIComponent(layerName)}&VERSION=1.1.1`;

    let describeLayerData = null;
    try {
      const describeResponse = await axios.get(describeLayerUrl, { httpsAgent });
      const parsedDescribe = await parseStringPromise(describeResponse.data);

      const layerDescription = parsedDescribe['WMS_DescribeLayerResponse']?.['LayerDescription']?.[0];
      if (layerDescription) {
        describeLayerData = {
          name: layerDescription.$.name,
          wfs: layerDescription.$.wfs,
          owsURL: layerDescription.$.owsURL,
          owsType: layerDescription.$.owsType,
          typeName: layerDescription.Query?.[0]?.$.typeName
        };
      }
    } catch (error) {
      console.warn(`Could not retrieve DescribeLayer for ${layerName}:`, error);
    }

    // Use the same discovery source as the frontend (GeoNode discovery) for consistency
    // This ensures the layer metadata endpoint uses the same layer source as the map component
    let layerMetadata = null;

    try {
      // First try to get from GeoNode discovery (same as frontend)
      const geoNodeResponse = await axios.get(`${process.env.BASE_URL || 'http://localhost:3001'}/api/datasets/geonode/discovery`);
      if (geoNodeResponse.data.success && geoNodeResponse.data.layers) {
        layerMetadata = geoNodeResponse.data.layers.find((layer: any) => layer.name === layerName);
        console.log(`🔍 Found layer in GeoNode discovery: ${!!layerMetadata}`);
      }
    } catch (error) {
      console.warn(`Could not retrieve from GeoNode discovery for ${layerName}:`, error);
    }

    // Fallback to direct GeoServer discovery if not found in GeoNode
    if (!layerMetadata) {
      console.log(`🔍 Falling back to GeoServer discovery for ${layerName}`);
      const fullDiscovery = await getFullDiscovery();
      layerMetadata = fullDiscovery.find((layer: any) => layer.name === layerName);
      console.log(`🔍 Found layer in GeoServer discovery: ${!!layerMetadata}`);
    }

    if (!layerMetadata) {
      console.error(`❌ Layer not found in any discovery source: ${layerName}`);
      return res.status(404).json({ error: 'Layer not found' });
    }

    // Enhance metadata with additional information
    const enhancedMetadata = {
      ...layerMetadata,
      describeLayer: describeLayerData,
      // Add computed fields for frontend
      capabilities: {
        hasTemporalData: !!layerMetadata.temporal,
        isQueryable: layerMetadata.queryable || false,
        supportsWMS: layerMetadata.supports?.WMS !== false,
        supportsWFS: layerMetadata.supports?.WFS === true,
        supportsWMTS: layerMetadata.supports?.WMTS === true
      },
      // Format temporal information for easier frontend consumption
      temporalInfo: layerMetadata.temporal ? {
        hasData: true,
        extent: layerMetadata.temporal.extent,
        defaultTime: layerMetadata.temporal.default,
        units: layerMetadata.temporal.units,
        values: layerMetadata.temporal.values || []
      } : null,
      // Add bounding box information
      boundingBox: layerMetadata.bbox ? {
        srs: layerMetadata.bbox.SRS,
        minx: parseFloat(layerMetadata.bbox.minx?.toString() || '0') || 0,
        miny: parseFloat(layerMetadata.bbox.miny?.toString() || '0') || 0,
        maxx: parseFloat(layerMetadata.bbox.maxx?.toString() || '0') || 0,
        maxy: parseFloat(layerMetadata.bbox.maxy?.toString() || '0') || 0
      } : null
    };

    res.status(200).json(enhancedMetadata);
  } catch (error) {
    console.error('Layer metadata error:', error);
    res.status(500).json({ error: 'Failed to retrieve layer metadata' });
  }
};

// New endpoint for layer styles
export const getLayerStyles = async (req: Request, res: Response) => {
  try {
    const { layerName } = req.params;

    if (!layerName) {
      return res.status(400).json({ error: 'Layer name is required' });
    }

    console.log(`🎨 Fetching styles for layer: ${layerName}`);

    // Use the same discovery source as the frontend (GeoNode discovery) for consistency
    let layerMetadata = null;

    try {
      // First try to get from GeoNode discovery (same as frontend)
      const geoNodeResponse = await axios.get(`${process.env.BASE_URL || 'http://localhost:3001'}/api/datasets/geonode/discovery`);
      if (geoNodeResponse.data.success && geoNodeResponse.data.layers) {
        layerMetadata = geoNodeResponse.data.layers.find((layer: any) => layer.name === layerName);
        console.log(`🎨 Found layer in GeoNode discovery: ${!!layerMetadata}`);
      }
    } catch (error) {
      console.warn(`Could not retrieve from GeoNode discovery for ${layerName}:`, error);
    }

    // Fallback to direct GeoServer discovery if not found in GeoNode
    if (!layerMetadata) {
      console.log(`🎨 Falling back to GeoServer discovery for ${layerName}`);
      const fullDiscovery = await getFullDiscovery();
      layerMetadata = fullDiscovery.find((layer: any) => layer.name === layerName);
      console.log(`🎨 Found layer in GeoServer discovery: ${!!layerMetadata}`);
    }

    if (!layerMetadata) {
      console.error(`❌ Layer not found in any discovery source: ${layerName}`);
      return res.status(404).json({ error: 'Layer not found' });
    }

    // Extract styles information
    const styles = layerMetadata.styles || [];
    const defaultStyle = layerMetadata.style || (styles.length > 0 ? styles[0].name : '');

    // Format styles for frontend
    const formattedStyles = styles.map((style: any) => ({
      name: style.name,
      title: style.title || style.name,
      abstract: style.abstract || '',
      legendUrl: style.legendUrl || `${GEOSERVER_BASE}/ows?SERVICE=WMS&REQUEST=GetLegendGraphic&LAYER=${encodeURIComponent(layerName)}&STYLE=${encodeURIComponent(style.name)}&FORMAT=image/png`,
      isDefault: style.name === defaultStyle
    }));

    res.status(200).json({
      layerName,
      defaultStyle,
      styles: formattedStyles,
      legendUrl: layerMetadata.legendUrl || `${GEOSERVER_BASE}/ows?SERVICE=WMS&REQUEST=GetLegendGraphic&LAYER=${encodeURIComponent(layerName)}&FORMAT=image/png`
    });
  } catch (error) {
    console.error('Layer styles error:', error);
    res.status(500).json({ error: 'Failed to retrieve layer styles' });
  }
};


