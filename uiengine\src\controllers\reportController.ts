// Phase 4: Report Controller
import { Request, Response, NextFunction } from 'express';
import { ReportService, ReportParams } from '../services/reportService';
import * as path from 'path';
import * as fs from 'fs';

export class ReportController {
  private reportService: ReportService;

  constructor() {
    this.reportService = new ReportService();
  }

  /**
   * Generate a new report
   */
  generateReport = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const {
        region,
        startDate,
        endDate,
        includeCharts = false,
        format = 'csv',
        datasetIds,
        reportType = 'alert_history'
      } = req.body;

      // Validate required parameters
      if (!startDate || !endDate) {
        res.status(400).json({
          success: false,
          message: 'Start date and end date are required'
        });
        return;
      }

      const params: ReportParams = {
        region,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        includeCharts,
        format,
        datasetIds,
        reportType
      };

      console.log('🔄 Generating report with params:', params);

      const result = await this.reportService.generateFloodReport(params);

      res.json({
        success: true,
        data: {
          filename: result.filename,
          recordCount: result.data.metadata.totalRecords,
          generatedAt: result.data.metadata.generatedAt,
          queryTime: result.data.metadata.queryTime,
          downloadUrl: `/api/reports/download/${result.filename}`
        },
        message: 'Report generated successfully'
      });

    } catch (error) {
      console.error('Error generating report:', error);
      next(error);
    }
  };

  /**
   * Get analytics data for dashboard
   */
  getAnalytics = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { period = '7d' } = req.query;

      console.log(`🔄 Generating analytics for period: ${period}`);

      const analytics = await this.reportService.generateAnalytics(period as string);

      res.json({
        success: true,
        data: analytics,
        message: 'Analytics generated successfully'
      });

    } catch (error) {
      console.error('Error generating analytics:', error);
      next(error);
    }
  };

  /**
   * Download a generated report
   */
  downloadReport = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { filename } = req.params;
      
      if (!filename) {
        res.status(400).json({
          success: false,
          message: 'Filename is required'
        });
        return;
      }

      // Sanitize filename to prevent directory traversal
      const sanitizedFilename = path.basename(filename);
      const reportsDir = path.join(process.cwd(), 'reports');
      const filepath = path.join(reportsDir, sanitizedFilename);

      // Check if file exists
      if (!fs.existsSync(filepath)) {
        res.status(404).json({
          success: false,
          message: 'Report file not found'
        });
        return;
      }

      // Determine content type based on file extension
      const ext = path.extname(sanitizedFilename).toLowerCase();
      let contentType = 'application/octet-stream';
      
      switch (ext) {
        case '.csv':
          contentType = 'text/csv';
          break;
        case '.html':
          contentType = 'text/html';
          break;
        case '.pdf':
          contentType = 'application/pdf';
          break;
        case '.xlsx':
          contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
      }

      // Set response headers
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${sanitizedFilename}"`);

      // Stream the file
      const fileStream = fs.createReadStream(filepath);
      fileStream.pipe(res);

      console.log(`📄 Report downloaded: ${sanitizedFilename}`);

    } catch (error) {
      console.error('Error downloading report:', error);
      next(error);
    }
  };

  /**
   * List available reports
   */
  listReports = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const reports = await this.reportService.getAvailableReports();

      res.json({
        success: true,
        data: reports.sort((a, b) => b.created.getTime() - a.created.getTime()),
        message: 'Reports retrieved successfully'
      });

    } catch (error) {
      console.error('Error listing reports:', error);
      next(error);
    }
  };

  /**
   * Delete a report
   */
  deleteReport = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { filename } = req.params;
      
      if (!filename) {
        res.status(400).json({
          success: false,
          message: 'Filename is required'
        });
        return;
      }

      // Sanitize filename
      const sanitizedFilename = path.basename(filename);
      const reportsDir = path.join(process.cwd(), 'reports');
      const filepath = path.join(reportsDir, sanitizedFilename);

      // Check if file exists
      if (!fs.existsSync(filepath)) {
        res.status(404).json({
          success: false,
          message: 'Report file not found'
        });
        return;
      }

      // Delete the file
      fs.unlinkSync(filepath);

      res.json({
        success: true,
        message: 'Report deleted successfully'
      });

      console.log(`🗑️ Report deleted: ${sanitizedFilename}`);

    } catch (error) {
      console.error('Error deleting report:', error);
      next(error);
    }
  };

  /**
   * Cleanup old reports
   */
  cleanupReports = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { maxAge = 7 } = req.query; // Default 7 days
      const maxAgeMs = parseInt(maxAge as string) * 24 * 60 * 60 * 1000;

      const deletedCount = await this.reportService.cleanupOldReports(maxAgeMs);

      res.json({
        success: true,
        data: { deletedCount },
        message: `Cleaned up ${deletedCount} old reports`
      });

    } catch (error) {
      console.error('Error cleaning up reports:', error);
      next(error);
    }
  };

  /**
   * Get report statistics
   */
  getReportStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const reports = await this.reportService.getAvailableReports();
      
      const stats = {
        totalReports: reports.length,
        totalSize: reports.reduce((sum, report) => sum + report.size, 0),
        newestReport: reports.length > 0 ? reports.reduce((newest, report) => 
          report.created > newest.created ? report : newest
        ) : null,
        oldestReport: reports.length > 0 ? reports.reduce((oldest, report) => 
          report.created < oldest.created ? report : oldest
        ) : null,
        averageSize: reports.length > 0 ? 
          Math.round(reports.reduce((sum, report) => sum + report.size, 0) / reports.length) : 0
      };

      res.json({
        success: true,
        data: stats,
        message: 'Report statistics retrieved successfully'
      });

    } catch (error) {
      console.error('Error getting report statistics:', error);
      next(error);
    }
  };

  /**
   * Get available report types and formats
   */
  getReportOptions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const options = {
        reportTypes: [
          { value: 'alert_history', label: 'Alert History', description: 'Historical alert events and their details' },
          { value: 'dataset_summary', label: 'Dataset Summary', description: 'Overview of available datasets' },
          { value: 'flood_events', label: 'Flood Events', description: 'Flood event analysis and impact assessment' },
          { value: 'risk_analysis', label: 'Risk Analysis', description: 'Risk assessment and trend analysis' }
        ],
        formats: [
          { value: 'csv', label: 'CSV', description: 'Comma-separated values for data analysis' },
          { value: 'html', label: 'HTML', description: 'Formatted report for web viewing' },
          { value: 'pdf', label: 'PDF', description: 'Portable document format (development)' },
          { value: 'excel', label: 'Excel', description: 'Excel spreadsheet format (development)' }
        ],
        periods: [
          { value: '24h', label: 'Last 24 Hours' },
          { value: '7d', label: 'Last 7 Days' },
          { value: '30d', label: 'Last 30 Days' },
          { value: '90d', label: 'Last 90 Days' },
          { value: '1y', label: 'Last Year' }
        ]
      };

      res.json({
        success: true,
        data: options,
        message: 'Report options retrieved successfully'
      });

    } catch (error) {
      console.error('Error getting report options:', error);
      next(error);
    }
  };
}
