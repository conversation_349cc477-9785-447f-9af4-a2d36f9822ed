import { Request, Response } from 'express';
import { ROIService, ROI, SpatialQuery } from '../services/roiService';
import { SimpleValidator } from '../utils/validation';

export class ROIController {
  // POST /api/roi - Create new ROI
  static async createROI(req: Request, res: Response): Promise<void> {
    try {
      const validation = SimpleValidator.validateROI(req.body);
      if (validation.error) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          details: validation.error.details.map((d: any) => d.message)
        });
        return;
      }

      const roi = await ROIService.createROI(validation.value as ROI);
      res.status(201).json({
        success: true,
        data: roi,
        message: 'ROI created successfully'
      });
    } catch (error) {
      console.error('Error in createROI:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // GET /api/roi - Get all ROIs
  static async getAllROIs(req: Request, res: Response): Promise<void> {
    try {
      const rois = await ROIService.getAllROIs();
      res.json({
        success: true,
        data: rois,
        count: rois.length
      });
    } catch (error) {
      console.error('Error in getAllROIs:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // GET /api/roi/:id - Get ROI by ID
  static async getROIById(req: Request, res: Response): Promise<void> {
    try {
      const idValidation = SimpleValidator.validateNumericId(req.params.id);
      if (!idValidation.valid) {
        res.status(400).json({
          success: false,
          error: idValidation.error
        });
        return;
      }

      const roi = await ROIService.getROIById(idValidation.id!);
      if (!roi) {
        res.status(404).json({
          success: false,
          error: 'ROI not found'
        });
        return;
      }

      res.json({
        success: true,
        data: roi
      });
    } catch (error) {
      console.error('Error in getROIById:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // PUT /api/roi/:id - Update ROI
  static async updateROI(req: Request, res: Response): Promise<void> {
    try {
      const idValidation = SimpleValidator.validateNumericId(req.params.id);
      if (!idValidation.valid) {
        res.status(400).json({
          success: false,
          error: idValidation.error
        });
        return;
      }

      const validation = SimpleValidator.validateROI(req.body);
      if (validation.error) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          details: validation.error.details.map((d: any) => d.message)
        });
        return;
      }

      const roi = await ROIService.updateROI(idValidation.id!, validation.value as Partial<ROI>);
      if (!roi) {
        res.status(404).json({
          success: false,
          error: 'ROI not found'
        });
        return;
      }

      res.json({
        success: true,
        data: roi,
        message: 'ROI updated successfully'
      });
    } catch (error) {
      console.error('Error in updateROI:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // DELETE /api/roi/:id - Delete ROI
  static async deleteROI(req: Request, res: Response): Promise<void> {
    try {
      const idValidation = SimpleValidator.validateNumericId(req.params.id);
      if (!idValidation.valid) {
        res.status(400).json({
          success: false,
          error: idValidation.error
        });
        return;
      }

      const deleted = await ROIService.deleteROI(idValidation.id!);
      if (!deleted) {
        res.status(404).json({
          success: false,
          error: 'ROI not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'ROI deleted successfully'
      });
    } catch (error) {
      console.error('Error in deleteROI:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // POST /api/roi/intersect - Find ROIs that intersect with geometry
  static async findIntersectingROIs(req: Request, res: Response): Promise<void> {
    try {
      const { geometry, buffer_meters = 0 } = req.body;
      
      if (!geometry) {
        res.status(400).json({
          success: false,
          error: 'Geometry is required'
        });
        return;
      }

      const rois = await ROIService.findIntersectingROIs(geometry, buffer_meters);
      res.json({
        success: true,
        data: rois,
        count: rois.length
      });
    } catch (error) {
      console.error('Error in findIntersectingROIs:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // GET /api/roi/:id/flood-events - Get flood events within ROI
  static async getFloodEventsInROI(req: Request, res: Response): Promise<void> {
    try {
      const idValidation = SimpleValidator.validateNumericId(req.params.id);
      if (!idValidation.valid) {
        res.status(400).json({
          success: false,
          error: idValidation.error
        });
        return;
      }

      const { start_date, end_date } = req.query;
      const events = await ROIService.getFloodEventsInROI(
        idValidation.id!, 
        start_date as string, 
        end_date as string
      );

      res.json({
        success: true,
        data: events,
        count: events.length
      });
    } catch (error) {
      console.error('Error in getFloodEventsInROI:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // GET /api/roi/:id/water-measurements - Get water measurements within ROI
  static async getWaterMeasurementsInROI(req: Request, res: Response): Promise<void> {
    try {
      const idValidation = SimpleValidator.validateNumericId(req.params.id);
      if (!idValidation.valid) {
        res.status(400).json({
          success: false,
          error: idValidation.error
        });
        return;
      }

      const { start_date, end_date } = req.query;
      const measurements = await ROIService.getWaterMeasurementsInROI(
        idValidation.id!, 
        start_date as string, 
        end_date as string
      );

      res.json({
        success: true,
        data: measurements,
        count: measurements.length
      });
    } catch (error) {
      console.error('Error in getWaterMeasurementsInROI:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // POST /api/roi/analysis - Perform spatial analysis
  static async performSpatialAnalysis(req: Request, res: Response): Promise<void> {
    try {
      const validation = SimpleValidator.validateSpatialQuery(req.body);
      if (validation.error) {
        res.status(400).json({
          success: false,
          error: 'Validation error',
          details: validation.error.details.map((d: any) => d.message)
        });
        return;
      }

      const analysis = await ROIService.performSpatialAnalysis(validation.value as SpatialQuery);
      res.json({
        success: true,
        data: analysis,
        message: 'Spatial analysis completed successfully'
      });
    } catch (error) {
      console.error('Error in performSpatialAnalysis:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}
