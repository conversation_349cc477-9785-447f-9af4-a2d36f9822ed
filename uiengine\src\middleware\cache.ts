import { Request, Response, NextFunction } from 'express';
import mcache from 'memory-cache';

export const cacheMiddleware = (duration: number) => {
    return (req: Request, res: Response, next: NextFunction) => {
        const key = `__express__${req.originalUrl || req.url}`;
        const cachedBody = mcache.get(key);

        if (cachedBody) {
            res.send(cachedBody);
            return;
        }

        const originalSend = res.send.bind(res);
        res.send = ((body: any) => {
            mcache.put(key, body, duration * 1000);
            return originalSend(body);
        }) as any;

        next();
    };
};

/**
 * Tiny in-memory cache middleware for capabilities requests.
 * Keeps things simple: uses memory-cache with TTL check.
 */
const DEFAULT_TTL = Number(process.env.CACHE_TTL) || 3600; // seconds

export function capabilitiesCache(ttl: number = DEFAULT_TTL) {
  return (req: Request, res: Response, next: NextFunction) => {
    const key = req.originalUrl;

    // serve from cache if valid
    const cached = mcache.get(key);
    if (cached) {
      return res.json(cached);
    }

    // monkey-patch res.json to save response
    const originalJson = res.json.bind(res);
    res.json = (body: any) => {
      mcache.put(key, body, ttl * 1000);
      return originalJson(body);
    };

    next();
  };
}
