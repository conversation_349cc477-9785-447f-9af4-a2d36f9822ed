import express from 'express';
import { <PERSON>ertEventController } from '../controllers/alertEventController';

/**
 * @swagger
 * components:
 *   schemas:
 *     AlertEvent:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique alert event identifier
 *         ruleId:
 *           type: string
 *           description: Associated alert rule ID
 *         ruleName:
 *           type: string
 *           description: Name of the triggered rule
 *         layerName:
 *           type: string
 *           description: Layer that triggered the alert
 *         severity:
 *           type: string
 *           enum: [low, medium, high, critical]
 *           description: Alert severity level
 *         message:
 *           type: string
 *           description: Alert message
 *         details:
 *           type: object
 *           description: Additional alert details
 *         location:
 *           type: object
 *           description: Geographic location of the alert
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: When the alert was triggered
 *         acknowledged:
 *           type: boolean
 *           description: Whether the alert has been acknowledged
 *         acknowledgedBy:
 *           type: string
 *           description: User who acknowledged the alert
 *         acknowledgedAt:
 *           type: string
 *           format: date-time
 *           description: When the alert was acknowledged
 */

const router = express.Router();
const alertEventController = new AlertEventController();

/**
 * @swagger
 * /api/alert-events:
 *   get:
 *     summary: Get alert events
 *     tags: [Alerts]
 *     parameters:
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [low, medium, high, critical]
 *         description: Filter by severity level
 *       - in: query
 *         name: acknowledged
 *         schema:
 *           type: boolean
 *         description: Filter by acknowledgment status
 *       - in: query
 *         name: ruleId
 *         schema:
 *           type: string
 *         description: Filter by alert rule ID
 *       - in: query
 *         name: layerName
 *         schema:
 *           type: string
 *         description: Filter by layer name
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for time range filter
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for time range filter
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Maximum number of results
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of results to skip
 *     responses:
 *       200:
 *         description: List of alert events
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 events:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/AlertEvent'
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 offset:
 *                   type: integer
 *       500:
 *         description: Server error
 */
router.get('/', alertEventController.getAlertEvents);

/**
 * @swagger
 * /api/alert-events/stats:
 *   get:
 *     summary: Get alert statistics
 *     tags: [Alerts]
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [hour, day, week, month]
 *           default: day
 *         description: Time period for statistics
 *     responses:
 *       200:
 *         description: Alert statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 bySeverity:
 *                   type: object
 *                   properties:
 *                     low:
 *                       type: integer
 *                     medium:
 *                       type: integer
 *                     high:
 *                       type: integer
 *                     critical:
 *                       type: integer
 *                 acknowledged:
 *                   type: integer
 *                 unacknowledged:
 *                   type: integer
 *                 byLayer:
 *                   type: object
 *                 timeline:
 *                   type: array
 *                   items:
 *                     type: object
 *       500:
 *         description: Server error
 */
router.get('/stats', alertEventController.getAlertStats);

/**
 * @swagger
 * /api/alert-events/unacknowledged/count:
 *   get:
 *     summary: Get count of unacknowledged alerts
 *     tags: [Alerts]
 *     responses:
 *       200:
 *         description: Count of unacknowledged alerts
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *                 bySeverity:
 *                   type: object
 *                   properties:
 *                     low:
 *                       type: integer
 *                     medium:
 *                       type: integer
 *                     high:
 *                       type: integer
 *                     critical:
 *                       type: integer
 *       500:
 *         description: Server error
 */
router.get('/unacknowledged/count', alertEventController.getUnacknowledgedCount);

/**
 * @swagger
 * /api/alert-events/{id}:
 *   get:
 *     summary: Get alert event by ID
 *     tags: [Alerts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert event ID
 *     responses:
 *       200:
 *         description: Alert event details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AlertEvent'
 *       404:
 *         description: Alert event not found
 *       500:
 *         description: Server error
 */
router.get('/:id', alertEventController.getAlertEvent);

/**
 * @swagger
 * /api/alert-events/{id}/acknowledge:
 *   patch:
 *     summary: Acknowledge an alert event
 *     tags: [Alerts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert event ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               acknowledgedBy:
 *                 type: string
 *                 description: User acknowledging the alert
 *               notes:
 *                 type: string
 *                 description: Optional acknowledgment notes
 *     responses:
 *       200:
 *         description: Alert acknowledged
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AlertEvent'
 *       404:
 *         description: Alert event not found
 *       500:
 *         description: Server error
 */
router.patch('/:id/acknowledge', alertEventController.acknowledgeAlert);

/**
 * @swagger
 * /api/alert-events/bulk-acknowledge:
 *   post:
 *     summary: Acknowledge multiple alert events
 *     tags: [Alerts]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - eventIds
 *               - acknowledgedBy
 *             properties:
 *               eventIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of alert event IDs
 *               acknowledgedBy:
 *                 type: string
 *                 description: User acknowledging the alerts
 *               notes:
 *                 type: string
 *                 description: Optional acknowledgment notes
 *     responses:
 *       200:
 *         description: Alerts acknowledged
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 acknowledged:
 *                   type: integer
 *                 failed:
 *                   type: array
 *                   items:
 *                     type: string
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Server error
 */
router.post('/bulk-acknowledge', alertEventController.bulkAcknowledgeAlerts);

export default router;
