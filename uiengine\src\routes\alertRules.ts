import express from 'express';
import { <PERSON>ertRuleController } from '../controllers/alertRuleController';

/**
 * @swagger
 * components:
 *   schemas:
 *     AlertRule:
 *       type: object
 *       required:
 *         - name
 *         - condition
 *         - threshold
 *       properties:
 *         id:
 *           type: string
 *           description: Unique alert rule identifier
 *         name:
 *           type: string
 *           description: Alert rule name
 *         description:
 *           type: string
 *           description: Alert rule description
 *         layerName:
 *           type: string
 *           description: Target layer name
 *         condition:
 *           type: string
 *           enum: [gt, lt, eq, gte, lte, between, outside]
 *           description: Comparison condition
 *         threshold:
 *           type: number
 *           description: Threshold value
 *         thresholdMax:
 *           type: number
 *           description: Maximum threshold (for between/outside conditions)
 *         attribute:
 *           type: string
 *           description: Layer attribute to monitor
 *         area:
 *           type: object
 *           description: Geographic area to monitor
 *         active:
 *           type: boolean
 *           description: Whether the rule is active
 *         severity:
 *           type: string
 *           enum: [low, medium, high, critical]
 *           description: Alert severity level
 *         notificationChannels:
 *           type: array
 *           items:
 *             type: string
 *           description: Notification channels
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 */

const router = express.Router();
const alertRuleController = new AlertRuleController();

/**
 * @swagger
 * /api/alert-rules:
 *   get:
 *     summary: Get all alert rules
 *     tags: [Alerts]
 *     parameters:
 *       - in: query
 *         name: active
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [low, medium, high, critical]
 *         description: Filter by severity level
 *     responses:
 *       200:
 *         description: List of alert rules
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/AlertRule'
 *       500:
 *         description: Server error
 */
router.get('/', alertRuleController.getAlertRules);

/**
 * @swagger
 * /api/alert-rules/{id}:
 *   get:
 *     summary: Get alert rule by ID
 *     tags: [Alerts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert rule ID
 *     responses:
 *       200:
 *         description: Alert rule details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AlertRule'
 *       404:
 *         description: Alert rule not found
 *       500:
 *         description: Server error
 */
router.get('/:id', alertRuleController.getAlertRule);

/**
 * @swagger
 * /api/alert-rules:
 *   post:
 *     summary: Create a new alert rule
 *     tags: [Alerts]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - condition
 *               - threshold
 *               - layerName
 *               - attribute
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               layerName:
 *                 type: string
 *               condition:
 *                 type: string
 *                 enum: [gt, lt, eq, gte, lte, between, outside]
 *               threshold:
 *                 type: number
 *               thresholdMax:
 *                 type: number
 *               attribute:
 *                 type: string
 *               area:
 *                 type: object
 *               severity:
 *                 type: string
 *                 enum: [low, medium, high, critical]
 *                 default: medium
 *               notificationChannels:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       201:
 *         description: Alert rule created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AlertRule'
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Server error
 */
router.post('/', alertRuleController.createAlertRule);

/**
 * @swagger
 * /api/alert-rules/{id}:
 *   put:
 *     summary: Update an alert rule
 *     tags: [Alerts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert rule ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AlertRule'
 *     responses:
 *       200:
 *         description: Alert rule updated
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AlertRule'
 *       404:
 *         description: Alert rule not found
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Server error
 */
router.put('/:id', alertRuleController.updateAlertRule);

/**
 * @swagger
 * /api/alert-rules/{id}:
 *   delete:
 *     summary: Delete an alert rule
 *     tags: [Alerts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert rule ID
 *     responses:
 *       200:
 *         description: Alert rule deleted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       404:
 *         description: Alert rule not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', alertRuleController.deleteAlertRule);

/**
 * @swagger
 * /api/alert-rules/{id}/toggle:
 *   post:
 *     summary: Toggle alert rule active status
 *     tags: [Alerts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert rule ID
 *     responses:
 *       200:
 *         description: Alert rule status toggled
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 active:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       404:
 *         description: Alert rule not found
 *       500:
 *         description: Server error
 */
router.post('/:id/toggle', alertRuleController.toggleAlertRule);

/**
 * @swagger
 * /api/alert-rules/{id}/test:
 *   get:
 *     summary: Test an alert rule
 *     tags: [Alerts]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert rule ID
 *     responses:
 *       200:
 *         description: Test results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 triggered:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       404:
 *         description: Alert rule not found
 *       500:
 *         description: Server error
 */
router.get('/:id/test', alertRuleController.testAlertRule);

export default router;
