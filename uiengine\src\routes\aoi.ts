/**
 * AOI Lifecycle API Routes
 * 
 * Provides centralized AOI management endpoints for the AOI-True Clipping system.
 * These routes serve as the single source of truth for AOI data.
 */

import { Router, Request, Response } from 'express';
import { getAOIService } from '../services/aoiService';
import { getCapabilitiesCache } from '../services/capabilitiesCache';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     AOIGeometry:
 *       type: object
 *       properties:
 *         type:
 *           type: string
 *           enum: [Polygon, MultiPolygon]
 *         coordinates:
 *           type: array
 *           description: GeoJSON coordinates array
 *     AOIBounds:
 *       type: object
 *       properties:
 *         north:
 *           type: number
 *         south:
 *           type: number
 *         east:
 *           type: number
 *         west:
 *           type: number
 *     AOICreateRequest:
 *       type: object
 *       required:
 *         - mode
 *         - geometry
 *       properties:
 *         mode:
 *           type: string
 *           enum: [administrative, drawn, pin]
 *         geometry:
 *           $ref: '#/components/schemas/AOIGeometry'
 *         dateRange:
 *           type: object
 *           properties:
 *             startDate:
 *               type: string
 *               format: date
 *             endDate:
 *               type: string
 *               format: date
 *         metadata:
 *           type: object
 *           properties:
 *             name:
 *               type: string
 *             level:
 *               type: string
 *               enum: [province, district, municipality, ward]
 *             code:
 *               type: string
 *     AOICreateResponse:
 *       type: object
 *       properties:
 *         aoiId:
 *           type: string
 *         bounds:
 *           $ref: '#/components/schemas/AOIBounds'
 *         centroid:
 *           type: object
 *           properties:
 *             lat:
 *               type: number
 *             lng:
 *               type: number
 *         meta:
 *           type: object
 *           properties:
 *             area:
 *               type: number
 *             vertices:
 *               type: number
 *             hasSimplified:
 *               type: boolean
 *             mode:
 *               type: string
 */

/**
 * @swagger
 * /aoi:
 *   post:
 *     summary: Create new AOI
 *     description: |
 *       Create and store a new Area of Interest (AOI) with validation, 
 *       canonicalization to MultiPolygon, and automatic simplification if needed.
 *       Returns AOI ID, bounds, centroid, and metadata for use in tile requests.
 *     tags: [AOI]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AOICreateRequest'
 *     responses:
 *       201:
 *         description: AOI created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AOICreateResponse'
 *       400:
 *         description: Invalid AOI data or validation failed
 *       500:
 *         description: Server error
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const aoiService = getAOIService();
    if (!aoiService) {
      return res.status(500).json({
        error: 'AOI service not initialized'
      });
    }

    const { mode, geometry, dateRange, metadata } = req.body;

    // Validate required fields
    if (!mode || !geometry) {
      return res.status(400).json({
        error: 'Missing required fields: mode and geometry are required'
      });
    }

    // Validate mode
    if (!['administrative', 'drawn', 'pin'].includes(mode)) {
      return res.status(400).json({
        error: 'Invalid mode. Must be one of: administrative, drawn, pin'
      });
    }

    console.log(`📍 Creating AOI: mode=${mode}, hasGeometry=${!!geometry}`);

    // Create AOI
    const result = await aoiService.createAOI({
      mode,
      geometry,
      dateRange,
      metadata
    });

    res.status(201).json(result);

  } catch (error) {
    console.error('❌ AOI creation failed:', error);
    res.status(400).json({
      error: error instanceof Error ? error.message : 'Failed to create AOI'
    });
  }
});

/**
 * @swagger
 * /aoi/{aoiId}:
 *   get:
 *     summary: Get AOI data
 *     description: Retrieve stored AOI data by ID
 *     tags: [AOI]
 *     parameters:
 *       - in: path
 *         name: aoiId
 *         required: true
 *         schema:
 *           type: string
 *         description: AOI identifier
 *     responses:
 *       200:
 *         description: AOI data retrieved successfully
 *       404:
 *         description: AOI not found
 *       500:
 *         description: Server error
 */
router.get('/:aoiId', async (req: Request, res: Response) => {
  try {
    const aoiService = getAOIService();
    if (!aoiService) {
      return res.status(500).json({
        error: 'AOI service not initialized'
      });
    }

    const { aoiId } = req.params;
    const aoi = aoiService.getAOI(aoiId);

    if (!aoi) {
      return res.status(404).json({
        error: 'AOI not found'
      });
    }

    res.json(aoi);

  } catch (error) {
    console.error('❌ AOI retrieval failed:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to retrieve AOI'
    });
  }
});

/**
 * @swagger
 * /aoi/{aoiId}:
 *   delete:
 *     summary: Delete AOI
 *     description: |
 *       Delete AOI and cleanup associated caches including tile caches,
 *       WPS products, and any other AOI-specific data.
 *     tags: [AOI]
 *     parameters:
 *       - in: path
 *         name: aoiId
 *         required: true
 *         schema:
 *           type: string
 *         description: AOI identifier
 *     responses:
 *       200:
 *         description: AOI deleted successfully
 *       404:
 *         description: AOI not found
 *       500:
 *         description: Server error
 */
router.delete('/:aoiId', async (req: Request, res: Response) => {
  try {
    const aoiService = getAOIService();
    if (!aoiService) {
      return res.status(500).json({
        error: 'AOI service not initialized'
      });
    }

    const { aoiId } = req.params;
    const deleted = aoiService.deleteAOI(aoiId);

    if (!deleted) {
      return res.status(404).json({
        error: 'AOI not found'
      });
    }

    res.json({
      message: 'AOI deleted successfully',
      aoiId
    });

  } catch (error) {
    console.error('❌ AOI deletion failed:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to delete AOI'
    });
  }
});

/**
 * @swagger
 * /aoi/stats:
 *   get:
 *     summary: Get AOI service statistics
 *     description: Get statistics about AOI usage and cache status
 *     tags: [AOI]
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *       500:
 *         description: Server error
 */
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const aoiService = getAOIService();
    if (!aoiService) {
      return res.status(500).json({
        error: 'AOI service not initialized'
      });
    }

    const stats = aoiService.getStats();
    res.json(stats);

  } catch (error) {
    console.error('❌ AOI stats retrieval failed:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to retrieve AOI stats'
    });
  }
});

/**
 * @swagger
 * /admin/refresh-capabilities:
 *   post:
 *     summary: Refresh capabilities cache
 *     description: |
 *       Manually refresh the capabilities cache by re-probing GeoServer.
 *       This endpoint is useful for updating layer capabilities after
 *       GeoServer configuration changes.
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: Capabilities cache refreshed successfully
 *       500:
 *         description: Server error
 */
router.post('/admin/refresh-capabilities', async (req: Request, res: Response) => {
  try {
    const capabilitiesCache = getCapabilitiesCache();
    if (!capabilitiesCache) {
      return res.status(500).json({
        error: 'Capabilities cache not initialized'
      });
    }

    await capabilitiesCache.refreshCapabilities();
    const stats = capabilitiesCache.getStats();

    res.json({
      message: 'Capabilities cache refreshed successfully',
      stats
    });

  } catch (error) {
    console.error('❌ Capabilities refresh failed:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to refresh capabilities cache'
    });
  }
});

/**
 * @swagger
 * /api/aoi/validation-stats:
 *   get:
 *     summary: Get validation pipeline performance statistics
 *     tags: [AOI]
 *     responses:
 *       200:
 *         description: Validation statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalValidations:
 *                   type: number
 *                   description: Total number of validations performed
 *                 successRate:
 *                   type: number
 *                   description: Percentage of successful validations
 *                 cacheHitRate:
 *                   type: number
 *                   description: Percentage of cache hits
 *                 averageValidationTime:
 *                   type: number
 *                   description: Average validation time in milliseconds
 *                 cacheSize:
 *                   type: number
 *                   description: Current validation cache size
 *       500:
 *         description: Server error
 */
router.get('/validation-stats', (req: Request, res: Response) => {
  try {
    const aoiService = getAOIService();
    if (!aoiService) {
      return res.status(500).json({ error: 'AOI service not initialized' });
    }

    const stats = aoiService.getValidationStats();
    res.json(stats);
  } catch (error) {
    console.error('❌ Failed to get validation stats:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to get validation statistics'
    });
  }
});

export default router;
