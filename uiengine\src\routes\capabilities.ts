import { Router } from 'express';
import { getCapabilities } from '../controllers/owsController';

const router = Router();

// Import specialized WMS proxy handler
const { streamSecureWmsRequest } = require('../utils/wmsProxy');

// Layer discovery endpoint
router.get('/capabilities', getCapabilities);

// WMS proxy endpoint for tile requests
router.get('/wms-proxy', async (req, res) => {
  try {
    const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
    const wmsUrl = `${geoserverUrl}/geonode/wms`;
    
    console.log('WMS Proxy request:', req.query);
    
    // Process the query parameters to handle workspace prefixes
    const processedQuery = { ...req.query };
    if (processedQuery.layers || processedQuery.LAYERS) {
      const layersParam = processedQuery.layers || processedQuery.LAYERS;
      if (typeof layersParam === 'string' && layersParam.startsWith('geonode:')) {
        // Strip the workspace prefix since we're already targeting the geonode workspace
        const cleanLayerName = layersParam.replace('geonode:', '');
        if (processedQuery.layers) processedQuery.layers = cleanLayerName;
        if (processedQuery.LAYERS) processedQuery.LAYERS = cleanLayerName;
        console.log(`Stripped workspace prefix: ${layersParam} -> ${cleanLayerName}`);
      }
    }
    
    // Stream the WMS request directly to the client
    const wmsResponse = await streamSecureWmsRequest(wmsUrl, processedQuery);
    
    // Set appropriate headers
    Object.keys(wmsResponse.headers).forEach(key => {
      if (key.toLowerCase() !== 'transfer-encoding') {
        res.set(key, wmsResponse.headers[key]);
      }
    });
    
    res.status(wmsResponse.status);
    
    // Pipe the response stream directly to the client
    wmsResponse.data.pipe(res);
    
  } catch (error) {
    console.error('WMS proxy error:', error);
    res.status(500).json({ 
      error: 'WMS proxy request failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as capabilitiesRouter };
