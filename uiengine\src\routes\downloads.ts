/**
 * AOI-True Download Routes
 * 
 * Provides download endpoints that use the same clipping logic as tiles
 * to ensure exact preview-download consistency.
 */

import { Router, Request, Response } from 'express';
import archiver from 'archiver';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { getAOIService, AOIData } from '../services/aoiService';
import { getCapabilitiesCache, LayerCapabilities } from '../services/capabilitiesCache';
import { streamSecureWmsRequest } from '../utils/wmsProxy';

const router = Router();

export interface DownloadRequest {
  layers: string[];
  aoiId?: string;
  time?: string;
  format?: 'shapefile' | 'geojson' | 'kml' | 'geotiff';
  includeMetadata?: boolean;
  includePreview?: boolean;
  aoiName?: string;
}

export interface DownloadResponse {
  success: boolean;
  downloadUrl?: string;
  filename?: string;
  layers: {
    name: string;
    success: boolean;
    clippingMethod: string;
    error?: string;
  }[];
  metadata: {
    aoiId?: string;
    totalLayers: number;
    successfulLayers: number;
    processingTime: number;
    downloadDate: string;
  };
}

/**
 * @swagger
 * /downloads/aoi-true:
 *   post:
 *     summary: Download layers with AOI-True clipping
 *     description: |
 *       Download multiple layers with exact AOI clipping using the same
 *       server-side clipping strategies as tile rendering to ensure
 *       preview-download consistency.
 *     tags: [Downloads]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - layers
 *             properties:
 *               layers:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: List of layer names to download
 *               aoiId:
 *                 type: string
 *                 description: AOI identifier for clipping
 *               time:
 *                 type: string
 *                 description: Temporal parameter (ISO 8601 format)
 *               format:
 *                 type: string
 *                 enum: [shapefile, geojson, kml, geotiff]
 *                 default: shapefile
 *               includeMetadata:
 *                 type: boolean
 *                 default: true
 *               includePreview:
 *                 type: boolean
 *                 default: false
 *               aoiName:
 *                 type: string
 *                 description: Name for the download package
 *     responses:
 *       200:
 *         description: Download package created successfully
 *         content:
 *           application/zip:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Invalid request parameters
 *       404:
 *         description: AOI or layers not found
 *       500:
 *         description: Download processing failed
 */
router.post('/aoi-true', async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  try {
    const {
      layers,
      aoiId,
      time,
      format = 'shapefile',
      includeMetadata = true,
      includePreview = false,
      aoiName = 'AOI_Download'
    }: DownloadRequest = req.body;

    console.log('📥 AOI-True download request:', {
      layers: layers?.length || 0,
      aoiId,
      format,
      time: !!time
    });

    // Validate request
    if (!layers || !Array.isArray(layers) || layers.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'layers array is required and must not be empty'
      });
    }

    // Get AOI data if provided
    let aoiData: AOIData | null = null;
    if (aoiId) {
      const aoiService = getAOIService();
      aoiData = aoiService?.getAOI(aoiId) || null;
      
      if (!aoiData) {
        return res.status(404).json({
          success: false,
          error: `AOI not found: ${aoiId}`
        });
      }
    }

    // Get capabilities cache
    const capabilitiesCache = getCapabilitiesCache();
    if (!capabilitiesCache) {
      return res.status(500).json({
        success: false,
        error: 'Capabilities cache not available'
      });
    }

    // Create temporary directory
    const tempDir = path.join(os.tmpdir(), `aoi_true_download_${Date.now()}`);
    fs.mkdirSync(tempDir, { recursive: true });

    console.log(`📁 Created temp directory: ${tempDir}`);

    // Process each layer
    const downloadResults: DownloadResponse['layers'] = [];
    const downloadedFiles: string[] = [];

    for (const layerName of layers) {
      try {
        console.log(`🔽 Processing layer: ${layerName}`);

        const capabilities = capabilitiesCache.getLayerCapabilities(layerName);
        if (!capabilities) {
          downloadResults.push({
            name: layerName,
            success: false,
            clippingMethod: 'NONE',
            error: 'Layer capabilities not found'
          });
          continue;
        }

        const result = await downloadLayerWithAOIClipping(
          layerName,
          capabilities,
          aoiData,
          time,
          format,
          tempDir
        );

        downloadResults.push(result);
        
        if (result.success && result.filePath) {
          downloadedFiles.push(result.filePath);
        }

      } catch (error) {
        console.error(`❌ Error processing layer ${layerName}:`, error);
        downloadResults.push({
          name: layerName,
          success: false,
          clippingMethod: 'ERROR',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Check if we have any successful downloads
    const successfulLayers = downloadResults.filter(r => r.success).length;
    if (successfulLayers === 0) {
      // Cleanup temp directory
      fs.rmSync(tempDir, { recursive: true, force: true });
      return res.status(500).json({
        success: false,
        error: 'No layers could be downloaded',
        layers: downloadResults
      });
    }

    // Create archive
    const archiveName = `${aoiName}_${successfulLayers}_layers_${new Date().toISOString().split('T')[0]}.zip`;
    
    console.log(`📦 Creating archive: ${archiveName} with ${downloadedFiles.length} files`);

    // Set response headers
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="${archiveName}"`);

    // Create archive stream
    const archive = archiver('zip', {
      zlib: { level: 9 } // Maximum compression
    });

    // Handle archive errors
    archive.on('error', (err) => {
      console.error('❌ Archive error:', err);
      throw err;
    });

    // Pipe archive to response
    archive.pipe(res);

    // Add downloaded files to archive
    for (const filePath of downloadedFiles) {
      const fileName = path.basename(filePath);
      archive.file(filePath, { name: fileName });
    }

    // Add metadata file if requested
    if (includeMetadata) {
      const metadata: DownloadResponse['metadata'] = {
        aoiId,
        totalLayers: layers.length,
        successfulLayers,
        processingTime: Date.now() - startTime,
        downloadDate: new Date().toISOString()
      };

      const metadataContent = {
        ...metadata,
        aoiData: aoiData ? {
          bounds: aoiData.bounds,
          centroid: aoiData.centroid,
          area: aoiData.area // FIX: use aoiData.area if present
        } : null,
        layers: downloadResults,
        clippingStrategies: {
          description: 'This download uses AOI-True clipping with the same strategies as tile rendering',
          vector: 'CQL_FILTER with INTERSECTS or BBOX fallback',
          raster: 'SLD clipping → WPS crop → server-side masking (tiered approach)'
        }
      };

      archive.append(JSON.stringify(metadataContent, null, 2), { 
        name: 'aoi_true_download_metadata.json' 
      });
    }

    // Finalize archive
    await archive.finalize();

    // Cleanup temp directory after a delay
    setTimeout(() => {
      try {
        fs.rmSync(tempDir, { recursive: true, force: true });
        console.log(`🧹 Cleaned up temp directory: ${tempDir}`);
      } catch (error) {
        console.warn(`⚠️ Failed to cleanup temp directory: ${tempDir}`, error);
      }
    }, 60000); // 1 minute delay

    console.log(`✅ AOI-True download completed: ${archiveName} (${Date.now() - startTime}ms)`);

  } catch (error) {
    console.error('❌ AOI-True download error:', error);
    
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Download processing failed',
        processingTime: Date.now() - startTime
      });
    }
  }
});

/**
 * Download a single layer with AOI clipping using same logic as tiles
 */
async function downloadLayerWithAOIClipping(
  layerName: string,
  capabilities: LayerCapabilities,
  aoiData: AOIData | null,
  time?: string,
  format: string = 'shapefile',
  tempDir: string = os.tmpdir()
): Promise<DownloadResponse['layers'][0] & { filePath?: string }> {
  
  console.log(`🔽 Downloading ${layerName} (${capabilities.type}) with AOI clipping`);

  try {
    // Determine clipping strategy based on layer type and AOI
    let clippingMethod = 'NONE';
    const wfsParams: Record<string, string> = {
      SERVICE: 'WFS',
      VERSION: '1.1.0',
      REQUEST: 'GetFeature',
      TYPENAME: layerName,
      OUTPUTFORMAT: getOutputFormat(format)
    };

    // Apply clipping if AOI is provided
    if (aoiData) {
      if (capabilities.type === 'vector') {
        // Use same logic as vector tiles
        if (capabilities.supports.cql && capabilities.geometryField) {
          try {
            const wkt = getWKTForDownload(aoiData);
            wfsParams.CQL_FILTER = `INTERSECTS(${capabilities.geometryField}, ${wkt})`;
            clippingMethod = 'CQL_FILTER';
          } catch (error) {
            // Fall back to BBOX
            const bbox = `${aoiData.bounds.west},${aoiData.bounds.south},${aoiData.bounds.east},${aoiData.bounds.north}`;
            wfsParams.BBOX = bbox;
            clippingMethod = 'BBOX';
          }
        } else if (capabilities.supports.bbox) {
          const bbox = `${aoiData.bounds.west},${aoiData.bounds.south},${aoiData.bounds.east},${aoiData.bounds.north}`;
          wfsParams.BBOX = bbox;
          clippingMethod = 'BBOX';
        }
      } else {
        // For raster layers, we'll need to use WCS or WMS
        // For now, fall back to BBOX
        const bbox = `${aoiData.bounds.west},${aoiData.bounds.south},${aoiData.bounds.east},${aoiData.bounds.north}`;
        wfsParams.BBOX = bbox;
        clippingMethod = 'BBOX';
      }
    }

    // Add temporal parameter if supported
    if (time && capabilities.supports.server_time) {
      wfsParams.TIME = time;
    }

    // Make WFS request
    const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
    const wfsUrl = `${geoserverUrl}/geonode/ows`;
    
    const response = await streamSecureWmsRequest(wfsUrl, wfsParams);
    
    if (response.status !== 200) {
      throw new Error(`WFS request failed: ${response.status}`);
    }

    // Save to file
    const fileExtension = getFileExtension(format);
    const filePath = path.join(tempDir, `${layerName}${fileExtension}`);
    
    const chunks: Buffer[] = [];
    response.data.on('data', (chunk: Buffer) => chunks.push(chunk));
    
    await new Promise<void>((resolve, reject) => {
      response.data.on('end', resolve);
      response.data.on('error', reject);
    });
    
    const fileData = Buffer.concat(chunks);
    fs.writeFileSync(filePath, fileData);

    console.log(`✅ Downloaded ${layerName} to ${filePath} (${fileData.length} bytes, ${clippingMethod})`);

    return {
      name: layerName,
      success: true,
      clippingMethod,
      filePath
    };

  } catch (error) {
    console.error(`❌ Failed to download ${layerName}:`, error);
    return {
      name: layerName,
      success: false,
      clippingMethod: 'ERROR',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get WKT string for download (same logic as tiles)
 */
function getWKTForDownload(aoiData: AOIData): string {
  if (aoiData.simplifiedWKT) {
    return aoiData.simplifiedWKT;
  }
  
  // Convert geometry to WKT (simplified implementation)
  const geometry = aoiData.geometry;
  if (geometry.type === 'Polygon') {
    // geometry.coordinates: number[][][]
    const rings = (geometry.coordinates as number[][][]).map((ring: number[][]) => 
      '(' + ring.map((coord: number[]) => `${coord[0]} ${coord[1]}`).join(', ') + ')'
    );
    return `POLYGON(${rings.join(', ')})`;
  }

  if (geometry.type === 'MultiPolygon') {
    // geometry.coordinates: number[][][][]
    const polygons = (geometry.coordinates as number[][][][]).map((polygon: number[][][]) => {
      const rings = polygon.map((ring: number[][]) => 
        '(' + ring.map((coord: number[]) => `${coord[0]} ${coord[1]}`).join(', ') + ')'
      );
      return `(${rings.join(', ')})`;
    });
    return `MULTIPOLYGON(${polygons.join(', ')})`;
  }
  
  throw new Error(`Unsupported geometry type for WKT: ${geometry.type}`);
}

/**
 * Get output format for WFS request
 */
function getOutputFormat(format: string): string {
  switch (format) {
    case 'geojson':
      return 'application/json';
    case 'kml':
      return 'application/vnd.google-earth.kml+xml';
    case 'shapefile':
      return 'SHAPE-ZIP';
    case 'geotiff':
      return 'image/geotiff';
    default:
      return 'SHAPE-ZIP';
  }
}

/**
 * Get file extension for format
 */
function getFileExtension(format: string): string {
  switch (format) {
    case 'geojson':
      return '.geojson';
    case 'kml':
      return '.kml';
    case 'shapefile':
      return '.zip';
    case 'geotiff':
      return '.tif';
    default:
      return '.zip';
  }
}

export default router;
