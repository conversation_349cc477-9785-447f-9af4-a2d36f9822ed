import { Router } from 'express';
import { getCapabilities, getLayerMetadata, getLayerStyles } from '../controllers/owsController';
import { cacheMiddleware } from '../middleware/cache';
import axios from 'axios';
import { secureGet } from '../utils/secureRequest';
import { handleRemoteLayerRequest, RemoteLayerInfo } from '../services/remoteLayerService';
import { fetchDatasets, datasetToLayerDiscovery } from '../services/datasetsService';
import * as path from 'path';
// Import specialized WMS proxy handler
import { streamSecureWmsRequest } from '../utils/wmsProxy';
// Import SLD and WPS helpers for raster clipping
import { buildRasterCropSLD } from '../sld/buildRasterCropSLD';
import { executeCropCoverageWPS } from '../services/wpsCrop';
// Import feature flags and monitoring
import { isSLDEnabled, isWPSEnabled } from '../config/featureFlags';
import { recordClipping } from '../services/clippingMonitor';

// Helper interfaces for administrative boundaries
interface AdministrativeRegion {
    id: string;
    name: string;
    code?: string;
    properties?: Record<string, any>;
}

interface DistrictInfo {
    id: string;
    name: string;
    code: string;
    type: 'district_municipality' | 'metropolitan_municipality';
    municipalities: string[];
    isMetro: boolean;
}

interface MetroInfo {
    id: string;
    name: string;
    code: string;
    type: 'metropolitan_municipality';
    isMetro: true;
}

interface LocalMunicipalityInfo {
    id: string;
    name: string;
    code: string;
    parentDistrict: string;
    type: 'local_municipality';
}

interface WardInfo {
    id: string;
    name: string;
    tlc: string;
    coordinates: [number, number];
}

interface AdministrativeClassification {
    level: 'district_municipality' | 'metropolitan_municipality' | 'local_municipality';
    isMetro: boolean;
    parentDistrict?: string;
    code: string;
}

// Known South African Metropolitan Municipalities (8 total)
// These codes represent the 8 metropolitan municipalities in South Africa:
// JHB - City of Johannesburg Metropolitan Municipality
// TSH - City of Tshwane Metropolitan Municipality
// EKU - Ekurhuleni Metropolitan Municipality
// CPT - City of Cape Town Metropolitan Municipality
// ETH - eThekwini Metropolitan Municipality
// MAN - Mangaung Metropolitan Municipality
// BUF - Buffalo City Metropolitan Municipality
// NMA - Nelson Mandela Bay Metropolitan Municipality
const METRO_CODES = ['JHB', 'TSH', 'EKU', 'CPT', 'ETH', 'MAN', 'BUF', 'NMA'];
const METRO_NAMES: Record<string, string> = {
    'JHB': 'City of Johannesburg Metropolitan Municipality',
    'TSH': 'City of Tshwane Metropolitan Municipality',
    'EKU': 'Ekurhuleni Metropolitan Municipality',
    'CPT': 'City of Cape Town Metropolitan Municipality',
    'ETH': 'eThekwini Metropolitan Municipality',
    'MAN': 'Mangaung Metropolitan Municipality',
    'BUF': 'Buffalo City Metropolitan Municipality',
    'NMA': 'Nelson Mandela Bay Metropolitan Municipality'
};

/**
 * Administrative Level Classifier Helper
 * Determines the correct administrative level of a feature
 */
function classifyAdministrativeLevel(feature: any): AdministrativeClassification {
    const props = feature.properties;
    const code = props.adm2_id || props.ADM2_ID;
    const name = props.adm2_en || props.ADM2_EN;

    if (!code) {
        throw new Error('Missing administrative code');
    }

    // Check if it's a Metropolitan Municipality
    if (METRO_CODES.includes(code)) {
        return {
            level: 'metropolitan_municipality',
            isMetro: true,
            code: code
        };
    }

    // Check if it's a District Municipality (DC## pattern)
    if (code.startsWith('DC')) {
        return {
            level: 'district_municipality',
            isMetro: false,
            code: code
        };
    }

    // Otherwise, it's likely a Local Municipality under a district
    return {
        level: 'local_municipality',
        isMetro: false,
        parentDistrict: extractParentDistrictFromName(props),
        code: code
    };
}

/**
 * Extract parent district from municipality feature properties
 * Tries to find the parent district code from the feature's properties.
 */
function extractParentDistrictFromName(featureProps: Record<string, any>): string | undefined {
    // Try common parent district code fields
    return featureProps.adm2_id || featureProps.ADM2_ID || featureProps.district_code || featureProps.DISTRICT_CODE;
}

/**
 * District Extraction Helper
 * Separates district municipalities from metropolitan municipalities
 */
function extractDistricts(municipalData: any[]): {
    districtMunicipalities: DistrictInfo[];
    metropolitanMunicipalities: DistrictInfo[];
} {
    const districtMunicipalities: DistrictInfo[] = [];
    const metropolitanMunicipalities: DistrictInfo[] = [];
    const processedCodes = new Set<string>();

    municipalData.forEach(feature => {
        const classification = classifyAdministrativeLevel(feature);
        const props = feature.properties;
        const name = props.adm2_en || props.ADM2_EN;

        if (processedCodes.has(classification.code)) {
            return; // Skip duplicates
        }
        processedCodes.add(classification.code);

        const districtInfo: DistrictInfo = {
            id: classification.code.toLowerCase(),
            name: classification.isMetro ? METRO_NAMES[classification.code] || name : name,
            code: classification.code,
            type: classification.level as 'district_municipality' | 'metropolitan_municipality',
            municipalities: [],
            isMetro: classification.isMetro
        };

        if (classification.isMetro) {
            metropolitanMunicipalities.push(districtInfo);
        } else if (classification.level === 'district_municipality') {
            districtMunicipalities.push(districtInfo);
        }
    });

    return {
        districtMunicipalities: districtMunicipalities.sort((a, b) => a.name.localeCompare(b.name)),
        metropolitanMunicipalities: metropolitanMunicipalities.sort((a, b) => a.name.localeCompare(b.name))
    };
}

/**
    municipalData.forEach(feature => {
        const classification = classifyAdministrativeLevel(feature);
        const props = feature.properties;

        // Only include local municipalities that belong to this district
        // Use the adm2_id or ADM2_ID property to match the district code directly
        if (
            classification.level === 'local_municipality' &&
            (props.adm2_id === districtCode || props.ADM2_ID === districtCode)
        ) {
            localMunicipalities.push({
                id: (props.adm2_en || props.ADM2_EN).toLowerCase().replace(/\s+/g, '_'),
                name: props.adm2_en || props.ADM2_EN,
                code: classification.code,
                parentDistrict: districtCode,
                type: 'local_municipality'
            });
        }
    });
            localMunicipalities.push({
                id: (props.adm2_en || props.ADM2_EN).toLowerCase().replace(/\s+/g, '_'),
                name: props.adm2_en || props.ADM2_EN,
                code: classification.code,
                parentDistrict: districtCode,
                type: 'local_municipality'
            });
        }
    });

    return localMunicipalities.sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * Hierarchy Validation Helper
 * Validates if a hierarchy path is correct for SA administrative structure
 */
function validateHierarchyPath(
    province: string,
    district?: string,
    municipality?: string
): {
    isValid: boolean;
    isMetroPath: boolean;
    skipDistrict: boolean;
} {
    // If district is a metro, municipality should be empty (metros skip to wards)
    if (district && METRO_CODES.includes(district)) {
        return {
            isValid: !municipality, // Valid only if no municipality selected
            isMetroPath: true,
            skipDistrict: false
        };
    }

    // For district municipalities, we need both district and municipality
    if (district && district.startsWith('DC')) {
        return {
            isValid: true,
            isMetroPath: false,
            skipDistrict: false
        };
    }

    return {
        isValid: true,
        isMetroPath: false,
        skipDistrict: false
    };
}

/**
 * Extract provinces from WFS GeoJSON response
 */
function extractProvincesFromGeoJSON(geoJsonData: any): AdministrativeRegion[] {
    const provinces: AdministrativeRegion[] = [];
    const uniqueProvinces = new Map<string, AdministrativeRegion>();

    if (geoJsonData?.features) {
        geoJsonData.features.forEach((feature: any) => {
            const props = feature.properties;

            // Try different possible field names for province
            const provinceName = props.PROVINCE || props.province || props.Province ||
                props.NAME_1 || props.name_1 || props.ADM1_EN || props.adm1_en ||
                props.NAME || props.name || props.PROV_NAME || props.prov_name ||
                props.PROVNAME || props.provname;

            if (provinceName && !uniqueProvinces.has(provinceName)) {
                uniqueProvinces.set(provinceName, {
                    id: provinceName.toLowerCase().replace(/\s+/g, '_'),
                    name: provinceName,
                    code: props.PROV_CODE || props.prov_code || props.CODE || props.code,
                    properties: props
                });
            }
        });

        provinces.push(...Array.from(uniqueProvinces.values()));
    }

    // Sort provinces alphabetically
    provinces.sort((a, b) => a.name.localeCompare(b.name));
    return provinces;
}

/**
 * Extract provinces from WMS GetFeatureInfo response
 */
function extractProvincesFromFeatureInfo(featureInfoData: any): AdministrativeRegion[] {
    const provinces: AdministrativeRegion[] = [];
    const uniqueProvinces = new Map<string, AdministrativeRegion>();

    // Handle different response formats
    let features: any[] = [];

    if (featureInfoData?.features) {
        features = featureInfoData.features;
    } else if (Array.isArray(featureInfoData)) {
        features = featureInfoData;
    } else if (featureInfoData?.results) {
        features = featureInfoData.results;
    }

    features.forEach((feature: any) => {
        const props = feature.properties || feature;

        // Try different possible field names for province (based on actual data structure)
        const provinceName = props.adm1_en || props.ADM1_EN || // Primary field from test data
            props.PROVINCE || props.province || props.Province ||
            props.NAME_1 || props.name_1 ||
            props.NAME || props.name || props.PROV_NAME || props.prov_name ||
            props.PROVNAME || props.provname;

        if (provinceName && !uniqueProvinces.has(provinceName)) {
            uniqueProvinces.set(provinceName, {
                id: provinceName.toLowerCase().replace(/\s+/g, '_'),
                name: provinceName,
                code: props.PROV_CODE || props.prov_code || props.CODE || props.code,
                properties: props
            });
        }
    });

    provinces.push(...Array.from(uniqueProvinces.values()));
    provinces.sort((a, b) => a.name.localeCompare(b.name));
    return provinces;
}

/**
 * Extract municipalities from WFS GeoJSON response
 */
function extractMunicipalitiesFromGeoJSON(geoJsonData: any): AdministrativeRegion[] {
    const municipalities: AdministrativeRegion[] = [];
    const uniqueMunicipalities = new Map<string, AdministrativeRegion>();

    if (geoJsonData?.features) {
        geoJsonData.features.forEach((feature: any) => {
            const props = feature.properties;

            // Try different possible field names for municipality (based on actual data structure)
            const municipalityName = props.adm2_en || props.ADM2_EN || // Primary field from test data
                props.MUNICIPALITY || props.municipality || props.Municipality ||
                props.NAME_2 || props.name_2 ||
                props.MUNICNAME || props.municname || props.MUN_NAME || props.mun_name ||
                props.NAME || props.name || props.LOCAL_MUNICIPALITY || props.local_municipality;

            if (municipalityName && !uniqueMunicipalities.has(municipalityName)) {
                uniqueMunicipalities.set(municipalityName, {
                    id: municipalityName.toLowerCase().replace(/\s+/g, '_'),
                    name: municipalityName,
                    code: props.MUN_CODE || props.mun_code || props.CODE || props.code,
                    properties: props
                });
            }
        });

        municipalities.push(...Array.from(uniqueMunicipalities.values()));
    }

    // Sort municipalities alphabetically
    municipalities.sort((a, b) => a.name.localeCompare(b.name));
    return municipalities;
}

/**
 * Extract municipalities from WMS GetFeatureInfo response
 */
function extractMunicipalitiesFromFeatureInfo(featureInfoData: any, provinceName?: string): AdministrativeRegion[] {
    const municipalities: AdministrativeRegion[] = [];
    const uniqueMunicipalities = new Map<string, AdministrativeRegion>();

    // Handle different response formats
    let features: any[] = [];

    if (featureInfoData?.features) {
        features = featureInfoData.features;
    } else if (Array.isArray(featureInfoData)) {
        features = featureInfoData;
    } else if (featureInfoData?.results) {
        features = featureInfoData.results;
    }

    features.forEach((feature: any) => {
        const props = feature.properties || feature;

        // Check if this municipality belongs to the specified province (if filtering)
        if (provinceName) {
            const featureProvince = props.adm1_en || props.ADM1_EN || // Primary field from test data
                props.PROVINCE || props.province || props.Province ||
                props.NAME_1 || props.name_1 ||
                props.PROV_NAME || props.prov_name;

            if (featureProvince !== provinceName) {
                return; // Skip this municipality if it doesn't match the province filter
            }
        }

        // Try different possible field names for municipality (based on actual data structure)
        const municipalityName = props.adm2_en || props.ADM2_EN || // Primary field from test data
            props.MUNICIPALITY || props.municipality || props.Municipality ||
            props.NAME_2 || props.name_2 ||
            props.MUNICNAME || props.municname || props.MUN_NAME || props.mun_name ||
            props.NAME || props.name || props.LOCAL_MUNICIPALITY || props.local_municipality;

        if (municipalityName && !uniqueMunicipalities.has(municipalityName)) {
            uniqueMunicipalities.set(municipalityName, {
                id: municipalityName.toLowerCase().replace(/\s+/g, '_'),
                name: municipalityName,
                code: props.MUN_CODE || props.mun_code || props.CODE || props.code,
                properties: props
            });
        }
    });

    municipalities.push(...Array.from(uniqueMunicipalities.values()));
    municipalities.sort((a, b) => a.name.localeCompare(b.name));
    return municipalities;
}

/**
 * Extract districts from municipal boundaries data
 */
function extractDistrictsFromMunicipalData(geoJsonData: any, provinceName?: string): DistrictInfo[] {
    const districts: DistrictInfo[] = [];
    const districtMap = new Map<string, DistrictInfo>();

    if (geoJsonData?.features) {
        geoJsonData.features.forEach((feature: any) => {
            const props = feature.properties;

            // Get municipality info
            const municipalityName = props.adm2_en || props.ADM2_EN;
            const districtCode = props.adm2_id || props.ADM2_ID;
            const province = props.adm1_en || props.ADM1_EN;

            if (districtCode && municipalityName) {
                // Determine district type and name based on code pattern
                let districtName = '';
                let districtType: 'district_municipality' | 'metropolitan_municipality' = 'district_municipality';

                if (districtCode.startsWith('DC')) {
                    // District Council (e.g., DC44 -> "District Council 44")
                    const dcNumber = districtCode.substring(2);
                    districtName = `District Council ${dcNumber}`;
                    districtType = 'district_municipality';
                } else if (['JHB', 'TSH', 'EKU', 'CPT', 'ETH', 'MAN', 'BUF', 'NMA'].includes(districtCode)) {
                    // Metropolitan municipalities (no district level)
                    districtName = `${municipalityName} Metropolitan`;
                    districtType = 'metropolitan_municipality';
                } else {
                    // Other patterns - treat as district municipality
                    districtName = `District ${districtCode}`;
                    districtType = 'district_municipality';
                }

                // Create or update district entry
                if (!districtMap.has(districtCode)) {
                    districtMap.set(districtCode, {
                        id: districtCode.toLowerCase(),
                        name: districtName,
                        code: districtCode,
                        type: districtType,
                        municipalities: [],
                        isMetro: districtType === 'metropolitan_municipality'
                    });
                }

                // Add municipality to district
                const district = districtMap.get(districtCode)!;
                if (!district.municipalities.includes(municipalityName)) {
                    district.municipalities.push(municipalityName);
                }
            }
        });

        districts.push(...Array.from(districtMap.values()));
    }

    // Sort districts by name
    districts.sort((a, b) => a.name.localeCompare(b.name));
    return districts;
}

/**
 * Extract wards from place names data
 */
function extractWardsFromPlaceNames(geoJsonData: any, municipalityFilter?: string): WardInfo[] {
    const wards: WardInfo[] = [];
    const uniqueWards = new Map<string, WardInfo>();

    if (geoJsonData?.features) {
        geoJsonData.features.forEach((feature: any) => {
            const props = feature.properties;
            const geometry = feature.geometry;

            // Get ward info from place names
            const placeName = props.placename;
            const tlc = props.tlc; // Transitional Local Council
            const coordinates = geometry?.coordinates;

            if (tlc && placeName && coordinates) {
                // Create unique ward ID based on TLC
                const wardId = tlc.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');

                if (!uniqueWards.has(wardId)) {
                    uniqueWards.set(wardId, {
                        id: wardId,
                        name: tlc,
                        tlc: tlc,
                        coordinates: [coordinates[0], coordinates[1]]
                    });
                }
            }
        });

        wards.push(...Array.from(uniqueWards.values()));
    }

    // Sort wards by name
    wards.sort((a, b) => a.name.localeCompare(b.name));
    return wards;
}

/**
 * Extract wards from official SA wards 2020 layer
 */
function extractWardsFromSALayer(geoJsonData: any): WardInfo[] {
    const wards: WardInfo[] = [];

    if (!geoJsonData?.features) {
        return wards;
    }

    geoJsonData.features.forEach((feature: any) => {
        const props = feature.properties;
        const geometry = feature.geometry;

        // Extract ward information from SA wards layer
        const wardId = props.wardid;
        const wardLabel = props.wardlabel;
        const wardNo = props.wardno;
        const municipality = props.municipali; // Note: truncated field name
        const municipalityCode = props.cat_b;
        const district = props.district;
        const districtCode = props.districtco;
        const province = props.province;

        // Get center coordinates from geometry
        let coordinates: [number, number] = [0, 0];
        if (geometry?.coordinates) {
            // For MultiPolygon, get first polygon's first ring's first coordinate
            if (geometry.type === 'MultiPolygon' && geometry.coordinates[0]?.[0]?.[0]) {
                coordinates = [geometry.coordinates[0][0][0][0], geometry.coordinates[0][0][0][1]];
            } else if (geometry.type === 'Polygon' && geometry.coordinates[0]?.[0]) {
                coordinates = [geometry.coordinates[0][0][0], geometry.coordinates[0][0][1]];
            }
        }

        if (wardId && wardLabel) {
            wards.push({
                id: wardId,
                name: `Ward ${wardNo} (${wardLabel})`,
                tlc: municipality || 'Unknown Municipality',
                coordinates: coordinates
            } as WardInfo);
        }
    });

    // Sort wards by ward number
    wards.sort((a, b) => {
        // Extract ward number from name for sorting
        const aMatch = a.name.match(/Ward (\d+)/);
        const bMatch = b.name.match(/Ward (\d+)/);
        const aWardNo = aMatch ? parseInt(aMatch[1]) : 0;
        const bWardNo = bMatch ? parseInt(bMatch[1]) : 0;
        return aWardNo - bWardNo;
    });

    return wards;
}

const router = Router();

/**
 * @swagger
 * /api/ows/capabilities:
 *   get:
 *     summary: Get WMS capabilities from GeoServer
 *     tags: [OWS]
 *     responses:
 *       200:
 *         description: WMS capabilities XML document
 *         content:
 *           application/xml:
 *             schema:
 *               type: string
 *       500:
 *         description: Failed to fetch capabilities
 */
router.get('/capabilities', getCapabilities);

// WMS GetCapabilities
// router.get('/capabilities', cacheMiddleware(300), getCapabilities);

/**
 * @swagger
 * /api/ows/layer-metadata/{layerName}:
 *   get:
 *     summary: Get metadata for a specific layer
 *     tags: [OWS]
 *     parameters:
 *       - in: path
 *         name: layerName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the layer to get metadata for
 *     responses:
 *       200:
 *         description: Layer metadata information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 name:
 *                   type: string
 *                 title:
 *                   type: string
 *                 abstract:
 *                   type: string
 *                 queryable:
 *                   type: boolean
 *                 temporal:
 *                   type: boolean
 *       404:
 *         description: Layer not found
 *       500:
 *         description: Failed to fetch layer metadata
 */
// Individual layer metadata endpoint
router.get('/layer-metadata/:layerName', cacheMiddleware(300), getLayerMetadata);

/**
 * @swagger
 * /api/ows/styles/{layerName}:
 *   get:
 *     summary: Get available styles for a specific layer
 *     tags: [OWS]
 *     parameters:
 *       - in: path
 *         name: layerName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the layer to get styles for
 *     responses:
 *       200:
 *         description: Layer styles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 layerName:
 *                   type: string
 *                 defaultStyle:
 *                   type: string
 *                 styles:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                       title:
 *                         type: string
 *                       abstract:
 *                         type: string
 *                       legendUrl:
 *                         type: string
 *                       isDefault:
 *                         type: boolean
 *                 legendUrl:
 *                   type: string
 *       404:
 *         description: Layer not found
 *       500:
 *         description: Failed to fetch layer styles
 */
// Layer styles endpoint
router.get('/styles/:layerName', cacheMiddleware(300), getLayerStyles);

/**
 * @swagger
 * /api/ows/wfs-proxy:
 *   get:
 *     summary: Proxy WFS requests to GeoServer
 *     tags: [OWS]
 *     description: Handles WFS GetCapabilities, DescribeFeatureType, and GetFeature requests
 *     parameters:
 *       - in: query
 *         name: SERVICE
 *         schema:
 *           type: string
 *           default: WFS
 *         description: OGC service type
 *       - in: query
 *         name: REQUEST
 *         required: true
 *         schema:
 *           type: string
 *           enum: [GetCapabilities, DescribeFeatureType, GetFeature]
 *         description: WFS request type
 *       - in: query
 *         name: VERSION
 *         schema:
 *           type: string
 *           default: "1.0.0"
 *         description: WFS version
 *       - in: query
 *         name: typeName
 *         schema:
 *           type: string
 *         description: Feature type name (for DescribeFeatureType and GetFeature)
 *       - in: query
 *         name: outputFormat
 *         schema:
 *           type: string
 *           enum: [application/json, application/xml, json]
 *         description: Output format for the response
 *       - in: query
 *         name: maxFeatures
 *         schema:
 *           type: integer
 *         description: Maximum number of features to return
 *       - in: query
 *         name: CQL_FILTER
 *         schema:
 *           type: string
 *         description: CQL filter expression
 *     responses:
 *       200:
 *         description: WFS response (XML or JSON based on outputFormat)
 *         content:
 *           application/xml:
 *             schema:
 *               type: string
 *           application/json:
 *             schema:
 *               type: object
 *       400:
 *         description: Invalid WFS request parameters
 *       500:
 *         description: Failed to fetch WFS data
 */
// WFS Proxy for GetCapabilities, DescribeFeatureType, and GetFeature requests
router.get('/wfs-proxy', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';

        // Ensure we have the required WFS service parameter
        const params = { ...req.query };
        if (!params.SERVICE) {
            params.SERVICE = 'WFS';
        }

        console.log('WFS Proxy request:', params);

        // Determine response type based on request type and output format
        let responseType: 'stream' | 'json' | 'text' = 'text';
        let acceptHeader = 'application/xml, text/xml';

        if (params.outputFormat === 'application/json' || params.outputFormat === 'json') {
            responseType = 'json';
            acceptHeader = 'application/json';
        }

        // Use /geonode/ows endpoint for WFS requests to match workspace
        const response = await secureGet(`${geoserverUrl}/geonode/ows`, {
            params,
            responseType,
            headers: {
                'Accept': acceptHeader
            }
        });

        console.log('WFS response received, content-type:', response.headers['content-type']);

        // Handle different response types
        if (responseType === 'json') {
            res.setHeader('Content-Type', 'application/json');
            res.json(response.data);
        } else {
            // Handle XML responses (GetCapabilities, DescribeFeatureType)
            res.setHeader('Content-Type', response.headers['content-type'] || 'application/xml');
            res.send(response.data);
        }

    } catch (error) {
        console.error('Error proxying WFS request:', error);
        if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as any;
            console.error('GeoServer response status:', axiosError.response?.status);
            console.error('GeoServer response data:', axiosError.response?.data);
        }
        res.status(500).json({ error: 'Failed to fetch WFS data' });
    }
});



/**
 * @swagger
 * /api/ows/wms-proxy:
 *   get:
 *     summary: Proxy WMS requests to GeoServer
 *     tags: [OWS]
 *     description: Handles WMS GetMap, GetFeatureInfo, and GetLegendGraphic requests
 *     parameters:
 *       - in: query
 *         name: SERVICE
 *         schema:
 *           type: string
 *           default: WMS
 *         description: OGC service type
 *       - in: query
 *         name: REQUEST
 *         required: true
 *         schema:
 *           type: string
 *           enum: [GetMap, GetFeatureInfo, GetLegendGraphic]
 *         description: WMS request type
 *       - in: query
 *         name: VERSION
 *         schema:
 *           type: string
 *           default: "1.1.1"
 *         description: WMS version
 *       - in: query
 *         name: LAYERS
 *         schema:
 *           type: string
 *         description: Comma-separated list of layers
 *       - in: query
 *         name: STYLES
 *         schema:
 *           type: string
 *         description: Comma-separated list of styles
 *       - in: query
 *         name: FORMAT
 *         schema:
 *           type: string
 *           default: image/png
 *         description: Output format
 *       - in: query
 *         name: TRANSPARENT
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Whether the background should be transparent
 *       - in: query
 *         name: SRS
 *         schema:
 *           type: string
 *           default: EPSG:4326
 *         description: Spatial reference system
 *       - in: query
 *         name: BBOX
 *         schema:
 *           type: string
 *         description: Bounding box (minx,miny,maxx,maxy)
 *       - in: query
 *         name: WIDTH
 *         schema:
 *           type: integer
 *         description: Width of the output image
 *       - in: query
 *         name: HEIGHT
 *         schema:
 *           type: integer
 *         description: Height of the output image
 *       - in: query
 *         name: TIME
 *         schema:
 *           type: string
 *         description: Time dimension for temporal layers
 *     responses:
 *       200:
 *         description: WMS response (image or JSON based on request type)
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *           image/jpeg:
 *             schema:
 *               type: string
 *               format: binary
 *           application/json:
 *             schema:
 *               type: object
 *       400:
 *         description: Invalid WMS request parameters
 *       500:
 *         description: Failed to fetch WMS data
 */
// WMS Proxy for thumbnails, GetMap, and GetFeatureInfo requests
router.get('/wms-proxy', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';

        // Ensure we have the required WMS service parameter for various request types
        const params = { ...req.query };
        if (params.REQUEST === 'GetLegendGraphic' && !params.SERVICE) {
            params.SERVICE = 'WMS';
        }
        if (params.REQUEST === 'GetMap' && !params.SERVICE) {
            params.SERVICE = 'WMS';
        }
        if (params.REQUEST === 'GetFeatureInfo' && !params.SERVICE) {
            params.SERVICE = 'WMS';
        }

        // Check if this is a request for a remote layer
        const layerName = params.LAYERS || params.LAYER;
        if (layerName && typeof layerName === 'string') {
            try {
                // Fetch all datasets to check for remote layers
                const datasetsResponse = await fetchDatasets();
                const datasets = datasetsResponse.datasets || [];
                const allLayers = datasets.map(datasetToLayerDiscovery);

                // Find the layer in our dataset
                const layer = allLayers.find(l => l.name === layerName);

                // Also try to find by original name without workspace prefix
                const layerByOriginalName = !layer ? allLayers.find(l => {
                    const originalName = l.name.includes(':') ? l.name.split(':').pop() : l.name;
                    const searchName = layerName.includes(':') ? layerName.split(':').pop() : layerName;
                    return originalName === searchName;
                }) : null;

                const foundLayer = layer || layerByOriginalName;

                if (foundLayer && foundLayer.isRemote) {

                    // Handle remote layer request
                    const remoteLayerInfo: RemoteLayerInfo = {
                        name: foundLayer.name,
                        title: foundLayer.title,
                        serviceType: foundLayer.serviceType,
                        remoteUrl: foundLayer.remoteUrl,
                        isRemote: true
                    };

                    const requestType = params.REQUEST as 'GetMap' | 'GetLegendGraphic' | 'GetCapabilities';
                    const remoteResponse = await handleRemoteLayerRequest(remoteLayerInfo, requestType, params as Record<string, string>);

                    // Forward the remote response
                    res.setHeader('Content-Type', remoteResponse.headers.get('content-type') || 'image/png');

                    // Convert response to buffer and send
                    const buffer = await remoteResponse.arrayBuffer();
                    res.send(Buffer.from(buffer));
                    return;
                } else if (foundLayer) {
                    // Local layer found, continue with normal processing
                } else {
                    console.log(`Layer "${layerName}" not found in datasets`);
                }
            } catch (remoteError) {
                console.warn(`Could not check remote layer status for "${layerName}":`, remoteError);
                // Continue with local processing
            }
        }

        // Determine response type based on request type
        let responseType: 'stream' | 'json' = 'stream';
        let acceptHeader = 'image/*';

        if (params.REQUEST === 'GetFeatureInfo') {
            responseType = 'json';
            acceptHeader = 'application/json, text/html, text/plain';
        }

        // Handle workspace prefixes for geonode layers
        let targetUrl = `${geoserverUrl}/geonode/ows`;
        const processedParams = { ...params };

        // Check LAYERS (GetMap), layers, or LAYER (GetLegendGraphic) parameters
        if (processedParams.LAYERS || processedParams.layers || processedParams.LAYER) {
            const layersParam = processedParams.LAYERS || processedParams.layers || processedParams.LAYER;
            if (typeof layersParam === 'string') {
                if (layersParam.startsWith('geonode:')) {
                    // Strip the workspace prefix and use workspace endpoint
                    const cleanLayerName = layersParam.replace('geonode:', '');
                    if (processedParams.LAYERS) processedParams.LAYERS = cleanLayerName;
                    if (processedParams.layers) processedParams.layers = cleanLayerName;
                    if (processedParams.LAYER) processedParams.LAYER = cleanLayerName;
                    targetUrl = `${geoserverUrl}/geonode/wms`;
                    console.log(`Stripped workspace prefix: ${layersParam} -> ${cleanLayerName}, using workspace endpoint`);
                } else {
                    // For layers without workspace prefix, check if they're likely geonode layers
                    // Common geonode layer patterns
                    const geonodeLayerPatterns = [
                        'africa_mosaic_optmised',
                        'flood_risk_layer_1m',
                        'damage_analysis',
                        'flood_affected_structures_clean',
                        'flood_plain_100yr'
                    ];

                    if (geonodeLayerPatterns.includes(layersParam)) {
                        targetUrl = `${geoserverUrl}/geonode/wms`;
                        console.log(`Detected geonode layer without prefix: ${layersParam}, using workspace endpoint`);
                    }
                }
            }
        }

        // For GetMap requests, use the specialized streaming handler
        let response;
        if (processedParams.REQUEST === 'GetMap') {
            response = await streamSecureWmsRequest(targetUrl, processedParams);
        } else {
            // For other requests (GetFeatureInfo, etc.), use the regular handler
            response = await secureGet(targetUrl, {
                params: processedParams,
                responseType,
                headers: {
                    'Accept': acceptHeader
                }
            })
                .then(response => {
                    console.log(`WMS Response received: ${response.status} ${response.statusText}`);
                    return response;
                })
                .catch(error => {
                    console.error(`WMS Request failed:`, error.message);
                    if (error.response) {
                        console.error(`Status: ${error.response.status}`);
                        console.error(`Headers: ${JSON.stringify(error.response.headers)}`);
                    }
                    throw error; // Re-throw to handle in the outer catch
                });
        }

        // Handle GetFeatureInfo responses differently
        if (params.REQUEST === 'GetFeatureInfo') {
            res.setHeader('Content-Type', response.headers['content-type'] || 'application/json');
            res.json(response.data);
        } else {
            // Forward the content type for image responses
            const contentType = response.headers['content-type'] || 'image/jpeg';
            res.setHeader('Content-Type', contentType);

            // Check if we have a proper stream
            if (response.data && typeof response.data.pipe === 'function') {
                response.data.pipe(res);
            } else {
                console.error(`WMS Response data is not a stream:`, typeof response.data);
                res.status(500).json({ error: 'Invalid response from GeoServer' });
            }
        }
    } catch (error) {
        console.error('Error proxying WMS request:', error);
        if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as any;
            console.error('GeoServer response status:', axiosError.response?.status);
            console.error('GeoServer response data:', axiosError.response?.data);
        }
        res.status(500).json({ error: 'Failed to fetch WMS data' });
    }
});

/**
 * @swagger
 * /api/ows/legend:
 *   get:
 *     summary: Get legend graphic for a layer
 *     tags: [OWS]
 *     description: Simplified endpoint for GetLegendGraphic requests
 *     parameters:
 *       - in: query
 *         name: layer
 *         required: true
 *         schema:
 *           type: string
 *         description: Layer name
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           default: image/png
 *           enum: [image/png, image/jpeg, image/gif]
 *         description: Output format
 *     responses:
 *       200:
 *         description: Legend graphic image
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *           image/jpeg:
 *             schema:
 *               type: string
 *               format: binary
 *           image/gif:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Missing required parameters
 *       500:
 *         description: Failed to fetch legend graphic
 */
// Legend endpoint specifically for GetLegendGraphic requests
router.get('/legend', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const { layer, format = 'image/png' } = req.query;

        if (!layer) {
            return res.status(400).json({ error: 'Layer parameter is required' });
        }

        const params = {
            SERVICE: 'WMS',
            REQUEST: 'GetLegendGraphic',
            LAYER: layer,
            FORMAT: format,
            VERSION: '1.1.1'
        };

        const response = await secureGet(`${geoserverUrl}/geonode/ows`, {
            params,
            responseType: 'stream',
            headers: {
                'Accept': 'image/*'
            }
        });

        // Forward the content type
        res.setHeader('Content-Type', response.headers['content-type'] || format);
        response.data.pipe(res);
    } catch (error) {
        console.error('Error fetching legend graphic:', error);
        if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as any;
            console.error('GeoServer legend response status:', axiosError.response?.status);
            console.error('GeoServer legend response data:', axiosError.response?.data);
        }
        res.status(500).json({ error: 'Failed to fetch legend graphic' });
    }
});

router.get('/layers', cacheMiddleware(300), getCapabilities);

/**
 * @swagger
 * /api/ows/download:
 *   get:
 *     summary: Download spatial data as shapefile
 *     tags: [OWS]
 *     description: Export spatial data in shapefile format with optional temporal and spatial filtering
 *     parameters:
 *       - in: query
 *         name: typeName
 *         required: true
 *         schema:
 *           type: string
 *         description: Feature type name to download
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for temporal filtering (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for temporal filtering (YYYY-MM-DD)
 *       - in: query
 *         name: bbox
 *         schema:
 *           type: string
 *         description: Bounding box for spatial filtering (minx,miny,maxx,maxy)
 *     responses:
 *       200:
 *         description: Shapefile ZIP archive
 *         content:
 *           application/zip:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Disposition:
 *             description: Attachment filename
 *             schema:
 *               type: string
 *               example: "attachment; filename=flood-data.zip"
 *       400:
 *         description: Missing required parameters
 *       500:
 *         description: Failed to download data
 */
// Download route for shapefile exports
/**
 * Helper function to download a single layer with AOI clipping
 */
async function downloadSingleLayer(
    layerName: string,
    options: {
        startDate?: string;
        endDate?: string;
        bbox?: string;
        format?: string;
        geometry?: string;
        cql_filter?: string;
        tempDir: string;
    }
): Promise<string | null> {
    const { startDate, endDate, bbox, format = 'shapefile', geometry, cql_filter, tempDir } = options;

    console.log(`🔽 Downloading single layer: ${layerName}`);

    // Format configuration
    const formatConfig = {
        'geojson': { outputFormat: 'application/json', extension: '.geojson', service: 'WFS' },
        'kml': { outputFormat: 'application/vnd.google-earth.kml+xml', extension: '.kml', service: 'WFS' },
        'csv': { outputFormat: 'csv', extension: '.csv', service: 'WFS' },
        'shapefile': { outputFormat: 'SHAPE-ZIP', extension: '.zip', service: 'WFS' },
        'tiff': { outputFormat: 'image/tiff', extension: '.tif', service: 'WCS' }
    };

    const config = formatConfig[format as keyof typeof formatConfig] || formatConfig['shapefile'];
    const filename = `${layerName.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}${config.extension}`;
    const filePath = path.join(tempDir, filename);

    // Detect if this is actually a raster layer that should use WCS
    const isRasterLayer = layerName.includes('mosaic') || layerName.includes('rgb') || layerName.includes('infrared') || format === 'tiff';
    const useWCS = isRasterLayer && format === 'tiff';

    // Build WFS/WCS parameters
    const params: any = {
        service: useWCS ? 'WCS' : 'WFS',
        version: '1.0.0',
        request: useWCS ? 'GetCoverage' : 'GetFeature',
        outputFormat: config.outputFormat,
        bbox
    };

    if (useWCS) {
        // WCS parameters for raster data
        params.coverage = layerName;
        params.format = 'image/tiff';
        params.crs = 'EPSG:4326';
        console.log(`🖼️ Using WCS for raster layer: ${layerName}`);
    } else {
        // WFS parameters for vector data
        params.typeName = layerName;
        params.format_options = `filename:${filename}`;
        console.log(`📊 Using WFS for vector layer: ${layerName}`);
    }

    // Add AOI clipping (only for WFS, not WCS)
    if (!useWCS && cql_filter) {
        params.CQL_FILTER = cql_filter;
        console.log(`✂️ Using provided CQL filter for ${layerName}`);
    } else if (!useWCS && geometry) {
        try {
            const geomObj = JSON.parse(String(geometry));
            // Convert GeoJSON to WKT for CQL filter
            const wkt = convertGeoJSONToWKT(geomObj);
            const geometryField = 'the_geom';
            params.CQL_FILTER = `INTERSECTS(${geometryField}, ${wkt})`;
            console.log(`✂️ Generated CQL filter from geometry for ${layerName}`);
        } catch (error) {
            console.warn(`❌ Failed to parse geometry for ${layerName}, using BBOX:`, error);
        }
    }

    // Add temporal filtering only for layers that actually have temporal data
    // Most layers like saps_stations, boundaries, etc. are NOT temporal
    const temporalLayers = ['flood_risk', 'soil_moisture', 'mosaic']; // Add known temporal layers
    const isTemporalLayer = temporalLayers.some(temporal => layerName.toLowerCase().includes(temporal));

    if (startDate && endDate && isTemporalLayer) {
        const temporalFilter = `time DURING ${startDate}/${endDate}`;
        if (params.CQL_FILTER) {
            params.CQL_FILTER += ` AND ${temporalFilter}`;
        } else {
            params.CQL_FILTER = temporalFilter;
        }
        console.log(`⏰ Added temporal filter for temporal layer ${layerName}`);
    } else if (startDate && endDate) {
        console.log(`⏰ Skipping temporal filter for non-temporal layer ${layerName}`);
    }

    // Determine target URL based on layer workspace
    let targetUrl = `${process.env.GEOSERVER_URL || 'https://*************/geoserver'}/ows`;

    if (layerName.startsWith('geonode:')) {
        const cleanLayerName = layerName.replace('geonode:', '');
        params.typeName = cleanLayerName;
        params.coverage = cleanLayerName;
        targetUrl = `${process.env.GEOSERVER_URL || 'https://*************/geoserver'}/geonode/${useWCS ? 'wcs' : 'wfs'}`;
        console.log(`🔧 Using geonode workspace for ${layerName}`);
    }

    console.log(`🌐 Request URL: ${targetUrl}`);
    console.log(`📋 Request params:`, params);

    try {
        const response = await axios.get(targetUrl, {
            params,
            responseType: 'arraybuffer',
            timeout: 120000, // 2 minutes
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            })
        });

        console.log(`📊 Response for ${layerName}:`, {
            status: response.status,
            contentType: response.headers['content-type'],
            contentLength: response.headers['content-length'],
            dataSize: response.data.byteLength
        });

        // Check if response is empty or error
        if (response.data.byteLength < 500) {
            console.warn(`⚠️ Suspiciously small response for ${layerName}: ${response.data.byteLength} bytes`);

            // Try to parse as text to see if it's an error
            const textResponse = Buffer.from(response.data).toString('utf8');
            console.log(`📄 Small response content: ${textResponse.substring(0, 1000)}`);

            if (textResponse.includes('Exception') || textResponse.includes('Error') ||
                textResponse.includes('ServiceException') || textResponse.includes('<?xml')) {
                throw new Error(`GeoServer error response: ${textResponse.substring(0, 500)}`);
            }
        }

        // Additional check for XML error responses
        const contentType = response.headers['content-type'] || '';
        if (contentType.includes('xml') && !useWCS && format !== 'kml') {
            const textResponse = Buffer.from(response.data).toString('utf8');
            console.warn(`⚠️ Unexpected XML response for ${layerName} (expected ${config.outputFormat})`);
            console.log(`📄 XML response: ${textResponse.substring(0, 500)}`);

            if (textResponse.includes('Exception') || textResponse.includes('Error')) {
                throw new Error(`GeoServer XML error: ${textResponse.substring(0, 300)}`);
            }
        }

        // Write file to temp directory
        const fs = require('fs');
        fs.writeFileSync(filePath, response.data);

        console.log(`💾 Saved ${layerName} to: ${filePath} (${response.data.byteLength} bytes)`);
        return filePath;

    } catch (error) {
        console.error(`❌ Failed to download ${layerName}:`, error);
        return null;
    }
}

// Note: convertGeoJSONToWKT function is now defined in the clip endpoint section

/**
 * @swagger
 * /api/ows/multi-layer-download:
 *   get:
 *     summary: Multi-layer AOI download with clipping
 *     description: |
 *       Downloads multiple geospatial layers with Area of Interest (AOI) clipping and combines them into a single archive.
 *
 *       **Features:**
 *       - Multiple layer processing in parallel
 *       - AOI geometry-based clipping using CQL filters
 *       - Support for multiple output formats
 *       - Temporal filtering for time-enabled layers
 *       - Automatic error handling and recovery
 *       - ZIP archive creation with metadata
 *
 *       **Supported Formats:**
 *       - `geojson`: GeoJSON format for vector data
 *       - `kml`: KML format for Google Earth compatibility
 *       - `csv`: CSV format for tabular data
 *       - `shapefile`: ESRI Shapefile format (ZIP archive)
 *       - `tiff`: GeoTIFF format for raster data
 *     tags: [OWS]
 *     parameters:
 *       - name: layers
 *         in: query
 *         required: true
 *         description: Comma-separated list of layer names to download
 *         schema:
 *           type: string
 *           example: "geonode:saps_stations,geonode:south_africa_provincial_boundaries"
 *       - name: format
 *         in: query
 *         description: Output format for the data
 *         schema:
 *           type: string
 *           enum: [geojson, kml, csv, shapefile, tiff]
 *           default: shapefile
 *       - name: geometry
 *         in: query
 *         description: GeoJSON geometry for AOI clipping
 *         schema:
 *           type: string
 *           example: '{"type":"Polygon","coordinates":[[[18,-35],[33,-35],[33,-22],[18,-22],[18,-35]]]}'
 *       - name: bbox
 *         in: query
 *         description: Bounding box for spatial filtering (minx,miny,maxx,maxy)
 *         schema:
 *           type: string
 *           example: "18.0,-35.0,33.0,-22.0"
 *       - name: startDate
 *         in: query
 *         description: Start date for temporal filtering (ISO 8601)
 *         schema:
 *           type: string
 *           format: date
 *           example: "2024-01-01"
 *       - name: endDate
 *         in: query
 *         description: End date for temporal filtering (ISO 8601)
 *         schema:
 *           type: string
 *           format: date
 *           example: "2024-12-31"
 *       - name: cql_filter
 *         in: query
 *         description: Custom CQL filter for advanced spatial/attribute filtering
 *         schema:
 *           type: string
 *           example: "INTERSECTS(the_geom, POLYGON((18 -35, 33 -35, 33 -22, 18 -22, 18 -35)))"
 *       - name: aoiName
 *         in: query
 *         description: Name for the AOI (used in filename)
 *         schema:
 *           type: string
 *           example: "Eastern_Cape_Study_Area"
 *       - name: includeMetadata
 *         in: query
 *         description: Include metadata file in the download
 *         schema:
 *           type: boolean
 *           default: false
 *       - name: includePreview
 *         in: query
 *         description: Include preview images in the download
 *         schema:
 *           type: boolean
 *           default: false
 *     responses:
 *       200:
 *         description: Multi-layer data archive
 *         content:
 *           application/zip:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Disposition:
 *             description: Attachment filename
 *             schema:
 *               type: string
 *               example: 'attachment; filename="AOI_3_layers_2024-08-11.zip"'
 *       400:
 *         description: Invalid parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               missing_layers:
 *                 summary: Missing layers parameter
 *                 value:
 *                   success: false
 *                   error: "layers parameter is required"
 *               no_layers:
 *                 summary: No valid layers specified
 *                 value:
 *                   success: false
 *                   error: "At least one layer must be specified"
 *       500:
 *         description: Download processing error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               no_data:
 *                 summary: No layers could be downloaded
 *                 value:
 *                   success: false
 *                   error: "No layers could be downloaded"
 *                   details: ["Layer 'invalid_layer' not found", "CQL filter error for layer 'test_layer'"]
 *               processing_error:
 *                 summary: Archive creation failed
 *                 value:
 *                   success: false
 *                   error: "Multi-layer download failed"
 *                   message: "Archive creation error"
 */
/**
 * Multi-layer AOI download endpoint
 * Handles multiple layers with AOI clipping and stitching
 * Supports both GET and POST methods (POST for large geometry data)
 */
const handleMultiLayerDownload = async (req: any, res: any) => {
    try {
        // Handle both GET (query) and POST (body) parameters
        const params = req.method === 'POST' ? req.body : req.query;
        const {
            layers,
            startDate,
            endDate,
            bbox,
            format = 'shapefile',
            geometry,
            includeMetadata,
            includePreview,
            cql_filter,
            aoiName = 'AOI'
        } = params;

        console.log(`📥 Multi-layer download request (${req.method}):`, {
            layers: typeof layers === 'string' ? layers.substring(0, 100) + '...' : layers,
            format,
            geometrySize: geometry ? geometry.length : 0,
            method: req.method
        });

        console.log('🔽 Multi-layer download request received:', {
            layers: layers ? String(layers).split(',') : [],
            startDate,
            endDate,
            bbox,
            format,
            geometryProvided: !!geometry,
            cqlFilterProvided: !!cql_filter,
            aoiName
        });

        // Validate required parameters
        if (!layers) {
            return res.status(400).json({ error: 'layers parameter is required' });
        }

        const layerList = String(layers).split(',').map(l => l.trim()).filter(l => l.length > 0);

        if (layerList.length === 0) {
            return res.status(400).json({ error: 'At least one layer must be specified' });
        }

        // Import required modules for multi-layer processing
        const archiver = require('archiver');
        const path = require('path');
        const fs = require('fs');
        const os = require('os');

        // Create temporary directory for processing
        const tempDir = path.join(os.tmpdir(), `multi_layer_download_${Date.now()}`);
        fs.mkdirSync(tempDir, { recursive: true });

        console.log(`📁 Created temp directory: ${tempDir}`);

        // Process each layer
        const downloadedFiles: string[] = [];
        const errors: string[] = [];

        for (const layerName of layerList) {
            try {
                console.log(`🔽 Processing layer: ${layerName}`);

                const layerFile = await downloadSingleLayer(layerName, {
                    startDate: startDate as string,
                    endDate: endDate as string,
                    bbox: bbox as string,
                    format: format as string,
                    geometry: geometry as string,
                    cql_filter: cql_filter as string,
                    tempDir
                });

                if (layerFile) {
                    downloadedFiles.push(layerFile);
                    console.log(`✅ Successfully downloaded: ${layerName}`);
                } else {
                    errors.push(`Failed to download layer: ${layerName}`);
                    console.warn(`❌ Failed to download: ${layerName}`);
                }
            } catch (error) {
                const errorMsg = `Error downloading ${layerName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
                errors.push(errorMsg);
                console.error(`❌ ${errorMsg}`);
            }
        }

        // Check if we have any successful downloads
        if (downloadedFiles.length === 0) {
            // Cleanup temp directory
            fs.rmSync(tempDir, { recursive: true, force: true });
            return res.status(500).json({
                error: 'No layers could be downloaded',
                details: errors
            });
        }

        // Create final archive
        const archiveName = `${aoiName}_${layerList.length}_layers_${new Date().toISOString().split('T')[0]}.zip`;

        console.log(`📦 Creating archive: ${archiveName}`);
        console.log(`📦 Files to archive: ${downloadedFiles.length}`);

        // Set response headers
        res.setHeader('Content-Type', 'application/zip');
        res.setHeader('Content-Disposition', `attachment; filename="${archiveName}"`);

        // Create archive and stream to response
        const archive = archiver('zip', { zlib: { level: 9 } });

        archive.on('error', (err: Error) => {
            console.error('❌ Archive error:', err);
            if (!res.headersSent) {
                res.status(500).json({ error: 'Archive creation failed' });
            }
        });

        archive.on('end', () => {
            console.log('✅ Archive created successfully');
            // Cleanup temp directory
            setTimeout(() => {
                fs.rmSync(tempDir, { recursive: true, force: true });
                console.log(`🗑️ Cleaned up temp directory: ${tempDir}`);
            }, 1000);
        });

        // Pipe archive to response
        archive.pipe(res);

        // Add each downloaded file to the archive
        for (const filePath of downloadedFiles) {
            const fileName = path.basename(filePath);
            archive.file(filePath, { name: fileName });
        }

        // Add metadata file if requested
        if (includeMetadata) {
            const metadata = {
                downloadDate: new Date().toISOString(),
                aoiName,
                layers: layerList,
                dateRange: startDate && endDate ? { startDate, endDate } : null,
                bbox,
                format,
                successfulLayers: downloadedFiles.map(f => path.basename(f, path.extname(f))),
                errors: errors.length > 0 ? errors : null
            };

            archive.append(JSON.stringify(metadata, null, 2), { name: 'download_metadata.json' });
        }

        // Finalize the archive
        await archive.finalize();

    } catch (error) {
        console.error('❌ Multi-layer download error:', error);

        // Enhanced error logging for debugging
        if (error instanceof Error) {
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });
        }

        if (!res.headersSent) {
            let errorMessage = 'Multi-layer download failed';
            let statusCode = 500;

            // Provide more specific error messages
            if (error instanceof Error) {
                if (error.message.includes('Field value too long') || error.message.includes('MulterError')) {
                    errorMessage = 'Geometry data too large - please try with a smaller area or use GET method';
                    statusCode = 413; // Payload Too Large
                } else if (error.message.includes('timeout')) {
                    errorMessage = 'Download timed out - please try with fewer layers or smaller area';
                    statusCode = 408;
                } else if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
                    errorMessage = 'GeoServer connection failed - please try again later';
                    statusCode = 503;
                } else if (error.message.includes('Layer') && error.message.includes('not found')) {
                    errorMessage = 'One or more layers not found on GeoServer';
                    statusCode = 404;
                }
            }

            res.status(statusCode).json({
                error: errorMessage,
                message: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            });
        }
    }
};

// Register both GET and POST handlers for multi-layer download
router.get('/multi-layer-download', cacheMiddleware(60), handleMultiLayerDownload);
router.post('/multi-layer-download', handleMultiLayerDownload); // No cache for POST requests

// Legacy single-layer download endpoint - redirects to multi-layer for consistency
router.get('/download', cacheMiddleware(60), async (req, res) => {
    try {
        const { typeName, startDate, endDate, bbox, format = 'shapefile', geometry, includeMetadata, includePreview } = req.query;

        // Validate required parameters
        if (!typeName) {
            return res.status(400).json({ error: 'typeName parameter is required' });
        }

        console.log('🔄 Legacy /download endpoint called, redirecting to multi-layer-download for consistency');
        console.log('Legacy download request:', {
            typeName, startDate, endDate, bbox, format,
            includeMetadata, includePreview,
            geometryProvided: !!geometry
        });

        // Convert single layer request to multi-layer format
        const multiLayerParams = {
            layers: String(typeName), // Convert typeName to layers parameter
            startDate,
            endDate,
            bbox,
            format,
            geometry,
            includeMetadata,
            includePreview,
            aoiName: `${typeName}_download` // Generate AOI name from layer
        };

        // Forward to multi-layer handler with converted parameters
        const mockReq = {
            method: 'GET',
            query: multiLayerParams
        };

        console.log('🔄 Forwarding to multi-layer handler with params:', multiLayerParams);
        return await handleMultiLayerDownload(mockReq, res);

    } catch (error: any) {
        console.error('❌ Legacy download endpoint error:', error);
        res.status(500).json({
            error: 'Download failed',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
            note: 'This endpoint redirects to multi-layer-download for consistency'
        });
    }
});

/**
 * @swagger
 * /api/ows/feature-info:
 *   get:
 *     summary: Get feature information at a specific point
 *     tags: [OWS]
 *     description: Dedicated endpoint for WMS GetFeatureInfo requests
 *     parameters:
 *       - in: query
 *         name: layers
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated list of layers
 *       - in: query
 *         name: query_layers
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated list of queryable layers
 *       - in: query
 *         name: x
 *         required: true
 *         schema:
 *           type: integer
 *         description: X coordinate in pixels
 *       - in: query
 *         name: y
 *         required: true
 *         schema:
 *           type: integer
 *         description: Y coordinate in pixels
 *       - in: query
 *         name: width
 *         required: true
 *         schema:
 *           type: integer
 *         description: Width of the map in pixels
 *       - in: query
 *         name: height
 *         required: true
 *         schema:
 *           type: integer
 *         description: Height of the map in pixels
 *       - in: query
 *         name: bbox
 *         required: true
 *         schema:
 *           type: string
 *         description: Bounding box (minx,miny,maxx,maxy)
 *       - in: query
 *         name: srs
 *         schema:
 *           type: string
 *           default: EPSG:4326
 *         description: Spatial reference system
 *       - in: query
 *         name: crs
 *         schema:
 *           type: string
 *         description: Coordinate reference system (alternative to srs)
 *       - in: query
 *         name: info_format
 *         schema:
 *           type: string
 *           default: application/json
 *           enum: [application/json, text/html, text/plain]
 *         description: Format of the feature info response
 *       - in: query
 *         name: feature_count
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Maximum number of features to return
 *     responses:
 *       200:
 *         description: Feature information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *           text/html:
 *             schema:
 *               type: string
 *           text/plain:
 *             schema:
 *               type: string
 *       400:
 *         description: Missing required parameters
 *       500:
 *         description: Failed to fetch feature information
 */
// Dedicated GetFeatureInfo endpoint for queryable layers
router.get('/feature-info', async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const {
            layers,
            query_layers,
            x,
            y,
            width,
            height,
            bbox,
            srs,
            crs,
            info_format = 'application/json',
            feature_count = 10
        } = req.query;

        // Validate required parameters
        if (!layers || !query_layers || !x || !y || !width || !height || !bbox) {
            return res.status(400).json({
                error: 'Missing required parameters: layers, query_layers, x, y, width, height, bbox'
            });
        }

        const params = {
            SERVICE: 'WMS',
            REQUEST: 'GetFeatureInfo',
            VERSION: '1.1.1',
            LAYERS: layers,
            QUERY_LAYERS: query_layers,
            X: x,
            Y: y,
            WIDTH: width,
            HEIGHT: height,
            BBOX: bbox,
            SRS: srs || crs || 'EPSG:4326',
            INFO_FORMAT: info_format,
            FEATURE_COUNT: feature_count
        };

        const response = await axios.get(`${geoserverUrl}/ows`, {
            params,
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            headers: {
                'Accept': 'application/json, text/html, text/plain'
            }
        });

        res.setHeader('Content-Type', response.headers['content-type'] || 'application/json');
        res.json(response.data);
    } catch (error) {
        console.error('Error fetching feature info:', error);
        if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as any;
            console.error('GeoServer response status:', axiosError.response?.status);
            console.error('GeoServer response data:', axiosError.response?.data);
        }
        res.status(500).json({ error: 'Failed to fetch feature information' });
    }
});

/**
 * @swagger
 * /api/ows/aoi-screenshot:
 *   get:
 *     summary: Generate a screenshot of an area of interest
 *     tags: [OWS]
 *     description: Creates a map image for a specified area with selected layers
 *     parameters:
 *       - in: query
 *         name: bbox
 *         required: true
 *         schema:
 *           type: string
 *         description: Bounding box in format west,south,east,north
 *       - in: query
 *         name: layers
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated list of layers to include
 *       - in: query
 *         name: width
 *         schema:
 *           type: integer
 *           default: 800
 *         description: Width of the output image in pixels
 *       - in: query
 *         name: height
 *         schema:
 *           type: integer
 *           default: 600
 *         description: Height of the output image in pixels
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           default: png
 *           enum: [png, jpeg, gif]
 *         description: Output image format
 *       - in: query
 *         name: time
 *         schema:
 *           type: string
 *         description: Time value for temporal layers (ISO format or range)
 *       - in: query
 *         name: styles
 *         schema:
 *           type: string
 *           default: ""
 *         description: Comma-separated list of styles to apply
 *       - in: query
 *         name: srs
 *         schema:
 *           type: string
 *           default: EPSG:4326
 *         description: Spatial reference system
 *       - in: query
 *         name: transparent
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Whether the background should be transparent
 *     responses:
 *       200:
 *         description: Map image
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *           image/jpeg:
 *             schema:
 *               type: string
 *               format: binary
 *           image/gif:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Invalid parameters
 *       408:
 *         description: Screenshot generation timed out
 *       500:
 *         description: Failed to generate screenshot
 */
// AOI Screenshot endpoint for generating map images of selected areas
router.get('/aoi-screenshot', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';

        // Extract and validate parameters
        const {
            bbox,
            layers,
            width = '800',
            height = '600',
            format = 'png',
            time,
            styles = '',
            srs = 'EPSG:4326',
            cql_filter
        } = req.query;

        // Validate required parameters
        if (!bbox) {
            return res.status(400).json({ error: 'bbox parameter is required' });
        }
        if (!layers) {
            return res.status(400).json({ error: 'layers parameter is required' });
        }

        // Parse and validate bbox format (west,south,east,north)
        const bboxParts = (bbox as string).split(',');
        if (bboxParts.length !== 4) {
            return res.status(400).json({ error: 'bbox must be in format: west,south,east,north' });
        }

        // Validate numeric values
        const [west, south, east, north] = bboxParts.map(Number);
        if (isNaN(west) || isNaN(south) || isNaN(east) || isNaN(north)) {
            return res.status(400).json({ error: 'bbox coordinates must be numeric' });
        }

        // Validate bbox bounds
        if (west >= east || south >= north) {
            return res.status(400).json({ error: 'Invalid bbox: west must be < east and south must be < north' });
        }

        // Prepare WMS GetMap parameters
        const wmsParams: any = {
            SERVICE: 'WMS',
            REQUEST: 'GetMap',
            VERSION: '1.1.1',
            LAYERS: layers,
            BBOX: bbox,
            WIDTH: width,
            HEIGHT: height,
            FORMAT: `image/${format}`,
            SRS: srs,
            STYLES: styles,
            TRANSPARENT: req.query.transparent || 'true' // Allow frontend to control transparency
        };

        // Add temporal parameter if provided
        if (time) {
            wmsParams.TIME = time as string;
        }
        // Forward CQL_FILTER for polygon clipping if provided (semicolon separated per layer)
        if (cql_filter) {
            wmsParams.CQL_FILTER = cql_filter as string;
        }

        console.log('Generating AOI screenshot with params:', wmsParams);

        // Make request to GeoServer using the same workspace as capabilities
        // This ensures layer names match the workspace where they're actually located
        const response = await axios.get(`${geoserverUrl}/geonode/ows`, {
            params: wmsParams,
            responseType: 'stream',
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            headers: {
                'Accept': 'image/*'
            },
            timeout: 30000 // 30 second timeout for image generation
        });

        // Set appropriate headers
        res.setHeader('Content-Type', response.headers['content-type'] || `image/${format}`);
        res.setHeader('Cache-Control', 'public, max-age=300'); // 5 minute cache

        // Stream the image response
        response.data.pipe(res);

    } catch (error) {
        console.error('Error generating AOI screenshot:', error);

        if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as any;
            console.error('GeoServer response status:', axiosError.response?.status);
            console.error('GeoServer response data:', axiosError.response?.data);

            // Return appropriate error based on GeoServer response
            if (axiosError.response?.status === 400) {
                return res.status(400).json({ error: 'Invalid parameters for map generation' });
            } else if (axiosError.response?.status === 404) {
                return res.status(404).json({ error: 'One or more layers not found' });
            }
        }

        // Check for timeout errors
        if (error && typeof error === 'object' && 'code' in error && error.code === 'ECONNABORTED') {
            return res.status(408).json({ error: 'Screenshot generation timed out' });
        }

        res.status(500).json({ error: 'Failed to generate AOI screenshot' });
    }
});

/**
 * @swagger
 * /api/ows/administrative-boundaries/provinces:
 *   get:
 *     summary: Get all provinces from administrative boundary layers
 *     tags: [Administrative Boundaries]
 *     description: Cascades through WFS → WMS → REST API to get province list
 *     responses:
 *       200:
 *         description: Provinces retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       code:
 *                         type: string
 *                 source:
 *                   type: string
 *                   enum: [WFS, WMS, REST]
 *       500:
 *         description: All methods failed to retrieve provinces
 */
router.get('/administrative-boundaries/provinces', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const layerName = 'geonode:south_africa_provincial_boundaries';

        console.log('🏛️ Loading all 9 provinces (optimized approach)...');

        const wfsResponse = await axios.get(`${geoserverUrl}/ows`, {
            params: {
                SERVICE: 'WFS',
                REQUEST: 'GetFeature',
                typeName: layerName,
                version: '1.0.0',
                outputFormat: 'application/json',
                propertyName: 'adm1_en,adm1_pcode' // Only get needed fields, exclude geometry
            },
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            timeout: 15000
        });

        const provinces = extractProvincesFromGeoJSON(wfsResponse.data);

        console.log(`Successfully loaded ${provinces.length} provinces`);

        return res.json({
            success: true,
            data: provinces,
            source: 'WFS_OPTIMIZED',
            count: provinces.length,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Failed to load provinces:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve provinces',
            details: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
});


/**
 * @swagger
 * /api/ows/administrative-boundaries/districts:
 *   get:
 *     summary: Get districts, optionally filtered by province
 *     tags: [Administrative Boundaries]
 *     description: Extract districts from municipal boundaries data
 *     parameters:
 *       - in: query
 *         name: province
 *         schema:
 *           type: string
 *         description: Filter districts by province name
 *     responses:
 *       200:
 *         description: Districts retrieved successfully
 *       500:
 *         description: Failed to retrieve districts
 */
router.get('/administrative-boundaries/districts', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const layerName = 'geonode:south_africa_municipal_boundaries';
        const provinceName = req.query.province as string;

        console.log(`🏛️ Loading districts${provinceName ? ` for province: ${provinceName}` : ''}...`);

        // Get all municipal data to extract district patterns
        try {
            console.log('📡 Getting municipal data to extract districts...');

            const params: any = {
                SERVICE: 'WFS',
                REQUEST: 'GetFeature',
                typeName: layerName,
                version: '1.0.0',
                outputFormat: 'application/json',
                propertyName: 'province,district,municipali,cat_b,districtco' // Only get needed fields
            };

            // Add CQL filter for province if specified
            if (provinceName) {
                params.CQL_FILTER = `province='${provinceName}'`;
            }

            // Add province filter if specified
            if (provinceName) {
                params.CQL_FILTER = `adm1_en='${provinceName}'`;
            }

            const wfsResponse = await axios.get(`${geoserverUrl}/ows`, {
                params,
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: false
                }),
                timeout: 15000
            });

            // Use the new helper function to extract districts properly
            const { districtMunicipalities, metropolitanMunicipalities } = extractDistricts(wfsResponse.data.features);

            // Combine both types and sort
            const allDistricts = [...districtMunicipalities, ...metropolitanMunicipalities];
            allDistricts.sort((a, b) => a.name.localeCompare(b.name));

            console.log(`✅ Extracted ${allDistricts.length} districts from municipal data (${districtMunicipalities.length} district municipalities + ${metropolitanMunicipalities.length} metropolitan municipalities)`);

            return res.json({
                success: true,
                data: allDistricts,
                source: 'WFS_MUNICIPAL_EXTRACTION_ENHANCED',
                count: allDistricts.length,
                breakdown: {
                    districtMunicipalities: districtMunicipalities.length,
                    metropolitanMunicipalities: metropolitanMunicipalities.length,
                    total: allDistricts.length
                },
                filteredBy: provinceName || null,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('❌ Failed to extract districts:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to extract districts from municipal data',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }

    } catch (error) {
        console.error('Error in districts endpoint:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve districts',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

/**
 * @swagger
 * /api/ows/administrative-boundaries/municipalities:
 *   get:
 *     summary: Get municipalities, optionally filtered by province or district
 *     tags: [Administrative Boundaries]
 *     description: Cascades through WFS → WMS → REST API to get municipality list
 *     parameters:
 *       - in: query
 *         name: province
 *         schema:
 *           type: string
 *         description: Filter municipalities by province name
 *       - in: query
 *         name: district
 *         schema:
 *           type: string
 *         description: Filter municipalities by district code
 *     responses:
 *       200:
 *         description: Municipalities retrieved successfully
 *       500:
 *         description: All methods failed to retrieve municipalities
 */
router.get('/administrative-boundaries/municipalities', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const layerName = 'geonode:south_africa_municipal_boundaries';
        const provinceName = req.query.province as string;
        const districtCode = req.query.district as string;

        console.log(`🏛️ Loading municipalities${provinceName ? ` for province: ${provinceName}` : ''}${districtCode ? ` for district: ${districtCode}` : ''} using CQL filter...`);

        const params: any = {
            SERVICE: 'WFS',
            REQUEST: 'GetFeature',
            typeName: layerName,
            version: '1.0.0',
            outputFormat: 'application/json',
            propertyName: 'province,district,municipali,cat_b,districtco' // Only get needed fields
        };

        // Build CQL filter based on parameters
        const filters = [];
        if (provinceName) {
            filters.push(`province='${provinceName}'`);
        }
        if (districtCode) {
            filters.push(`districtco='${districtCode}'`);
        }
        if (filters.length > 0) {
            params.CQL_FILTER = filters.join(' AND ');
        }

        const wfsResponse = await axios.get(`${geoserverUrl}/ows`, {
            params,
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            timeout: 15000
        });

        // Extract municipalities from response
        const municipalities = extractMunicipalitiesFromGeoJSON(wfsResponse.data);

        console.log(`Successfully loaded ${municipalities.length} municipalities`);

        return res.json({
            success: true,
            data: municipalities,
            source: 'WFS_CQL_FILTERED',
            count: municipalities.length,
            filteredBy: { province: provinceName, district: districtCode },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Failed to load municipalities:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve municipalities',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

/**
 * @swagger
 * /api/ows/administrative-boundaries/test:
 *   get:
 *     summary: Test basic access to administrative boundary layers
 *     tags: [Administrative Boundaries]
 *     description: Simple test to verify layer access and get sample data
 *     responses:
 *       200:
 *         description: Test results
 */
router.get('/administrative-boundaries/test', async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';

        const testResults: Record<string, any> = {};

        // Test 1: Basic WFS GetCapabilities
        try {
            const capabilitiesResponse = await axios.get(`${geoserverUrl}/ows`, {
                params: {
                    SERVICE: 'WFS',
                    REQUEST: 'GetCapabilities',
                    version: '1.0.0'
                },
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: false
                }),
                timeout: 10000
            });

            testResults.wfsCapabilities = {
                success: true,
                status: capabilitiesResponse.status,
                hasData: capabilitiesResponse.data.length > 0
            };
        } catch (error) {
            testResults.wfsCapabilities = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }

        // Test 2: Municipal boundaries layer - get 1 feature
        try {
            const municipalResponse = await axios.get(`${geoserverUrl}/ows`, {
                params: {
                    SERVICE: 'WFS',
                    REQUEST: 'GetFeature',
                    typeName: 'geonode:south_africa_municipal_boundaries',
                    version: '1.0.0',
                    outputFormat: 'application/json',
                    maxFeatures: 1
                },
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: false
                }),
                timeout: 15000
            });

            const sampleFeature = municipalResponse.data?.features?.[0];
            testResults.municipalLayer = {
                success: true,
                status: municipalResponse.status,
                featureCount: municipalResponse.data?.features?.length || 0,
                sampleFields: sampleFeature ? Object.keys(sampleFeature.properties) : [],
                sampleProperties: sampleFeature?.properties || null
            };
        } catch (error) {
            testResults.municipalLayer = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                status: (error as any)?.response?.status,
                responseData: (error as any)?.response?.data
            };
        }

        // Test 3: Provincial boundaries layer - get 1 feature
        try {
            const provincialResponse = await axios.get(`${geoserverUrl}/ows`, {
                params: {
                    SERVICE: 'WFS',
                    REQUEST: 'GetFeature',
                    typeName: 'geonode:south_africa_provincial_boundaries',
                    version: '1.0.0',
                    outputFormat: 'application/json',
                    maxFeatures: 1
                },
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: false
                }),
                timeout: 15000
            });

            const sampleFeature = provincialResponse.data?.features?.[0];
            testResults.provincialLayer = {
                success: true,
                status: provincialResponse.status,
                featureCount: provincialResponse.data?.features?.length || 0,
                sampleFields: sampleFeature ? Object.keys(sampleFeature.properties) : [],
                sampleProperties: sampleFeature?.properties || null
            };
        } catch (error) {
            testResults.provincialLayer = {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                status: (error as any)?.response?.status,
                responseData: (error as any)?.response?.data
            };
        }

        res.json({
            success: true,
            geoserverUrl,
            testResults,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Error in administrative boundaries test:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to run administrative boundaries test',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

/**
 * @swagger
 * /api/ows/administrative-boundaries/debug-municipalities:
 *   get:
 *     summary: Debug municipalities data structure
 *     tags: [Administrative Boundaries]
 *     description: Get raw municipality data to debug field structure
 *     parameters:
 *       - in: query
 *         name: province
 *         schema:
 *           type: string
 *         description: Filter by province name
 *       - in: query
 *         name: maxFeatures
 *         schema:
 *           type: integer
 *           default: 5
 *         description: Maximum features to return
 *     responses:
 *       200:
 *         description: Raw municipality data for debugging
 */
router.get('/administrative-boundaries/debug-municipalities', async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const layerName = 'geonode:south_africa_municipal_boundaries';
        const provinceName = req.query.province as string;
        const maxFeatures = parseInt(req.query.maxFeatures as string) || 5;

        console.log(`🔍 Debug municipalities: province=${provinceName}, maxFeatures=${maxFeatures}`);

        // Try different output formats to see which one works
        const outputFormats = ['application/json', 'json', 'text/javascript'];
        let workingParams: any = null;
        let workingResponse: any = null;

        for (const format of outputFormats) {
            try {
                const params: any = {
                    SERVICE: 'WFS',
                    REQUEST: 'GetFeature',
                    typeName: layerName,
                    version: '1.0.0',
                    outputFormat: format,
                    maxFeatures
                };

                // Add province filter if specified (simplified to use only the correct field)
                if (provinceName) {
                    params.CQL_FILTER = `adm1_en='${provinceName}'`;
                }
                const testResponse = await axios.get(`${geoserverUrl}/ows`, {
                    params,
                    httpsAgent: new (require('https').Agent)({
                        rejectUnauthorized: false
                    }),
                    timeout: 10000
                });

                // Check if this format gives us proper GeoJSON
                if (testResponse.data?.features && Array.isArray(testResponse.data.features)) {
                    console.log(`✅ Format ${format} works! Found ${testResponse.data.features.length} features`);
                    workingParams = params;
                    workingResponse = testResponse;
                    break;
                } else {
                    console.log(` Format ${format} failed - no features array`);
                }

            } catch (formatError) {
                console.log(` Format ${format} error:`, formatError instanceof Error ? formatError.message : 'Unknown error');
            }
        }

        if (!workingResponse) {
            throw new Error('All output formats failed to return proper GeoJSON');
        }

        const params = workingParams;
        const wfsResponse = workingResponse;

        const responseData = wfsResponse.data;

        // Debug: Log raw response info
        console.log('🔍 Raw WFS Response Info:', {
            status: wfsResponse.status,
            contentType: wfsResponse.headers['content-type'],
            dataType: typeof responseData,
            isArray: Array.isArray(responseData),
            dataKeys: Object.keys(responseData || {}).slice(0, 10), // First 10 keys
            dataLength: Array.isArray(responseData) ? responseData.length : 'not array'
        });

        res.json({
            success: true,
            debug: {
                requestParams: params,
                responseStructure: {
                    hasFeatures: !!responseData?.features,
                    featureCount: responseData?.features?.length || 0,
                    responseKeys: Object.keys(responseData || {}),
                    type: responseData?.type,
                    crs: responseData?.crs
                },
                sampleFeatures: responseData?.features?.slice(0, 3).map((feature: any) => ({
                    properties: feature.properties,
                    geometryType: feature.geometry?.type
                })) || [],
                extractionTest: {
                    foundMunicipalities: extractMunicipalitiesFromGeoJSON(responseData).length,
                    fieldAnalysis: responseData?.features?.[0] ? {
                        allFields: Object.keys(responseData.features[0].properties || {}),
                        adm2_en: responseData.features[0].properties?.adm2_en,
                        adm1_en: responseData.features[0].properties?.adm1_en,
                        municipalityFieldCandidates: Object.keys(responseData.features[0].properties || {}).filter((key: string) =>
                            key.toLowerCase().includes('mun') ||
                            key.toLowerCase().includes('adm2') ||
                            key.toLowerCase().includes('municipality')
                        )
                    } : null
                }
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Error in debug municipalities endpoint:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to debug municipalities',
            details: error instanceof Error ? error.message : 'Unknown error',
            axiosError: {
                status: (error as any)?.response?.status,
                statusText: (error as any)?.response?.statusText,
                data: (error as any)?.response?.data
            }
        });
    }
});

/**
 * @swagger
 * /api/ows/administrative-boundaries/wards:
 *   get:
 *     summary: Get wards from the official SA wards layer
 *     tags: [Administrative Boundaries]
 *     description: Get ward-level administrative areas from the official sa_wards2020 layer
 *     parameters:
 *       - in: query
 *         name: municipality
 *         schema:
 *           type: string
 *         description: Filter wards by municipality name or code (cat_b)
 *       - in: query
 *         name: municipalityCode
 *         schema:
 *           type: string
 *         description: Filter wards by municipality code (cat_b field)
 *       - in: query
 *         name: district
 *         schema:
 *           type: string
 *         description: Filter wards by district name or code
 *       - in: query
 *         name: districtCode
 *         schema:
 *           type: string
 *         description: Filter wards by district code (districtco field)
 *       - in: query
 *         name: province
 *         schema:
 *           type: string
 *         description: Filter wards by province name
 *     responses:
 *       200:
 *         description: Wards retrieved successfully
 *       500:
 *         description: Failed to retrieve wards
 */
router.get('/administrative-boundaries/wards', cacheMiddleware(300), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const layerName = 'geonode:sa_wards2020'; // Official SA wards layer

        const municipality = req.query.municipality as string;
        const municipalityCode = req.query.municipalityCode as string;
        const district = req.query.district as string;
        const districtCode = req.query.districtCode as string;
        const province = req.query.province as string;

        console.log(`🏛️ Loading wards from ${layerName}...`, {
            municipality,
            municipalityCode,
            district,
            districtCode,
            province
        });

        // Build CQL filter based on provided parameters
        const filters = [];

        if (province) {
            filters.push(`province='${province}'`);
        }

        if (districtCode) {
            filters.push(`districtco='${districtCode}'`);
        } else if (district) {
            filters.push(`district='${district}'`);
        }

        if (municipalityCode) {
            filters.push(`cat_b='${municipalityCode}'`);
        } else if (municipality) {
            // Handle municipality name matching (municipali field is truncated)
            filters.push(`municipali LIKE '%${municipality}%'`);
        }

        const params: any = {
            SERVICE: 'WFS',
            REQUEST: 'GetFeature',
            typeName: layerName,
            version: '1.0.0',
            outputFormat: 'application/json',
            maxFeatures: 5000, // ✅ CRITICAL FIX: Increased from 50 to handle all wards
            propertyName: 'province,district,cat_b,municipali,wardid,wardno,wardlabel,districtco' // Only get needed fields
        };

        if (filters.length > 0) {
            params.CQL_FILTER = filters.join(' AND ');
        }

        const wfsResponse = await axios.get(`${geoserverUrl}/ows`, {
            params,
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            timeout: 15000
        });

        const wards = extractWardsFromSALayer(wfsResponse.data);

        console.log(`✅ Successfully extracted ${wards.length} wards from SA wards layer`);

        res.json({
            success: true,
            data: wards,
            source: 'SA_WARDS_2020',
            count: wards.length,
            filteredBy: {
                province,
                district,
                districtCode,
                municipality,
                municipalityCode
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Failed to load wards from SA wards layer:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve wards from SA wards layer',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

// ===== STANDARDIZED GEOSERVER PROXY ROUTES =====

/**
 * @swagger
 * /ows/styles/{layerName}:
 *   get:
 *     summary: Get layer styles information
 *     description: Fetch style information for a specific layer from GeoServer
 *     tags: [OWS]
 *     parameters:
 *       - in: path
 *         name: layerName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the layer
 *     responses:
 *       200:
 *         description: Layer styles information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       404:
 *         description: Layer not found
 *       500:
 *         description: Server error
 */
router.get('/styles/:layerName', cacheMiddleware(300), async (req, res) => {
    try {
        const { layerName } = req.params;
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';

        console.log(`🎨 Fetching styles for layer: ${layerName}`);

        // Try multiple approaches to get style information
        let styleData = null;
        let source = '';

        // Approach 1: Skip REST API (requires authentication) - use WMS-based methods instead
        // Note: REST API requires authentication which is not configured
        console.log(`⏭️ Skipping REST API for ${layerName} (authentication required)`);

        // Skip REST API to avoid 401 errors
        // try {
        //     const layerResponse = await secureGet(`${geoserverUrl}/rest/layers/${encodeURIComponent(layerName)}.json`, {
        //         headers: {
        //             'Accept': 'application/json'
        //         }
        //     });
        //     if (layerResponse.data?.layer) {
        //         styleData = { ... };
        //         source = 'REST_API';
        //     }
        // } catch (restError) {
        //     console.warn(`REST API failed for ${layerName}:`, restError.message);
        // }

        // Approach 2: Try WMS DescribeLayer request
        if (!styleData) {
            try {
                const describeResponse = await secureGet(`${geoserverUrl}/geonode/ows`, {
                    params: {
                        service: 'WMS',
                        version: '1.3.0',
                        request: 'DescribeLayer',
                        layers: layerName,
                        outputFormat: 'application/json'
                    }
                });

                if (describeResponse.data) {
                    styleData = {
                        layer: { name: layerName },
                        describeLayer: describeResponse.data,
                        styles: []
                    };
                    source = 'DESCRIBE_LAYER';
                }
            } catch (describeError) {
                console.warn(`DescribeLayer failed for ${layerName}:`,
                    describeError instanceof Error ? describeError.message : 'Unknown error');
            }
        }

        // Approach 3: Basic fallback with layer name
        if (!styleData) {
            styleData = {
                layer: { name: layerName },
                defaultStyle: { name: 'default' },
                styles: [],
                fallback: true
            };
            source = 'FALLBACK';
        }

        // Add legend URL information
        const legendUrl = `${geoserverUrl}/geonode/ows?service=WMS&version=1.3.0&request=GetLegendGraphic&layer=${encodeURIComponent(layerName)}&format=image/png&width=20&height=20&legend_options=fontAntiAliasing:true;fontSize:12;forceLabels:on`;

        const result = {
            ...styleData,
            legendUrl,
            source,
            timestamp: new Date().toISOString()
        };

        console.log(`✅ Successfully fetched styles for ${layerName} using ${source}`);
        res.json(result);

    } catch (error: any) {
        console.error(`❌ Error fetching styles for layer ${req.params.layerName}:`, error);

        if (error.response?.status === 404) {
            res.status(404).json({
                error: `Layer ${req.params.layerName} not found`,
                layerName: req.params.layerName,
                timestamp: new Date().toISOString()
            });
        } else {
            res.status(500).json({
                error: 'Failed to fetch layer styles',
                layerName: req.params.layerName,
                details: error.message || 'Unknown error',
                timestamp: new Date().toISOString()
            });
        }
    }
});

/**
 * @swagger
 * /ows/capabilities:
 *   get:
 *     summary: Get WMS capabilities
 *     description: Fetch WMS capabilities document from GeoServer
 *     tags: [OWS]
 *     responses:
 *       200:
 *         description: WMS capabilities XML document
 *         content:
 *           application/xml:
 *             schema:
 *               type: string
 *       500:
 *         description: Server error
 */
router.get('/capabilities', cacheMiddleware(600), async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';

        console.log('🌐 Fetching WMS capabilities');

        const response = await secureGet(`${geoserverUrl}/geonode/ows`, {
            params: {
                service: 'WMS',
                version: '1.3.0',
                request: 'GetCapabilities'
            }
        });

        // Set proper content type for XML
        res.set('Content-Type', 'application/xml');
        res.send(response.data);

        console.log('✅ Successfully fetched WMS capabilities');

    } catch (error: any) {
        console.error('❌ Error fetching capabilities:', error);
        res.status(500).json({
            error: 'Failed to fetch capabilities',
            details: error.message || 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
});

/**
 * @swagger
 * /ows:
 *   get:
 *     summary: Proxy OWS requests to GeoServer
 *     description: Generic proxy for all OWS requests (WMS, WFS, etc.) to GeoServer
 *     tags: [OWS]
 *     parameters:
 *       - in: query
 *         name: service
 *         schema:
 *           type: string
 *           enum: [WMS, WFS, WCS]
 *         description: OGC service type
 *       - in: query
 *         name: request
 *         schema:
 *           type: string
 *         description: Request type (GetMap, GetFeature, etc.)
 *       - in: query
 *         name: version
 *         schema:
 *           type: string
 *         description: Service version
 *     responses:
 *       200:
 *         description: OWS response (format depends on request)
 *       500:
 *         description: Server error
 */
router.get('/', async (req, res) => {
    try {
        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const requestType = req.query.request as string;

        console.log(`🔄 Proxying OWS request: ${requestType || 'unknown'}`);

        // Use the existing streamSecureWmsRequest for GetMap requests (handles streaming)
        if (requestType === 'GetMap') {
            console.log('📍 Using WMS proxy for GetMap request');
            try {
                const response = await streamSecureWmsRequest(`${geoserverUrl}/geonode/ows`, req.query);

                // Set response headers
                res.set(response.headers);

                // Pipe the stream to response
                response.data.pipe(res);
                return;
            } catch (error) {
                console.error('🌐 WMS stream request failed:', error instanceof Error ? error.message : String(error));
                return res.status(500).json({ error: 'Failed to proxy WMS request' });
            }
        }

        // For other requests, use regular proxy
        const response = await secureGet(`${geoserverUrl}/geonode/ows`, {
            params: req.query,
            responseType: requestType === 'GetMap' ? 'stream' : 'text'
        });

        // Set appropriate content type based on request
        if (requestType === 'GetMap') {
            res.set('Content-Type', 'image/png');
        } else if (requestType === 'GetFeature' &&
            typeof req.query.outputFormat === 'string' &&
            req.query.outputFormat.includes('json')) {
            res.set('Content-Type', 'application/json');
        } else {
            res.set('Content-Type', response.headers['content-type'] || 'application/xml');
        }

        // Handle streaming for large responses
        if (response.data && typeof response.data.pipe === 'function') {
            response.data.pipe(res);
        } else {
            res.send(response.data);
        }

        console.log(`✅ Successfully proxied ${requestType} request`);

    } catch (error: any) {
        console.error('❌ Error proxying OWS request:', error);
        res.status(500).json({
            error: 'Failed to proxy OWS request',
            request: req.query.request,
            details: error.message || 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
});

// Helper functions for the clip endpoint

/**
 * Convert GeoJSON geometry to WKT format
 */
function convertGeoJSONToWKT(geometry: any): string {
    if (!geometry || !geometry.type) {
        throw new Error('Invalid GeoJSON geometry');
    }

    switch (geometry.type) {
        case 'Polygon':
            const rings = geometry.coordinates.map((ring: number[][]) => {
                const coords = ring.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
                return `(${coords})`;
            });
            return `POLYGON(${rings.join(', ')})`;

        case 'MultiPolygon':
            const polygons = geometry.coordinates.map((polygon: number[][][]) => {
                const rings = polygon.map(ring => {
                    const coords = ring.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
                    return `(${coords})`;
                });
                return `(${rings.join(', ')})`;
            });
            return `MULTIPOLYGON(${polygons.join(', ')})`;

        default:
            throw new Error(`Unsupported geometry type: ${geometry.type}`);
    }
}

/**
 * Calculate bounding box from WKT
 */
function calculateBBoxFromWKT(wkt: string): { west: number; south: number; east: number; north: number } {
    // Simple regex to extract coordinates from WKT
    const coordRegex = /(-?\d+\.?\d*)\s+(-?\d+\.?\d*)/g;
    const matches = Array.from(wkt.matchAll(coordRegex));

    if (matches.length === 0) {
        throw new Error('No coordinates found in WKT');
    }

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    for (const match of matches) {
        const x = parseFloat(match[1]);
        const y = parseFloat(match[2]);

        minX = Math.min(minX, x);
        maxX = Math.max(maxX, x);
        minY = Math.min(minY, y);
        maxY = Math.max(maxY, y);
    }

    return {
        west: minX,
        south: minY,
        east: maxX,
        north: maxY
    };
}

/**
 * Process clipping for a single layer
 */
async function processLayerClipping(options: {
    layerName: string;
    wkt: string;
    bbox: { west: number; south: number; east: number; north: number };
    crs: string;
    time?: string;
    width: number;
    height: number;
    geoserverUrl: string;
}): Promise<any> {
    const { layerName, wkt, bbox, crs, time, width, height, geoserverUrl } = options;

    // TODO: Get layer capabilities to determine best clipping strategy
    // For now, implement a simple heuristic-based approach

    // Check if layer name suggests it's a raster layer
    const rasterPatterns = ['mosaic', 'imagery', 'satellite', 'tiff', 'raster', 'rgb', 'infrared',
                           'coverage', 'landsat', 'sentinel', 'modis', 'dem', 'elevation'];
    const isLikelyRaster = rasterPatterns.some(pattern =>
        layerName.toLowerCase().includes(pattern)
    );

    if (isLikelyRaster) {
        // Try raster clipping strategies
        return await processRasterLayerClipping(options);
    } else {
        // Try vector clipping strategies
        return await processVectorLayerClipping(options);
    }
}

/**
 * Process vector layer clipping
 */
async function processVectorLayerClipping(options: {
    layerName: string;
    wkt: string;
    bbox: { west: number; south: number; east: number; north: number };
    crs: string;
    time?: string;
    geoserverUrl: string;
}): Promise<any> {
    const { layerName, wkt, bbox, crs, time, geoserverUrl } = options;

    // Try CQL_FILTER first
    if (wkt.length <= 6000) { // WKT length threshold
        try {
            const cqlFilter = `INTERSECTS(the_geom, ${wkt})`;
            const params: any = {
                SERVICE: 'WMS',
                REQUEST: 'GetMap',
                VERSION: '1.1.1',
                LAYERS: layerName,
                CQL_FILTER: cqlFilter,
                SRS: crs,
                FORMAT: 'image/png',
                TRANSPARENT: true
            };

            if (time) {
                params.TIME = time;
            }

            const url = `${geoserverUrl}/geonode/ows`;

            return {
                method: 'CQL_FILTER',
                url,
                parameters: params,
                fallbackReason: null
            };
        } catch (error) {
            console.warn(`CQL filtering failed for ${layerName}, falling back to BBOX`);
        }
    }

    // Fallback to BBOX
    return {
        method: 'BBOX',
        url: `${geoserverUrl}/geonode/ows`,
        parameters: {
            SERVICE: 'WMS',
            REQUEST: 'GetMap',
            VERSION: '1.1.1',
            LAYERS: layerName,
            BBOX: `${bbox.west},${bbox.south},${bbox.east},${bbox.north}`,
            SRS: crs,
            FORMAT: 'image/png',
            TRANSPARENT: true,
            ...(time && { TIME: time })
        },
        fallbackReason: wkt.length > 6000 ? 'WKT too long for CQL filter' : 'CQL filter failed'
    };
}

/**
 * Helper: call WMS GetMap with SLD_BODY for raster clipping
 */
async function getMapWithSLD(geoserverUrl: string, layerName: string, aoiWkt: string, crs: string, width: number, height: number, time?: string) {
  const params: any = {
    SERVICE: "WMS", REQUEST: "GetMap", VERSION: "1.1.1",
    LAYERS: layerName, SRS: crs, FORMAT: "image/png", TRANSPARENT: true,
    WIDTH: width, HEIGHT: height,
    SLD_BODY: buildRasterCropSLD(aoiWkt)
  };
  if (time) params.TIME = time;
  return await secureGet(`${geoserverUrl}/geonode/ows`, {
    params,
    responseType: "arraybuffer"
  });
}

/**
 * Raster preview with AOI-True clipping (SLD → WPS → BBOX priority)
 */
async function rasterPreviewAoiTrue(opts: {
  geoserverUrl: string; layerName: string; aoiWkt: string; crs: string;
  width: number; height: number; time?: string; username?: string; password?: string;
}) {
  const startTime = Date.now();

  // 1) SLD (fast, exact polygon) - if enabled
  if (isSLDEnabled()) {
    try {
      const res = await getMapWithSLD(opts.geoserverUrl, opts.layerName, opts.aoiWkt, opts.crs, opts.width, opts.height, opts.time);
      const processingTime = Date.now() - startTime;

      recordClipping({
        method: "SLD",
        layerName: opts.layerName,
        success: true,
        processingTime
      });

      return { method: "SLD", buffer: res.data as Buffer, contentType: res.headers["content-type"] || "image/png" };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      recordClipping({
        method: "SLD",
        layerName: opts.layerName,
        success: false,
        processingTime,
        errorCode: error instanceof Error ? error.message : 'SLD clipping failed'
      });
      /* → WPS */
    }
  }

  // 2) WPS crop (exact polygon) - if enabled
  if (isWPSEnabled()) {
    try {
      const wps = await executeCropCoverageWPS({
        geoserverBaseUrl: opts.geoserverUrl,
        coverageName: opts.layerName,
        aoiWkt: opts.aoiWkt,
        crs: opts.crs,
        outputFormat: "image/geotiff",
        time: opts.time,
        username: opts.username,
        password: opts.password,
        filenameHint: "preview"
      });

      const processingTime = Date.now() - startTime;

      if (wps.ok) {
        recordClipping({
          method: "WPS",
          layerName: opts.layerName,
          success: true,
          processingTime
        });

        return { method: "WPS", buffer: wps.bytes, contentType: wps.mime || "image/geotiff" };
      } else {
        recordClipping({
          method: "WPS",
          layerName: opts.layerName,
          success: false,
          processingTime,
          errorCode: wps.detail
        });
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;
      recordClipping({
        method: "WPS",
        layerName: opts.layerName,
        success: false,
        processingTime,
        errorCode: error instanceof Error ? error.message : 'WPS clipping failed'
      });
      /* → BBOX */
    }
  }

  // 3) BBOX LAST (warn UI)
  const processingTime = Date.now() - startTime;
  const reason = "SLD/WPS unavailable or failed";

  recordClipping({
    method: "BBOX",
    layerName: opts.layerName,
    success: true, // BBOX is considered successful as a fallback
    processingTime,
    fallbackReason: reason
  });

  return { method: "BBOX", reason };
}

/**
 * Process raster layer clipping
 */
async function processRasterLayerClipping(options: {
    layerName: string;
    wkt: string;
    bbox: { west: number; south: number; east: number; north: number };
    crs: string;
    time?: string;
    width: number;
    height: number;
    geoserverUrl: string;
}): Promise<any> {
    const { layerName, wkt, bbox, crs, time, width, height, geoserverUrl } = options;

    // Try AOI-True clipping with SLD → WPS → BBOX priority
    try {
        const result = await rasterPreviewAoiTrue({
            geoserverUrl,
            layerName,
            aoiWkt: wkt,
            crs,
            width,
            height,
            time
        });

        if (result.method === "BBOX") {
            // Fall back to existing BBOX builder with last-resort reason
            return {
                method: 'BBOX',
                url: `${geoserverUrl}/geonode/ows`,
                parameters: {
                    SERVICE: 'WMS',
                    REQUEST: 'GetMap',
                    VERSION: '1.1.1',
                    LAYERS: layerName,
                    BBOX: `${bbox.west},${bbox.south},${bbox.east},${bbox.north}`,
                    SRS: crs,
                    FORMAT: 'image/png',
                    TRANSPARENT: true,
                    WIDTH: width,
                    HEIGHT: height,
                    ...(time && { TIME: time })
                },
                fallbackReason: result.reason || 'SLD/WPS unavailable or failed'
            };
        }

        // Return successful SLD or WPS result
        return {
            method: result.method,
            buffer: result.buffer,
            contentType: result.contentType,
            url: `${geoserverUrl}/geonode/ows`, // For compatibility
            parameters: {} // SLD/WPS results are already processed
        };

    } catch (error) {
        console.error(`❌ Raster clipping failed for "${layerName}":`, error);

        // Final fallback to BBOX
        return {
            method: 'BBOX',
            url: `${geoserverUrl}/geonode/ows`,
            parameters: {
                SERVICE: 'WMS',
                REQUEST: 'GetMap',
                VERSION: '1.1.1',
                LAYERS: layerName,
                BBOX: `${bbox.west},${bbox.south},${bbox.east},${bbox.north}`,
                SRS: crs,
                FORMAT: 'image/png',
                TRANSPARENT: true,
                WIDTH: width,
                HEIGHT: height,
                ...(time && { TIME: time })
            },
            fallbackReason: `Raster clipping error: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
    }
}

/**
 * @swagger
 * /ows/clip:
 *   post:
 *     summary: Intelligent AOI clipping endpoint
 *     description: |
 *       Applies intelligent clipping strategy based on layer capabilities.
 *       Supports vector layers with CQL_FILTER, raster layers with SLD/WPS,
 *       and BBOX fallback for all layer types.
 *     tags: [OWS]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - layers
 *               - aoi_wkt
 *             properties:
 *               layers:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of layer names to clip
 *               aoi_wkt:
 *                 type: string
 *                 description: Area of Interest in WKT format
 *               aoi_geojson:
 *                 type: object
 *                 description: Alternative to aoi_wkt - GeoJSON geometry
 *               crs:
 *                 type: string
 *                 default: EPSG:4326
 *                 description: Coordinate reference system
 *               time:
 *                 type: string
 *                 description: Temporal parameter for time-enabled layers
 *               width:
 *                 type: integer
 *                 default: 256
 *                 description: Tile width for raster clipping
 *               height:
 *                 type: integer
 *                 default: 256
 *                 description: Tile height for raster clipping
 *     responses:
 *       200:
 *         description: Clipping URLs generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 layers:
 *                   type: object
 *                   additionalProperties:
 *                     type: object
 *                     properties:
 *                       method:
 *                         type: string
 *                         enum: [CQL_FILTER, SLD_CLIP, WPS_CROP, BBOX, FAILED]
 *                       url:
 *                         type: string
 *                       fallbackReason:
 *                         type: string
 *                       parameters:
 *                         type: object
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Server error
 */
router.post('/clip', async (req, res) => {
    try {
        const {
            layers,
            aoi_wkt,
            aoi_geojson,
            crs = 'EPSG:4326',
            time,
            width = 256,
            height = 256
        } = req.body;

        // Validate required parameters
        if (!layers || !Array.isArray(layers) || layers.length === 0) {
            return res.status(400).json({
                error: 'Missing or invalid layers parameter - must be non-empty array'
            });
        }

        if (!aoi_wkt && !aoi_geojson) {
            return res.status(400).json({
                error: 'Either aoi_wkt or aoi_geojson must be provided'
            });
        }

        console.log(`🔧 Processing clip request for ${layers.length} layers`);

        const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
        const result: any = {
            success: true,
            layers: {}
        };

        // Convert GeoJSON to WKT if needed
        let wkt = aoi_wkt;
        if (!wkt && aoi_geojson) {
            try {
                wkt = convertGeoJSONToWKT(aoi_geojson);
            } catch (error) {
                return res.status(400).json({
                    error: 'Failed to convert GeoJSON to WKT',
                    details: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }

        // Calculate BBOX from WKT for fallback
        const bbox = calculateBBoxFromWKT(wkt);

        // Process each layer
        for (const layerName of layers) {
            try {
                const layerResult = await processLayerClipping({
                    layerName,
                    wkt,
                    bbox,
                    crs,
                    time,
                    width,
                    height,
                    geoserverUrl
                });

                result.layers[layerName] = layerResult;
                console.log(`✅ Layer "${layerName}" processed: ${layerResult.method}`);

            } catch (error) {
                console.error(`❌ Failed to process layer "${layerName}":`, error);
                result.layers[layerName] = {
                    method: 'FAILED',
                    error: error instanceof Error ? error.message : 'Unknown error',
                    fallbackReason: 'Layer processing failed'
                };
            }
        }

        res.json(result);

    } catch (error) {
        console.error('❌ Clip endpoint error:', error);
        res.status(500).json({
            error: 'Failed to process clipping request',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

export const owsRouter = router;

