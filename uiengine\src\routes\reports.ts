// Phase 4: Report Routes
import express from 'express';
import { ReportController } from '../controllers/reportController';

/**
 * @swagger
 * components:
 *   schemas:
 *     Report:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique report identifier
 *         filename:
 *           type: string
 *           description: Report filename
 *         title:
 *           type: string
 *           description: Report title
 *         type:
 *           type: string
 *           enum: [flood_analysis, damage_assessment, temporal_analysis, custom]
 *           description: Report type
 *         format:
 *           type: string
 *           enum: [pdf, html, excel, csv]
 *           description: Report format
 *         status:
 *           type: string
 *           enum: [pending, generating, completed, failed]
 *           description: Report generation status
 *         parameters:
 *           type: object
 *           description: Report generation parameters
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         completedAt:
 *           type: string
 *           format: date-time
 *           description: Completion timestamp
 *         fileSize:
 *           type: integer
 *           description: File size in bytes
 *         downloadCount:
 *           type: integer
 *           description: Number of times downloaded
 */

const router = express.Router();
const reportController = new ReportController();

/**
 * @swagger
 * /api/reports/generate:
 *   post:
 *     summary: Generate a new report
 *     tags: [Reports]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - type
 *               - format
 *             properties:
 *               title:
 *                 type: string
 *                 description: Report title
 *               type:
 *                 type: string
 *                 enum: [flood_analysis, damage_assessment, temporal_analysis, custom]
 *                 description: Report type
 *               format:
 *                 type: string
 *                 enum: [pdf, html, excel, csv]
 *                 description: Report format
 *               layers:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Layers to include in report
 *               bbox:
 *                 type: string
 *                 description: Bounding box (west,south,east,north)
 *               dateRange:
 *                 type: object
 *                 properties:
 *                   startDate:
 *                     type: string
 *                     format: date
 *                   endDate:
 *                     type: string
 *                     format: date
 *               includeMap:
 *                 type: boolean
 *                 default: true
 *               includeCharts:
 *                 type: boolean
 *                 default: true
 *               includeStatistics:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       202:
 *         description: Report generation started
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 status:
 *                   type: string
 *                   enum: [pending, generating]
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Server error
 */
// Generate new report
router.post('/generate', reportController.generateReport);

/**
 * @swagger
 * /api/reports/analytics:
 *   get:
 *     summary: Get analytics data for reports
 *     tags: [Reports]
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [day, week, month, year]
 *           default: month
 *         description: Time period for analytics
 *     responses:
 *       200:
 *         description: Analytics data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 generationCount:
 *                   type: integer
 *                 downloadCount:
 *                   type: integer
 *                 byType:
 *                   type: object
 *                 byFormat:
 *                   type: object
 *                 timeline:
 *                   type: array
 *                   items:
 *                     type: object
 *       500:
 *         description: Server error
 */
// Get analytics data
router.get('/analytics', reportController.getAnalytics);

/**
 * @swagger
 * /api/reports/download/{filename}:
 *   get:
 *     summary: Download a generated report
 *     tags: [Reports]
 *     parameters:
 *       - in: path
 *         name: filename
 *         required: true
 *         schema:
 *           type: string
 *         description: Report filename
 *     responses:
 *       200:
 *         description: Report file
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *           application/vnd.ms-excel:
 *             schema:
 *               type: string
 *               format: binary
 *           text/html:
 *             schema:
 *               type: string
 *           text/csv:
 *             schema:
 *               type: string
 *       404:
 *         description: Report not found
 *       500:
 *         description: Server error
 */
// Download generated report
router.get('/download/:filename', reportController.downloadReport);

/**
 * @swagger
 * /api/reports:
 *   get:
 *     summary: List available reports
 *     tags: [Reports]
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by report type
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *         description: Filter by report format
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by report status
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Maximum number of results
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of results to skip
 *     responses:
 *       200:
 *         description: List of reports
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 reports:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Report'
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 offset:
 *                   type: integer
 *       500:
 *         description: Server error
 */
// List available reports
router.get('/', reportController.listReports);

/**
 * @swagger
 * /api/reports/{filename}:
 *   delete:
 *     summary: Delete a report
 *     tags: [Reports]
 *     parameters:
 *       - in: path
 *         name: filename
 *         required: true
 *         schema:
 *           type: string
 *         description: Report filename
 *     responses:
 *       200:
 *         description: Report deleted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       404:
 *         description: Report not found
 *       500:
 *         description: Server error
 */
// Delete a report
router.delete('/:filename', reportController.deleteReport);

/**
 * @swagger
 * /api/reports/cleanup:
 *   post:
 *     summary: Cleanup old reports
 *     tags: [Reports]
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               olderThan:
 *                 type: integer
 *                 description: Age in days to clean up (default 30)
 *               status:
 *                 type: string
 *                 enum: [all, completed, failed]
 *                 description: Status filter for cleanup
 *     responses:
 *       200:
 *         description: Cleanup results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 deleted:
 *                   type: integer
 *                 message:
 *                   type: string
 *       500:
 *         description: Server error
 */
// Cleanup old reports
router.post('/cleanup', reportController.cleanupReports);

/**
 * @swagger
 * /api/reports/stats:
 *   get:
 *     summary: Get report statistics
 *     tags: [Reports]
 *     responses:
 *       200:
 *         description: Report statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 byStatus:
 *                   type: object
 *                   properties:
 *                     pending:
 *                       type: integer
 *                     generating:
 *                       type: integer
 *                     completed:
 *                       type: integer
 *                     failed:
 *                       type: integer
 *                 byType:
 *                   type: object
 *                 byFormat:
 *                   type: object
 *                 totalSize:
 *                   type: integer
 *                   description: Total size in bytes
 *                 averageSize:
 *                   type: integer
 *                   description: Average size in bytes
 *       500:
 *         description: Server error
 */
// Get report statistics
router.get('/stats', reportController.getReportStats);

/**
 * @swagger
 * /api/reports/options:
 *   get:
 *     summary: Get available report options
 *     tags: [Reports]
 *     responses:
 *       200:
 *         description: Available report options
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 types:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                 formats:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       extension:
 *                         type: string
 *                 templates:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *       500:
 *         description: Server error
 */
// Get available report options
router.get('/options', reportController.getReportOptions);

export default router;
