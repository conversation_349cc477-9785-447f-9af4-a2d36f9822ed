import { Router } from 'express';
import { ROIController } from '../controllers/roiController';

const router = Router();

// ROI CRUD operations
router.post('/', ROIController.createROI);
router.get('/', ROIController.getAllROIs);
router.get('/:id', ROIController.getROIById);
router.put('/:id', ROIController.updateROI);
router.delete('/:id', ROIController.deleteROI);

// Spatial operations
router.post('/intersect', ROIController.findIntersectingROIs);
router.post('/analysis', ROIController.performSpatialAnalysis);

// ROI-specific data queries
router.get('/:id/flood-events', ROIController.getFloodEventsInROI);
router.get('/:id/water-measurements', ROIController.getWaterMeasurementsInROI);

export default router;
