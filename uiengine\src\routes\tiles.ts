/**
 * Tile Routes for AOI-True Clipping System
 * 
 * Provides tile serving endpoints with exact AOI clipping:
 * - GET /tiles/vector/{z}/{x}/{y}.png - Vector tiles with CQL/BBOX clipping
 * - GET /tiles/raster/{z}/{x}/{y}.png - Raster tiles with tiered clipping
 */

import { Router, Request, Response } from 'express';
import { TileService, TileRequest } from '../services/tileService';
import { getCapabilitiesCache } from '../services/capabilitiesCache';
import { cacheMiddleware } from '../middleware/cache';

const router = Router();

// Global tile service instance
let tileService: TileService | null = null;

/**
 * Initialize tile service
 */
export function initializeTileService(config: any): void {
  tileService = new TileService(config);
}

/**
 * @swagger
 * components:
 *   schemas:
 *     TileRequest:
 *       type: object
 *       properties:
 *         layer:
 *           type: string
 *           description: Layer name
 *         aoiId:
 *           type: string
 *           description: AOI identifier for clipping
 *         time:
 *           type: string
 *           description: Temporal parameter (ISO 8601 format)
 *         format:
 *           type: string
 *           default: image/png
 *         srs:
 *           type: string
 *           default: EPSG:4326
 */

/**
 * @swagger
 * /tiles/vector/{z}/{x}/{y}.png:
 *   get:
 *     summary: Get vector tile with AOI clipping
 *     description: |
 *       Generate vector tile with exact AOI clipping using CQL_FILTER 
 *       with INTERSECTS or BBOX fallback. Supports temporal filtering.
 *     tags: [Tiles]
 *     parameters:
 *       - in: path
 *         name: z
 *         required: true
 *         schema:
 *           type: integer
 *         description: Zoom level
 *       - in: path
 *         name: x
 *         required: true
 *         schema:
 *           type: integer
 *         description: Tile X coordinate
 *       - in: path
 *         name: y
 *         required: true
 *         schema:
 *           type: integer
 *         description: Tile Y coordinate
 *       - in: query
 *         name: layer
 *         required: true
 *         schema:
 *           type: string
 *         description: Vector layer name
 *       - in: query
 *         name: aoiId
 *         schema:
 *           type: string
 *         description: AOI identifier for clipping
 *       - in: query
 *         name: time
 *         schema:
 *           type: string
 *         description: Temporal parameter
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           default: image/png
 *       - in: query
 *         name: srs
 *         schema:
 *           type: string
 *           default: EPSG:4326
 *     responses:
 *       200:
 *         description: Vector tile generated successfully
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           X-Tile-Strategy:
 *             description: Clipping strategy used
 *             schema:
 *               type: string
 *           X-Tile-Cache-Key:
 *             description: Cache key for debugging
 *             schema:
 *               type: string
 *           X-Processing-Time:
 *             description: Processing time in milliseconds
 *             schema:
 *               type: integer
 *       400:
 *         description: Invalid tile request
 *       404:
 *         description: Layer not found or not a vector layer
 *       500:
 *         description: Tile generation failed
 */
router.get('/vector/:z/:x/:y.png', cacheMiddleware(300), async (req: Request, res: Response) => {
  try {
    if (!tileService) {
      return res.status(500).json({ error: 'Tile service not initialized' });
    }

    const { z, x, y } = req.params;
    const { layer, aoiId, time, format, srs } = req.query;

    // Validate required parameters
    if (!layer) {
      return res.status(400).json({ error: 'layer parameter is required' });
    }

    // Validate layer exists and is vector
    const capabilitiesCache = getCapabilitiesCache();
    const capabilities = capabilitiesCache?.getLayerCapabilities(layer as string);
    
    if (!capabilities) {
      return res.status(404).json({ error: `Layer not found: ${layer}` });
    }
    
    if (capabilities.type !== 'vector') {
      return res.status(400).json({ 
        error: `Layer ${layer} is not a vector layer (type: ${capabilities.type})` 
      });
    }

    // Build tile request
    const tileRequest: TileRequest = {
      z: parseInt(z),
      x: parseInt(x),
      y: parseInt(y),
      layer: layer as string,
      aoiId: aoiId as string | undefined,
      time: time as string | undefined,
      format: format as string | undefined,
      srs: srs as string | undefined
    };

    console.log(`🎯 Vector tile request: ${layer} ${z}/${x}/${y}${aoiId ? ` (AOI: ${aoiId})` : ''}`);

    // Generate tile
    const result = await tileService.generateVectorTile(tileRequest);

    // Set response headers
    res.setHeader('Content-Type', result.contentType);
    res.setHeader('X-Tile-Strategy', result.metadata.clippingMethod);
    res.setHeader('X-Tile-Cache-Key', result.cacheKey);
    res.setHeader('X-Processing-Time', result.metadata.processingTime.toString());
    res.setHeader('X-From-Cache', result.metadata.fromCache.toString());
    
    // Cache control
    res.setHeader('Cache-Control', 'public, max-age=300'); // 5 minutes

    res.send(result.data);

  } catch (error) {
    console.error('❌ Vector tile generation failed:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Vector tile generation failed'
    });
  }
});

/**
 * @swagger
 * /tiles/raster/{z}/{x}/{y}.png:
 *   get:
 *     summary: Get raster tile with AOI clipping
 *     description: |
 *       Generate raster tile with exact AOI clipping using tiered approach:
 *       1. SLD clipping with ras:CropCoverage
 *       2. WPS crop with gs:CropCoverage (if available)
 *       3. Server-side masking with alpha channel
 *     tags: [Tiles]
 *     parameters:
 *       - in: path
 *         name: z
 *         required: true
 *         schema:
 *           type: integer
 *         description: Zoom level
 *       - in: path
 *         name: x
 *         required: true
 *         schema:
 *           type: integer
 *         description: Tile X coordinate
 *       - in: path
 *         name: y
 *         required: true
 *         schema:
 *           type: integer
 *         description: Tile Y coordinate
 *       - in: query
 *         name: layer
 *         required: true
 *         schema:
 *           type: string
 *         description: Raster layer name
 *       - in: query
 *         name: aoiId
 *         schema:
 *           type: string
 *         description: AOI identifier for clipping
 *       - in: query
 *         name: time
 *         schema:
 *           type: string
 *         description: Temporal parameter
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           default: image/png
 *       - in: query
 *         name: srs
 *         schema:
 *           type: string
 *           default: EPSG:4326
 *     responses:
 *       200:
 *         description: Raster tile generated successfully
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           X-Tile-Strategy:
 *             description: Clipping strategy used (SLD_CLIP, WPS_CROP, SERVER_MASK, NONE)
 *             schema:
 *               type: string
 *           X-Tile-Cache-Key:
 *             description: Cache key for debugging
 *             schema:
 *               type: string
 *           X-Processing-Time:
 *             description: Processing time in milliseconds
 *             schema:
 *               type: integer
 *       400:
 *         description: Invalid tile request
 *       404:
 *         description: Layer not found or not a raster layer
 *       500:
 *         description: Tile generation failed
 */
router.get('/raster/:z/:x/:y.png', cacheMiddleware(300), async (req: Request, res: Response) => {
  try {
    if (!tileService) {
      return res.status(500).json({ error: 'Tile service not initialized' });
    }

    const { z, x, y } = req.params;
    const { layer, aoiId, time, format, srs } = req.query;

    // Validate required parameters
    if (!layer) {
      return res.status(400).json({ error: 'layer parameter is required' });
    }

    // Validate layer exists and is raster
    const capabilitiesCache = getCapabilitiesCache();
    const capabilities = capabilitiesCache?.getLayerCapabilities(layer as string);
    
    if (!capabilities) {
      return res.status(404).json({ error: `Layer not found: ${layer}` });
    }
    
    if (capabilities.type !== 'raster') {
      return res.status(400).json({ 
        error: `Layer ${layer} is not a raster layer (type: ${capabilities.type})` 
      });
    }

    // Build tile request
    const tileRequest: TileRequest = {
      z: parseInt(z),
      x: parseInt(x),
      y: parseInt(y),
      layer: layer as string,
      aoiId: aoiId as string | undefined,
      time: time as string | undefined,
      format: format as string | undefined,
      srs: srs as string | undefined
    };

    console.log(`🖼️ Raster tile request: ${layer} ${z}/${x}/${y}${aoiId ? ` (AOI: ${aoiId})` : ''}`);

    // Generate tile
    const result = await tileService.generateRasterTile(tileRequest);

    // Set response headers
    res.setHeader('Content-Type', result.contentType);
    res.setHeader('X-Tile-Strategy', result.metadata.clippingMethod);
    res.setHeader('X-Tile-Cache-Key', result.cacheKey);
    res.setHeader('X-Processing-Time', result.metadata.processingTime.toString());
    res.setHeader('X-From-Cache', result.metadata.fromCache.toString());
    
    // Cache control
    res.setHeader('Cache-Control', 'public, max-age=300'); // 5 minutes

    res.send(result.data);

  } catch (error) {
    console.error('❌ Raster tile generation failed:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Raster tile generation failed'
    });
  }
});

export default router;
