import { AlertRule, AlertEvaluationResult, ThresholdOperator, DatasetValue } from '../types/alertRule';
import { AlertRuleService } from './alertRuleService';
import { AlertEventService } from './alertEventService';
import { DatasetService } from './datasetService';
// import { GeoServerService } from './geoServerService';
import { NotificationService } from './notificationService';

export class AlertEngine {
  private alertRuleService: AlertRuleService;
  private alertEventService: AlertEventService;
  private datasetService: DatasetService;
  // private geoServerService: GeoServerService;
  private notificationService: NotificationService;
  private isRunning: boolean = false;
  private intervalId: NodeJS.Timeout | null = null;
  private evaluationQueue: Map<number, Date> = new Map(); // Track last evaluation time per rule

  constructor() {
    this.alertRuleService = new AlertRuleService();
    this.alertEventService = new AlertEventService();
    this.datasetService = new DatasetService();
    // this.geoServerService = new GeoServerService();
    this.notificationService = new NotificationService();
  }
  /**
   * Start the alert engine with periodic polling
   */
  async start(intervalMinutes: number = 1): Promise<void> {
    if (this.isRunning) {
      console.log('Alert engine is already running');
      return;
    }

    this.isRunning = true;
    console.log(`Starting alert engine with ${intervalMinutes} minute intervals`);

    // Run immediately on start
    await this.evaluateAllRules();

    // Schedule periodic evaluations
    this.intervalId = setInterval(async () => {
      await this.evaluateAllRules();
    }, intervalMinutes * 60 * 1000);
  }

  /**
   * Start the alert engine in fallback mode (in-memory only, no database persistence)
   */
  async startFallbackMode(intervalMinutes: number = 1): Promise<void> {
    if (this.isRunning) {
      console.log('Alert engine is already running');
      return;
    }

    this.isRunning = true;
    console.log(`🔄 Starting alert engine in FALLBACK mode with ${intervalMinutes} minute intervals`);
    console.log('📝 Note: Alert rules and events will be stored in memory only (not persistent)');

    // In fallback mode, we'll create some default alert rules
    await this.createDefaultFallbackRules();

    // Run evaluation cycle
    await this.evaluateAllRulesFallback();

    // Schedule periodic evaluations (fallback mode)
    this.intervalId = setInterval(async () => {
      await this.evaluateAllRulesFallback();
    }, intervalMinutes * 60 * 1000);
  }

  /**
   * Stop the alert engine
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    console.log('Alert engine stopped');
  }

  /**
   * Evaluate all active alert rules
   */
  async evaluateAllRules(): Promise<void> {
    try {
      console.log('Starting alert rule evaluation cycle');
      const activeRules = await this.alertRuleService.getActiveAlertRules();
      
      console.log(`Found ${activeRules.length} active alert rules`);

      // Filter rules that need evaluation based on their polling interval
      const rulesToEvaluate = activeRules.filter(rule => this.shouldEvaluateRule(rule));
      
      console.log(`Evaluating ${rulesToEvaluate.length} rules this cycle`);

      // Evaluate rules in parallel with concurrency limit
      await this.evaluateRulesInBatches(rulesToEvaluate, 5);

      console.log('Alert rule evaluation cycle completed');
    } catch (error) {
      console.error('Error during alert rule evaluation:', error);
    }
  }

  /**
   * Check if a rule should be evaluated based on its polling interval
   */
  private shouldEvaluateRule(rule: AlertRule): boolean {
    const lastEvaluation = this.evaluationQueue.get(rule.id!);
    const now = new Date();

    if (!lastEvaluation) {
      return true; // First evaluation
    }

    const timeSinceLastEval = now.getTime() - lastEvaluation.getTime();
    const intervalMs = rule.pollingIntervalMinutes * 60 * 1000;

    return timeSinceLastEval >= intervalMs;
  }

  /**
   * Evaluate rules in batches to prevent overwhelming the system
   */
  private async evaluateRulesInBatches(rules: AlertRule[], batchSize: number): Promise<void> {
    for (let i = 0; i < rules.length; i += batchSize) {
      const batch = rules.slice(i, i + batchSize);
      
      await Promise.all(
        batch.map(rule => this.evaluateRule(rule).catch(error => {
          console.error(`Error evaluating rule ${rule.id}:`, error);
        }))
      );
      
      // Small delay between batches to prevent overwhelming the system
      if (i + batchSize < rules.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
  }

  /**
   * Evaluate a single alert rule
   */
  async evaluateRule(rule: AlertRule): Promise<AlertEvaluationResult | null> {
    try {
      console.log(`Evaluating rule: ${rule.name} (ID: ${rule.id})`);
      
      // Update last evaluation time
      this.evaluationQueue.set(rule.id!, new Date());

      // Get dataset information
      const dataset = await this.datasetService.getDatasetById(rule.datasetId);
      if (!dataset) {
        console.error(`Dataset not found for rule ${rule.id}: ${rule.datasetId}`);
        return null;
      }

      // Get current data value from OGC services
      const currentValue = await this.getDatasetValue(dataset, rule.conditionField, rule.roiId);
      if (currentValue === null) {
        console.warn(`Could not retrieve value for rule ${rule.id}`);
        return null;
      }

      // Evaluate threshold condition
      const isTriggered = this.evaluateThreshold(
        currentValue.value,
        rule.thresholdValue,
        rule.thresholdOperator
      );

      const evaluationResult: AlertEvaluationResult = {
        alertRuleId: rule.id!,
        triggered: isTriggered,
        currentValue: currentValue.value,
        thresholdValue: rule.thresholdValue,
        operator: rule.thresholdOperator,
        datasetSnapshot: {
          dataset: {
            id: dataset.pk,
            title: dataset.title,
            abstract: dataset.abstract
          },
          measurement: currentValue,
          evaluatedAt: new Date()
        },
        timestamp: new Date()
      };

      // If triggered, create alert event and send notifications
      if (isTriggered) {
        console.log(`ALERT TRIGGERED: Rule ${rule.name} - ${currentValue.value} ${rule.thresholdOperator} ${rule.thresholdValue}`);
        await this.handleTriggeredAlert(rule, evaluationResult);
      } else {
        console.log(`Rule ${rule.name} - OK: ${currentValue.value} ${this.negateOperator(rule.thresholdOperator)} ${rule.thresholdValue}`);
      }

      return evaluationResult;
    } catch (error) {
      console.error(`Error evaluating rule ${rule.id}:`, error);
      return null;
    }
  }

  /**
   * Get current value from dataset using OGC services
   */
  private async getDatasetValue(dataset: any, fieldName: string, roiId?: number): Promise<DatasetValue | null> {
    try {
      // Find WFS or WCS link in dataset
      const wfsLink = dataset.links?.find((link: any) => link.link_type === 'OGC:WFS');
      const wcsLink = dataset.links?.find((link: any) => link.link_type === 'OGC:WCS');

      let serviceUrl: string;
      let isRaster = false;

      if (wfsLink) {
        serviceUrl = wfsLink.url;
      } else if (wcsLink) {
        serviceUrl = wcsLink.url;
        isRaster = true;
      } else {
        // Fallback to constructed URL using alternate
        serviceUrl = `${process.env.GEOSERVER_BASE_URL || 'http://localhost:8080/geoserver'}/ows`;
      }      // Get capabilities first to understand the service
      // const capabilities = await this.geoServerService.getCapabilities();
      
      if (isRaster || dataset.subtype === 'raster') {
        // Handle raster data using WCS
        return await this.getRasterValue(serviceUrl, dataset.alternate, fieldName, roiId);
      } else {
        // Handle vector data using WFS
        return await this.getVectorValue(serviceUrl, dataset.alternate, fieldName, roiId);
      }
    } catch (error) {
      console.error(`Error getting dataset value for ${dataset.pk}:`, error);
      return null;
    }
  }

  /**
   * Get value from raster dataset using WCS
   */
  private async getRasterValue(serviceUrl: string, layerName: string, fieldName: string, roiId?: number): Promise<DatasetValue | null> {
    try {
      // This is a simplified implementation
      // In reality, you would need to:
      // 1. Get coverage description
      // 2. Get coverage data for specific area/point
      // 3. Process raster values
      
      // For demonstration, return a mock value
      console.log(`Getting raster value from ${serviceUrl} for layer ${layerName}`);
      
      return {
        field: fieldName,
        value: Math.random() * 100, // Mock value
        timestamp: new Date(),
        metadata: {
          source: 'WCS',
          layer: layerName,
          serviceUrl
        }
      };
    } catch (error) {
      console.error(`Error getting raster value:`, error);
      return null;
    }
  }

  /**
   * Get value from vector dataset using WFS
   */
  private async getVectorValue(serviceUrl: string, layerName: string, fieldName: string, roiId?: number): Promise<DatasetValue | null> {
    try {
      // Build WFS GetFeature request
      const params = new URLSearchParams({
        service: 'WFS',
        version: '2.0.0',
        request: 'GetFeature',
        typeName: layerName,
        outputFormat: 'application/json',
        maxFeatures: '1'
      });

      // Add spatial filter if ROI is specified
      if (roiId) {
        // Get ROI geometry from database and add as CQL filter
        // This is simplified - in practice you'd construct proper CQL
        params.append('CQL_FILTER', `INTERSECTS(the_geom, POINT(0 0))`);
      }

      const response = await fetch(`${serviceUrl}?${params}`);
      if (!response.ok) {
        throw new Error(`WFS request failed: ${response.statusText}`);
      }

      const geojson = await response.json();
      
      if (geojson.features && geojson.features.length > 0) {
        const feature = geojson.features[0];
        const value = feature.properties[fieldName];

        if (value !== undefined && value !== null) {
          return {
            field: fieldName,
            value: parseFloat(value) || 0,
            timestamp: new Date(),
            metadata: {
              source: 'WFS',
              layer: layerName,
              serviceUrl,
              featureId: feature.id
            },
            geometry: feature.geometry
          };
        }
      }

      return null;
    } catch (error) {
      console.error(`Error getting vector value:`, error);
      return null;
    }
  }

  /**
   * Evaluate threshold condition
   */
  private evaluateThreshold(currentValue: number, thresholdValue: number, operator: ThresholdOperator): boolean {
    switch (operator) {
      case '>':
        return currentValue > thresholdValue;
      case '<':
        return currentValue < thresholdValue;
      case '>=':
        return currentValue >= thresholdValue;
      case '<=':
        return currentValue <= thresholdValue;
      case '=':
        return Math.abs(currentValue - thresholdValue) < 0.001; // Floating point comparison
      case '!=':
        return Math.abs(currentValue - thresholdValue) >= 0.001;
      default:
        return false;
    }
  }

  /**
   * Handle a triggered alert - create event and send notifications
   */
  private async handleTriggeredAlert(rule: AlertRule, evaluation: AlertEvaluationResult): Promise<void> {
    try {
      // Create alert event in database
      const alertEvent = await this.alertEventService.createAlertEvent({
        alertRuleId: rule.id!,
        triggeredValue: evaluation.currentValue,
        thresholdValue: evaluation.thresholdValue,
        operatorUsed: evaluation.operator,
        datasetSnapshot: evaluation.datasetSnapshot,
        notificationStatus: {
          email: 'pending',
          websocket: 'pending',
          sms: 'pending'
        }
      });

      console.log(`Created alert event ${alertEvent.id} for rule ${rule.id}`);

      // Send notifications based on rule configuration
      await this.notificationService.sendAlertNotifications(rule, alertEvent, evaluation);

    } catch (error) {
      console.error(`Error handling triggered alert for rule ${rule.id}:`, error);
    }
  }

  /**
   * Negate operator for logging purposes
   */
  private negateOperator(operator: ThresholdOperator): string {
    const negations: Record<ThresholdOperator, string> = {
      '>': '<=',
      '<': '>=',
      '>=': '<',
      '<=': '>',
      '=': '!=',
      '!=': '='
    };
    return negations[operator];
  }

  /**
   * Test a specific rule immediately
   */
  async testRule(ruleId: number): Promise<AlertEvaluationResult | null> {
    try {
      const rule = await this.alertRuleService.getAlertRuleById(ruleId);
      if (!rule) {
        throw new Error(`Alert rule ${ruleId} not found`);
      }

      return await this.evaluateRule(rule);
    } catch (error) {
      console.error(`Error testing rule ${ruleId}:`, error);
      return null;
    }
  }
  /**
   * Get engine status
   */
  getStatus(): {
    isRunning: boolean;
    rulesInQueue: number;
    lastEvaluationTimes: Record<number, Date>;
  } {
    return {
      isRunning: this.isRunning,
      rulesInQueue: this.evaluationQueue.size,
      lastEvaluationTimes: Object.fromEntries(this.evaluationQueue)
    };
  }

  // === FALLBACK MODE METHODS ===

  private fallbackRules: AlertRule[] = [];
  private fallbackEvents: any[] = [];
  /**
   * Create default alert rules for fallback mode
   */
  private async createDefaultFallbackRules(): Promise<void> {
    console.log('🔧 Creating default alert rules for fallback mode...');
    
    this.fallbackRules = [
      {
        id: 1,
        name: 'High Water Level Alert (Demo)',
        description: 'Demonstrates water level monitoring alerts',
        datasetId: 'demo:sentinel_mosaic',
        conditionField: 'band_1',
        thresholdOperator: '>=' as ThresholdOperator,
        thresholdValue: 0.8,
        userId: 1,
        notificationChannels: ['websocket'],
        pollingIntervalMinutes: 5,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        roiId: undefined
      },
      {
        id: 2,
        name: 'Flood Risk Monitoring (Demo)',
        description: 'Demonstrates flood risk area monitoring',
        datasetId: 'demo:flood_risk_areas',
        conditionField: 'risk_level',
        thresholdOperator: '>=' as ThresholdOperator,
        thresholdValue: 0.7,
        userId: 1,
        notificationChannels: ['websocket'],
        pollingIntervalMinutes: 10,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        roiId: undefined
      }
    ];

    console.log(`✅ Created ${this.fallbackRules.length} default alert rules for fallback mode`);
  }

  /**
   * Evaluate all rules in fallback mode (using in-memory rules)
   */
  private async evaluateAllRulesFallback(): Promise<void> {
    try {
      console.log('🔄 Starting fallback alert rule evaluation cycle');
      
      const activeRules = this.fallbackRules.filter(rule => rule.isActive);
      console.log(`Found ${activeRules.length} active fallback alert rules`);

      // Filter rules that need evaluation
      const rulesToEvaluate = activeRules.filter(rule => this.shouldEvaluateRule(rule));
      console.log(`Evaluating ${rulesToEvaluate.length} fallback rules this cycle`);

      // Evaluate rules (simplified for fallback mode)
      for (const rule of rulesToEvaluate) {
        try {
          await this.evaluateRuleFallback(rule);
          this.evaluationQueue.set(rule.id!, new Date());
        } catch (error) {
          console.error(`Error evaluating fallback rule ${rule.id}:`, error);
        }
      }

      console.log('✅ Fallback alert rule evaluation cycle completed');
    } catch (error) {
      console.error('Error during fallback alert rule evaluation:', error);
    }
  }  /**
   * Evaluate a single rule in fallback mode
   */
  private async evaluateRuleFallback(rule: AlertRule): Promise<void> {
    console.log(`📋 Evaluating fallback rule: ${rule.name}`);
    
    // Simulate evaluation with demo data
    const triggered = Math.random() > 0.8; // 20% chance to trigger for demo
    
    if (triggered) {
      const actualValue = rule.thresholdValue + (Math.random() * 0.2); // Slightly above threshold
      
      const alertEvent = {
        id: this.fallbackEvents.length + 1,
        alertRuleId: rule.id!,
        triggeredValue: actualValue,
        thresholdValue: rule.thresholdValue,
        operatorUsed: rule.thresholdOperator,
        triggeredAt: new Date(),
        notificationStatus: 'pending' as any,
        metadata: {
          mode: 'fallback',
          description: 'This is a demonstration alert generated in fallback mode'
        }
      };

      const evaluationResult: AlertEvaluationResult = {
        alertRuleId: rule.id!,
        triggered: true,
        currentValue: actualValue,
        thresholdValue: rule.thresholdValue,
        operator: rule.thresholdOperator,
        datasetSnapshot: {
          datasetId: rule.datasetId,
          field: rule.conditionField,
          value: actualValue,
          fallbackMode: true
        },
        timestamp: new Date()
      };

      // Store in memory
      this.fallbackEvents.push(alertEvent);

      // Send notification
      console.log(`🚨 FALLBACK ALERT TRIGGERED: ${rule.name} - Value: ${actualValue.toFixed(3)} (threshold: ${rule.thresholdValue})`);
      
      try {
        await this.notificationService.sendAlertNotifications(rule, alertEvent, evaluationResult);
        console.log(`✅ Fallback alert notification sent for rule: ${rule.name}`);
      } catch (error) {
        console.error(`❌ Error sending fallback notification for rule ${rule.name}:`, error);
      }
    } else {
      console.log(`✅ Fallback rule evaluated: ${rule.name} - No alert triggered`);
    }
  }
}
