import { Pool } from 'pg';
import { AlertEvent, AlertEventResponse, AlertEventsQuery, AcknowledgeAlertRequest } from '../types/alertRule';
import { AlertEventWithDetails } from '../types/alertEvent';
import { DatabaseService } from './databaseService';

export class AlertEventService {
  private db: Pool;

  constructor() {
    this.db = DatabaseService.getPool();
  }

  async getAlertEvents(query: AlertEventsQuery): Promise<{
    events: AlertEventResponse[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      userId,
      alertRuleId,
      acknowledged,
      startDate,
      endDate,
      page = 1,
      limit = 20
    } = query;

    const offset = (page - 1) * limit;
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    // Build WHERE conditions
    if (userId) {
      params.push(userId);
      whereClause += ` AND ar.user_id = $${params.length}`;
    }

    if (alertRuleId) {
      params.push(alertRuleId);
      whereClause += ` AND ae.alert_rule_id = $${params.length}`;
    }

    if (acknowledged !== undefined) {
      if (acknowledged) {
        whereClause += ` AND ae.acknowledged_at IS NOT NULL`;
      } else {
        whereClause += ` AND ae.acknowledged_at IS NULL`;
      }
    }

    if (startDate) {
      params.push(startDate);
      whereClause += ` AND ae.triggered_at >= $${params.length}`;
    }

    if (endDate) {
      params.push(endDate);
      whereClause += ` AND ae.triggered_at <= $${params.length}`;
    }

    // Add pagination params
    params.push(limit, offset);

    const dataQuery = `
      SELECT 
        ae.*,
        ar.name as alert_rule_name,
        ar.description as alert_rule_description,
        ar.dataset_id,
        u.username as acknowledged_by_username
      FROM alert_events ae
      JOIN alert_rules ar ON ae.alert_rule_id = ar.id
      LEFT JOIN users u ON ae.acknowledged_by = u.id
      ${whereClause}
      ORDER BY ae.triggered_at DESC
      LIMIT $${params.length - 1} OFFSET $${params.length}
    `;

    const countQuery = `
      SELECT COUNT(*) as total
      FROM alert_events ae
      JOIN alert_rules ar ON ae.alert_rule_id = ar.id
      ${whereClause}
    `;

    try {
      const [dataResult, countResult] = await Promise.all([
        this.db.query(dataQuery, params),
        this.db.query(countQuery, params.slice(0, -2)) // Remove limit and offset for count
      ]);

      const events: AlertEventResponse[] = dataResult.rows.map(row => ({
        id: row.id,
        alertRuleId: row.alert_rule_id,
        triggeredValue: parseFloat(row.triggered_value),
        thresholdValue: parseFloat(row.threshold_value),
        operatorUsed: row.operator_used,
        datasetSnapshot: row.dataset_snapshot,
        triggeredAt: row.triggered_at,
        acknowledgedAt: row.acknowledged_at,
        acknowledgedBy: row.acknowledged_by,
        notificationStatus: row.notification_status,
        metadata: row.metadata,
        alertRule: {
          id: row.alert_rule_id,
          name: row.alert_rule_name,
          description: row.alert_rule_description,
          datasetId: row.dataset_id
        },
        acknowledgedByUser: row.acknowledged_by ? {
          id: row.acknowledged_by,
          username: row.acknowledged_by_username
        } : undefined
      }));

      return {
        events,
        total: parseInt(countResult.rows[0].total),
        page,
        limit
      };
    } catch (error) {
      console.error('Error fetching alert events:', error);
      throw new Error('Failed to fetch alert events');
    }
  }

  async getAlertEventById(id: number, userId?: number): Promise<AlertEventResponse | null> {
    let query = `
      SELECT 
        ae.*,
        ar.name as alert_rule_name,
        ar.description as alert_rule_description,
        ar.dataset_id,
        ar.user_id as rule_user_id,
        u.username as acknowledged_by_username
      FROM alert_events ae
      JOIN alert_rules ar ON ae.alert_rule_id = ar.id
      LEFT JOIN users u ON ae.acknowledged_by = u.id
      WHERE ae.id = $1
    `;

    const params = [id];

    if (userId) {
      query += ` AND ar.user_id = $2`;
      params.push(userId);
    }

    try {
      const result = await this.db.query(query, params);

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];

      return {
        id: row.id,
        alertRuleId: row.alert_rule_id,
        triggeredValue: parseFloat(row.triggered_value),
        thresholdValue: parseFloat(row.threshold_value),
        operatorUsed: row.operator_used,
        datasetSnapshot: row.dataset_snapshot,
        triggeredAt: row.triggered_at,
        acknowledgedAt: row.acknowledged_at,
        acknowledgedBy: row.acknowledged_by,
        notificationStatus: row.notification_status,
        metadata: row.metadata,
        alertRule: {
          id: row.alert_rule_id,
          name: row.alert_rule_name,
          description: row.alert_rule_description,
          datasetId: row.dataset_id
        },
        acknowledgedByUser: row.acknowledged_by ? {
          id: row.acknowledged_by,
          username: row.acknowledged_by_username
        } : undefined
      };
    } catch (error) {
      console.error('Error fetching alert event:', error);
      throw new Error('Failed to fetch alert event');
    }
  }

  async acknowledgeAlert(id: number, request: AcknowledgeAlertRequest): Promise<AlertEventResponse | null> {
    const { userId } = request;

    const query = `
      UPDATE alert_events 
      SET acknowledged_at = NOW(), acknowledged_by = $2
      WHERE id = $1 AND acknowledged_at IS NULL
      RETURNING *
    `;

    try {
      const result = await this.db.query(query, [id, userId]);

      if (result.rows.length === 0) {
        return null;
      }

      return this.getAlertEventById(id);
    } catch (error) {
      console.error('Error acknowledging alert:', error);
      throw new Error('Failed to acknowledge alert');
    }
  }

  async createAlertEvent(alertEvent: Omit<AlertEvent, 'id' | 'triggeredAt'>): Promise<AlertEvent> {
    const query = `
      INSERT INTO alert_events (
        alert_rule_id, triggered_value, threshold_value, operator_used,
        dataset_snapshot, notification_status, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const params = [
      alertEvent.alertRuleId,
      alertEvent.triggeredValue,
      alertEvent.thresholdValue,
      alertEvent.operatorUsed,
      JSON.stringify(alertEvent.datasetSnapshot || {}),
      JSON.stringify(alertEvent.notificationStatus),
      JSON.stringify(alertEvent.metadata || {})
    ];

    try {
      const result = await this.db.query(query, params);
      const row = result.rows[0];

      return {
        id: row.id,
        alertRuleId: row.alert_rule_id,
        triggeredValue: parseFloat(row.triggered_value),
        thresholdValue: parseFloat(row.threshold_value),
        operatorUsed: row.operator_used,
        datasetSnapshot: row.dataset_snapshot,
        triggeredAt: row.triggered_at,
        acknowledgedAt: row.acknowledged_at,
        acknowledgedBy: row.acknowledged_by,
        notificationStatus: row.notification_status,
        metadata: row.metadata
      };
    } catch (error) {
      console.error('Error creating alert event:', error);
      throw new Error('Failed to create alert event');
    }
  }

  async getAlertStats(options: {
    userId?: number;
    startDate?: string;
    endDate?: string;
  }): Promise<{
    total: number;
    acknowledged: number;
    unacknowledged: number;
    byRule: Array<{ alertRuleId: number; ruleName: string; count: number }>;
    byDay: Array<{ date: string; count: number }>;
  }> {
    const { userId, startDate, endDate } = options;

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (userId) {
      params.push(userId);
      whereClause += ` AND ar.user_id = $${params.length}`;
    }

    if (startDate) {
      params.push(startDate);
      whereClause += ` AND ae.triggered_at >= $${params.length}`;
    }

    if (endDate) {
      params.push(endDate);
      whereClause += ` AND ae.triggered_at <= $${params.length}`;
    }

    const statsQuery = `
      SELECT 
        COUNT(*) as total,
        COUNT(ae.acknowledged_at) as acknowledged,
        COUNT(*) - COUNT(ae.acknowledged_at) as unacknowledged
      FROM alert_events ae
      JOIN alert_rules ar ON ae.alert_rule_id = ar.id
      ${whereClause}
    `;

    const byRuleQuery = `
      SELECT 
        ae.alert_rule_id,
        ar.name as rule_name,
        COUNT(*) as count
      FROM alert_events ae
      JOIN alert_rules ar ON ae.alert_rule_id = ar.id
      ${whereClause}
      GROUP BY ae.alert_rule_id, ar.name
      ORDER BY count DESC
    `;

    const byDayQuery = `
      SELECT 
        DATE(ae.triggered_at) as date,
        COUNT(*) as count
      FROM alert_events ae
      JOIN alert_rules ar ON ae.alert_rule_id = ar.id
      ${whereClause}
      GROUP BY DATE(ae.triggered_at)
      ORDER BY date DESC
      LIMIT 30
    `;

    try {
      const [statsResult, byRuleResult, byDayResult] = await Promise.all([
        this.db.query(statsQuery, params),
        this.db.query(byRuleQuery, params),
        this.db.query(byDayQuery, params)
      ]);

      return {
        total: parseInt(statsResult.rows[0].total),
        acknowledged: parseInt(statsResult.rows[0].acknowledged),
        unacknowledged: parseInt(statsResult.rows[0].unacknowledged),
        byRule: byRuleResult.rows.map(row => ({
          alertRuleId: row.alert_rule_id,
          ruleName: row.rule_name,
          count: parseInt(row.count)
        })),
        byDay: byDayResult.rows.map(row => ({
          date: row.date,
          count: parseInt(row.count)
        }))
      };
    } catch (error) {
      console.error('Error fetching alert stats:', error);
      throw new Error('Failed to fetch alert stats');
    }
  }

  async getUnacknowledgedCount(userId?: number): Promise<number> {
    let query = `
      SELECT COUNT(*) as count
      FROM alert_events ae
      JOIN alert_rules ar ON ae.alert_rule_id = ar.id
      WHERE ae.acknowledged_at IS NULL
    `;

    const params: any[] = [];

    if (userId) {
      params.push(userId);
      query += ` AND ar.user_id = $${params.length}`;
    }

    try {
      const result = await this.db.query(query, params);
      return parseInt(result.rows[0].count);
    } catch (error) {
      console.error('Error fetching unacknowledged count:', error);
      throw new Error('Failed to fetch unacknowledged count');
    }
  }

  async bulkAcknowledgeAlerts(alertEventIds: number[], userId: number): Promise<number> {
    if (alertEventIds.length === 0) return 0;

    const placeholders = alertEventIds.map((_, index) => `$${index + 1}`).join(',');
    const query = `
      UPDATE alert_events 
      SET acknowledged_at = NOW(), acknowledged_by = $${alertEventIds.length + 1}
      WHERE id IN (${placeholders}) AND acknowledged_at IS NULL
    `;

    const params = [...alertEventIds, userId];

    try {
      const result = await this.db.query(query, params);
      return result.rowCount || 0;
    } catch (error) {
      console.error('Error bulk acknowledging alerts:', error);
      throw new Error('Failed to bulk acknowledge alerts');
    }
  }
}
