import { Pool } from 'pg';
import { 
  AlertRule, 
  AlertRuleResponse, 
  CreateAlertRuleRequest, 
  UpdateAlertRuleRequest,
  AlertEvaluationResult 
} from '../types/alertRule';
import { DatabaseService } from './databaseService';
import { DatasetService } from './datasetService';

export class AlertRuleService {
  private db: Pool;
  private datasetService: DatasetService;

  constructor() {
    this.db = DatabaseService.getPool();
    this.datasetService = new DatasetService();
  }

  async getAlertRules(options: {
    userId?: number;
    page: number;
    limit: number;
    isActive?: boolean;
  }): Promise<{ rules: AlertRuleResponse[]; total: number; page: number; limit: number }> {
    const { userId, page, limit, isActive } = options;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (userId) {
      params.push(userId);
      whereClause += ` AND ar.user_id = $${params.length}`;
    }

    if (isActive !== undefined) {
      params.push(isActive);
      whereClause += ` AND ar.is_active = $${params.length}`;
    }

    // Add pagination params
    params.push(limit, offset);

    const query = `
      SELECT 
        ar.*,
        u.username, u.email,
        roi.name as roi_name
      FROM alert_rules ar
      LEFT JOIN users u ON ar.user_id = u.id
      LEFT JOIN regions_of_interest roi ON ar.roi_id = roi.id
      ${whereClause}
      ORDER BY ar.created_at DESC
      LIMIT $${params.length - 1} OFFSET $${params.length}
    `;

    const countQuery = `
      SELECT COUNT(*) as total
      FROM alert_rules ar
      ${whereClause}
    `;

    try {
      const [dataResult, countResult] = await Promise.all([
        this.db.query(query, params),
        this.db.query(countQuery, params.slice(0, -2)) // Remove limit and offset for count
      ]);

      const rules: AlertRuleResponse[] = await Promise.all(
        dataResult.rows.map(async (row) => {          // Fetch dataset info
          const dataset = await this.datasetService.getDatasetById(row.dataset_id);
          
          return {
            id: row.id,
            name: row.name,
            description: row.description,
            datasetId: row.dataset_id,
            thresholdValue: parseFloat(row.threshold_value),
            thresholdOperator: row.threshold_operator,
            conditionField: row.condition_field,
            userId: row.user_id,
            roiId: row.roi_id,
            notificationChannels: row.notification_channels,
            isActive: row.is_active,
            pollingIntervalMinutes: row.polling_interval_minutes,
            metadata: row.metadata,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
            dataset: dataset ? {
              id: dataset.pk,
              title: dataset.title,
              description: dataset.abstract
            } : undefined,
            user: {
              id: row.user_id,
              username: row.username,
              email: row.email
            },
            roi: row.roi_id ? {
              id: row.roi_id,
              name: row.roi_name
            } : undefined
          };
        })
      );

      return {
        rules,
        total: parseInt(countResult.rows[0].total),
        page,
        limit
      };
    } catch (error) {
      console.error('Error fetching alert rules:', error);
      throw new Error('Failed to fetch alert rules');
    }
  }

  async getAlertRuleById(id: number, userId?: number): Promise<AlertRuleResponse | null> {
    let query = `
      SELECT 
        ar.*,
        u.username, u.email,
        roi.name as roi_name
      FROM alert_rules ar
      LEFT JOIN users u ON ar.user_id = u.id
      LEFT JOIN regions_of_interest roi ON ar.roi_id = roi.id
      WHERE ar.id = $1
    `;
    
    const params = [id];
    
    if (userId) {
      query += ` AND ar.user_id = $2`;
      params.push(userId);
    }

    try {
      const result = await this.db.query(query, params);
      
      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];
      const dataset = await this.datasetService.getDatasetById(row.dataset_id);

      return {
        id: row.id,
        name: row.name,
        description: row.description,
        datasetId: row.dataset_id,
        thresholdValue: parseFloat(row.threshold_value),
        thresholdOperator: row.threshold_operator,
        conditionField: row.condition_field,
        userId: row.user_id,
        roiId: row.roi_id,
        notificationChannels: row.notification_channels,
        isActive: row.is_active,
        pollingIntervalMinutes: row.polling_interval_minutes,
        metadata: row.metadata,
        createdAt: row.created_at,
        updatedAt: row.updated_at,        dataset: dataset ? {
          id: dataset.pk,
          title: dataset.title,
          description: dataset.abstract
        } : undefined,
        user: {
          id: row.user_id,
          username: row.username,
          email: row.email
        },
        roi: row.roi_id ? {
          id: row.roi_id,
          name: row.roi_name
        } : undefined
      };
    } catch (error) {
      console.error('Error fetching alert rule:', error);
      throw new Error('Failed to fetch alert rule');
    }
  }

  async createAlertRule(request: CreateAlertRuleRequest & { userId: number }): Promise<AlertRuleResponse> {
    const {
      name,
      description,
      datasetId,
      thresholdValue,
      thresholdOperator,
      conditionField = 'value',
      userId,
      roiId,
      notificationChannels = ['websocket', 'email'],
      pollingIntervalMinutes = 5
    } = request;

    const query = `
      INSERT INTO alert_rules (
        name, description, dataset_id, threshold_value, threshold_operator,
        condition_field, user_id, roi_id, notification_channels, polling_interval_minutes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `;

    const params = [
      name, description, datasetId, thresholdValue, thresholdOperator,
      conditionField, userId, roiId, notificationChannels, pollingIntervalMinutes
    ];

    try {
      const result = await this.db.query(query, params);
      const row = result.rows[0];

      // Fetch related data
      const dataset = await this.datasetService.getDatasetById(datasetId);

      return {
        id: row.id,
        name: row.name,
        description: row.description,
        datasetId: row.dataset_id,
        thresholdValue: parseFloat(row.threshold_value),
        thresholdOperator: row.threshold_operator,
        conditionField: row.condition_field,
        userId: row.user_id,
        roiId: row.roi_id,
        notificationChannels: row.notification_channels,
        isActive: row.is_active,
        pollingIntervalMinutes: row.polling_interval_minutes,
        metadata: row.metadata,
        createdAt: row.created_at,
        updatedAt: row.updated_at,        dataset: dataset ? {
          id: dataset.pk,
          title: dataset.title,
          description: dataset.abstract
        } : undefined
      };
    } catch (error) {
      console.error('Error creating alert rule:', error);
      throw new Error('Failed to create alert rule');
    }
  }

  async updateAlertRule(
    id: number, 
    request: UpdateAlertRuleRequest, 
    userId?: number
  ): Promise<AlertRuleResponse | null> {
    const updateFields: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(request).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = this.camelToSnake(key);
        updateFields.push(`${dbField} = $${paramIndex}`);
        params.push(value);
        paramIndex++;
      }
    });

    if (updateFields.length === 0) {
      return this.getAlertRuleById(id, userId);
    }

    // Add updated_at
    updateFields.push(`updated_at = NOW()`);

    let query = `
      UPDATE alert_rules 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
    `;
    params.push(id);

    if (userId) {
      query += ` AND user_id = $${paramIndex + 1}`;
      params.push(userId);
    }

    query += ` RETURNING *`;

    try {
      const result = await this.db.query(query, params);
      
      if (result.rows.length === 0) {
        return null;
      }

      return this.getAlertRuleById(id, userId);
    } catch (error) {
      console.error('Error updating alert rule:', error);
      throw new Error('Failed to update alert rule');
    }
  }

  async deleteAlertRule(id: number, userId?: number): Promise<boolean> {
    let query = `DELETE FROM alert_rules WHERE id = $1`;
    const params = [id];

    if (userId) {
      query += ` AND user_id = $2`;
      params.push(userId);
    }

    try {
      const result = await this.db.query(query, params);
      return (result.rowCount || 0) > 0;
    } catch (error) {
      console.error('Error deleting alert rule:', error);
      throw new Error('Failed to delete alert rule');
    }
  }

  async toggleAlertRule(id: number, userId?: number): Promise<AlertRuleResponse | null> {
    let query = `
      UPDATE alert_rules 
      SET is_active = NOT is_active, updated_at = NOW()
      WHERE id = $1
    `;
    const params = [id];

    if (userId) {
      query += ` AND user_id = $2`;
      params.push(userId);
    }

    query += ` RETURNING *`;

    try {
      const result = await this.db.query(query, params);
      
      if (result.rows.length === 0) {
        return null;
      }

      return this.getAlertRuleById(id, userId);
    } catch (error) {
      console.error('Error toggling alert rule:', error);
      throw new Error('Failed to toggle alert rule');
    }
  }

  async testAlertRule(id: number, userId?: number): Promise<AlertEvaluationResult | null> {
    const alertRule = await this.getAlertRuleById(id, userId);
    if (!alertRule) {
      return null;
    }

    // This would integrate with the alert engine to test the rule
    // For now, return a mock result
    return {
      alertRuleId: id,
      triggered: false,
      currentValue: 0,
      thresholdValue: alertRule.thresholdValue,
      operator: alertRule.thresholdOperator,
      datasetSnapshot: {},
      timestamp: new Date()
    };
  }

  async getActiveAlertRules(): Promise<AlertRule[]> {
    const query = `
      SELECT * FROM alert_rules 
      WHERE is_active = true 
      ORDER BY polling_interval_minutes ASC
    `;

    try {
      const result = await this.db.query(query);
      return result.rows.map(row => ({
        id: row.id,
        name: row.name,
        description: row.description,
        datasetId: row.dataset_id,
        thresholdValue: parseFloat(row.threshold_value),
        thresholdOperator: row.threshold_operator,
        conditionField: row.condition_field,
        userId: row.user_id,
        roiId: row.roi_id,
        notificationChannels: row.notification_channels,
        isActive: row.is_active,
        pollingIntervalMinutes: row.polling_interval_minutes,
        metadata: row.metadata,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));
    } catch (error) {
      console.error('Error fetching active alert rules:', error);
      throw new Error('Failed to fetch active alert rules');
    }
  }

  private camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
