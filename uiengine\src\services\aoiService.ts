/**
 * AOI Lifecycle Service for AOI-True Clipping System
 * 
 * This service manages Area of Interest (AOI) lifecycle including storage,
 * validation, simplification, and cleanup. It serves as the single source
 * of truth for AOI data across the application.
 */

import { v4 as uuidv4 } from 'uuid';
import * as turf from '@turf/turf';

export interface AOIGeometry {
  type: 'Polygon' | 'MultiPolygon';
  coordinates: number[][][] | number[][][][];
}

export interface AOIBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

export interface AOICentroid {
  lat: number;
  lng: number;
}

export interface AOIDateRange {
  startDate: string;
  endDate: string;
}

export interface AOIData {
  id: string;
  mode: 'administrative' | 'drawn' | 'pin';
  geometry: AOIGeometry;
  bounds: AOIBounds;
  centroid: AOICentroid;
  area: number; // in square kilometers
  originalGeometry: AOIGeometry; // Unmodified geometry for downloads
  simplifiedWKT?: string; // For CQL when original WKT is too long
  dateRange?: AOIDateRange;
  metadata: {
    name?: string;
    level?: 'province' | 'district' | 'municipality' | 'ward';
    code?: string;
    createdAt: Date;
    lastAccessed: Date;
    validation?: {
      qualityScore: number;
      repairHistory: string[];
      performanceMetrics: {
        validationTime: number;
        repairCount: number;
      };
      geometryHash: string;
    };
  };
}

export interface AOIValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  stats: {
    vertices: number;
    area: number;
    complexity: number;
  };
  repaired?: AOIGeometry;
  repairStatistics?: {
    originalVertexCount: number;
    finalVertexCount: number;
    selfIntersections: number;
    duplicateCoordinates: number;
    repairMethod?: 'none' | 'clean-coords' | 'unkink' | 'simplify' | 'bbox-fallback';
  };
  performance?: {
    totalTime: number; // milliseconds
    parseTime: number;
    cleanTime: number;
    repairTime: number;
    validateTime: number;
  };
  qualityScore?: number; // 0-100 score based on geometry quality
  geometryHash?: string; // Hash for caching validation results
}

export interface AOIServiceConfig {
  maxVertices: number;
  maxArea: number; // in square kilometers
  wktLengthThreshold: number;
  simplificationTolerance: number;
  cacheTimeout: number; // minutes
  validationCacheSize: number; // maximum validation cache entries
  validationCacheTTL: number; // validation cache TTL in minutes
}

interface ValidationCacheEntry {
  result: AOIValidationResult;
  timestamp: number;
  accessCount: number;
}

export class AOIService {
  private aoiCache = new Map<string, AOIData>();
  private validationCache = new Map<string, ValidationCacheEntry>();
  private config: AOIServiceConfig;
  private performanceStats = {
    totalValidations: 0,
    successfulValidations: 0,
    cacheHits: 0,
    averageValidationTime: 0
  };

  // Phase 2A: Functional metrics for clipping strategy decisions
  private clippingMetrics = {
    wktLengthFailures: 0,
    cqlFilterFailures: 0,
    topologyRepairs: 0,
    simplificationTriggers: 0,
    qualityScoreDistribution: {
      excellent: 0, // 90-100
      good: 0,      // 70-89
      fair: 0,      // 50-69
      poor: 0       // 0-49
    }
  };

  constructor(config: AOIServiceConfig) {
    this.config = config;

    // Set default validation cache settings if not provided
    if (!this.config.validationCacheSize) {
      this.config.validationCacheSize = 1000;
    }
    if (!this.config.validationCacheTTL) {
      this.config.validationCacheTTL = 60; // 1 hour default
    }
  }

  /**
   * Create and store a new AOI
   */
  async createAOI(input: {
    mode: 'administrative' | 'drawn' | 'pin';
    geometry: any; // GeoJSON geometry in EPSG:4326
    dateRange?: AOIDateRange;
    metadata?: {
      name?: string;
      level?: 'province' | 'district' | 'municipality' | 'ward';
      code?: string;
    };
  }): Promise<{ aoiId: string; bounds: AOIBounds; centroid: AOICentroid; meta: any }> {
    
    console.log('🎯 Creating new AOI:', { mode: input.mode, hasGeometry: !!input.geometry });

    // Validate input geometry
    const validation = await this.validateGeometry(input.geometry);
    if (!validation.valid) {
      throw new Error(`AOI validation failed: ${validation.errors.join(', ')}`);
    }

    // Log warnings if any
    if (validation.warnings.length > 0) {
      console.warn('⚠️ AOI validation warnings:', validation.warnings);
    }

    // Use repaired geometry if available, otherwise use original
    const geometryToUse = validation.repaired || input.geometry;

    // Log repair statistics if geometry was repaired
    if (validation.repaired && validation.repairStatistics) {
      console.log('🔧 Geometry repairs applied:', validation.repairStatistics);
    }

    // Canonicalize to MultiPolygon
    const canonicalGeometry = this.canonicalizeGeometry(geometryToUse);

    // Calculate bounds and centroid
    const bounds = this.calculateBounds(canonicalGeometry);
    const centroid = this.calculateCentroid(canonicalGeometry);
    const area = this.calculateArea(canonicalGeometry);

    // Generate simplified WKT using enhanced multi-stage simplification (Phase 2A)
    let simplifiedWKT: string | undefined;
    const originalWKT = this.geometryToWKT(canonicalGeometry);

    if (originalWKT.length > this.config.wktLengthThreshold) {
      console.log(`📏 Original WKT too long (${originalWKT.length} chars), using multi-stage simplification`);
      const simplified = await this.multiStageSimplification(canonicalGeometry);
      simplifiedWKT = this.geometryToWKT(simplified);
      console.log(`📏 Multi-stage simplified WKT length: ${simplifiedWKT.length} chars`);
    }

    // Create AOI data
    const aoiId = uuidv4();
    const aoiData: AOIData = {
      id: aoiId,
      mode: input.mode,
      geometry: canonicalGeometry,
      bounds,
      centroid,
      area,
      originalGeometry: canonicalGeometry, // Keep original for downloads
      simplifiedWKT,
      dateRange: input.dateRange,
      metadata: {
        name: input.metadata?.name,
        level: input.metadata?.level,
        code: input.metadata?.code,
        createdAt: new Date(),
        lastAccessed: new Date(),
        validation: {
          qualityScore: validation.qualityScore || 0,
          repairHistory: validation.repairStatistics ? [
            `${validation.repairStatistics.repairMethod}: ${validation.repairStatistics.duplicateCoordinates} duplicates, ${validation.repairStatistics.selfIntersections} intersections`
          ] : [],
          performanceMetrics: {
            validationTime: validation.performance?.totalTime || 0,
            repairCount: (validation.repairStatistics?.duplicateCoordinates || 0) + (validation.repairStatistics?.selfIntersections || 0)
          },
          geometryHash: validation.geometryHash || ''
        }
      }
    };

    // Store in cache
    this.aoiCache.set(aoiId, aoiData);

    console.log(`✅ AOI created: ${aoiId}`, {
      mode: input.mode,
      area: area.toFixed(2) + ' km²',
      vertices: validation.stats.vertices,
      hasSimplified: !!simplifiedWKT
    });

    return {
      aoiId,
      bounds,
      centroid,
      meta: {
        area,
        vertices: validation.stats.vertices,
        hasSimplified: !!simplifiedWKT,
        mode: input.mode
      }
    };
  }

  /**
   * Get AOI data by ID
   */
  getAOI(aoiId: string): AOIData | null {
    const aoi = this.aoiCache.get(aoiId);
    if (aoi) {
      // Update last accessed time
      aoi.metadata.lastAccessed = new Date();
    }
    return aoi || null;
  }

  /**
   * Delete AOI and cleanup associated caches
   */
  deleteAOI(aoiId: string): boolean {
    const deleted = this.aoiCache.delete(aoiId);
    if (deleted) {
      console.log(`🗑️ AOI deleted: ${aoiId}`);
      // TODO: Cleanup associated tile caches, WPS products, etc.
    }
    return deleted;
  }

  /**
   * Get all AOI IDs (for cleanup/monitoring)
   */
  getAllAOIIds(): string[] {
    return Array.from(this.aoiCache.keys());
  }

  /**
   * Cleanup expired AOIs
   */
  cleanupExpiredAOIs(): number {
    const now = new Date();
    const expiredIds: string[] = [];

    for (const [id, aoi] of this.aoiCache.entries()) {
      const ageMinutes = (now.getTime() - aoi.metadata.lastAccessed.getTime()) / (1000 * 60);
      if (ageMinutes > this.config.cacheTimeout) {
        expiredIds.push(id);
      }
    }

    for (const id of expiredIds) {
      this.deleteAOI(id);
    }

    if (expiredIds.length > 0) {
      console.log(`🧹 Cleaned up ${expiredIds.length} expired AOIs`);
    }

    return expiredIds.length;
  }

  /**
   * Get service statistics
   */
  getStats(): {
    totalAOIs: number;
    byMode: Record<string, number>;
    averageArea: number;
    cacheSize: number;
  } {
    const aois = Array.from(this.aoiCache.values());
    const byMode: Record<string, number> = {};

    for (const aoi of aois) {
      byMode[aoi.mode] = (byMode[aoi.mode] || 0) + 1;
    }

    const averageArea = aois.length > 0 
      ? aois.reduce((sum, aoi) => sum + aoi.area, 0) / aois.length 
      : 0;

    return {
      totalAOIs: aois.length,
      byMode,
      averageArea,
      cacheSize: this.aoiCache.size
    };
  }

  /**
   * Update functional clipping metrics (Phase 2A)
   * These metrics directly impact clipping strategy decisions
   */
  private updateClippingMetrics(
    qualityScore: number,
    selfIntersections: number,
    hasTopologyIssues: boolean,
    finalVertices: number
  ): void {
    // Track topology repairs
    if (hasTopologyIssues || selfIntersections > 0) {
      this.clippingMetrics.topologyRepairs++;
    }

    // Track simplification triggers
    if (finalVertices > this.config.wktLengthThreshold / 20) { // Estimate based on WKT length
      this.clippingMetrics.simplificationTriggers++;
    }

    // Track quality score distribution for strategy decisions
    if (qualityScore >= 90) {
      this.clippingMetrics.qualityScoreDistribution.excellent++;
    } else if (qualityScore >= 70) {
      this.clippingMetrics.qualityScoreDistribution.good++;
    } else if (qualityScore >= 50) {
      this.clippingMetrics.qualityScoreDistribution.fair++;
    } else {
      this.clippingMetrics.qualityScoreDistribution.poor++;
    }
  }

  /**
   * Track CQL filter failure for functional metrics (Phase 2A)
   * Called when CQL filter generation or execution fails
   */
  trackCQLFailure(reason: string, layerName?: string): void {
    this.clippingMetrics.cqlFilterFailures++;
    console.warn(`📊 CQL Filter Failure tracked: ${reason}${layerName ? ` (layer: ${layerName})` : ''}`);
  }

  /**
   * Get validation performance statistics
   */
  getValidationStats(): {
    totalValidations: number;
    successRate: number;
    cacheHitRate: number;
    averageValidationTime: number;
    cacheSize: number;
    clippingMetrics?: {
      wktLengthFailures: number;
      cqlFilterFailures: number;
      topologyRepairs: number;
      simplificationTriggers: number;
      qualityScoreDistribution: {
        excellent: number;
        good: number;
        fair: number;
        poor: number;
      };
    };
  } {
    const successRate = this.performanceStats.totalValidations > 0
      ? (this.performanceStats.successfulValidations / this.performanceStats.totalValidations) * 100
      : 0;

    const cacheHitRate = this.performanceStats.totalValidations > 0
      ? (this.performanceStats.cacheHits / this.performanceStats.totalValidations) * 100
      : 0;

    return {
      totalValidations: this.performanceStats.totalValidations,
      successRate: Math.round(successRate * 100) / 100,
      cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      averageValidationTime: Math.round(this.performanceStats.averageValidationTime * 100) / 100,
      cacheSize: this.validationCache.size,
      clippingMetrics: this.clippingMetrics
    };
  }

  /**
   * Generate a hash for geometry to enable validation caching
   */
  private generateGeometryHash(geometry: any): string {
    try {
      const geometryString = JSON.stringify(geometry);
      // Simple hash function for geometry caching
      let hash = 0;
      for (let i = 0; i < geometryString.length; i++) {
        const char = geometryString.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
      }
      return Math.abs(hash).toString(36);
    } catch (error) {
      return 'invalid-geometry';
    }
  }

  /**
   * Calculate geometry quality score (0-100)
   */
  private calculateQualityScore(
    originalVertices: number,
    finalVertices: number,
    selfIntersections: number,
    duplicatesRemoved: number,
    area: number
  ): number {
    let score = 100;

    // Penalize for repairs needed
    if (selfIntersections > 0) {
      score -= Math.min(selfIntersections * 10, 30); // Max 30 point penalty
    }

    if (duplicatesRemoved > 0) {
      score -= Math.min(duplicatesRemoved * 2, 20); // Max 20 point penalty
    }

    // Penalize for excessive complexity
    const complexity = area > 0 ? finalVertices / area : 0;
    if (complexity > 100) {
      score -= Math.min((complexity - 100) / 10, 20); // Max 20 point penalty
    }

    // Penalize for excessive simplification
    if (originalVertices > 0) {
      const simplificationRatio = (originalVertices - finalVertices) / originalVertices;
      if (simplificationRatio > 0.5) {
        score -= Math.min((simplificationRatio - 0.5) * 40, 15); // Max 15 point penalty
      }
    }

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Manage validation cache (cleanup old entries)
   */
  private cleanupValidationCache(): void {
    const now = Date.now();
    const ttlMs = this.config.validationCacheTTL * 60 * 1000;

    // Remove expired entries
    for (const [hash, entry] of this.validationCache.entries()) {
      if (now - entry.timestamp > ttlMs) {
        this.validationCache.delete(hash);
      }
    }

    // Remove oldest entries if cache is too large
    if (this.validationCache.size > this.config.validationCacheSize) {
      const entries = Array.from(this.validationCache.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp);

      const toRemove = this.validationCache.size - this.config.validationCacheSize;
      for (let i = 0; i < toRemove; i++) {
        this.validationCache.delete(entries[i][0]);
      }
    }
  }

  /**
   * Phase 1 & 2A: Enhanced Comprehensive Validation Pipeline
   * Input Geometry → Parse → Clean Coords → Detect Kinks → Repair → Validate → Output
   */
  private async validateGeometry(geometry: any): Promise<AOIValidationResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    let repairedGeometry = geometry;

    console.log('🔍 Starting comprehensive geometry validation pipeline');

    // Performance tracking
    const performance = {
      totalTime: 0,
      parseTime: 0,
      cleanTime: 0,
      repairTime: 0,
      validateTime: 0
    };

    try {
      // Check validation cache first
      const geometryHash = this.generateGeometryHash(geometry);
      const cachedResult = this.validationCache.get(geometryHash);

      if (cachedResult) {
        const now = Date.now();
        const ttlMs = this.config.validationCacheTTL * 60 * 1000;

        if (now - cachedResult.timestamp < ttlMs) {
          cachedResult.accessCount++;
          this.performanceStats.cacheHits++;
          console.log('🎯 Using cached validation result');
          return cachedResult.result;
        } else {
          this.validationCache.delete(geometryHash);
        }
      }

      // Step 1: Basic structure validation
      const parseStart = Date.now();
      if (!geometry || !geometry.type || !geometry.coordinates) {
        errors.push('Invalid GeoJSON geometry structure');
        return {
          valid: false,
          errors,
          warnings,
          stats: { vertices: 0, area: 0, complexity: 0 },
          geometryHash
        };
      }

      if (geometry.type === 'GeometryCollection') {
        errors.push('GeometryCollection not supported');
        return {
          valid: false,
          errors,
          warnings,
          stats: { vertices: 0, area: 0, complexity: 0 },
          geometryHash
        };
      }

      // Calculate original statistics
      const originalVertices = this.countVertices(geometry);
      const originalArea = this.calculateArea(geometry) / 1000000; // Convert to km²
      performance.parseTime = Date.now() - parseStart;

      // Log original geometry statistics
      console.log(`📊 Original geometry stats: ${originalVertices} vertices, ${originalArea.toFixed(2)} km²`);

      // Step 2: Enhanced coordinate cleaning (Phase 2A)
      const cleanStart = Date.now();
      let duplicatesRemoved = 0;
      try {
        const cleanedGeometry = this.cleanCoordinatesAdvanced(geometry);
        const cleanedVertices = this.countVertices(cleanedGeometry);
        duplicatesRemoved = originalVertices - cleanedVertices;

        if (duplicatesRemoved > 0) {
          warnings.push(`Removed ${duplicatesRemoved} duplicate/near-duplicate coordinates`);
          repairedGeometry = cleanedGeometry;
        }
      } catch (error) {
        warnings.push(`Enhanced coordinate cleaning failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
      performance.cleanTime = Date.now() - cleanStart;

      // Step 3: Enhanced self-intersection detection and repair (Phase 2A)
      const repairStart = Date.now();
      let selfIntersections = 0;
      let repairMethod: string = duplicatesRemoved > 0 ? 'enhanced-clean-coords' : 'none';
      let hasTopologyIssues = false;

      try {
        const repairedWithIntersections = await this.repairSelfIntersections(repairedGeometry);

        // Check if repair was needed (geometry changed)
        const originalWKT = this.geometryToWKT(repairedGeometry);
        const repairedWKT = this.geometryToWKT(repairedWithIntersections);

        if (originalWKT !== repairedWKT) {
          // Estimate number of intersections based on vertex reduction
          const originalVerticesAfterClean = this.countVertices(repairedGeometry);
          const finalVerticesAfterRepair = this.countVertices(repairedWithIntersections);
          selfIntersections = Math.max(0, Math.floor((originalVerticesAfterClean - finalVerticesAfterRepair) / 4));

          if (selfIntersections > 0) {
            warnings.push(`Detected and repaired ${selfIntersections} self-intersections`);
            repairMethod = 'enhanced-unkink';
            hasTopologyIssues = true;
          }

          repairedGeometry = repairedWithIntersections;
        }
      } catch (error) {
        warnings.push(`Enhanced intersection repair failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        hasTopologyIssues = true;
      }
      performance.repairTime = Date.now() - repairStart;

      // Step 4: Final validation
      const validateStart = Date.now();
      const finalValidation = this.performFinalValidation(repairedGeometry);
      errors.push(...finalValidation.errors);
      warnings.push(...finalValidation.warnings);
      performance.validateTime = Date.now() - validateStart;

      // Calculate final statistics
      const finalVertices = this.countVertices(repairedGeometry);
      const finalArea = this.calculateArea(repairedGeometry) / 1000000;
      const complexity = finalArea > 0 ? finalVertices / finalArea : 0;

      // Validate constraints
      if (finalVertices > this.config.maxVertices) {
        errors.push(`Too many vertices: ${finalVertices} > ${this.config.maxVertices}`);
      }

      if (finalArea > this.config.maxArea) {
        errors.push(`Area too large: ${finalArea.toFixed(2)} km² > ${this.config.maxArea} km²`);
      }

      // Performance warnings
      if (complexity > 100) {
        warnings.push(`High complexity geometry: ${complexity.toFixed(1)} vertices/km²`);
      }

      if (finalVertices > 1000) {
        warnings.push(`Large number of vertices: ${finalVertices} (may impact performance)`);
      }

      // Calculate enhanced quality score (Phase 2A)
      const qualityScore = this.calculateGeometryQualityScore(
        originalVertices,
        finalVertices,
        selfIntersections,
        duplicatesRemoved,
        hasTopologyIssues
      );

      // Complete performance tracking
      performance.totalTime = Date.now() - startTime;

      // Update performance statistics
      this.performanceStats.totalValidations++;
      if (errors.length === 0) {
        this.performanceStats.successfulValidations++;
      }
      this.performanceStats.averageValidationTime =
        (this.performanceStats.averageValidationTime * (this.performanceStats.totalValidations - 1) + performance.totalTime) /
        this.performanceStats.totalValidations;

      // Update functional clipping metrics (Phase 2A)
      this.updateClippingMetrics(qualityScore, selfIntersections, hasTopologyIssues, finalVertices);

      // Log validation statistics
      console.log('🔍 Validation Pipeline Results:', {
        originalVertices,
        finalVertices,
        duplicatesRemoved,
        selfIntersections,
        repairMethod,
        qualityScore,
        isValid: errors.length === 0,
        warningCount: warnings.length,
        performanceMs: performance.totalTime
      });

      // Create validation result
      const result: AOIValidationResult = {
        valid: errors.length === 0,
        errors,
        warnings,
        stats: { vertices: finalVertices, area: finalArea, complexity },
        repaired: repairedGeometry !== geometry ? repairedGeometry : undefined,
        repairStatistics: {
          originalVertexCount: originalVertices,
          finalVertexCount: finalVertices,
          selfIntersections,
          duplicateCoordinates: duplicatesRemoved,
          repairMethod: repairMethod as any
        },
        performance,
        qualityScore,
        geometryHash
      };

      // Cache the validation result
      this.validationCache.set(geometryHash, {
        result,
        timestamp: Date.now(),
        accessCount: 1
      });

      // Cleanup cache if needed
      this.cleanupValidationCache();

      return result;

    } catch (error) {
      performance.totalTime = Date.now() - startTime;
      errors.push(`Validation pipeline failed: ${error instanceof Error ? error.message : 'Unknown error'}`);

      const failureResult: AOIValidationResult = {
        valid: false,
        errors,
        warnings,
        stats: { vertices: 0, area: 0, complexity: 0 },
        performance,
        qualityScore: 0,
        geometryHash: this.generateGeometryHash(geometry)
      };

      return failureResult;
    }
  }

  /**
   * Canonicalize geometry to MultiPolygon
   */
  private canonicalizeGeometry(geometry: any): AOIGeometry {
    if (geometry.type === 'MultiPolygon') {
      return geometry as AOIGeometry;
    }

    if (geometry.type === 'Polygon') {
      return {
        type: 'MultiPolygon',
        coordinates: [geometry.coordinates]
      };
    }

    throw new Error(`Unsupported geometry type: ${geometry.type}`);
  }

  /**
   * Calculate bounding box from geometry
   */
  private calculateBounds(geometry: AOIGeometry): AOIBounds {
    const coordinates = this.getAllCoordinates(geometry);

    if (coordinates.length === 0) {
      throw new Error('No coordinates found in geometry');
    }

    let minX = coordinates[0][0];
    let minY = coordinates[0][1];
    let maxX = coordinates[0][0];
    let maxY = coordinates[0][1];

    for (const coord of coordinates) {
      minX = Math.min(minX, coord[0]);
      minY = Math.min(minY, coord[1]);
      maxX = Math.max(maxX, coord[0]);
      maxY = Math.max(maxY, coord[1]);
    }

    return {
      west: minX,
      south: minY,
      east: maxX,
      north: maxY
    };
  }

  /**
   * Calculate centroid from geometry using Turf.js
   */
  private calculateCentroid(geometry: AOIGeometry): AOICentroid {
    try {
      // Use Turf.js centroid for more accurate calculation
      if (turf.centroid) {
        const feature: any = {
          type: 'Feature',
          properties: {},
          geometry: geometry
        };

        const centroidFeature = turf.centroid(feature);
        const [lng, lat] = centroidFeature.geometry.coordinates;

        return { lng, lat };
      } else {
        console.warn('⚠️ Turf.centroid not available, using manual calculation');
        return this.calculateCentroidManual(geometry);
      }
    } catch (error) {
      console.warn('⚠️ Turf centroid calculation failed, using manual fallback:', error);
      return this.calculateCentroidManual(geometry);
    }
  }

  /**
   * Manual centroid calculation fallback
   */
  private calculateCentroidManual(geometry: AOIGeometry): AOICentroid {
    const coordinates = this.getAllCoordinates(geometry);

    if (coordinates.length === 0) {
      throw new Error('No coordinates found in geometry');
    }

    let sumX = 0;
    let sumY = 0;

    for (const coord of coordinates) {
      sumX += coord[0];
      sumY += coord[1];
    }

    return {
      lng: sumX / coordinates.length,
      lat: sumY / coordinates.length
    };
  }

  /**
   * Calculate area in square kilometers (simplified calculation)
   */
  private calculateArea(geometry: AOIGeometry): number {
    // Simplified area calculation using bounding box
    const bounds = this.calculateBounds(geometry);
    const width = bounds.east - bounds.west;
    const height = bounds.north - bounds.south;

    // Convert degrees to approximate km (rough calculation)
    const kmPerDegree = 111; // Approximate km per degree at equator
    return width * height * kmPerDegree * kmPerDegree;
  }

  /**
   * Count vertices in geometry
   */
  private countVertices(geometry: any): number {
    let count = 0;

    if (geometry.type === 'Polygon') {
      for (const ring of geometry.coordinates) {
        count += ring.length;
      }
    } else if (geometry.type === 'MultiPolygon') {
      for (const polygon of geometry.coordinates) {
        for (const ring of polygon) {
          count += ring.length;
        }
      }
    }

    return count;
  }

  /**
   * Simplify geometry to reduce complexity using Turf.js
   */
  private simplifyGeometry(geometry: AOIGeometry, tolerance: number = 0.01): AOIGeometry {
    try {
      // Convert to GeoJSON Feature for Turf operations
      const feature: any = {
        type: 'Feature',
        properties: {},
        geometry: geometry
      };

      // Use Turf simplify with high quality setting
      if (turf.simplify) {
        const simplified = turf.simplify(feature, {
          tolerance: tolerance,
          highQuality: true,
          mutate: false
        });

        console.log(`🔧 Geometry simplified with tolerance ${tolerance}`);
        return simplified.geometry as AOIGeometry;
      } else {
        console.warn('⚠️ Turf.simplify not available, returning original geometry');
        return geometry;
      }
    } catch (error) {
      console.warn('⚠️ Geometry simplification failed, returning original:', error);
      return geometry;
    }
  }

  /**
   * Multi-stage simplification algorithm for optimal WKT generation
   * Phase 2A: Enhanced validation to prevent WKT length failures
   */
  private async multiStageSimplification(geometry: AOIGeometry): Promise<AOIGeometry> {
    try {
      console.log('🔧 Starting multi-stage simplification...');

      // Stage 1: Clean coordinates (remove duplicates)
      let simplified = this.cleanCoordinatesAdvanced(geometry);
      let currentWKTLength = this.estimateWKTLength(simplified);

      console.log(`📏 After coordinate cleaning: ${currentWKTLength} chars (target: ${this.config.wktLengthThreshold})`);

      // Stage 2: Light simplification if approaching threshold
      if (currentWKTLength > this.config.wktLengthThreshold * 0.8) {
        simplified = this.simplifyGeometry(simplified, 0.001);
        currentWKTLength = this.estimateWKTLength(simplified);
        console.log(`📏 After light simplification: ${currentWKTLength} chars`);
      }

      // Stage 3: Moderate simplification if still too large
      if (currentWKTLength > this.config.wktLengthThreshold * 0.9) {
        simplified = this.simplifyGeometry(simplified, 0.005);
        currentWKTLength = this.estimateWKTLength(simplified);
        console.log(`📏 After moderate simplification: ${currentWKTLength} chars`);
      }

      // Stage 4: Aggressive simplification if still exceeding threshold
      if (currentWKTLength > this.config.wktLengthThreshold) {
        simplified = this.simplifyGeometry(simplified, 0.01);
        currentWKTLength = this.estimateWKTLength(simplified);
        console.log(`📏 After aggressive simplification: ${currentWKTLength} chars`);
      }

      // Stage 5: Emergency simplification if still too large
      if (currentWKTLength > this.config.wktLengthThreshold) {
        simplified = this.simplifyGeometry(simplified, 0.05);
        currentWKTLength = this.estimateWKTLength(simplified);
        console.log(`📏 After emergency simplification: ${currentWKTLength} chars`);
      }

      console.log(`✅ Multi-stage simplification complete: ${currentWKTLength} chars`);
      return simplified;

    } catch (error) {
      console.warn('⚠️ Multi-stage simplification failed, using basic simplification:', error);
      return this.simplifyGeometry(geometry, this.config.simplificationTolerance);
    }
  }

  /**
   * Estimate WKT length without generating full WKT string
   * Phase 2A: Performance optimization for decision making
   */
  private estimateWKTLength(geometry: AOIGeometry): number {
    try {
      const vertexCount = this.countVertices(geometry);

      // Estimation based on geometry type and vertex count
      // Each coordinate pair averages ~20 characters in WKT
      let estimatedLength = 0;

      if (geometry.type === 'Polygon') {
        estimatedLength = 50 + (vertexCount * 20); // "POLYGON((" + coords + "))"
      } else if (geometry.type === 'MultiPolygon') {
        estimatedLength = 60 + (vertexCount * 22); // "MULTIPOLYGON(((" + coords + ")))"
      } else {
        // For any other geometry types (future extensibility)
        estimatedLength = vertexCount * 20;
      }

      return estimatedLength;
    } catch (error) {
      console.warn('⚠️ WKT length estimation failed:', error);
      return this.config.wktLengthThreshold + 1; // Conservative estimate
    }
  }

  /**
   * Advanced coordinate cleaning with duplicate removal and precision optimization
   * Phase 2A: Enhanced cleaning for better geometry quality
   */
  private cleanCoordinatesAdvanced(geometry: AOIGeometry): AOIGeometry {
    try {
      // Use Turf.js cleanCoords if available
      if (turf.cleanCoords) {
        const feature: any = {
          type: 'Feature',
          properties: {},
          geometry: geometry
        };

        const cleaned = turf.cleanCoords(feature);
        console.log('🧹 Advanced coordinate cleaning with Turf.js');
        return cleaned.geometry as AOIGeometry;
      } else {
        // Fallback to manual cleaning
        return this.manualCoordinateCleaning(geometry);
      }
    } catch (error) {
      console.warn('⚠️ Advanced coordinate cleaning failed, using manual cleaning:', error);
      return this.manualCoordinateCleaning(geometry);
    }
  }

  /**
   * Manual coordinate cleaning fallback
   */
  private manualCoordinateCleaning(geometry: AOIGeometry): AOIGeometry {
    const tolerance = 0.000001; // ~0.1 meter precision

    const cleanRing = (coordinates: number[][]): number[][] => {
      if (coordinates.length < 2) return coordinates;

      const cleaned: number[][] = [coordinates[0]]; // Always keep first point

      for (let i = 1; i < coordinates.length; i++) {
        const prev = cleaned[cleaned.length - 1];
        const curr = coordinates[i];

        // Check if point is significantly different from previous
        const distance = Math.sqrt(
          Math.pow(curr[0] - prev[0], 2) + Math.pow(curr[1] - prev[1], 2)
        );

        if (distance > tolerance) {
          // Round to reasonable precision (6 decimal places)
          cleaned.push([
            Math.round(curr[0] * 1000000) / 1000000,
            Math.round(curr[1] * 1000000) / 1000000
          ]);
        }
      }

      // Ensure ring is closed for polygons
      if (cleaned.length > 2) {
        const first = cleaned[0];
        const last = cleaned[cleaned.length - 1];
        if (first[0] !== last[0] || first[1] !== last[1]) {
          cleaned.push([first[0], first[1]]);
        }
      }

      return cleaned;
    };

    const cleanedGeometry = { ...geometry };

    switch (geometry.type) {
      case 'Polygon':
        cleanedGeometry.coordinates = (geometry.coordinates as number[][][]).map(cleanRing);
        break;
      case 'MultiPolygon':
        cleanedGeometry.coordinates = (geometry.coordinates as number[][][][]).map(polygon =>
          polygon.map(cleanRing)
        );
        break;
    }

    console.log('🧹 Manual coordinate cleaning completed');
    return cleanedGeometry;
  }

  /**
   * Enhanced self-intersection repair using Turf.js
   * Phase 2A: Prevent CQL filter failures due to invalid geometry
   */
  private async repairSelfIntersections(geometry: AOIGeometry): Promise<AOIGeometry> {
    try {
      console.log('🔧 Checking for self-intersections...');

      // Use Turf.js to detect self-intersections
      if (turf.kinks) {
        const feature: any = {
          type: 'Feature',
          properties: {},
          geometry: geometry
        };

        const kinks = turf.kinks(feature);

        if (kinks.features.length > 0) {
          console.log(`⚠️ Found ${kinks.features.length} self-intersections, attempting repair...`);

          // Use Turf.js unkinkPolygon for automatic repair
          if (turf.unkinkPolygon && (geometry.type === 'Polygon' || geometry.type === 'MultiPolygon')) {
            try {
              const repaired = turf.unkinkPolygon(feature);

              if (repaired && repaired.features && repaired.features.length > 0) {
                // Take the largest polygon if multiple are returned
                const largestFeature = repaired.features.reduce((largest, current) => {
                  const largestArea = turf.area ? turf.area(largest) : 0;
                  const currentArea = turf.area ? turf.area(current) : 0;
                  return currentArea > largestArea ? current : largest;
                });

                console.log('✅ Self-intersections repaired using Turf.js unkinkPolygon');
                return largestFeature.geometry as AOIGeometry;
              }
            } catch (unkinkError) {
              console.warn('⚠️ Turf.js unkinkPolygon failed, trying buffer repair:', unkinkError);
              return this.bufferBasedRepair(geometry);
            }
          } else {
            console.warn('⚠️ Turf.js unkinkPolygon not available, trying buffer repair');
            return this.bufferBasedRepair(geometry);
          }
        } else {
          console.log('✅ No self-intersections detected');
          return geometry;
        }
      } else {
        console.warn('⚠️ Turf.js kinks detection not available, skipping intersection check');
        return geometry;
      }

      return geometry;

    } catch (error) {
      console.warn('⚠️ Self-intersection repair failed:', error);
      return geometry;
    }
  }

  /**
   * Buffer-based repair for topology issues
   * Phase 2A: Fallback repair method using micro-buffer technique
   */
  private bufferBasedRepair(geometry: AOIGeometry): AOIGeometry {
    try {
      console.log('🔧 Attempting buffer-based repair...');

      if (turf.buffer) {
        const feature: any = {
          type: 'Feature',
          properties: {},
          geometry: geometry
        };

        // Apply micro-buffer (0.1 meter) to fix topology issues
        const buffered = turf.buffer(feature, 0.0001, { units: 'kilometers' });

        if (buffered && buffered.geometry) {
          console.log('✅ Buffer-based repair successful');
          return buffered.geometry as AOIGeometry;
        }
      }

      console.warn('⚠️ Buffer-based repair failed or not available');
      return geometry;

    } catch (error) {
      console.warn('⚠️ Buffer-based repair failed:', error);
      return geometry;
    }
  }

  /**
   * Calculate geometry quality score for decision making
   * Phase 2A: Functional metric to guide clipping strategy
   */
  private calculateGeometryQualityScore(
    originalVertices: number,
    finalVertices: number,
    selfIntersections: number,
    duplicatesRemoved: number,
    hasTopologyIssues: boolean
  ): number {
    let score = 100; // Start with perfect score

    // Penalize for self-intersections (major issue)
    if (selfIntersections > 0) {
      score -= Math.min(selfIntersections * 15, 40); // Max 40 point penalty
    }

    // Penalize for topology issues
    if (hasTopologyIssues) {
      score -= 20;
    }

    // Penalize for excessive duplicate coordinates
    if (duplicatesRemoved > originalVertices * 0.1) {
      score -= Math.min((duplicatesRemoved / originalVertices) * 30, 20); // Max 20 point penalty
    }

    // Penalize for excessive simplification
    if (originalVertices > 0) {
      const simplificationRatio = (originalVertices - finalVertices) / originalVertices;
      if (simplificationRatio > 0.5) {
        score -= Math.min((simplificationRatio - 0.5) * 40, 15); // Max 15 point penalty
      }
    }

    // Bonus for reasonable complexity
    if (finalVertices >= 4 && finalVertices <= 1000) {
      score += 5;
    }

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Convert geometry to WKT string with failure tracking (Phase 2A)
   */
  private geometryToWKT(geometry: AOIGeometry): string {
    try {
      let wkt = '';

      if (geometry.type === 'Polygon') {
        const rings = geometry.coordinates.map(ring => {
          const coords = ring as number[][];
          return '(' + coords.map(coord => `${coord[0]} ${coord[1]}`).join(', ') + ')';
        });
        wkt = `POLYGON(${rings.join(', ')})`;
      } else if (geometry.type === 'MultiPolygon') {
        const polygons = geometry.coordinates.map(polygon => {
          const rings = polygon.map(ring => {
            const coords = ring as number[][];
            return '(' + coords.map(coord => `${coord[0]} ${coord[1]}`).join(', ') + ')';
          });
          return `(${rings.join(', ')})`;
        });
        wkt = `MULTIPOLYGON(${polygons.join(', ')})`;
      } else {
        throw new Error(`Unsupported geometry type: ${geometry.type}`);
      }

      // Track WKT length failures for functional metrics
      if (wkt.length > this.config.wktLengthThreshold) {
        this.clippingMetrics.wktLengthFailures++;
        console.warn(`⚠️ WKT length exceeds threshold: ${wkt.length} > ${this.config.wktLengthThreshold}`);
      }

      return wkt;
    } catch (error) {
      this.clippingMetrics.wktLengthFailures++;
      console.error('❌ WKT generation failed:', error);
      throw error;
    }
  }

  /**
   * Get all coordinates from geometry
   */
  private getAllCoordinates(geometry: AOIGeometry): number[][] {
    const coordinates: number[][] = [];

    if (geometry.type === 'Polygon') {
      for (const ring of geometry.coordinates) {
        for (const coord of ring as number[][]) {
          coordinates.push(coord as number[]);
        }
      }
    } else if (geometry.type === 'MultiPolygon') {
      for (const polygon of geometry.coordinates) {
        for (const ring of polygon as number[][][]) {
          for (const coord of ring as number[][]) {
            coordinates.push(coord as number[]);
          }
        }
      }
    }

    return coordinates;
  }

  /**
   * Clean coordinates by removing duplicates and near-duplicates
   */
  private cleanCoordinates(geometry: any, tolerance: number): any {
    try {
      // Convert to GeoJSON Feature for Turf operations
      const feature: any = {
        type: 'Feature',
        properties: {},
        geometry: geometry
      };

      // Use Turf's cleanCoords if available
      if (turf.cleanCoords) {
        const cleaned = turf.cleanCoords(feature, { mutate: false });
        return cleaned.geometry;
      } else {
        // Fallback to manual coordinate cleaning
        return this.manualCleanCoordinates(geometry, tolerance);
      }
    } catch (error) {
      console.warn('Turf cleanCoords failed, using manual cleaning:', error);
      return this.manualCleanCoordinates(geometry, tolerance);
    }
  }

  /**
   * Manual coordinate cleaning fallback
   */
  private manualCleanCoordinates(geometry: any, tolerance: number): any {
    const cleanedGeom = { ...geometry };

    const cleanCoordArray = (coords: number[][]): number[][] => {
      if (coords.length <= 2) return coords;

      const cleaned: number[][] = [coords[0]]; // Always keep first point

      for (let i = 1; i < coords.length; i++) {
        const prev = cleaned[cleaned.length - 1];
        const curr = coords[i];

        // Calculate distance between points
        const distance = Math.sqrt(
          Math.pow(curr[0] - prev[0], 2) + Math.pow(curr[1] - prev[1], 2)
        );

        // Keep point if it's far enough from the previous point
        if (distance > tolerance) {
          cleaned.push(curr);
        }
      }

      return cleaned;
    };

    switch (geometry.type) {
      case 'Polygon':
        cleanedGeom.coordinates = geometry.coordinates.map((ring: number[][]) => cleanCoordArray(ring));
        break;
      case 'MultiPolygon':
        cleanedGeom.coordinates = geometry.coordinates.map((polygon: number[][][]) =>
          polygon.map((ring: number[][]) => cleanCoordArray(ring))
        );
        break;
      case 'LineString':
        cleanedGeom.coordinates = cleanCoordArray(geometry.coordinates);
        break;
      case 'MultiLineString':
        cleanedGeom.coordinates = geometry.coordinates.map((line: number[][]) => cleanCoordArray(line));
        break;
      // Points don't need cleaning
    }

    return cleanedGeom;
  }

  /**
   * Detect and repair self-intersections (kinks) in geometry
   */
  private detectAndRepairKinks(geometry: any): {
    hadKinks: boolean;
    kinkCount: number;
    repairedGeometry: any;
  } {
    try {
      // Convert to GeoJSON Feature for Turf operations
      const feature: any = {
        type: 'Feature',
        properties: {},
        geometry: geometry
      };

      // Check for kinks using Turf
      let kinks: any = null;
      try {
        if (turf.kinks) {
          kinks = turf.kinks(feature);
        }
      } catch (error) {
        console.warn('Turf kinks detection failed:', error);
      }

      const hadKinks = kinks && kinks.features && kinks.features.length > 0;
      const kinkCount = hadKinks ? kinks.features.length : 0;

      if (!hadKinks) {
        return {
          hadKinks: false,
          kinkCount: 0,
          repairedGeometry: geometry
        };
      }

      // Attempt to repair kinks using Turf's unkinkPolygon
      try {
        if (turf.unkinkPolygon && (geometry.type === 'Polygon' || geometry.type === 'MultiPolygon')) {
          const unkinked = turf.unkinkPolygon(feature);

          // unkinkPolygon returns a FeatureCollection, extract the first valid geometry
          if (unkinked.features && unkinked.features.length > 0) {
            const firstFeature = unkinked.features[0];
            if (firstFeature.geometry) {
              return {
                hadKinks: true,
                kinkCount,
                repairedGeometry: firstFeature.geometry
              };
            }
          }
        }
      } catch (error) {
        console.warn('Turf unkinkPolygon failed:', error);
      }

      // Fallback: return original geometry with warning
      return {
        hadKinks: true,
        kinkCount,
        repairedGeometry: geometry
      };

    } catch (error) {
      console.warn('Kink detection failed:', error);
      return {
        hadKinks: false,
        kinkCount: 0,
        repairedGeometry: geometry
      };
    }
  }

  /**
   * Perform final validation checks on geometry
   */
  private performFinalValidation(geometry: any): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const result = {
      isValid: true,
      errors: [] as string[],
      warnings: [] as string[]
    };

    try {
      // Use Turf.js booleanValid for comprehensive validation
      if (turf.booleanValid) {
        try {
          const feature: any = {
            type: 'Feature',
            properties: {},
            geometry: geometry
          };

          const isValidGeometry = turf.booleanValid(feature);
          if (!isValidGeometry) {
            result.isValid = false;
            result.errors.push('Geometry failed Turf.js validity check');
          }
        } catch (error) {
          result.warnings.push(`Turf.js validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Check coordinate structure
      if (!geometry.coordinates || geometry.coordinates.length === 0) {
        result.isValid = false;
        result.errors.push('Empty coordinates array');
        return result;
      }

      // Type-specific validation
      switch (geometry.type) {
        case 'Polygon':
          this.validatePolygonStructure(geometry.coordinates, result);
          break;
        case 'MultiPolygon':
          geometry.coordinates.forEach((polygon: number[][][], index: number) => {
            this.validatePolygonStructure(polygon, result, `MultiPolygon[${index}]`);
          });
          break;
        case 'LineString':
          this.validateLineStringStructure(geometry.coordinates, result);
          break;
        case 'MultiLineString':
          geometry.coordinates.forEach((line: number[][], index: number) => {
            this.validateLineStringStructure(line, result, `MultiLineString[${index}]`);
          });
          break;
        case 'Point':
          this.validatePointStructure(geometry.coordinates, result);
          break;
        case 'MultiPoint':
          geometry.coordinates.forEach((point: number[], index: number) => {
            this.validatePointStructure(point, result, `MultiPoint[${index}]`);
          });
          break;
      }

      // Check for valid coordinate ranges (basic sanity check)
      const bbox = this.calculateBounds(geometry);
      if (bbox) {
        if (Math.abs(bbox.west) > 180 || Math.abs(bbox.east) > 180) {
          result.warnings.push('Longitude values outside valid range (-180 to 180)');
        }
        if (Math.abs(bbox.north) > 90 || Math.abs(bbox.south) > 90) {
          result.warnings.push('Latitude values outside valid range (-90 to 90)');
        }
      }

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Final validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Validate polygon structure
   */
  private validatePolygonStructure(coordinates: number[][][], result: any, prefix = 'Polygon') {
    if (coordinates.length === 0) {
      result.isValid = false;
      result.errors.push(`${prefix}: No rings found`);
      return;
    }

    coordinates.forEach((ring, ringIndex) => {
      if (ring.length < 4) {
        result.isValid = false;
        result.errors.push(`${prefix} ring[${ringIndex}]: Insufficient coordinates (minimum 4 required)`);
      }

      // Check if ring is closed
      const first = ring[0];
      const last = ring[ring.length - 1];
      if (first[0] !== last[0] || first[1] !== last[1]) {
        result.warnings.push(`${prefix} ring[${ringIndex}]: Ring not properly closed`);
      }
    });
  }

  /**
   * Validate LineString structure
   */
  private validateLineStringStructure(coordinates: number[][], result: any, prefix = 'LineString') {
    if (coordinates.length < 2) {
      result.isValid = false;
      result.errors.push(`${prefix}: Insufficient coordinates (minimum 2 required)`);
    }
  }

  /**
   * Validate Point structure
   */
  private validatePointStructure(coordinates: number[], result: any, prefix = 'Point') {
    if (coordinates.length < 2) {
      result.isValid = false;
      result.errors.push(`${prefix}: Insufficient coordinates (longitude and latitude required)`);
    }
  }
}

// Global AOI service instance
let globalAOIService: AOIService | null = null;

/**
 * Initialize global AOI service
 */
export function initializeAOIService(config: AOIServiceConfig): AOIService {
  if (!globalAOIService) {
    globalAOIService = new AOIService(config);
  }
  return globalAOIService;
}

/**
 * Get global AOI service instance
 */
export function getAOIService(): AOIService | null {
  return globalAOIService;
}
