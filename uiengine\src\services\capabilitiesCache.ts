/**
 * Capabilities Cache Service for AOI-True Clipping System
 * 
 * This service probes GeoServer on startup and caches per-layer capabilities
 * to determine the best clipping strategy for each layer type.
 */

import { parseStringPromise } from 'xml2js';
import { secureGet } from '../utils/secureRequest';

export interface LayerCapabilities {
  name: string;
  title: string;
  type: 'vector' | 'raster';
  supports: {
    cql: boolean;
    bbox: boolean;
    wms_sld_clip: boolean;
    wps_crop: boolean;
    server_time: boolean;
  };
  geometryField?: string; // For vector layers
  temporal?: {
    hasData: boolean;
    extent?: string;
    defaultTime?: string;
    units?: string;
  };
  bbox?: {
    minx: number;
    miny: number;
    maxx: number;
    maxy: number;
    crs: string;
  };
  lastUpdated: Date;
}

export interface CapabilitiesCacheConfig {
  geoserverUrl: string;
  username?: string;
  password?: string;
  cacheTimeout: number; // minutes
  wktLengthThreshold: number;
  allowedWorkspaces: string[];
}

export class CapabilitiesCache {
  private cache = new Map<string, LayerCapabilities>();
  private config: CapabilitiesCacheConfig;
  private lastRefresh: Date | null = null;

  constructor(config: CapabilitiesCacheConfig) {
    this.config = config;
  }

  /**
   * Initialize capabilities cache by probing GeoServer
   */
  async initialize(): Promise<void> {
    console.log('🔍 Initializing capabilities cache...');
    await this.refreshCapabilities();
    console.log(`✅ Capabilities cache initialized with ${this.cache.size} layers`);
  }

  /**
   * Refresh capabilities cache by re-probing GeoServer
   */
  async refreshCapabilities(): Promise<void> {
    console.log('🔄 Refreshing capabilities cache...');
    
    try {
      // Clear existing cache
      this.cache.clear();
      
      // Probe WMS capabilities for layer discovery
      const wmsLayers = await this.probeWMSCapabilities();
      
      // Probe WFS capabilities for vector layer details
      const wfsLayers = await this.probeWFSCapabilities();
      
      // Probe WPS capabilities for processing support
      const wpsSupport = await this.probeWPSCapabilities();

      // Probe WMS SLD capabilities for raster clipping
      const sldSupport = await this.probeWMSSLDCapabilities();

      // Combine capabilities for each layer
      for (const wmsLayer of wmsLayers) {
        const capabilities = await this.buildLayerCapabilities(
          wmsLayer,
          wfsLayers,
          wpsSupport,
          sldSupport
        );
        
        if (capabilities) {
          this.cache.set(capabilities.name, capabilities);
        }
      }
      
      this.lastRefresh = new Date();
      console.log(`✅ Capabilities cache refreshed: ${this.cache.size} layers`);
      
    } catch (error) {
      console.error('❌ Failed to refresh capabilities cache:', error);
      throw error;
    }
  }

  /**
   * Get capabilities for a specific layer
   */
  getLayerCapabilities(layerName: string): LayerCapabilities | null {
    return this.cache.get(layerName) || null;
  }

  /**
   * Get all cached layer capabilities
   */
  getAllCapabilities(): LayerCapabilities[] {
    return Array.from(this.cache.values());
  }

  /**
   * Check if cache needs refresh
   */
  needsRefresh(): boolean {
    if (!this.lastRefresh) return true;
    
    const ageMinutes = (Date.now() - this.lastRefresh.getTime()) / (1000 * 60);
    return ageMinutes > this.config.cacheTimeout;
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    totalLayers: number;
    vectorLayers: number;
    rasterLayers: number;
    temporalLayers: number;
    cqlSupportedLayers: number;
    lastRefresh: Date | null;
  } {
    const layers = Array.from(this.cache.values());
    
    return {
      totalLayers: layers.length,
      vectorLayers: layers.filter(l => l.type === 'vector').length,
      rasterLayers: layers.filter(l => l.type === 'raster').length,
      temporalLayers: layers.filter(l => l.temporal?.hasData).length,
      cqlSupportedLayers: layers.filter(l => l.supports.cql).length,
      lastRefresh: this.lastRefresh
    };
  }

  /**
   * Probe WMS capabilities to discover layers
   */
  private async probeWMSCapabilities(): Promise<any[]> {
    const wmsUrl = `${this.config.geoserverUrl}/geonode/ows?service=WMS&version=1.3.0&request=GetCapabilities`;
    
    try {
      console.log(`🌐 Probing WMS capabilities: ${wmsUrl}`);
      const response = await secureGet(wmsUrl);
      
      if (response.status !== 200) {
        throw new Error(`WMS capabilities returned status ${response.status}`);
      }
      
      const parsed = await parseStringPromise(response.data);
      const capabilities = parsed['WMS_Capabilities'];
      
      if (!capabilities?.Capability?.[0]?.Layer?.[0]?.Layer) {
        console.warn('⚠️ No layers found in WMS capabilities');
        return [];
      }
      
      const layers = capabilities.Capability[0].Layer[0].Layer;
      console.log(`📋 Found ${layers.length} WMS layers`);
      
      return layers;
      
    } catch (error) {
      console.error('❌ Failed to probe WMS capabilities:', error);
      return [];
    }
  }

  /**
   * Probe WFS capabilities to identify vector layers
   */
  private async probeWFSCapabilities(): Promise<Map<string, any>> {
    const wfsUrl = `${this.config.geoserverUrl}/geonode/ows?service=WFS&version=2.0.0&request=GetCapabilities`;
    const wfsLayers = new Map<string, any>();
    
    try {
      console.log(`🌐 Probing WFS capabilities: ${wfsUrl}`);
      const response = await secureGet(wfsUrl);
      
      if (response.status !== 200) {
        console.warn(`⚠️ WFS capabilities returned status ${response.status}`);
        return wfsLayers;
      }
      
      const parsed = await parseStringPromise(response.data);
      const featureTypes = parsed['WFS_Capabilities']?.FeatureTypeList?.[0]?.FeatureType || [];
      
      for (const ft of featureTypes) {
        const name = ft.Name?.[0];
        if (name) {
          wfsLayers.set(name, ft);
        }
      }
      
      console.log(`📋 Found ${wfsLayers.size} WFS layers`);
      return wfsLayers;
      
    } catch (error) {
      console.error('❌ Failed to probe WFS capabilities:', error);
      return wfsLayers;
    }
  }

  /**
   * Probe WPS capabilities for processing support
   */
  private async probeWPSCapabilities(): Promise<boolean> {
    const wpsUrl = `${this.config.geoserverUrl}/ows?service=WPS&version=1.0.0&request=GetCapabilities`;

    try {
      console.log(`🌐 Probing WPS capabilities: ${wpsUrl}`);
      const response = await secureGet(wpsUrl);

      if (response.status !== 200) {
        console.warn(`⚠️ WPS capabilities returned status ${response.status}`);
        return false;
      }

      const parsed = await parseStringPromise(response.data);
      const capabilities = parsed['wps:Capabilities'];

      if (!capabilities) {
        console.log(`📋 WPS support: Not available`);
        return false;
      }

      // Check for specific processes we need (gs:CropCoverage)
      const processOfferings = capabilities['wps:ProcessOfferings']?.[0]?.['wps:Process'] || [];
      const hasCropCoverage = processOfferings.some((process: any) => {
        const identifier = process['ows:Identifier']?.[0];
        return identifier === 'gs:CropCoverage';
      });

      console.log(`📋 WPS support: Available${hasCropCoverage ? ' (with gs:CropCoverage)' : ' (no gs:CropCoverage)'}`);
      return hasCropCoverage; // Only return true if we have the specific process we need

    } catch (error) {
      console.warn('⚠️ WPS capabilities probe failed:', error);
      return false;
    }
  }

  /**
   * Probe WMS SLD capabilities for raster clipping support
   */
  private async probeWMSSLDCapabilities(): Promise<boolean> {
    const wmsUrl = `${this.config.geoserverUrl}/ows?service=WMS&version=1.1.1&request=GetCapabilities`;

    try {
      console.log(`🌐 Probing WMS SLD capabilities: ${wmsUrl}`);
      const response = await secureGet(wmsUrl);

      if (response.status !== 200) {
        console.warn(`⚠️ WMS capabilities returned status ${response.status}`);
        return false;
      }

      const parsed = await parseStringPromise(response.data);
      const capability = parsed['WMT_MS_Capabilities']?.Capability?.[0] || parsed['WMS_Capabilities']?.Capability?.[0];

      if (!capability) {
        console.log(`📋 WMS SLD support: Not available (no capabilities)`);
        return false;
      }

      // Check for SLD support in Request capabilities
      const request = capability.Request?.[0];
      const getMap = request?.GetMap?.[0];
      const formats = getMap?.Format || [];

      // Check if SLD_BODY parameter is supported
      const hasSLDSupport = formats.some((format: string) =>
        format.includes('SLD') || getMap?.DCPType?.[0]?.HTTP?.[0]?.Get?.[0]?.OnlineResource?.[0]?.$?.['xlink:href']
      );

      console.log(`📋 WMS SLD support: ${hasSLDSupport ? 'Available' : 'Not available'}`);
      return hasSLDSupport;

    } catch (error) {
      console.warn('⚠️ WMS SLD capabilities probe failed:', error);
      return false;
    }
  }

  /**
   * Build comprehensive capabilities for a layer
   */
  private async buildLayerCapabilities(
    wmsLayer: any,
    wfsLayers: Map<string, any>,
    wpsSupport: boolean,
    sldSupport: boolean
  ): Promise<LayerCapabilities | null> {
    const name = wmsLayer.Name?.[0];
    const title = wmsLayer.Title?.[0] || name;

    if (!name) {
      console.warn('⚠️ Skipping layer without name');
      return null;
    }

    // Check if layer is in allowed workspaces and normalize layer name
    let normalizedName = name;
    if (this.config.allowedWorkspaces.length > 0) {
      let workspace: string;

      if (name.includes(':')) {
        // Layer has explicit workspace prefix (e.g., "geonode:layer_name")
        workspace = name.split(':')[0];
      } else {
        // Layer has no workspace prefix - assume it's in the default workspace
        // For GeoNode, layers without prefix are typically in the 'geonode' workspace
        workspace = 'geonode';
        // Normalize the name to include the workspace prefix for consistency
        normalizedName = `${workspace}:${name}`;
      }

      if (!this.config.allowedWorkspaces.includes(workspace)) {
        console.log(`🚫 Skipping layer ${name} - workspace ${workspace} not allowed`);
        return null;
      }

      console.log(`✅ Layer ${name} accepted - workspace: ${workspace}, normalized: ${normalizedName}`);
    }

    // Determine layer type (vector vs raster)
    const isVector = wfsLayers.has(name);
    const type: 'vector' | 'raster' = isVector ? 'vector' : 'raster';

    // Extract bbox
    const bboxNode = wmsLayer.BoundingBox?.[0]?.$;
    let bbox: LayerCapabilities['bbox'] | undefined;
    if (bboxNode) {
      bbox = {
        minx: parseFloat(bboxNode.minx),
        miny: parseFloat(bboxNode.miny),
        maxx: parseFloat(bboxNode.maxx),
        maxy: parseFloat(bboxNode.maxy),
        crs: bboxNode.CRS || bboxNode.SRS || 'EPSG:4326'
      };
    }

    // Extract temporal information
    let temporal: LayerCapabilities['temporal'] | undefined;
    const dimensions = wmsLayer.Dimension || [];
    const timeDimension = dimensions.find((dim: any) =>
      dim.$.name?.toLowerCase() === 'time'
    );

    if (timeDimension) {
      temporal = {
        hasData: true,
        extent: timeDimension._ || undefined,
        defaultTime: timeDimension.$.default || undefined,
        units: timeDimension.$.units || undefined
      };
    }

    // Determine geometry field for vector layers
    let geometryField: string | undefined;
    if (isVector) {
      // Use the original name (without workspace prefix) for DescribeFeatureType requests
      geometryField = await this.detectGeometryField(name);
    }

    // Determine support capabilities based on layer type and server capabilities
    const supports = {
      cql: isVector, // CQL filtering only supported for vector layers
      bbox: true, // BBOX always supported
      wms_sld_clip: sldSupport && !isVector, // SLD clipping for raster layers if WMS supports SLD
      wps_crop: wpsSupport && !isVector, // WPS crop for raster layers if WPS available
      server_time: !!temporal // Time filtering if temporal dimension exists
    };

    return {
      name: normalizedName,
      title,
      type,
      supports,
      geometryField,
      temporal,
      bbox,
      lastUpdated: new Date()
    };
  }

  /**
   * Detect geometry field for vector layers
   */
  private async detectGeometryField(layerName: string): Promise<string> {
    const describeUrl = `${this.config.geoserverUrl}/geonode/ows?` + new URLSearchParams({
      service: 'WFS',
      request: 'DescribeFeatureType',
      typeName: layerName,
      version: '1.0.0'
    });

    try {
      const response = await secureGet(describeUrl);
      if (response.status !== 200) {
        console.warn(`⚠️ DescribeFeatureType failed for ${layerName}: ${response.status}`);
        return 'the_geom'; // Default fallback
      }

      const parsed = await parseStringPromise(response.data);
      const schema = parsed['xsd:schema'] || parsed.schema;

      if (!schema) {
        console.warn(`⚠️ No schema found for ${layerName}`);
        return 'the_geom';
      }

      // Look for geometry fields in complexType elements
      const complexTypes = schema['xsd:complexType'] || [];
      for (const complexType of complexTypes) {
        const sequence = complexType['xsd:sequence']?.[0];
        if (!sequence) continue;

        const elements = sequence['xsd:element'] || [];
        for (const element of elements) {
          const attrs = element.$;
          if (!attrs) continue;

          const name = attrs.name;
          const type = attrs.type;

          // Check for common geometry field patterns
          if (name && (
            name.toLowerCase().includes('geom') ||
            name.toLowerCase().includes('shape') ||
            type?.includes('gml:') ||
            type?.includes('Geometry')
          )) {
            console.log(`🎯 Detected geometry field for ${layerName}: ${name}`);
            return name;
          }
        }
      }

      console.warn(`⚠️ No geometry field detected for ${layerName}, using default`);
      return 'the_geom';

    } catch (error) {
      console.warn(`⚠️ Error detecting geometry field for ${layerName}:`, error);
      return 'the_geom';
    }
  }
}

// Global capabilities cache instance
let globalCapabilitiesCache: CapabilitiesCache | null = null;

/**
 * Initialize global capabilities cache
 */
export async function initializeCapabilitiesCache(config: CapabilitiesCacheConfig): Promise<CapabilitiesCache> {
  if (!globalCapabilitiesCache) {
    globalCapabilitiesCache = new CapabilitiesCache(config);
    await globalCapabilitiesCache.initialize();
  }
  return globalCapabilitiesCache;
}

/**
 * Get global capabilities cache instance
 */
export function getCapabilitiesCache(): CapabilitiesCache | null {
  return globalCapabilitiesCache;
}
