/**
 * Clipping Method Monitoring Service
 * 
 * Tracks success rates and performance metrics for different clipping methods
 * (SLD, WPS, BBOX) to provide operational visibility.
 */

import { isMonitoringEnabled } from '../config/featureFlags';

export interface ClippingMetrics {
  method: 'SLD' | 'WPS' | 'BBOX' | 'FAILED';
  layerName: string;
  success: boolean;
  processingTime: number;
  timestamp: Date;
  errorCode?: string;
  fallbackReason?: string;
}

export interface ClippingStats {
  totalRequests: number;
  successRate: number;
  methodBreakdown: {
    SLD: { count: number; successRate: number; avgTime: number };
    WPS: { count: number; successRate: number; avgTime: number };
    BBOX: { count: number; successRate: number; avgTime: number };
    FAILED: { count: number; successRate: number; avgTime: number };
  };
  layerStats: Record<string, {
    count: number;
    successRate: number;
    preferredMethod: string;
  }>;
  recentErrors: Array<{
    timestamp: Date;
    method: string;
    layerName: string;
    error: string;
  }>;
}

class ClippingMonitorService {
  private metrics: ClippingMetrics[] = [];
  private readonly maxMetrics = 10000; // Keep last 10k metrics
  private readonly maxErrors = 100; // Keep last 100 errors

  /**
   * Record a clipping operation
   */
  recordClipping(metrics: Omit<ClippingMetrics, 'timestamp'>): void {
    if (!isMonitoringEnabled()) return;

    const record: ClippingMetrics = {
      ...metrics,
      timestamp: new Date()
    };

    this.metrics.push(record);

    // Trim old metrics to prevent memory growth
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log significant events
    if (!record.success) {
      console.warn(`📊 Clipping failed: ${record.method} for ${record.layerName} - ${record.errorCode || 'Unknown error'}`);
    } else if (record.method === 'BBOX' && record.fallbackReason) {
      console.warn(`📊 BBOX fallback used for ${record.layerName}: ${record.fallbackReason}`);
    }
  }

  /**
   * Get comprehensive clipping statistics
   */
  getStats(timeWindow?: number): ClippingStats {
    const cutoff = timeWindow ? new Date(Date.now() - timeWindow * 1000) : new Date(0);
    const relevantMetrics = this.metrics.filter(m => m.timestamp >= cutoff);

    if (relevantMetrics.length === 0) {
      return this.getEmptyStats();
    }

    const totalRequests = relevantMetrics.length;
    const successfulRequests = relevantMetrics.filter(m => m.success).length;
    const successRate = successfulRequests / totalRequests;

    // Method breakdown
    const methodBreakdown = this.calculateMethodBreakdown(relevantMetrics);

    // Layer statistics
    const layerStats = this.calculateLayerStats(relevantMetrics);

    // Recent errors
    const recentErrors = relevantMetrics
      .filter(m => !m.success)
      .slice(-this.maxErrors)
      .map(m => ({
        timestamp: m.timestamp,
        method: m.method,
        layerName: m.layerName,
        error: m.errorCode || m.fallbackReason || 'Unknown error'
      }));

    return {
      totalRequests,
      successRate,
      methodBreakdown,
      layerStats,
      recentErrors
    };
  }

  /**
   * Calculate method-specific statistics
   */
  private calculateMethodBreakdown(metrics: ClippingMetrics[]) {
    const methods = ['SLD', 'WPS', 'BBOX', 'FAILED'] as const;
    const breakdown: any = {};

    for (const method of methods) {
      const methodMetrics = metrics.filter(m => m.method === method);
      const successfulMetrics = methodMetrics.filter(m => m.success);
      
      breakdown[method] = {
        count: methodMetrics.length,
        successRate: methodMetrics.length > 0 ? successfulMetrics.length / methodMetrics.length : 0,
        avgTime: methodMetrics.length > 0 
          ? methodMetrics.reduce((sum, m) => sum + m.processingTime, 0) / methodMetrics.length 
          : 0
      };
    }

    return breakdown;
  }

  /**
   * Calculate per-layer statistics
   */
  private calculateLayerStats(metrics: ClippingMetrics[]) {
    const layerGroups = metrics.reduce((groups, metric) => {
      if (!groups[metric.layerName]) {
        groups[metric.layerName] = [];
      }
      groups[metric.layerName].push(metric);
      return groups;
    }, {} as Record<string, ClippingMetrics[]>);

    const layerStats: Record<string, any> = {};

    for (const [layerName, layerMetrics] of Object.entries(layerGroups)) {
      const successfulMetrics = layerMetrics.filter(m => m.success);
      const methodCounts = layerMetrics.reduce((counts, m) => {
        counts[m.method] = (counts[m.method] || 0) + 1;
        return counts;
      }, {} as Record<string, number>);

      const preferredMethod = Object.entries(methodCounts)
        .sort(([,a], [,b]) => b - a)[0]?.[0] || 'UNKNOWN';

      layerStats[layerName] = {
        count: layerMetrics.length,
        successRate: layerMetrics.length > 0 ? successfulMetrics.length / layerMetrics.length : 0,
        preferredMethod
      };
    }

    return layerStats;
  }

  /**
   * Get empty statistics structure
   */
  private getEmptyStats(): ClippingStats {
    return {
      totalRequests: 0,
      successRate: 0,
      methodBreakdown: {
        SLD: { count: 0, successRate: 0, avgTime: 0 },
        WPS: { count: 0, successRate: 0, avgTime: 0 },
        BBOX: { count: 0, successRate: 0, avgTime: 0 },
        FAILED: { count: 0, successRate: 0, avgTime: 0 }
      },
      layerStats: {},
      recentErrors: []
    };
  }

  /**
   * Clear all metrics (for testing or reset)
   */
  clearMetrics(): void {
    this.metrics = [];
    console.log('📊 Clipping metrics cleared');
  }

  /**
   * Get raw metrics for detailed analysis
   */
  getRawMetrics(limit?: number): ClippingMetrics[] {
    return limit ? this.metrics.slice(-limit) : [...this.metrics];
  }
}

// Global monitoring service instance
let globalMonitor: ClippingMonitorService | null = null;

/**
 * Get global clipping monitor instance
 */
export function getClippingMonitor(): ClippingMonitorService {
  if (!globalMonitor) {
    globalMonitor = new ClippingMonitorService();
  }
  return globalMonitor;
}

/**
 * Convenience function to record clipping operation
 */
export function recordClipping(metrics: Omit<ClippingMetrics, 'timestamp'>): void {
  getClippingMonitor().recordClipping(metrics);
}

/**
 * Convenience function to get clipping statistics
 */
export function getClippingStats(timeWindow?: number): ClippingStats {
  return getClippingMonitor().getStats(timeWindow);
}
