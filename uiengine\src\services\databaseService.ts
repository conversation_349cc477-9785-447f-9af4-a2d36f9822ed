import { Pool, PoolClient } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

export class DatabaseService {
  private static instance: Pool;

  private constructor() {}

  public static getPool(): Pool {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new Pool({
        host: process.env.DB_HOST || 'database',
        port: parseInt(process.env.DB_PORT || '5432'),
        user: process.env.POSTGRES_USER || 'sansa_user',
        password: process.env.POSTGRES_PASSWORD || 'secure_password_123',
        database: process.env.POSTGRES_DB || 'sansa_flood_db',
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });
    }
    return DatabaseService.instance;
  }

  public static async getClient(): Promise<PoolClient> {
    const pool = DatabaseService.getPool();
    return await pool.connect();
  }

  public static async testConnection(): Promise<boolean> {
    try {
      const pool = DatabaseService.getPool();
      const client = await pool.connect();
      await client.query('SELECT NOW()');
      client.release();
      return true;
    } catch (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
  }

  public static async closePool(): Promise<void> {
    if (DatabaseService.instance) {
      await DatabaseService.instance.end();
    }
  }
}
