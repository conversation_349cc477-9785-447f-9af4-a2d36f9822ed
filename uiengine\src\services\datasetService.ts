import { Dataset, DatasetSummary, DatasetSearchParams, ExportOptions } from '../types/dataset';
import { secureGet } from '../utils/secureRequest';

/**
 * Dataset Service - Handles interaction with SANSA GeoNode API
 * This service fetches and processes dataset information from the external SANSA API
 */
export class DatasetService {
  private baseUrl: string;
  private cache: Map<string, any> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  constructor(baseUrl: string = 'https://10.150.16.184') {
    this.baseUrl = baseUrl;
  }

  /**
   * Get all datasets from SANSA API
   */
  async getAllDatasets(): Promise<DatasetSummary[]> {
    const cacheKey = 'all_datasets';
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    try {
      // Fetch from SANSA API using secure request to handle SSL certificates
      const response = await secureGet(`${this.baseUrl}/api/v2/datasets/`);
      if (response.status !== 200) {
        throw new Error(`Failed to fetch datasets: ${response.statusText || response.status}`);
      }

      const data = response.data;
      
      // Handle different response structures
      const datasets = Array.isArray(data) ? data : data.results || data.datasets || [];
      
      const summaries: DatasetSummary[] = datasets.map((dataset: Dataset) => ({
        pk: dataset.pk,
        uuid: dataset.uuid,
        name: dataset.name,
        title: dataset.title,
        abstract: dataset.abstract || dataset.raw_abstract || '',
        thumbnail_url: dataset.thumbnail_url,
        owner: {
          username: dataset.owner.username,
          first_name: dataset.owner.first_name,
          last_name: dataset.owner.last_name,
        },
        created: dataset.created,
        last_updated: dataset.last_updated,
        popular_count: dataset.popular_count,
        keywords: dataset.keywords || [],
        category: dataset.category,
        subtype: dataset.subtype,
        extent: dataset.extent,
        download_urls: dataset.download_urls || []
      }));

      this.setCache(cacheKey, summaries);
      return summaries;
    } catch (error) {
      console.error('Error fetching datasets:', error);
      throw new Error('Failed to fetch datasets from SANSA API');
    }
  }

  /**
   * Get a specific dataset by ID
   */
  async getDatasetById(id: string): Promise<Dataset | null> {
    const cacheKey = `dataset_${id}`;
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    try {
      const response = await fetch(`${this.baseUrl}/api/v2/datasets/${id}/`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error(`Failed to fetch dataset: ${response.statusText}`);
      }

      const dataset: Dataset = await response.json();
      this.setCache(cacheKey, dataset);
      return dataset;
    } catch (error) {
      console.error(`Error fetching dataset ${id}:`, error);
      throw new Error(`Failed to fetch dataset ${id}`);
    }
  }

  /**
   * Search datasets with filters
   */
  async searchDatasets(params: DatasetSearchParams): Promise<{
    datasets: DatasetSummary[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      
      if (params.q) {
        queryParams.append('search', params.q);
      }
      
      if (params.keywords && params.keywords.length > 0) {
        queryParams.append('keywords', params.keywords.join(','));
      }
      
      if (params.owner) {
        queryParams.append('owner', params.owner);
      }
      
      if (params.category) {
        queryParams.append('category', params.category);
      }
      
      if (params.subtype) {
        queryParams.append('subtype', params.subtype);
      }
      
      if (params.date_from) {
        queryParams.append('date_from', params.date_from);
      }
      
      if (params.date_to) {
        queryParams.append('date_to', params.date_to);
      }
      
      if (params.bbox) {
        queryParams.append('bbox', params.bbox.join(','));
      }
      
      const limit = params.limit || 20;
      const offset = params.offset || 0;
      queryParams.append('limit', limit.toString());
      queryParams.append('offset', offset.toString());
      
      if (params.sort) {
        const orderBy = params.order === 'desc' ? `-${params.sort}` : params.sort;
        queryParams.append('ordering', orderBy);
      }

      const response = await fetch(`${this.baseUrl}/api/v2/datasets/?${queryParams.toString()}`);
      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`);
      }

      const data = await response.json();
      const datasets = Array.isArray(data) ? data : data.results || [];
      const total = data.count || datasets.length;

      const summaries: DatasetSummary[] = datasets.map((dataset: Dataset) => ({
        pk: dataset.pk,
        uuid: dataset.uuid,
        name: dataset.name,
        title: dataset.title,
        abstract: dataset.abstract || dataset.raw_abstract || '',
        thumbnail_url: dataset.thumbnail_url,
        owner: {
          username: dataset.owner.username,
          first_name: dataset.owner.first_name,
          last_name: dataset.owner.last_name,
        },
        created: dataset.created,
        last_updated: dataset.last_updated,
        popular_count: dataset.popular_count,
        keywords: dataset.keywords || [],
        category: dataset.category,
        subtype: dataset.subtype,
        extent: dataset.extent,
        download_urls: dataset.download_urls || []
      }));

      return {
        datasets: summaries,
        total,
        page: Math.floor(offset / limit) + 1,
        limit
      };
    } catch (error) {
      console.error('Error searching datasets:', error);
      throw new Error('Dataset search failed');
    }
  }

  /**
   * Get dataset metadata in various formats
   */
  async getDatasetMetadata(id: string, format: 'json' | 'xml' | 'iso' | 'dublin-core' = 'json') {
    try {
      const dataset = await this.getDatasetById(id);
      if (!dataset) {
        throw new Error('Dataset not found');
      }

      // Find metadata links based on format
      const metadataLink = dataset.links.find(link => {
        switch (format) {
          case 'xml':
          case 'iso':
            return link.name === 'ISO' && link.link_type === 'metadata';
          case 'dublin-core':
            return link.name === 'Dublin Core' && link.link_type === 'metadata';
          default:
            return false;
        }
      });

      if (format === 'json') {
        // Return structured JSON metadata
        return {
          id: dataset.pk,
          uuid: dataset.uuid,
          title: dataset.title,
          abstract: dataset.abstract,
          keywords: dataset.keywords,
          owner: dataset.owner,
          license: dataset.license,
          created: dataset.created,
          updated: dataset.last_updated,
          extent: dataset.extent,
          spatial_representation_type: dataset.spatial_representation_type,
          language: dataset.language,
          charset: dataset.charset,
          supplemental_information: dataset.supplemental_information,
          purpose: dataset.purpose,
          constraints: dataset.constraints_other,
          contact: dataset.poc,
          distributor: dataset.distributor,
          originator: dataset.originator,
          publisher: dataset.publisher
        };
      } else if (metadataLink) {
        // Fetch external metadata
        const response = await fetch(metadataLink.url);
        if (response.ok) {
          return await response.text();
        }
      }

      throw new Error(`Metadata format ${format} not available`);
    } catch (error) {
      console.error(`Error fetching metadata for dataset ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get export URL for dataset in specified format
   */
  getExportUrl(dataset: Dataset, options: ExportOptions): string {
    const { format, bbox, srid, attributes } = options;
    
    // Find the appropriate download link
    const downloadLink = dataset.links.find(link => {
      switch (format) {
        case 'csv':
          return link.name === 'CSV' && link.link_type === 'data';
        case 'excel':
          return link.name === 'Excel' && link.link_type === 'data';
        case 'geojson':
          return link.name === 'GeoJSON' && link.link_type === 'data';
        case 'shapefile':
          return link.name === 'Zipped Shapefile' && link.link_type === 'data';
        case 'gml':
          return link.name.includes('GML') && link.link_type === 'data';
        case 'pdf':
          return link.name === 'PDF' && link.link_type === 'image';
        default:
          return false;
      }
    });

    if (!downloadLink) {
      throw new Error(`Export format ${format} not available for this dataset`);
    }

    let url = downloadLink.url;

    // Add additional parameters if needed
    if (bbox && format !== 'pdf') {
      const bboxParam = bbox.join(',');
      url += url.includes('?') ? `&bbox=${bboxParam}` : `?bbox=${bboxParam}`;
    }

    if (srid && format !== 'pdf') {
      url += url.includes('?') ? `&srs=${srid}` : `?srs=${srid}`;
    }

    return url;
  }

  /**
   * Get popular datasets
   */
  async getPopularDatasets(limit: number = 10): Promise<DatasetSummary[]> {
    const datasets = await this.getAllDatasets();
    return datasets
      .sort((a, b) => parseInt(b.popular_count) - parseInt(a.popular_count))
      .slice(0, limit);
  }

  /**
   * Get recent datasets
   */
  async getRecentDatasets(limit: number = 10): Promise<DatasetSummary[]> {
    const datasets = await this.getAllDatasets();
    return datasets
      .sort((a, b) => new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime())
      .slice(0, limit);
  }

  /**
   * Get datasets by keyword
   */
  async getDatasetsByKeyword(keyword: string): Promise<DatasetSummary[]> {
    const datasets = await this.getAllDatasets();
    return datasets.filter(dataset => 
      dataset.keywords.some(k => k.toLowerCase().includes(keyword.toLowerCase())) ||
      dataset.title.toLowerCase().includes(keyword.toLowerCase()) ||
      dataset.abstract.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  // Cache management
  private getFromCache(key: string): any {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  public clearCache(): void {
    this.cache.clear();
  }
}

// Singleton instance
export const datasetService = new DatasetService();
