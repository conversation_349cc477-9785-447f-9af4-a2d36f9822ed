import axios from 'axios';

// TypeScript interfaces for the datasets API response
export interface Main {
    links:     Links;
    total:     number;
    page:      number;
    page_size: number;
    datasets:  Dataset[];
}

export interface Dataset {
    rating:                       string;
    purpose:                      null;
    srid:                         string;
    has_time:                     boolean;
    charset:                      string;
    maintenance_frequency:        null;
    processed:                    boolean;
    temporal_extent_end:          null;
    is_approved:                  boolean;
    abstract:                     string;
    time_regex:                   null;
    temporal_extent_start:        null;
    ll_bbox_polygon:              BboxPolygon;
    keywords:                     Keyword[];
    popular_count:                string;
    processor:                    any[];
    advertised:                   boolean;
    raw_constraints_other:        string;
    polymorphic_ctype_id:         string;
    uuid:                         string;
    license:                      License;
    resource_provider:            any[];
    extent:                       Extent;
    doi:                          null;
    last_updated:                 Date;
    custodian:                    any[];
    link:                         string;
    metadata_uploaded_preserve:   boolean;
    owner:                        Owner;
    spatial_representation_type:  null;
    featured:                     boolean;
    is_mosaic:                    boolean;
    raw_purpose:                  string;
    perms:                        string[];
    links:                        Link[];
    raw_abstract:                 string;
    resource_user:                any[];
    metadata_author:              Owner[];
    distributor:                  any[];
    regions:                      any[];
    thumbnail_url:                string;
    bbox_polygon:                 BboxPolygon;
    publisher:                    any[];
    metadata_only:                boolean;
    state:                        string;
    pk:                           string;
    elevation_regex:              null;
    has_elevation:                boolean;
    store:                        string;
    subtype:                      string;
    sourcetype:                   string;
    group:                        null;
    download_urls:                DownloadURL[];
    principal_investigator:       any[];
    styles:                       Style[];
    is_published:                 boolean;
    attribution:                  null;
    share_count:                  string;
    featureinfo_custom_template:  string;
    ptype:                        string;
    download_url:                 string;
    edition:                      null;
    workspace:                    string;
    tkeywords:                    any[];
    title:                        string;
    date:                         Date;
    data_quality_statement:       null;
    detail_url:                   string;
    originator:                   any[];
    alternate:                    string;
    created:                      Date;
    embed_url:                    string;
    category:                     Category;
    restriction_code_type:        null;
    resource_type:                string;
    language:                     string;
    supplemental_information:     string;
    favorite:                     boolean;
    raw_data_quality_statement:   string;
    name:                         string;
    default_style:                Style;
    poc:                          Owner[];
    constraints_other:            null;
    raw_supplemental_information: string;
    is_copyable:                  boolean;
    date_type:                    string;
}

export interface BboxPolygon {
    type:        string;
    coordinates: Array<Array<number[]>>;
}

export interface Category {
    identifier:     string;
    gn_description: string;
}

export interface Style {
    pk:        number;
    name:      string;
    workspace: string;
    sld_title: string;
    sld_url:   string;
}

export interface DownloadURL {
    url:       string;
    ajax_safe: boolean;
    default:   boolean;
}

export interface Extent {
    coords: number[];
    srid:   string;
}

export interface Keyword {
    name: string;
    slug: string;
}

export interface License {
    identifier: string;
}

export interface Link {
    extension: string;
    link_type: string;
    name:      string;
    mime:      string;
    url:       string;
    extras?:   Extras;
}

export interface Extras {
    type:    string;
    content: Content;
}

export interface Content {
    title:        string;
    description:  null;
    type:         string;
    download_url: string;
}

export interface Owner {
    pk:           number;
    username:     string;
    first_name:   string;
    last_name:    string;
    avatar:       string;
    is_superuser: boolean;
    is_staff:     boolean;
    email:        string;
    link:         string;
    perms?:       string[];
}

export interface Links {
    next:     string;
    previous: null;
}

// Categorized dataset structure
export interface CategorizedDatasets {
    [categoryKey: string]: Dataset[];
}

export interface CategoryInfo {
    key: string;
    title: string;
    description: string;
    count: number;
}

/**
 * Fetch datasets from the GeoNode API
 */
/**
 * Fetch a single page of datasets from the GeoNode API
 */
async function fetchDatasetsPage(page: number = 1): Promise<Main> {
    try {
        console.log(`Fetching datasets page ${page}...`);

        const options = {
            method: 'GET' as const,
            url: `https://*************/api/v2/datasets?page=${page}`,
            headers: {
                'Accept': '*/*',
                'User-Agent': 'SANSA Flood Monitoring App',
                'Authorization': 'Basic Tmdvbmk6QGZyaWNAMSYyMDI1IzI='
            },
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false
            }),
            timeout: 30000
        };

        const { data } = await axios.request(options);
        console.log(`Successfully fetched page ${page}: ${data.datasets?.length || 0} datasets`);

        return data;
    } catch (error: any) {
        console.error(`Error fetching datasets page ${page}:`, error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
        throw new Error(`Failed to fetch datasets page ${page}: ${error.message}`);
    }
}

export async function fetchDatasets(): Promise<Main> {
    try {
        console.log('Starting paginated dataset fetch...');

        // Fetch first page to get pagination info
        const firstPage = await fetchDatasetsPage(1);
        let allDatasets = [...firstPage.datasets];

        // Calculate total pages
        const totalPages = Math.ceil(firstPage.total / firstPage.page_size);
        console.log(`Total datasets: ${firstPage.total}, Page size: ${firstPage.page_size}, Total pages: ${totalPages}`);

        // Fetch remaining pages if any
        if (totalPages > 1) {
            const pagePromises = [];
            for (let page = 2; page <= totalPages; page++) {
                pagePromises.push(fetchDatasetsPage(page));
            }

            console.log(`Fetching ${pagePromises.length} additional pages...`);
            const additionalPages = await Promise.all(pagePromises);

            // Combine all datasets
            for (const pageData of additionalPages) {
                allDatasets = allDatasets.concat(pageData.datasets);
            }
        }

        console.log(`Successfully fetched all ${allDatasets.length} datasets from ${totalPages} pages`);

        // Return combined result
        return {
            ...firstPage,
            datasets: allDatasets,
            total: allDatasets.length
        };

    } catch (error: any) {
        console.error('Error fetching paginated datasets:', error.message);
        throw new Error(`Failed to fetch datasets: ${error.message}`);
    }
}

/**
 * Categorize datasets by their category identifier
 */
export function categorizeDatasets(datasets: Dataset[]): CategorizedDatasets {
    const categorized: CategorizedDatasets = {};
    
    datasets.forEach(dataset => {
        const categoryKey = dataset.category?.identifier || 'other';
        console.log(categoryKey)
        if (!categorized[categoryKey]) {
            categorized[categoryKey] = [];
        }
        
        categorized[categoryKey].push(dataset);
    });
    
    console.log('Datasets categorized:', Object.keys(categorized).map(key => 
        `${key}: ${categorized[key].length} datasets`
    ));
    
    return categorized;
}

/**
 * Extract category information from datasets
 */
export function extractCategories(datasets: Dataset[]): CategoryInfo[] {
    const categoryMap = new Map<string, CategoryInfo>();
    
    datasets.forEach(dataset => {
        const category = dataset.category;
        const categoryKey = category?.identifier || 'other';
        
        if (!categoryMap.has(categoryKey)) {
            categoryMap.set(categoryKey, {
                key: categoryKey,
                title: mapCategoryToUserFriendlyTitle(categoryKey, category?.gn_description),
                description: category?.gn_description || `${mapCategoryToUserFriendlyTitle(categoryKey, category?.gn_description)} datasets`,
                count: 0
            });
        }
        
        const categoryInfo = categoryMap.get(categoryKey)!;
        categoryInfo.count++;
    });
    
    const categories = Array.from(categoryMap.values());
    console.log('Extracted categories:', categories.map(cat => `${cat.title} (${cat.count})`));
    
    return categories.sort((a, b) => b.count - a.count); // Sort by count descending
}

/**
 * Map category key to user-friendly title
 */
function mapCategoryToUserFriendlyTitle(categoryKey: string, gnDescription?: string): string {
    // Use GeoNode description if available and meaningful
    if (gnDescription && gnDescription !== categoryKey && gnDescription.length > 0) {
        return gnDescription;
    }

    // Map common GeoNode categories to user-friendly names
    const categoryMapping: { [key: string]: string } = {
        // Common GeoNode categories
        'biota': 'Biological Data',
        'boundaries': 'Administrative Boundaries',
        'climatologyMeteorologyAtmosphere': 'Climate & Weather',
        'economy': 'Economic Data',
        'elevation': 'Elevation & Terrain',
        'environment': 'Environmental Data',
        'farming': 'Agriculture & Farming',
        'geoscientificInformation': 'Geological Data',
        'health': 'Health Data',
        'imageryBaseMapsEarthCover': 'Satellite & Imagery',
        'intelligenceMilitary': 'Military & Intelligence',
        'inlandWaters': 'Water Resources',
        'location': 'Location Services',
        'oceans': 'Marine & Coastal',
        'planningCadastre': 'Planning & Cadastre',
        'society': 'Social Data',
        'structure': 'Infrastructure',
        'transportation': 'Transportation',
        'utilitiesCommunication': 'Utilities & Communication',

        // Custom categories
        'flood_risk': 'Flood Risk',
        'satellite': 'Satellite Data',
        'historical': 'Historical Data',
        'climatology': 'Climate Data',
        'settlements': 'Human Settlements',
        'service_points': 'Service Points',
        'admin': 'Administrative Layers',
        'basemaps': 'Basemaps & Elevation',
        'other': 'Other Datasets'
    };

    return categoryMapping[categoryKey] || formatCategoryTitle(categoryKey);
}

/**
 * Format category key into a readable title
 */
function formatCategoryTitle(categoryKey: string): string {
    return categoryKey
        .split(/[_-]/)
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}

/**
 * Detect if a dataset is a remote/external layer
 */
function isRemoteLayer(dataset: Dataset): boolean {
    return (
        dataset.subtype === 'remote' ||
        dataset.sourcetype === 'REMOTE' ||
        dataset.workspace === 'remoteWorkspace' ||
        (dataset as any).ptype === 'gxp_arcrestsource' ||
        ((dataset as any).ows_url && !(dataset as any).ows_url.includes('*************'))
    );
}

/**
 * Generate the appropriate layer name based on layer type (local vs remote)
 */
function generateLayerName(dataset: Dataset): string {
    if (isRemoteLayer(dataset)) {
        // For remote layers, use the original name or alternate without workspace prefix
        const originalName = (dataset as any).alternate || dataset.name;

        // Remove any workspace prefix if it exists (e.g., "remoteWorkspace:GPM_3IMERGHH_06" -> "GPM_3IMERGHH_06")
        if (originalName.includes(':')) {
            return originalName.split(':').pop() || originalName;
        }

        return originalName;
    }

    // For local layers, use workspace:layername format
    if (dataset.workspace && dataset.workspace.trim() !== '' && dataset.workspace !== 'remoteWorkspace') {
        return `${dataset.workspace.trim()}:${dataset.name}`;
    } else {
        return `geonode:${dataset.name}`;
    }
}

/**
 * Convert dataset to LayerDiscovery format for compatibility
 */
export function datasetToLayerDiscovery(dataset: Dataset): any {
    // Detect if this is a remote layer
    const isRemote = isRemoteLayer(dataset);

    // Generate appropriate layer name based on type
    const layerName = generateLayerName(dataset);

    console.log(`Converting dataset "${dataset.name}" (workspace: "${dataset.workspace}", remote: ${isRemote}) to layer "${layerName}"`);

    // Ensure we have the required fields for map rendering
    const layerDiscovery = {
        name: layerName, // Use the appropriate layer name
        title: dataset.title,
        abstract: dataset.abstract,
        keywords: dataset.keywords?.map(k => k.name) || [],
        bbox: dataset.extent?.coords || [],
        srs: dataset.srid || 'EPSG:4326',
        temporal: {
            hasTemporal: dataset.has_time,
            timeExtent: dataset.has_time ? {
                start: dataset.temporal_extent_start,
                end: dataset.temporal_extent_end
            } : null
        },
        queryable: true, // Assume all datasets are queryable
        styles: dataset.styles?.map(style => ({
            name: style.name,
            title: style.sld_title,
            url: style.sld_url
        })) || [],
        category: dataset.category?.identifier || 'other',
        subtype: dataset.subtype,
        workspace: dataset.workspace,
        thumbnail_url: dataset.thumbnail_url,
        detail_url: dataset.detail_url,
        download_urls: dataset.download_urls,

        // Additional fields that might be needed for map rendering
        url: isRemote
            ? (dataset as any).ows_url || (dataset as any).dataset_ows_url || dataset.link
            : dataset.link || `https://*************/geoserver/${dataset.workspace}/wms`,
        layers: layerName, // Use the appropriate layer name
        format: 'image/png',
        transparent: true,
        version: '1.1.0',

        // Remote layer specific fields
        isRemote: isRemote,
        serviceType: isRemote ? (dataset as any).ptype : 'wms',
        remoteUrl: isRemote ? (dataset as any).ows_url || (dataset as any).dataset_ows_url : null,

        // Ensure we have proper metadata
        metadata: {
            title: dataset.title,
            abstract: dataset.abstract,
            keywords: dataset.keywords?.map(k => k.name) || [],
            thumbnail: dataset.thumbnail_url,
            detail_url: dataset.detail_url
        }
    };

    console.log(`Converted dataset "${dataset.name}" to LayerDiscovery format`);
    return layerDiscovery;
}
