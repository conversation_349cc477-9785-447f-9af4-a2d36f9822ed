import axios, { AxiosInstance, AxiosResponse } from 'axios';

const DISCOVERY_API_BASE = process.env.DISCOVERY_API_URL || 'https://10.150.16.184/api/v2';
const DISCOVERY_LOGIN_URL = process.env.DISCOVERY_LOGIN_URL || 'https://10.150.16.184/account/login/';
const DISCOVERY_USERNAME = process.env.DISCOVERY_USERNAME;
const DISCOVERY_PASSWORD = process.env.DISCOVERY_PASSWORD;

export class DiscoveriesService {
  private axiosInstance: AxiosInstance;
  private sessionCookie: string | null = null;
  private isLoggingIn = false;
  private loginPromise: Promise<void> | null = null;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: DISCOVERY_API_BASE,
      withCredentials: true,
      // If self-signed certs are used:
      httpsAgent: new (require('https').Agent)({
        rejectUnauthorized: false
      }),
      // Note: Do not set Cookie header globally here
    });
  }

  /**
   * Programmatically log in to Django and store the session cookie.
   */
  private async login(): Promise<void> {
    if (this.isLoggingIn && this.loginPromise) {
      return this.loginPromise; // Prevent multiple concurrent logins
    }
    this.isLoggingIn = true;
    this.loginPromise = new Promise(async (resolve, reject) => {
      try {
        if (!DISCOVERY_USERNAME || !DISCOVERY_PASSWORD) {
          throw new Error('Discovery username or password not set in environment variables.');
        }

        // Django's login form expects CSRF token. Let's get it first.
        const loginPage = await axios.get(DISCOVERY_LOGIN_URL, {
          httpsAgent: new (require('https').Agent)({ rejectUnauthorized: false }),
        });
        const cookies = loginPage.headers['set-cookie'] || [];
        // Extract CSRF token from cookies or HTML (choose your method)
        let csrfToken = '';
        for (const cookie of cookies) {
          if (cookie.startsWith('csrftoken=')) {
            csrfToken = cookie.split(';')[0].split('=')[1];
            break;
          }
        }
        if (!csrfToken) {
          // Optionally: Parse HTML for csrfmiddlewaretoken value in the page
          throw new Error('CSRF token not found in cookies from login page.');
        }

        // POST login credentials (form-encoded)
        const loginResponse: AxiosResponse = await axios.post(
          DISCOVERY_LOGIN_URL,
          new URLSearchParams({
            username: DISCOVERY_USERNAME,
            password: DISCOVERY_PASSWORD,
            csrfmiddlewaretoken: csrfToken,
            next: '/api/v2/',
          }),
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'Cookie': `csrftoken=${csrfToken}`,
              'Referer': DISCOVERY_LOGIN_URL,
            },
            maxRedirects: 0,
            validateStatus: (status) => status === 302 || status === 200,
            httpsAgent: new (require('https').Agent)({ rejectUnauthorized: false }),
          }
        );

        // Get sessionid cookie from login response
        const setCookie = loginResponse.headers['set-cookie'] || [];
        const sessionCookie = setCookie.find((c: string) => c.startsWith('sessionid='));
        if (!sessionCookie) {
          throw new Error('SessionID not found after login.');
        }
        // Save only "sessionid=..." part
        this.sessionCookie = sessionCookie.split(';')[0];

        this.isLoggingIn = false;
        resolve();
      } catch (err) {
        this.isLoggingIn = false;
        this.sessionCookie = null;
        reject(err);
      }
    });
    return this.loginPromise;
  }

  /**
   * Helper to make authenticated requests with session cookie.
   * Automatically (re-)logs in if session is missing/expired.
   */
  private async fetch<T = any>(url: string, options: object = {}): Promise<T> {
    // Ensure login/session first
    if (!this.sessionCookie) {
      await this.login();
    }
    try {
      const response = await this.axiosInstance.get(url, {
        ...options,
        headers: {
          ...(options as any).headers,
          Cookie: this.sessionCookie,
        },
        validateStatus: (status) => status < 500
      });
      // If session expired, try re-login once and retry
      if (response.status === 401 || response.status === 403) {
        this.sessionCookie = null;
        await this.login();
        return await this.fetch<T>(url, options);
      }
      return response.data;
    } catch (err) {
      this.sessionCookie = null;
      throw err;
    }
  }

  async getDatasets(): Promise<any[]> {
    return this.fetch('/datasets');
  }

  async getDatasetById(id: string): Promise<any> {
    return this.fetch(`/datasets/${id}`);
  }

  async getCategories(): Promise<any[]> {
    return this.fetch('/categories');
  }
}

export const discoveriesService = new DiscoveriesService();