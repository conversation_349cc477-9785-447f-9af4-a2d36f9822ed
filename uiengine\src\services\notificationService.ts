// Phase 3 Notification Service - Console Implementation for Development
// Email/SMS code preserved for future production implementation

// IMPORTANT: All notifications are currently output to console for development
// Production email/SMS code is commented out but preserved for future use

// import nodemailer from 'nodemailer'; // TODO: Enable for production
// import { Server as SocketIOServer } from 'socket.io'; // TODO: Enable for production
import { AlertRule, AlertEvent, AlertEvaluationResult, AlertEmailTemplate, AlertSMSTemplate } from '../types/alertRule';

export class NotificationService {
  // private emailTransporter: nodemailer.Transporter | null = null; // TODO: Enable for production
  private socketIO: any | null = null; // Temporarily use 'any' to avoid socket.io dependency
  private developmentMode: boolean = true; // Flag to control console vs actual notifications

  constructor() {
    console.log('🔧 NotificationService initialized in development mode (console output only)');
    console.log('📧 Email notifications: Console output (production code preserved)');
    console.log('🔌 WebSocket notifications: Console output (production code preserved)');
    console.log('📱 SMS notifications: Console output (production code preserved)');
    // this.initializeEmailTransporter(); // TODO: Enable for production
  }
  /**
   * Initialize email transporter - DISABLED FOR DEVELOPMENT
   */
  private initializeEmailTransporter(): void {
    console.log('📧 Email transporter initialization skipped (development mode)');
    
    /* TODO: Enable for production
    try {
      if (!process.env.SMTP_HOST) {
        console.warn('SMTP configuration not found - email notifications disabled');
        return;
      }

      this.emailTransporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      });

      console.log('Email transporter initialized');
    } catch (error) {
      console.error('Failed to initialize email transporter:', error);
    }
    */
  }

  /**
   * Set Socket.IO server instance - DEVELOPMENT MODE
   */
  setSocketIO(io: any): void {
    this.socketIO = io;
    console.log('🔌 Socket.IO server set for notifications (development mode)');
  }

  /**
   * Send all configured notifications for an alert
   */
  async sendAlertNotifications(
    rule: AlertRule,
    alertEvent: AlertEvent,
    evaluation: AlertEvaluationResult
  ): Promise<void> {
    const notifications = [];

    // Send notifications based on rule configuration
    if (rule.notificationChannels.includes('email')) {
      notifications.push(this.sendEmailNotification(rule, alertEvent, evaluation));
    }

    if (rule.notificationChannels.includes('websocket')) {
      notifications.push(this.sendWebSocketNotification(rule, alertEvent, evaluation));
    }

    if (rule.notificationChannels.includes('sms')) {
      notifications.push(this.sendSMSNotification(rule, alertEvent, evaluation));
    }

    // Wait for all notifications to complete
    await Promise.allSettled(notifications);
  }
  /**
   * Send email notification - CONSOLE MODE FOR DEVELOPMENT
   */
  async sendEmailNotification(
    rule: AlertRule,
    alertEvent: AlertEvent,
    evaluation: AlertEvaluationResult
  ): Promise<void> {
    console.log('📧 EMAIL NOTIFICATION (Console Mode):');
    console.log('==========================================');
    
    try {
      // Get user email (would normally fetch from user service)
      const userEmail = await this.getUserEmail(rule.userId);
      if (!userEmail) {
        console.warn(`❌ No email found for user ${rule.userId}`);
        return;
      }

      const templateData: AlertEmailTemplate = {
        alertRuleName: rule.name,
        description: rule.description,
        triggeredValue: evaluation.currentValue,
        thresholdValue: evaluation.thresholdValue,
        operator: evaluation.operator,
        datasetName: evaluation.datasetSnapshot?.dataset?.title || rule.datasetId,
        triggeredAt: new Date(),
        acknowledgeUrl: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/alerts/${alertEvent.id}/acknowledge`,
        dashboardUrl: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/alerts`
      };

      // Console output instead of actual email
      console.log(`📧 TO: ${userEmail}`);
      console.log(`📧 SUBJECT: 🚨 SANSA Flood Alert: ${rule.name}`);
      console.log(`📧 ALERT DETAILS:`);
      console.log(`   • Rule: ${rule.name}`);
      console.log(`   • Dataset: ${templateData.datasetName}`);
      console.log(`   • Current Value: ${evaluation.currentValue}`);
      console.log(`   • Threshold: ${evaluation.thresholdValue} (${evaluation.operator})`);      console.log(`   • Status: THRESHOLD EXCEEDED`);
      console.log(`   • Triggered At: ${templateData.triggeredAt.toLocaleString()}`);
      console.log(`   • Acknowledge URL: ${templateData.acknowledgeUrl}`);
      console.log(`   • Dashboard URL: ${templateData.dashboardUrl}`);
      console.log('==========================================');

      /* TODO: Enable for production
      const emailHtml = this.generateEmailHTML(templateData);
      const emailText = this.generateEmailText(templateData);

      if (!this.emailTransporter) {
        console.warn('Email transporter not available');
        return;
      }

      await this.emailTransporter.sendMail({
        from: process.env.SMTP_FROM || process.env.SMTP_USER,
        to: userEmail,
        subject: `🚨 SANSA Flood Alert: ${rule.name}`,
        html: emailHtml,
        text: emailText
      });
      */

      console.log(`✅ Email notification logged for alert ${alertEvent.id}`);
      
      // Update notification status
      await this.updateNotificationStatus(alertEvent.id!, 'email', 'sent');

    } catch (error) {
      console.error('❌ Failed to process email notification:', error);
      await this.updateNotificationStatus(alertEvent.id!, 'email', 'failed');
    }
  }
  /**
   * Send WebSocket notification - CONSOLE MODE FOR DEVELOPMENT
   */
  async sendWebSocketNotification(
    rule: AlertRule,
    alertEvent: AlertEvent,
    evaluation: AlertEvaluationResult
  ): Promise<void> {
    console.log('🔌 WEBSOCKET NOTIFICATION (Console Mode):');
    console.log('==========================================');
    
    try {
      const notificationPayload = {
        type: 'alert_triggered',
        payload: {
          id: alertEvent.id,
          alertRuleId: rule.id,
          alertRuleName: rule.name,
          description: rule.description,
          triggeredValue: evaluation.currentValue,
          thresholdValue: evaluation.thresholdValue,
          operator: evaluation.operator,
          datasetName: evaluation.datasetSnapshot?.dataset?.title || rule.datasetId,
          triggeredAt: new Date(),
          severity: this.calculateSeverity(evaluation)
        },
        timestamp: new Date()
      };

      // Console output instead of actual WebSocket
      console.log(`🔌 TARGET: user_${rule.userId} room + admin room`);
      console.log(`🔌 EVENT: alert`);
      console.log(`🔌 PAYLOAD:`, JSON.stringify(notificationPayload, null, 2));
      console.log('==========================================');

      /* TODO: Enable for production
      if (!this.socketIO) {
        console.warn('Socket.IO not available');
        return;
      }

      // Send to specific user room
      this.socketIO.to(`user_${rule.userId}`).emit('alert', notificationPayload);
      
      // Also send to admin room
      this.socketIO.to('admin').emit('alert', notificationPayload);
      */

      console.log(`✅ WebSocket notification logged for alert ${alertEvent.id}`);
      await this.updateNotificationStatus(alertEvent.id!, 'websocket', 'sent');

    } catch (error) {
      console.error('❌ Failed to process WebSocket notification:', error);
      await this.updateNotificationStatus(alertEvent.id!, 'websocket', 'failed');
    }
  }
  /**
   * Send SMS notification - CONSOLE MODE FOR DEVELOPMENT
   */
  async sendSMSNotification(
    rule: AlertRule,
    alertEvent: AlertEvent,
    evaluation: AlertEvaluationResult
  ): Promise<void> {
    console.log('📱 SMS NOTIFICATION (Console Mode):');
    console.log('==========================================');
    
    try {
      // This would integrate with Twilio or similar SMS service
      const userPhone = await this.getUserPhone(rule.userId);
      if (!userPhone) {
        console.warn(`❌ No phone number found for user ${rule.userId}`);
        return;
      }

      const templateData: AlertSMSTemplate = {
        alertRuleName: rule.name,
        triggeredValue: evaluation.currentValue,
        thresholdValue: evaluation.thresholdValue,
        operator: evaluation.operator,
        timestamp: new Date()
      };

      const smsText = this.generateSMSText(templateData);

      // Console output instead of actual SMS
      console.log(`📱 TO: ${userPhone}`);
      console.log(`📱 MESSAGE: ${smsText}`);
      console.log('==========================================');

      /* TODO: Implement actual SMS sending with Twilio
      console.log(`SMS would be sent to ${userPhone}: ${smsText}`);
      */
      
      console.log(`✅ SMS notification logged for alert ${alertEvent.id}`);
      await this.updateNotificationStatus(alertEvent.id!, 'sms', 'sent');

    } catch (error) {
      console.error('❌ Failed to process SMS notification:', error);
      await this.updateNotificationStatus(alertEvent.id!, 'sms', 'failed');
    }
  }

  /**
   * Generate HTML email template
   */
  private generateEmailHTML(data: AlertEmailTemplate): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>SANSA Flood Alert</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          .header { background: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px; }
          .alert-info { background: #fef2f2; border: 1px solid #fecaca; border-radius: 4px; padding: 15px; margin: 20px 0; }
          .threshold { font-size: 18px; font-weight: bold; color: #dc2626; margin: 10px 0; }
          .actions { margin: 30px 0; text-align: center; }
          .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 0 10px; }
          .footer { background: #f9fafb; padding: 20px; text-align: center; font-size: 12px; color: #6b7280; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚨 SANSA Flood Alert</h1>
            <p>Alert Rule: ${data.alertRuleName}</p>
          </div>
          <div class="content">
            <div class="alert-info">
              <h3>Alert Details</h3>
              <p><strong>Dataset:</strong> ${data.datasetName}</p>
              <p><strong>Description:</strong> ${data.description || 'No description provided'}</p>
              <div class="threshold">
                Current Value: ${data.triggeredValue} ${data.operator} ${data.thresholdValue} (Threshold)
              </div>
              <p><strong>Triggered At:</strong> ${data.triggeredAt.toLocaleString()}</p>
            </div>
            <div class="actions">
              <a href="${data.acknowledgeUrl}" class="button">Acknowledge Alert</a>
              <a href="${data.dashboardUrl}" class="button">View Dashboard</a>
            </div>
          </div>
          <div class="footer">
            <p>This alert was generated by the SANSA Flood Mapping System</p>
            <p>If you no longer wish to receive these alerts, please update your notification preferences</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate plain text email
   */
  private generateEmailText(data: AlertEmailTemplate): string {
    return `
SANSA FLOOD ALERT

Alert Rule: ${data.alertRuleName}
Dataset: ${data.datasetName}
Description: ${data.description || 'No description provided'}

THRESHOLD EXCEEDED:
Current Value: ${data.triggeredValue} ${data.operator} ${data.thresholdValue} (Threshold)
Triggered At: ${data.triggeredAt.toLocaleString()}

Actions:
- Acknowledge Alert: ${data.acknowledgeUrl}
- View Dashboard: ${data.dashboardUrl}

This alert was generated by the SANSA Flood Mapping System.
    `.trim();
  }

  /**
   * Generate SMS text
   */
  private generateSMSText(data: AlertSMSTemplate): string {
    return `SANSA Alert: ${data.alertRuleName} - ${data.triggeredValue} ${data.operator} ${data.thresholdValue} at ${data.timestamp.toLocaleTimeString()}`;
  }

  /**
   * Calculate alert severity based on evaluation
   */
  private calculateSeverity(evaluation: AlertEvaluationResult): 'low' | 'medium' | 'high' | 'critical' {
    const ratio = Math.abs(evaluation.currentValue / evaluation.thresholdValue);
    
    if (ratio >= 2) return 'critical';
    if (ratio >= 1.5) return 'high';
    if (ratio >= 1.2) return 'medium';
    return 'low';
  }

  /**
   * Get user email (mock implementation)
   */
  private async getUserEmail(userId: number): Promise<string | null> {
    // TODO: Implement actual user service lookup
    return `user${userId}@example.com`;
  }

  /**
   * Get user phone (mock implementation)
   */
  private async getUserPhone(userId: number): Promise<string | null> {
    // TODO: Implement actual user service lookup
    return '+1234567890';
  }

  /**
   * Update notification status in database
   */
  private async updateNotificationStatus(
    alertEventId: number,
    notificationType: 'email' | 'websocket' | 'sms',
    status: 'sent' | 'failed'
  ): Promise<void> {
    // TODO: Implement database update
    console.log(`Notification status updated: Alert ${alertEventId}, ${notificationType}: ${status}`);
  }
  /**
   * Test notification system - CONSOLE MODE
   */
  async testNotifications(): Promise<void> {
    console.log('🧪 Testing notification system (Development Mode)...');
    
    /* TODO: Enable for production
    if (this.emailTransporter) {
      try {
        await this.emailTransporter.verify();
        console.log('✅ Email transporter is ready');
      } catch (error) {
        console.error('❌ Email transporter error:', error);
      }
    }
    */
    
    console.log('✅ Email notifications: Configured for console output');

    if (this.socketIO) {
      console.log('✅ WebSocket server: Ready for console output');
    } else {
      console.log('⚠️ WebSocket server: Will use console output when available');
    }
    
    console.log('✅ SMS notifications: Configured for console output');
    console.log('🧪 Notification system test completed (Development Mode)');
  }
}
