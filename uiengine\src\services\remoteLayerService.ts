/**
 * Service for handling remote/external layers
 */

export interface RemoteLayerInfo {
    name: string;
    title: string;
    serviceType: string;
    remoteUrl: string;
    isRemote: boolean;
    // Authentication options
    authType?: 'none' | 'token' | 'apikey' | 'basic';
    authToken?: string;
    apiKey?: string;
    username?: string;
    password?: string;
}

/**
 * Handle remote layer requests by proxying to external services
 */
export async function handleRemoteLayerRequest(
    layer: RemoteLayerInfo,
    requestType: 'GetMap' | 'GetLegendGraphic' | 'GetCapabilities',
    params: Record<string, string>
): Promise<Response> {
    
    console.log(`🌐 Handling remote layer request: ${requestType} for ${layer.name}`);
    
    try {
        // Construct the remote service URL
        let remoteUrl = layer.remoteUrl;
        
        // Handle different service types
        if (layer.serviceType === 'gxp_arcrestsource') {
            // ArcGIS REST ImageServer
            remoteUrl = handleArcGISRequest(layer, requestType, params);
        } else {
            // Standard WMS service
            remoteUrl = handleWMSRequest(layer, requestType, params);
        }
        
        console.log(`🌐 Remote request URL: ${remoteUrl}`);

        // Make the request to the remote service
        const response = await fetch(remoteUrl, {
            method: 'GET',
            headers: {
                'User-Agent': 'SANSA-GeoNode-Proxy/1.0',
                'Accept': '*/*'
            }
        });

        console.log(`🌐 Remote service response: ${response.status} ${response.statusText}`);
        console.log(`🌐 Response headers:`, Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`🚨 Remote service error response:`, errorText);
            throw new Error(`Remote service error: ${response.status} ${response.statusText} - ${errorText}`);
        }
        
        return response;
        
    } catch (error) {
        console.error(`🚨 Remote layer request failed for ${layer.name}:`, error);
        throw error;
    }
}

/**
 * Handle ArcGIS REST ImageServer requests
 */
function handleArcGISRequest(
    layer: RemoteLayerInfo,
    requestType: string,
    params: Record<string, string>
): string {

    const baseUrl = layer.remoteUrl;

    switch (requestType) {
        case 'GetMap':
            // Convert WMS GetMap to ArcGIS REST export request
            // ArcGIS REST ImageServer export endpoint
            const exportUrl = `${baseUrl}/export`;

            // Parse bbox from WMS format (minx,miny,maxx,maxy) to ArcGIS format
            const bbox = params.BBOX || '-180,-90,180,90';
            const [minx, miny, maxx, maxy] = bbox.split(',').map(Number);

            // ArcGIS REST export parameters
            const exportParams = new URLSearchParams({
                f: 'image',
                bbox: `${minx},${miny},${maxx},${maxy}`,
                bboxSR: '4326', // WGS84
                imageSR: '4326',
                size: `${params.WIDTH || '256'},${params.HEIGHT || '256'}`,
                format: params.FORMAT?.includes('jpeg') ? 'jpg' : 'png',
                transparent: params.TRANSPARENT === 'true' ? 'true' : 'false',
                dpi: '96',
                time: '', // Can be added for temporal data
                layerDefs: '', // Can be used for filtering
                layers: '', // Can specify specific layers
                renderingRule: '', // Can be used for styling
                mosaicRule: '' // Can be used for mosaic datasets
            });

            console.log(`🌐 ArcGIS Export URL: ${exportUrl}?${exportParams.toString()}`);
            return `${exportUrl}?${exportParams.toString()}`;

        case 'GetLegendGraphic':
            // ArcGIS REST legend request - use the legend endpoint
            const legendUrl = `${baseUrl}/legend`;
            const legendParams = new URLSearchParams({
                f: 'json'
            });

            console.log(`🌐 ArcGIS Legend URL: ${legendUrl}?${legendParams.toString()}`);
            return `${legendUrl}?${legendParams.toString()}`;

        case 'GetCapabilities':
            // ArcGIS REST service info - equivalent to capabilities
            const infoParams = new URLSearchParams({
                f: 'json'
            });

            console.log(`🌐 ArcGIS Info URL: ${baseUrl}?${infoParams.toString()}`);
            return `${baseUrl}?${infoParams.toString()}`;

        default:
            throw new Error(`Unsupported request type for ArcGIS: ${requestType}`);
    }
}

/**
 * Handle standard WMS requests
 */
function handleWMSRequest(
    layer: RemoteLayerInfo,
    requestType: string,
    params: Record<string, string>
): string {
    
    const baseUrl = layer.remoteUrl;
    const urlParams = new URLSearchParams();
    
    // Add standard WMS parameters
    urlParams.set('SERVICE', 'WMS');
    urlParams.set('REQUEST', requestType);
    urlParams.set('VERSION', params.VERSION || '1.1.1');
    
    switch (requestType) {
        case 'GetMap':
            urlParams.set('LAYERS', layer.name);
            urlParams.set('STYLES', params.STYLES || '');
            urlParams.set('FORMAT', params.FORMAT || 'image/png');
            urlParams.set('TRANSPARENT', params.TRANSPARENT || 'true');
            urlParams.set('SRS', params.SRS || 'EPSG:4326');
            urlParams.set('BBOX', params.BBOX || '-180,-90,180,90');
            urlParams.set('WIDTH', params.WIDTH || '256');
            urlParams.set('HEIGHT', params.HEIGHT || '256');
            break;
            
        case 'GetLegendGraphic':
            urlParams.set('LAYER', layer.name);
            urlParams.set('FORMAT', 'image/png');
            break;
            
        case 'GetCapabilities':
            // No additional parameters needed
            break;
            
        default:
            throw new Error(`Unsupported request type for WMS: ${requestType}`);
    }
    
    return `${baseUrl}?${urlParams.toString()}`;
}

/**
 * Validate if a remote service is accessible
 */
export async function validateRemoteService(remoteUrl: string): Promise<boolean> {
    try {
        const capabilitiesUrl = `${remoteUrl}?SERVICE=WMS&REQUEST=GetCapabilities&VERSION=1.1.1`;

        // Create a timeout promise
        const timeoutPromise = new Promise<Response>((_, reject) => {
            setTimeout(() => reject(new Error('Request timeout')), 5000);
        });

        // Race between fetch and timeout
        const response = await Promise.race([
            fetch(capabilitiesUrl, { method: 'HEAD' }),
            timeoutPromise
        ]);

        return response.ok;
    } catch (error) {
        console.warn(`Remote service validation failed for ${remoteUrl}:`, error);
        return false;
    }
}

/**
 * Extract layer information from remote capabilities
 */
export async function getRemoteLayerInfo(remoteUrl: string): Promise<any> {
    try {
        const capabilitiesUrl = `${remoteUrl}?SERVICE=WMS&REQUEST=GetCapabilities&VERSION=1.1.1`;
        const response = await fetch(capabilitiesUrl);
        
        if (!response.ok) {
            throw new Error(`Failed to fetch capabilities: ${response.statusText}`);
        }
        
        const capabilitiesXml = await response.text();
        
        // Parse capabilities XML (simplified)
        // In a full implementation, you'd use a proper XML parser
        return {
            service: 'WMS',
            version: '1.1.1',
            title: 'Remote Service',
            layers: []
        };
        
    } catch (error) {
        console.error('Failed to get remote layer info:', error);
        throw error;
    }
}
