import { db } from '../config/database';
import { Request, Response } from 'express';

export interface ROI {
  id?: number;
  name: string;
  description?: string;
  geometry: any; // GeoJSON geometry
  created_by?: string;
  status?: 'active' | 'archived' | 'processing';
  metadata?: any;
  area_sqkm?: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface SpatialQuery {
  roi_id?: number;
  geometry?: any; // GeoJSON geometry for spatial queries
  buffer_meters?: number;
  analysis_type?: 'flood_extent' | 'water_level' | 'damage_assessment' | 'temporal_change';
  start_date?: string;
  end_date?: string;
}

export class ROIService {
  // Create a new ROI from user-drawn polygon
  static async createROI(roiData: ROI): Promise<ROI> {
    try {
      const [newROI] = await db('regions_of_interest')
        .insert({
          name: roiData.name,
          description: roiData.description,
          geometry: db.raw('ST_GeomFromGeoJSON(?)', [JSON.stringify(roiData.geometry)]),
          created_by: roiData.created_by || 'user',
          status: roiData.status || 'active',
          metadata: roiData.metadata || {}
        })
        .returning('*');

      return newROI;
    } catch (error) {
      console.error('Error creating ROI:', error);
      throw new Error('Failed to create ROI');
    }
  }

  // Get all ROIs with their statistics
  static async getAllROIs(): Promise<ROI[]> {
    try {
      const rois = await db('roi_with_stats')
        .select('*')
        .orderBy('created_at', 'desc');      // Convert geometry to GeoJSON for frontend
      return rois.map((roi: any) => ({
        ...roi,
        geometry: roi.geometry ? this.geometryToGeoJSON(roi.geometry) : null
      }));
    } catch (error) {
      console.error('Error fetching ROIs:', error);
      throw new Error('Failed to fetch ROIs');
    }
  }

  // Get ROI by ID
  static async getROIById(id: number): Promise<ROI | null> {
    try {
      const roi = await db('regions_of_interest')
        .where('id', id)
        .first();

      if (!roi) return null;

      return {
        ...roi,
        geometry: roi.geometry ? this.geometryToGeoJSON(roi.geometry) : null
      };
    } catch (error) {
      console.error('Error fetching ROI by ID:', error);
      throw new Error('Failed to fetch ROI');
    }
  }

  // Update ROI
  static async updateROI(id: number, updates: Partial<ROI>): Promise<ROI | null> {
    try {
      const updateData: any = { ...updates };
      
      if (updates.geometry) {
        updateData.geometry = db.raw('ST_GeomFromGeoJSON(?)', [JSON.stringify(updates.geometry)]);
      }

      const [updatedROI] = await db('regions_of_interest')
        .where('id', id)
        .update(updateData)
        .returning('*');

      return updatedROI ? {
        ...updatedROI,
        geometry: updatedROI.geometry ? this.geometryToGeoJSON(updatedROI.geometry) : null
      } : null;
    } catch (error) {
      console.error('Error updating ROI:', error);
      throw new Error('Failed to update ROI');
    }
  }

  // Delete ROI
  static async deleteROI(id: number): Promise<boolean> {
    try {
      const deletedCount = await db('regions_of_interest')
        .where('id', id)
        .del();

      return deletedCount > 0;
    } catch (error) {
      console.error('Error deleting ROI:', error);
      throw new Error('Failed to delete ROI');
    }
  }

  // Find ROIs that intersect with a given geometry
  static async findIntersectingROIs(geometry: any, bufferMeters: number = 0): Promise<ROI[]> {
    try {
      let query = db('regions_of_interest')
        .select('*')
        .whereRaw('ST_Intersects(geometry, ST_GeomFromGeoJSON(?))', [JSON.stringify(geometry)]);

      if (bufferMeters > 0) {
        query = query.whereRaw(
          'ST_Intersects(ST_Buffer(geometry::geography, ?)::geometry, ST_GeomFromGeoJSON(?))',
          [bufferMeters, JSON.stringify(geometry)]
        );
      }

      const rois = await query;      return rois.map((roi: any) => ({
        ...roi,
        geometry: roi.geometry ? this.geometryToGeoJSON(roi.geometry) : null
      }));
    } catch (error) {
      console.error('Error finding intersecting ROIs:', error);
      throw new Error('Failed to find intersecting ROIs');
    }
  }

  // Get flood events within ROI
  static async getFloodEventsInROI(roiId: number, startDate?: string, endDate?: string): Promise<any[]> {
    try {      let query = db('flood_events as fe')
        .join('regions_of_interest as roi', function(this: any) {
          this.on(db.raw('ST_Intersects(fe.affected_geometry, roi.geometry)'));
        })
        .where('roi.id', roiId)
        .select('fe.*');

      if (startDate) {
        query = query.where('fe.event_date', '>=', startDate);
      }
      if (endDate) {
        query = query.where('fe.event_date', '<=', endDate);
      }

      const events = await query.orderBy('fe.event_date', 'desc');      return events.map((event: any) => ({
        ...event,
        affected_geometry: event.affected_geometry ? this.geometryToGeoJSON(event.affected_geometry) : null
      }));
    } catch (error) {
      console.error('Error fetching flood events in ROI:', error);
      throw new Error('Failed to fetch flood events');
    }
  }

  // Get water measurements within ROI
  static async getWaterMeasurementsInROI(roiId: number, startDate?: string, endDate?: string): Promise<any[]> {
    try {      let query = db('water_measurements as wm')
        .join('regions_of_interest as roi', function(this: any) {
          this.on(db.raw('ST_Within(wm.measurement_point, roi.geometry)'));
        })
        .where('roi.id', roiId)
        .select('wm.*');

      if (startDate) {
        query = query.where('wm.measurement_date', '>=', startDate);
      }
      if (endDate) {
        query = query.where('wm.measurement_date', '<=', endDate);
      }

      const measurements = await query.orderBy('wm.measurement_date', 'desc');      return measurements.map((measurement: any) => ({
        ...measurement,
        measurement_point: measurement.measurement_point ? this.geometryToGeoJSON(measurement.measurement_point) : null
      }));
    } catch (error) {
      console.error('Error fetching water measurements in ROI:', error);
      throw new Error('Failed to fetch water measurements');
    }
  }

  // Perform spatial analysis for ROI
  static async performSpatialAnalysis(query: SpatialQuery): Promise<any> {
    try {
      const analysisData = {
        roi_id: query.roi_id,
        analysis_type: query.analysis_type || 'flood_extent',
        start_date: query.start_date,
        end_date: query.end_date,
        results: {},
        metadata: {
          buffer_meters: query.buffer_meters || 0,
          query_timestamp: new Date().toISOString()
        }
      };

      // Perform different types of analysis based on type
      switch (query.analysis_type) {
        case 'flood_extent':
          analysisData.results = await this.analyzeFloodExtent(query);
          break;
        case 'water_level':
          analysisData.results = await this.analyzeWaterLevels(query);
          break;
        case 'damage_assessment':
          analysisData.results = await this.analyzeDamageAssessment(query);
          break;
        case 'temporal_change':
          analysisData.results = await this.analyzeTemporalChange(query);
          break;
        default:
          analysisData.results = await this.analyzeFloodExtent(query);
      }

      // Save analysis results
      const [savedAnalysis] = await db('spatial_analyses')
        .insert(analysisData)
        .returning('*');

      return savedAnalysis;
    } catch (error) {
      console.error('Error performing spatial analysis:', error);
      throw new Error('Failed to perform spatial analysis');
    }
  }

  // Helper method to convert PostGIS geometry to GeoJSON
  private static geometryToGeoJSON(geometry: any): any {
    // This would typically use ST_AsGeoJSON in a real query
    // For now, we'll assume geometry is already in a usable format
    return geometry;
  }

  // Analysis helper methods
  private static async analyzeFloodExtent(query: SpatialQuery): Promise<any> {
    // Implementation for flood extent analysis
    return {
      total_area_affected_sqkm: 0,
      flood_severity: 'low',
      affected_population_estimate: 0,
      analysis_summary: 'Flood extent analysis completed'
    };
  }

  private static async analyzeWaterLevels(query: SpatialQuery): Promise<any> {
    // Implementation for water level analysis
    return {
      average_water_level_m: 0,
      max_water_level_m: 0,
      measurement_count: 0,
      trend: 'stable',
      analysis_summary: 'Water level analysis completed'
    };
  }

  private static async analyzeDamageAssessment(query: SpatialQuery): Promise<any> {
    // Implementation for damage assessment
    return {
      estimated_damage_usd: 0,
      infrastructure_affected: [],
      population_displaced: 0,
      recovery_time_estimate_days: 0,
      analysis_summary: 'Damage assessment completed'
    };
  }

  private static async analyzeTemporalChange(query: SpatialQuery): Promise<any> {
    // Implementation for temporal change analysis
    return {
      change_detection: [],
      trend_analysis: 'stable',
      seasonal_patterns: [],
      anomalies_detected: 0,
      analysis_summary: 'Temporal change analysis completed'
    };
  }
}
