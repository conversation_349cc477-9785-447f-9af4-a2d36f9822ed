import axios from 'axios';
import { parseStringPromise } from 'xml2js';

export interface TemporalValidationResult {
  isValid: boolean;
  hasTemporal: boolean;
  hasGeometry: boolean;
  geometryField?: string;
  temporalFields?: string[];
  timeExtent?: string;
  reason?: string;
  error?: string;
}

export interface LayerCapabilities {
  name: string;
  title?: string;
  temporal?: {
    hasData: boolean;
    extent?: string;
    defaultTime?: string;
    units?: string;
    values?: string[];
  };
  queryable?: boolean;
  geometryField?: string;
}

export class TemporalLayerValidator {
  private static readonly GEOSERVER_URL = process.env.GEOSERVER_BASE_URL || 'http://localhost:8080/geoserver';
  private static validationCache = new Map<string, TemporalValidationResult>();

  /**
   * Comprehensive temporal layer validation
   */
  static async validateTemporalLayer(layerName: string): Promise<TemporalValidationResult> {
    // Check cache first
    if (this.validationCache.has(layerName)) {
      return this.validationCache.get(layerName)!;
    }

    console.log(`🔍 Validating temporal layer: ${layerName}`);

    try {
      // Step 1: Check WMS capabilities for temporal information
      const wmsCapabilities = await this.checkWMSCapabilities(layerName);
      
      // Step 2: Check WFS capabilities for geometry and data access
      const wfsCapabilities = await this.checkWFSCapabilities(layerName);
      
      // Step 3: Test actual data access
      const dataAccess = await this.testDataAccess(layerName, wfsCapabilities.geometryField);

      // Combine results
      const result: TemporalValidationResult = {
        isValid: wmsCapabilities.hasTemporal && wfsCapabilities.hasGeometry && dataAccess.hasData,
        hasTemporal: wmsCapabilities.hasTemporal,
        hasGeometry: wfsCapabilities.hasGeometry,
        geometryField: wfsCapabilities.geometryField,
        temporalFields: wmsCapabilities.temporalFields,
        timeExtent: wmsCapabilities.timeExtent,
        reason: this.generateValidationReason(wmsCapabilities, wfsCapabilities, dataAccess)
      };

      // Cache the result
      this.validationCache.set(layerName, result);
      
      console.log(`✅ Temporal validation complete for ${layerName}:`, {
        isValid: result.isValid,
        hasTemporal: result.hasTemporal,
        hasGeometry: result.hasGeometry,
        reason: result.reason
      });

      return result;

    } catch (error) {
      const errorResult: TemporalValidationResult = {
        isValid: false,
        hasTemporal: false,
        hasGeometry: false,
        error: error instanceof Error ? error.message : 'Unknown validation error',
        reason: 'Validation failed due to error'
      };

      this.validationCache.set(layerName, errorResult);
      console.error(`❌ Temporal validation failed for ${layerName}:`, error);
      return errorResult;
    }
  }

  /**
   * Check WMS capabilities for temporal information
   */
  private static async checkWMSCapabilities(layerName: string): Promise<{
    hasTemporal: boolean;
    temporalFields?: string[];
    timeExtent?: string;
  }> {
    try {
      const capabilitiesUrl = `${this.GEOSERVER_URL}/geonode/wms?SERVICE=WMS&REQUEST=GetCapabilities&VERSION=1.1.1`;
      const response = await axios.get(capabilitiesUrl, { timeout: 10000 });
      
      const parsed = await parseStringPromise(response.data);
      const capabilities = parsed['WMS_Capabilities'];
      
      if (!capabilities?.Capability?.[0]?.Layer?.[0]?.Layer) {
        return { hasTemporal: false };
      }

      const layers = capabilities.Capability[0].Layer[0].Layer;
      const targetLayer = layers.find((layer: any) => 
        layer.Name?.[0] === layerName || 
        layer.Name?.[0] === `geonode:${layerName}` ||
        layer.Name?.[0] === layerName.replace('geonode:', '')
      );

      if (!targetLayer) {
        return { hasTemporal: false };
      }

      // Check for temporal dimensions
      const dimensions = targetLayer.Dimension || [];
      const timeDimension = dimensions.find((dim: any) =>
        dim.$.name?.toLowerCase() === 'time' || dim.$.name?.toLowerCase() === 'temporal'
      );

      if (timeDimension) {
        return {
          hasTemporal: true,
          temporalFields: [timeDimension.$.name],
          timeExtent: timeDimension._
        };
      }

      return { hasTemporal: false };

    } catch (error) {
      console.warn(`⚠️ WMS capabilities check failed for ${layerName}:`, error);
      return { hasTemporal: false };
    }
  }

  /**
   * Check WFS capabilities for geometry and schema information
   */
  private static async checkWFSCapabilities(layerName: string): Promise<{
    hasGeometry: boolean;
    geometryField?: string;
  }> {
    try {
      const describeUrl = `${this.GEOSERVER_URL}/geonode/wfs?` + new URLSearchParams({
        service: 'WFS',
        request: 'DescribeFeatureType',
        typeName: layerName,
        version: '1.0.0'
      });

      const response = await axios.get(describeUrl, { timeout: 10000 });
      const parsed = await parseStringPromise(response.data);

      // Parse schema to find geometry field
      const schema = parsed['xsd:schema'] || parsed.schema;
      if (!schema) {
        return { hasGeometry: false };
      }

      const complexTypes = schema['xsd:complexType'] || schema.complexType || [];
      let geometryField: string | undefined;

      for (const complexType of complexTypes) {
        const sequence = complexType['xsd:sequence']?.[0] || complexType.sequence?.[0];
        if (!sequence) continue;

        const elements = sequence['xsd:element'] || sequence.element || [];
        for (const element of elements) {
          const name = element.$.name || element.name;
          const type = element.$.type || element.type;

          if (type?.includes('gml:') || 
              name?.toLowerCase().includes('geom') ||
              name?.toLowerCase() === 'geometry' ||
              name?.toLowerCase() === 'the_geom' ||
              name?.toLowerCase() === 'wkb_geometry') {
            geometryField = name;
            break;
          }
        }
        if (geometryField) break;
      }

      return {
        hasGeometry: !!geometryField,
        geometryField
      };

    } catch (error) {
      console.warn(`⚠️ WFS capabilities check failed for ${layerName}:`, error);
      return { hasGeometry: false };
    }
  }

  /**
   * Test actual data access to verify layer is accessible
   */
  private static async testDataAccess(layerName: string, geometryField?: string): Promise<{
    hasData: boolean;
    featureCount?: number;
  }> {
    try {
      const getFeatureUrl = `${this.GEOSERVER_URL}/geonode/wfs?` + new URLSearchParams({
        service: 'WFS',
        version: '1.0.0',
        request: 'GetFeature',
        typeName: layerName,
        maxFeatures: '1',
        outputFormat: 'application/json'
      });

      const response = await axios.get(getFeatureUrl, { timeout: 10000 });
      const data = response.data;

      if (!data.features || !Array.isArray(data.features)) {
        return { hasData: false };
      }

      return {
        hasData: data.features.length > 0,
        featureCount: data.features.length
      };

    } catch (error) {
      console.warn(`⚠️ Data access test failed for ${layerName}:`, error);
      return { hasData: false };
    }
  }

  /**
   * Generate human-readable validation reason
   */
  private static generateValidationReason(
    wmsCapabilities: any,
    wfsCapabilities: any,
    dataAccess: any
  ): string {
    if (!dataAccess.hasData) {
      return 'Layer has no accessible data or features';
    }
    
    if (!wfsCapabilities.hasGeometry) {
      return 'Layer does not have a valid geometry field for spatial analysis';
    }
    
    if (!wmsCapabilities.hasTemporal) {
      return 'Layer does not have temporal dimensions or time-based data';
    }
    
    return 'Layer is valid for temporal analysis';
  }

  /**
   * Batch validate multiple layers
   */
  static async validateMultipleLayers(layerNames: string[]): Promise<Map<string, TemporalValidationResult>> {
    const results = new Map<string, TemporalValidationResult>();
    
    // Validate layers in parallel with limited concurrency
    const batchSize = 3;
    for (let i = 0; i < layerNames.length; i += batchSize) {
      const batch = layerNames.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(async (layerName) => {
          const result = await this.validateTemporalLayer(layerName);
          return { layerName, result };
        })
      );
      
      batchResults.forEach(({ layerName, result }) => {
        results.set(layerName, result);
      });
    }
    
    return results;
  }

  /**
   * Clear validation cache
   */
  static clearCache(): void {
    this.validationCache.clear();
  }

  /**
   * Get cached validation result
   */
  static getCachedValidation(layerName: string): TemporalValidationResult | null {
    return this.validationCache.get(layerName) || null;
  }

  /**
   * Check if layer is potentially temporal based on name patterns
   */
  static isPotentiallyTemporal(layerName: string): boolean {
    const temporalPatterns = [
      'flood_risk', 'soil_moisture', 'mosaic', 'ndvi', 'sentinel', 
      'landsat', 'modis', 'temporal', 'time_series', 'daily', 
      'monthly', 'yearly', 'seasonal'
    ];
    
    return temporalPatterns.some(pattern => 
      layerName.toLowerCase().includes(pattern)
    );
  }
}
