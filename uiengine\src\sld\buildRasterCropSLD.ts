/**
 * SLD Template Builder for Raster AOI Clipping
 * 
 * Generates SLD XML for exact polygon clipping using ras:CropCoverage transformation.
 * This enables precise raster clipping at the WMS level via SLD_BODY parameter.
 */

/**
 * Build SLD XML for raster cropping with AOI polygon
 * 
 * @param aoiWkt - WKT polygon/multipolygon geometry for clipping
 * @returns Complete SLD XML string for WMS SLD_BODY parameter
 */
export function buildRasterCropSLD(aoiWkt: string): string {
  return `<?xml version="1.0" encoding="UTF-8"?>
<StyledLayerDescriptor version="1.0.0"
  xmlns="http://www.opengis.net/sld"
  xmlns:sld="http://www.opengis.net/sld"
  xmlns:ogc="http://www.opengis.net/ogc"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <NamedLayer>
    <Name>raster</Name>
    <UserStyle>
      <Title>AOI Cropped Raster</Title>
      <FeatureTypeStyle>
        <Transformation>
          <ogc:Function name="ras:CropCoverage">
            <!-- pass the rendering coverage -->
            <ogc:Function name="parameter">
              <ogc:Literal>coverage</ogc:Literal>
              <ogc:Function name="coverage"/>
            </ogc:Function>
            <!-- crop geometry -->
            <ogc:Function name="parameter">
              <ogc:Literal>cropShape</ogc:Literal>
              <ogc:Function name="geomFromWKT">
                <ogc:Literal>${aoiWkt}</ogc:Literal>
              </ogc:Function>
            </ogc:Function>
          </ogc:Function>
        </Transformation>
        <Rule>
          <RasterSymbolizer>
            <Opacity>1.0</Opacity>
          </RasterSymbolizer>
        </Rule>
      </FeatureTypeStyle>
    </UserStyle>
  </NamedLayer>
</StyledLayerDescriptor>`;
}
