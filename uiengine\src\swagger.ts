import swaggerJSDoc from 'swagger-jsdoc';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'SANSA Flood Monitoring UI Engine API',
      version: '2.0.0',
      description: `
# SANSA Flood Monitoring UI Engine API

This API provides comprehensive flood monitoring and geospatial data services for the SANSA (South African National Space Agency) flood monitoring system.

## Features

- **🗺️ Geospatial Data Services**: WMS/WFS proxy, layer discovery, and capabilities
- **📊 Dataset Management**: Dataset discovery, metadata, and export functionality
- **🚨 Alert System**: Real-time flood alerts, rules management, and notifications
- **📍 AOI Processing**: Area of Interest selection, clipping, and multi-layer downloads
- **📈 Analytics**: Spatial analysis, reporting, and statistics
- **🔍 Feature Queries**: GetFeatureInfo, spatial intersections, and attribute queries

## Authentication

Most endpoints are currently open for development. Production deployments should implement proper authentication.

## Rate Limiting

API requests are cached where appropriate. Heavy operations like downloads may have longer response times.

## Error Handling

All endpoints return consistent error responses with appropriate HTTP status codes and descriptive messages.
      `,
      contact: {
        name: 'SANSA Development Team',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:3001',
        description: 'Development server'
      },
      {
        url: 'https://api.sansa.org.za',
        description: 'Production server'
      }
    ],
    tags: [
      {
        name: 'Health',
        description: 'System health and status endpoints'
      },
      {
        name: 'OWS',
        description: 'OGC Web Services (WMS/WFS) proxy and geospatial operations'
      },
      {
        name: 'Datasets',
        description: 'Dataset discovery, metadata, and export operations'
      },
      {
        name: 'Alerts',
        description: 'Flood alert rules, events, and notification management'
      },
      {
        name: 'ROI',
        description: 'Region of Interest spatial operations (requires PostGIS)'
      },
      {
        name: 'Reports',
        description: 'Analytics and reporting endpoints'
      }
    ],
    components: {
      schemas: {
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            error: {
              type: 'string',
              description: 'Error message'
            },
            message: {
              type: 'string',
              description: 'Detailed error description'
            },
            code: {
              type: 'string',
              description: 'Error code for programmatic handling'
            }
          }
        },
        HealthStatus: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'OK'
            },
            timestamp: {
              type: 'string',
              format: 'date-time'
            },
            service: {
              type: 'string',
              example: 'UIEngine'
            },
            geoserver: {
              type: 'string',
              description: 'GeoServer URL'
            },
            database: {
              type: 'string',
              enum: ['connected', 'disconnected']
            },
            postgis: {
              type: 'string',
              enum: ['available', 'unavailable']
            },
            features: {
              type: 'object',
              properties: {
                core: { type: 'boolean' },
                spatial: { type: 'boolean' },
                alerts: { type: 'boolean' },
                analytics: { type: 'boolean' },
                reports: { type: 'boolean' },
                advanced_spatial: { type: 'boolean' }
              }
            }
          }
        }
      },
      parameters: {
        BoundingBox: {
          name: 'bbox',
          in: 'query',
          description: 'Bounding box in format: minx,miny,maxx,maxy',
          schema: {
            type: 'string',
            pattern: '^-?\\d+(\\.\\d+)?,-?\\d+(\\.\\d+)?,-?\\d+(\\.\\d+)?,-?\\d+(\\.\\d+)?$',
            example: '18.0,-35.0,33.0,-22.0'
          }
        },
        DateRange: {
          name: 'dateRange',
          in: 'query',
          description: 'Date range for temporal filtering',
          schema: {
            type: 'object',
            properties: {
              startDate: {
                type: 'string',
                format: 'date',
                example: '2024-01-01'
              },
              endDate: {
                type: 'string',
                format: 'date',
                example: '2024-12-31'
              }
            }
          }
        }
      }
    }
  },
  apis: ['./src/routes/*.ts', './src/controllers/*.ts', './src/models/*.ts'],
};

const swaggerSpec = swaggerJSDoc(options);

export default swaggerSpec;