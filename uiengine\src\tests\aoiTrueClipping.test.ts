/**
 * AOI-True Clipping System Tests
 * 
 * Comprehensive acceptance tests for exact edge clipping, holes,
 * multipolygons, and performance validation.
 */

import request from 'supertest';
import { app } from '../server';
import { getAOIService } from '../services/aoiService';
import { getCapabilitiesCache } from '../services/capabilitiesCache';

describe('AOI-True Clipping System', () => {
  let testAOIId: string;

  // Test geometries
  const simplePolygon = {
    type: 'Polygon',
    coordinates: [[
      [18.0, -34.0],
      [19.0, -34.0],
      [19.0, -33.0],
      [18.0, -33.0],
      [18.0, -34.0]
    ]]
  };

  const polygonWithHole = {
    type: 'Polygon',
    coordinates: [
      // Exterior ring
      [[18.0, -34.0], [20.0, -34.0], [20.0, -32.0], [18.0, -32.0], [18.0, -34.0]],
      // Interior ring (hole)
      [[18.5, -33.5], [19.5, -33.5], [19.5, -32.5], [18.5, -32.5], [18.5, -33.5]]
    ]
  };

  const multiPolygon = {
    type: 'MultiPolygon',
    coordinates: [
      [[[18.0, -34.0], [19.0, -34.0], [19.0, -33.0], [18.0, -33.0], [18.0, -34.0]]],
      [[[20.0, -34.0], [21.0, -34.0], [21.0, -33.0], [20.0, -33.0], [20.0, -34.0]]]
    ]
  };

  beforeAll(async () => {
    // Wait for services to initialize
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  afterEach(async () => {
    // Cleanup test AOI
    if (testAOIId) {
      try {
        await request(app)
          .delete(`/api/aoi/${testAOIId}`)
          .expect(200);
      } catch (error) {
        console.warn('Failed to cleanup test AOI:', testAOIId);
      }
      testAOIId = '';
    }
  });

  describe('AOI Lifecycle Management', () => {
    test('should create AOI with simple polygon', async () => {
      const response = await request(app)
        .post('/api/aoi')
        .send({
          mode: 'drawn',
          geometry: simplePolygon,
          metadata: { name: 'Test Simple Polygon' }
        })
        .expect(201);

      expect(response.body).toHaveProperty('aoiId');
      expect(response.body).toHaveProperty('bounds');
      expect(response.body).toHaveProperty('centroid');
      expect(response.body.meta.vertices).toBeGreaterThan(0);

      testAOIId = response.body.aoiId;
    });

    test('should create AOI with polygon containing holes', async () => {
      const response = await request(app)
        .post('/api/aoi')
        .send({
          mode: 'drawn',
          geometry: polygonWithHole,
          metadata: { name: 'Test Polygon with Hole' }
        })
        .expect(201);

      expect(response.body).toHaveProperty('aoiId');
      expect(response.body.meta.vertices).toBeGreaterThan(8); // Exterior + interior rings

      testAOIId = response.body.aoiId;
    });

    test('should create AOI with multipolygon', async () => {
      const response = await request(app)
        .post('/api/aoi')
        .send({
          mode: 'drawn',
          geometry: multiPolygon,
          metadata: { name: 'Test MultiPolygon' }
        })
        .expect(201);

      expect(response.body).toHaveProperty('aoiId');
      expect(response.body.meta.vertices).toBeGreaterThan(8); // Multiple polygons

      testAOIId = response.body.aoiId;
    });

    test('should retrieve AOI by ID', async () => {
      // Create AOI first
      const createResponse = await request(app)
        .post('/api/aoi')
        .send({
          mode: 'drawn',
          geometry: simplePolygon
        })
        .expect(201);

      testAOIId = createResponse.body.aoiId;

      // Retrieve AOI
      const getResponse = await request(app)
        .get(`/api/aoi/${testAOIId}`)
        .expect(200);

      expect(getResponse.body.aoiId).toBe(testAOIId);
      expect(getResponse.body).toHaveProperty('geometry');
      expect(getResponse.body).toHaveProperty('bounds');
    });

    test('should delete AOI', async () => {
      // Create AOI first
      const createResponse = await request(app)
        .post('/api/aoi')
        .send({
          mode: 'drawn',
          geometry: simplePolygon
        })
        .expect(201);

      const aoiId = createResponse.body.aoiId;

      // Delete AOI
      await request(app)
        .delete(`/api/aoi/${aoiId}`)
        .expect(200);

      // Verify deletion
      await request(app)
        .get(`/api/aoi/${aoiId}`)
        .expect(404);
    });

    test('should handle large geometry simplification', async () => {
      // Create a complex polygon with many vertices
      const complexCoordinates = [];
      for (let i = 0; i < 1000; i++) {
        const angle = (i / 1000) * 2 * Math.PI;
        const x = 18.5 + Math.cos(angle) * 0.5;
        const y = -33.5 + Math.sin(angle) * 0.5;
        complexCoordinates.push([x, y]);
      }
      complexCoordinates.push(complexCoordinates[0]); // Close the ring

      const complexPolygon = {
        type: 'Polygon',
        coordinates: [complexCoordinates]
      };

      const response = await request(app)
        .post('/api/aoi')
        .send({
          mode: 'drawn',
          geometry: complexPolygon,
          metadata: { name: 'Complex Polygon' }
        })
        .expect(201);

      expect(response.body.meta.hasSimplified).toBe(true);
      testAOIId = response.body.aoiId;
    });
  });

  describe('Tile Generation', () => {
    beforeEach(async () => {
      // Create test AOI
      const response = await request(app)
        .post('/api/aoi')
        .send({
          mode: 'drawn',
          geometry: simplePolygon
        })
        .expect(201);

      testAOIId = response.body.aoiId;
    });

    test('should generate vector tile with AOI clipping', async () => {
      const response = await request(app)
        .get('/api/tiles/vector/10/512/512.png')
        .query({
          layer: 'geonode:test_vector_layer',
          aoiId: testAOIId
        })
        .expect(200);

      expect(response.headers['content-type']).toMatch(/image/);
      expect(response.headers['x-tile-strategy']).toBeDefined();
      expect(response.headers['x-processing-time']).toBeDefined();
    });

    test('should generate raster tile with AOI clipping', async () => {
      const response = await request(app)
        .get('/api/tiles/raster/10/512/512.png')
        .query({
          layer: 'geonode:test_raster_layer',
          aoiId: testAOIId
        })
        .expect(200);

      expect(response.headers['content-type']).toMatch(/image/);
      expect(response.headers['x-tile-strategy']).toBeDefined();
      expect(response.headers['x-processing-time']).toBeDefined();
    });

    test('should generate tile without AOI (no clipping)', async () => {
      const response = await request(app)
        .get('/api/tiles/vector/10/512/512.png')
        .query({
          layer: 'geonode:test_vector_layer'
        })
        .expect(200);

      expect(response.headers['content-type']).toMatch(/image/);
      expect(response.headers['x-tile-strategy']).toBeDefined();
    });

    test('should handle temporal parameters', async () => {
      const response = await request(app)
        .get('/api/tiles/vector/10/512/512.png')
        .query({
          layer: 'geonode:test_temporal_layer',
          aoiId: testAOIId,
          time: '2023-01-01T00:00:00Z'
        })
        .expect(200);

      expect(response.headers['content-type']).toMatch(/image/);
    });

    test('should return 404 for non-existent layer', async () => {
      await request(app)
        .get('/api/tiles/vector/10/512/512.png')
        .query({
          layer: 'geonode:non_existent_layer',
          aoiId: testAOIId
        })
        .expect(404);
    });

    test('should return 404 for non-existent AOI', async () => {
      await request(app)
        .get('/api/tiles/vector/10/512/512.png')
        .query({
          layer: 'geonode:test_vector_layer',
          aoiId: 'non-existent-aoi-id'
        })
        .expect(404);
    });
  });

  describe('Download Functionality', () => {
    beforeEach(async () => {
      // Create test AOI
      const response = await request(app)
        .post('/api/aoi')
        .send({
          mode: 'drawn',
          geometry: simplePolygon
        })
        .expect(201);

      testAOIId = response.body.aoiId;
    });

    test('should download layers with AOI clipping', async () => {
      const response = await request(app)
        .post('/api/downloads/aoi-true')
        .send({
          layers: ['geonode:test_vector_layer'],
          aoiId: testAOIId,
          format: 'geojson',
          includeMetadata: true
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('application/zip');
      expect(response.headers['content-disposition']).toMatch(/attachment/);
    });

    test('should download multiple layers', async () => {
      const response = await request(app)
        .post('/api/downloads/aoi-true')
        .send({
          layers: ['geonode:test_vector_layer', 'geonode:test_raster_layer'],
          aoiId: testAOIId,
          format: 'shapefile',
          includeMetadata: true
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('application/zip');
    });

    test('should handle download without AOI', async () => {
      const response = await request(app)
        .post('/api/downloads/aoi-true')
        .send({
          layers: ['geonode:test_vector_layer'],
          format: 'geojson'
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('application/zip');
    });

    test('should return error for empty layers array', async () => {
      await request(app)
        .post('/api/downloads/aoi-true')
        .send({
          layers: [],
          aoiId: testAOIId
        })
        .expect(400);
    });
  });

  describe('Performance Tests', () => {
    beforeEach(async () => {
      // Create test AOI
      const response = await request(app)
        .post('/api/aoi')
        .send({
          mode: 'drawn',
          geometry: simplePolygon
        })
        .expect(201);

      testAOIId = response.body.aoiId;
    });

    test('should generate tiles within performance budget', async () => {
      const startTime = Date.now();

      const response = await request(app)
        .get('/api/tiles/vector/10/512/512.png')
        .query({
          layer: 'geonode:test_vector_layer',
          aoiId: testAOIId
        })
        .expect(200);

      const processingTime = Date.now() - startTime;
      const serverProcessingTime = parseInt(response.headers['x-processing-time'] || '0');

      // Performance assertions
      expect(processingTime).toBeLessThan(5000); // Total request time < 5s
      expect(serverProcessingTime).toBeLessThan(3000); // Server processing < 3s
    });

    test('should handle concurrent tile requests', async () => {
      const promises = [];
      
      for (let i = 0; i < 5; i++) {
        promises.push(
          request(app)
            .get(`/api/tiles/vector/10/512/${512 + i}.png`)
            .query({
              layer: 'geonode:test_vector_layer',
              aoiId: testAOIId
            })
        );
      }

      const responses = await Promise.all(promises);
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.headers['content-type']).toMatch(/image/);
      });
    });
  });

  describe('Debug and Monitoring', () => {
    test('should return system health status', async () => {
      const response = await request(app)
        .get('/api/debug/health')
        .expect(200);

      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('uptime');
      expect(response.body).toHaveProperty('memory');
      expect(response.body).toHaveProperty('services');
    });

    test('should return performance metrics', async () => {
      const response = await request(app)
        .get('/api/debug/metrics')
        .expect(200);

      expect(response.body).toHaveProperty('tileRequests');
      expect(response.body).toHaveProperty('aoiOperations');
      expect(response.body).toHaveProperty('downloadRequests');
      expect(response.body).toHaveProperty('systemHealth');
    });

    test('should return capabilities information', async () => {
      const response = await request(app)
        .get('/api/debug/capabilities')
        .expect(200);

      expect(response.body).toHaveProperty('stats');
      expect(response.body).toHaveProperty('summary');
      expect(response.body).toHaveProperty('layersByType');
    });

    test('should reset metrics', async () => {
      await request(app)
        .post('/api/debug/reset-metrics')
        .expect(200);

      const response = await request(app)
        .get('/api/debug/metrics')
        .expect(200);

      expect(response.body.tileRequests.total).toBe(0);
      expect(response.body.aoiOperations.created).toBe(0);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle invalid geometry', async () => {
      await request(app)
        .post('/api/aoi')
        .send({
          mode: 'drawn',
          geometry: {
            type: 'InvalidGeometry',
            coordinates: []
          }
        })
        .expect(400);
    });

    test('should handle missing required parameters', async () => {
      await request(app)
        .post('/api/aoi')
        .send({
          mode: 'drawn'
          // Missing geometry
        })
        .expect(400);
    });

    test('should handle tile request with invalid coordinates', async () => {
      await request(app)
        .get('/api/tiles/vector/-1/-1/-1.png')
        .query({
          layer: 'geonode:test_vector_layer'
        })
        .expect(400);
    });

    test('should handle very large AOI geometry', async () => {
      // Create a very large polygon
      const largeCoordinates = [];
      for (let i = 0; i < 10000; i++) {
        largeCoordinates.push([18 + Math.random(), -34 + Math.random()]);
      }
      largeCoordinates.push(largeCoordinates[0]); // Close the ring

      const largePolygon = {
        type: 'Polygon',
        coordinates: [largeCoordinates]
      };

      const response = await request(app)
        .post('/api/aoi')
        .send({
          mode: 'drawn',
          geometry: largePolygon
        });

      // Should either succeed with simplification or fail gracefully
      expect([201, 400]).toContain(response.status);

      if (response.status === 201) {
        testAOIId = response.body.aoiId;
        expect(response.body.meta.hasSimplified).toBe(true);
      }
    });
  });
});
