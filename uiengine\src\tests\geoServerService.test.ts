// import { GeoServerService, GetFeatureParams, GetLegendParams } from '../services/geoServerService';
import { config } from 'dotenv';

// Load environment variables
// config();

// describe('GeoServerService', () => {
//     let service: GeoServerService;

//     beforeEach(() => {
//         service = new GeoServerService();
//     });

//     it('should get capabilities', async () => {
//         const result = await service.getCapabilities();
//         expect(result).toBeDefined();
//     });

//     it('should get feature data', async () => {
//         const params: GetFeatureParams = {
//             typeName: 'test:layer',
//             outputFormat: 'application/json'
//         };
//         const result = await service.getFeature(params);
//         expect(result).toBeDefined();
//     });

//     it('should get legend graphic', async () => {
//         const params: GetLegendParams = {
//             layer: 'test:layer',
//             format: 'image/png'
//         };
//         const result = await service.getLegendGraphic(params);
//         expect(result).toBeDefined();
//     });
// });
