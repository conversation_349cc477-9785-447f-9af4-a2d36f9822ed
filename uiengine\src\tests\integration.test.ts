/**
 * Integration Tests for AOI-True Clipping System
 * 
 * Basic integration tests to validate the system works end-to-end
 */

import { getCapabilitiesCache } from '../services/capabilitiesCache';
import { getAOIService } from '../services/aoiService';

describe('AOI-True Clipping Integration', () => {
  
  describe('Service Initialization', () => {
    test('capabilities cache should be initialized', () => {
      const capabilitiesCache = getCapabilitiesCache();
      expect(capabilitiesCache).toBeDefined();
    });

    test('AOI service should be initialized', () => {
      const aoiService = getAOIService();
      expect(aoiService).toBeDefined();
    });
  });

  describe('Basic Functionality', () => {
    test('should create and delete AOI', async () => {
      const aoiService = getAOIService();
      
      if (!aoiService) {
        throw new Error('AOI service not available');
      }

      const testGeometry = {
        type: 'Polygon',
        coordinates: [[
          [18.0, -34.0],
          [19.0, -34.0],
          [19.0, -33.0],
          [18.0, -33.0],
          [18.0, -34.0]
        ]]
      };

      // Create AOI
      const aoiData = await aoiService.createAOI({
        mode: 'drawn',
        geometry: testGeometry,
        metadata: { name: 'Test AOI' }
      });

      expect(aoiData).toBeDefined();
      expect(aoiData.aoiId).toBeDefined();
      // Get the full AOI data to check geometry
      const fullAOI = aoiService.getAOI(aoiData.aoiId);
      expect(fullAOI?.originalGeometry).toBeDefined();

      // Retrieve AOI
      const retrievedAOI = aoiService.getAOI('test-aoi');
      expect(retrievedAOI).toBeDefined();
      expect(retrievedAOI?.id).toBe(aoiData.aoiId);

      // Delete AOI
      const deleted = aoiService.deleteAOI('test-aoi');
      expect(deleted).toBe(true);

      // Verify deletion
      const deletedAOI = aoiService.getAOI('test-aoi');
      expect(deletedAOI).toBeNull();
    });

    test('should handle geometry validation', () => {
      const aoiService = getAOIService();
      
      if (!aoiService) {
        throw new Error('AOI service not available');
      }

      // Test invalid geometry
      const invalidGeometry = {
        type: 'InvalidType',
        coordinates: []
      };

      expect(() => {
        aoiService.createAOI({
          mode: 'drawn',
          geometry: invalidGeometry as any
        });
      }).toThrow();
    });

    test('should handle WKT generation', async () => {
      const aoiService = getAOIService();
      
      if (!aoiService) {
        throw new Error('AOI service not available');
      }

      const testGeometry = {
        type: 'Polygon',
        coordinates: [[
          [18.0, -34.0],
          [19.0, -34.0],
          [19.0, -33.0],
          [18.0, -33.0],
          [18.0, -34.0]
        ]]
      };

      const aoiData = await aoiService.createAOI({
        mode: 'drawn',
        geometry: testGeometry
      });

      // Get the full AOI data to check WKT
      const fullAOI = aoiService.getAOI(aoiData.aoiId);
      expect(fullAOI).toBeDefined();
      expect(fullAOI?.geometry).toBeDefined();
      expect(fullAOI?.geometry.type).toBe('MultiPolygon');

      // Cleanup
      aoiService.deleteAOI('wkt-test');
    });
  });

  describe('Performance Validation', () => {
    test('AOI creation should be fast', async () => {
      const aoiService = getAOIService();
      
      if (!aoiService) {
        throw new Error('AOI service not available');
      }

      const testGeometry = {
        type: 'Polygon',
        coordinates: [[
          [18.0, -34.0],
          [19.0, -34.0],
          [19.0, -33.0],
          [18.0, -33.0],
          [18.0, -34.0]
        ]]
      };

      const startTime = Date.now();
      
      const aoiData = await aoiService.createAOI({
        mode: 'drawn',
        geometry: testGeometry
      });
      
      const processingTime = Date.now() - startTime;

      expect(processingTime).toBeLessThan(1000); // Should be under 1 second
      expect(aoiData).toBeDefined();

      // Cleanup
      aoiService.deleteAOI('perf-test');
    });

    test('should handle multiple AOIs efficiently', () => {
      const aoiService = getAOIService();
      
      if (!aoiService) {
        throw new Error('AOI service not available');
      }

      const testGeometry = {
        type: 'Polygon',
        coordinates: [[
          [18.0, -34.0],
          [19.0, -34.0],
          [19.0, -33.0],
          [18.0, -33.0],
          [18.0, -34.0]
        ]]
      };

      const startTime = Date.now();
      const aoiIds: string[] = [];

      // Create multiple AOIs
      for (let i = 0; i < 10; i++) {
        const aoiId = `multi-test-${i}`;
        aoiService.createAOI({
          mode: 'drawn',
          geometry: testGeometry,
          metadata: { name: `Test AOI ${i}` }
        });
        aoiIds.push(aoiId);
      }

      const creationTime = Date.now() - startTime;
      expect(creationTime).toBeLessThan(5000); // Should be under 5 seconds

      // Verify all AOIs exist
      aoiIds.forEach(aoiId => {
        const aoi = aoiService.getAOI(aoiId);
        expect(aoi).toBeDefined();
        expect(aoi?.id).toBeDefined();
      });

      // Cleanup
      aoiIds.forEach(aoiId => {
        aoiService.deleteAOI(aoiId);
      });
    });
  });

  describe('Edge Cases', () => {
    test('should handle complex geometries', async () => {
      const aoiService = getAOIService();
      
      if (!aoiService) {
        throw new Error('AOI service not available');
      }

      // Polygon with hole
      const complexGeometry = {
        type: 'Polygon',
        coordinates: [
          // Exterior ring
          [[18.0, -34.0], [20.0, -34.0], [20.0, -32.0], [18.0, -32.0], [18.0, -34.0]],
          // Interior ring (hole)
          [[18.5, -33.5], [19.5, -33.5], [19.5, -32.5], [18.5, -32.5], [18.5, -33.5]]
        ]
      };

      const aoiData = await aoiService.createAOI({
        mode: 'drawn',
        geometry: complexGeometry,
        metadata: { name: 'Complex Geometry Test' }
      });

      expect(aoiData).toBeDefined();
      // Get the full AOI data to check geometry
      const fullAOI = aoiService.getAOI(aoiData.aoiId);
      expect(fullAOI?.originalGeometry).toBeDefined();
      expect(aoiData.meta.vertices).toBeGreaterThan(8); // Should count all vertices

      // Cleanup
      aoiService.deleteAOI('complex-test');
    });

    test('should handle MultiPolygon geometries', async () => {
      const aoiService = getAOIService();
      
      if (!aoiService) {
        throw new Error('AOI service not available');
      }

      const multiPolygon = {
        type: 'MultiPolygon',
        coordinates: [
          [[[18.0, -34.0], [19.0, -34.0], [19.0, -33.0], [18.0, -33.0], [18.0, -34.0]]],
          [[[20.0, -34.0], [21.0, -34.0], [21.0, -33.0], [20.0, -33.0], [20.0, -34.0]]]
        ]
      };

      const aoiData = await aoiService.createAOI({
        mode: 'drawn',
        geometry: multiPolygon,
        metadata: { name: 'MultiPolygon Test' }
      });

      expect(aoiData).toBeDefined();
      // Get the full AOI data to check geometry
      const fullAOI = aoiService.getAOI(aoiData.aoiId);
      expect(fullAOI?.originalGeometry).toBeDefined();
      expect(fullAOI?.geometry.type).toBe('MultiPolygon');

      // Cleanup
      aoiService.deleteAOI('multi-test');
    });

    test('should handle duplicate AOI IDs', async () => {
      const aoiService = getAOIService();
      
      if (!aoiService) {
        throw new Error('AOI service not available');
      }

      const testGeometry = {
        type: 'Polygon',
        coordinates: [[
          [18.0, -34.0],
          [19.0, -34.0],
          [19.0, -33.0],
          [18.0, -33.0],
          [18.0, -34.0]
        ]]
      };

      // Create first AOI
      const firstAOI = await aoiService.createAOI({
        mode: 'drawn',
        geometry: testGeometry,
        metadata: { name: 'First AOI' }
      });

      expect(firstAOI).toBeDefined();

      // Try to create second AOI with same ID
      expect(() => {
        aoiService.createAOI({
          mode: 'drawn',
          geometry: testGeometry,
          metadata: { name: 'Second AOI' }
        });
      }).toThrow();

      // Cleanup
      aoiService.deleteAOI('duplicate-test');
    });
  });
});
