import { testConnection, checkPostGIS, db } from '../config/database';

describe('PostGIS Integration Tests', () => {
  beforeAll(async () => {
    // Wait for database connection
    await testConnection();
    await checkPostGIS();
  });

  afterAll(async () => {
    await db.destroy();
  });

  test('Database connection should work', async () => {
    const result = await testConnection();
    expect(result).toBe(true);
  });

  test('PostGIS should be available', async () => {
    const result = await checkPostGIS();
    expect(result).toBe(true);
  });

  test('Should create and query ROI table', async () => {
    // Test basic table existence
    const tableExists = await db.schema.hasTable('regions_of_interest');
    expect(tableExists).toBe(true);
  });

  test('Should perform basic spatial query', async () => {
    // Test basic spatial function
    const result = await db.raw('SELECT ST_GeomFromText(?) as geom', [
      'POINT(27.5 -26.5)'
    ]);
    
    expect(result.rows).toHaveLength(1);
    expect(result.rows[0].geom).toBeDefined();
  });

  test('Should calculate area correctly', async () => {
    // Test area calculation
    const result = await db.raw(`
      SELECT ST_Area(ST_GeomFromText('POLYGON((0 0, 1 0, 1 1, 0 1, 0 0))', 4326)::geography) / 1000000 as area_sqkm
    `);
    
    const area = parseFloat(result.rows[0].area_sqkm);
    expect(area).toBeGreaterThan(0);
    // Should be approximately 12100 sq km for a 1x1 degree box near equator
    expect(area).toBeCloseTo(12100, -2);
  });

  test('Should perform intersection query', async () => {
    // Test spatial intersection
    const polygon1 = 'POLYGON((0 0, 2 0, 2 2, 0 2, 0 0))';
    const polygon2 = 'POLYGON((1 1, 3 1, 3 3, 1 3, 1 1))';
    
    const result = await db.raw(`
      SELECT ST_Intersects(
        ST_GeomFromText(?, 4326),
        ST_GeomFromText(?, 4326)
      ) as intersects
    `, [polygon1, polygon2]);
    
    expect(result.rows[0].intersects).toBe(true);
  });
});
