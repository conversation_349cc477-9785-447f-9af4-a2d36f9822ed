/**
 * Test Setup for AOI-True Clipping System
 * 
 * Configures the test environment and initializes required services
 */

import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';
process.env.GEOSERVER_URL = process.env.GEOSERVER_URL || 'https://*************/geoserver';

// Mock console methods to reduce test noise
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

beforeAll(() => {
  // Reduce console noise during tests
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  // Restore console methods
  console.log = originalConsoleLog;
  console.warn = originalConsoleWarn;
  console.error = originalConsoleError;
});

// Global test timeout
jest.setTimeout(30000);

// Mock external dependencies if needed
jest.mock('canvas', () => ({
  createCanvas: jest.fn(() => ({
    getContext: jest.fn(() => ({
      drawImage: jest.fn(),
      globalCompositeOperation: '',
      fillStyle: '',
      beginPath: jest.fn(),
      moveTo: jest.fn(),
      lineTo: jest.fn(),
      closePath: jest.fn(),
      fill: jest.fn()
    })),
    toBuffer: jest.fn(() => Buffer.from('mock-image-data'))
  })),
  loadImage: jest.fn(() => Promise.resolve({}))
}));

// Export test utilities
export const testUtils = {
  createTestAOI: (geometry: any) => ({
    mode: 'drawn',
    geometry,
    metadata: { name: 'Test AOI' }
  }),
  
  createSimplePolygon: () => ({
    type: 'Polygon',
    coordinates: [[
      [18.0, -34.0],
      [19.0, -34.0],
      [19.0, -33.0],
      [18.0, -33.0],
      [18.0, -34.0]
    ]]
  }),
  
  createPolygonWithHole: () => ({
    type: 'Polygon',
    coordinates: [
      // Exterior ring
      [[18.0, -34.0], [20.0, -34.0], [20.0, -32.0], [18.0, -32.0], [18.0, -34.0]],
      // Interior ring (hole)
      [[18.5, -33.5], [19.5, -33.5], [19.5, -32.5], [18.5, -32.5], [18.5, -33.5]]
    ]
  }),
  
  createMultiPolygon: () => ({
    type: 'MultiPolygon',
    coordinates: [
      [[[18.0, -34.0], [19.0, -34.0], [19.0, -33.0], [18.0, -33.0], [18.0, -34.0]]],
      [[[20.0, -34.0], [21.0, -34.0], [21.0, -33.0], [20.0, -33.0], [20.0, -34.0]]]
    ]
  }),
  
  waitForServices: async (timeout: number = 5000) => {
    return new Promise(resolve => setTimeout(resolve, timeout));
  }
};
