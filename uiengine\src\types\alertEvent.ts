// Alert Event Types and Interfaces

export interface AlertEvent {
  id?: number;
  alertRuleId: number;
  triggeredValue: number;
  thresholdValue: number;
  operatorUsed: string;
  datasetSnapshot?: Record<string, any>;
  triggeredAt?: Date;
  acknowledgedAt?: Date;
  acknowledgedBy?: number;
  notificationStatus: {
    email: string;
    websocket: string;
    sms: string;
  };
  metadata?: Record<string, any>;
}

export interface AlertEventQuery {
  userId?: number;
  alertRuleId?: number;
  acknowledged?: boolean;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

export interface AlertEventWithDetails extends AlertEvent {
  alertRule?: {
    id: number;
    name: string;
    description?: string;
    datasetId: string;
  };
  acknowledgedByUser?: {
    id: number;
    username: string;
  };
}
