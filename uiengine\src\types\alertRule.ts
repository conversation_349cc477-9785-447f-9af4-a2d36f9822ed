// Alert Rule Types and Interfaces for Phase 3 Real-time Alerting System

export interface AlertRule {
  id?: number;
  name: string;
  description?: string;
  datasetId: string; // References dataset from catalog API
  thresholdValue: number;
  thresholdOperator: ThresholdOperator;
  conditionField: string; // Field name to monitor in dataset (default: 'value')
  userId: number;
  roiId?: number; // Optional spatial constraint
  notificationChannels: NotificationChannel[];
  isActive: boolean;
  pollingIntervalMinutes: number;
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AlertEvent {
  id?: number;
  alertRuleId: number;
  triggeredValue: number;
  thresholdValue: number;
  operatorUsed: ThresholdOperator;
  datasetSnapshot?: Record<string, any>; // Store dataset info at trigger time
  triggeredAt?: Date;
  acknowledgedAt?: Date;
  acknowledgedBy?: number;
  notificationStatus: NotificationStatus;
  metadata?: Record<string, any>;
}

export interface AlertNotification {
  id?: number;
  alertEventId: number;
  notificationType: NotificationChannel;
  recipient: string;
  status: NotificationDeliveryStatus;
  sentAt?: Date;
  deliveredAt?: Date;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

// Enums and Types
export type ThresholdOperator = '>' | '<' | '>=' | '<=' | '=' | '!=';

export type NotificationChannel = 'email' | 'websocket' | 'sms';

export type NotificationDeliveryStatus = 'pending' | 'sent' | 'delivered' | 'failed';

export interface NotificationStatus {
  email: NotificationDeliveryStatus;
  websocket: NotificationDeliveryStatus;
  sms: NotificationDeliveryStatus;
}

// Request/Response DTOs
export interface CreateAlertRuleRequest {
  name: string;
  description?: string;
  datasetId: string;
  thresholdValue: number;
  thresholdOperator: ThresholdOperator;
  conditionField?: string;
  roiId?: number;
  notificationChannels?: NotificationChannel[];
  pollingIntervalMinutes?: number;
}

export interface UpdateAlertRuleRequest extends Partial<CreateAlertRuleRequest> {
  isActive?: boolean;
}

export interface AlertRuleResponse extends AlertRule {
  id: number;
  dataset?: {
    id: string;
    title: string;
    description?: string;
  };
  user?: {
    id: number;
    username: string;
    email: string;
  };
  roi?: {
    id: number;
    name: string;
  };
}

export interface AlertEventResponse extends AlertEvent {
  id: number;
  alertRule?: {
    id: number;
    name: string;
    description?: string;
    datasetId: string;
  };
  acknowledgedByUser?: {
    id: number;
    username: string;
  };
}

export interface AlertEventsQuery {
  userId?: number;
  alertRuleId?: number;
  acknowledged?: boolean;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

export interface AcknowledgeAlertRequest {
  userId: number;
}

// WebSocket Events
export interface AlertWebSocketEvent {
  type: 'alert_triggered' | 'alert_acknowledged' | 'rule_updated';
  payload: AlertEventResponse | AlertRuleResponse;
  timestamp: Date;
}

// Dataset Value Response (from OGC services)
export interface DatasetValue {
  field: string;
  value: number;
  timestamp: Date;
  metadata?: Record<string, any>;
  geometry?: any; // GeoJSON geometry if spatial
}

// Alert Engine Processing Result
export interface AlertEvaluationResult {
  alertRuleId: number;
  triggered: boolean;
  currentValue: number;
  thresholdValue: number;
  operator: ThresholdOperator;
  datasetSnapshot: Record<string, any>;
  timestamp: Date;
}

// Email Template Data
export interface AlertEmailTemplate {
  alertRuleName: string;
  description?: string;
  triggeredValue: number;
  thresholdValue: number;
  operator: ThresholdOperator;
  datasetName: string;
  triggeredAt: Date;
  acknowledgeUrl: string;
  dashboardUrl: string;
}

// SMS Template Data
export interface AlertSMSTemplate {
  alertRuleName: string;
  triggeredValue: number;
  thresholdValue: number;
  operator: ThresholdOperator;
  timestamp: Date;
}

// Configuration interfaces
export interface AlertEngineConfig {
  defaultPollingInterval: number;
  maxConcurrentEvaluations: number;
  retryAttempts: number;
  retryDelay: number;
}

export interface NotificationConfig {
  email: {
    from: string;
    templates: {
      subject: string;
      html: string;
      text: string;
    };
  };
  sms: {
    provider: 'twilio' | 'aws-sns';
    maxLength: number;
  };
  websocket: {
    rooms: string[];
  };
}
