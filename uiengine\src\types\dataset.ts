// Dataset interface based on SANSA API JSON structure
export interface Dataset {
  // Core identification
  pk: string;
  uuid: string;
  name: string;
  title: string;
  alternate: string;
  workspace: string;
  
  // Content and metadata
  abstract: string;
  raw_abstract: string;
  raw_supplemental_information: string;
  supplemental_information: string;
  keywords: string[];
  tkeywords: string[];
  
  // Ownership and permissions
  owner: {
    pk: number;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    avatar: string;
    is_superuser: boolean;
    is_staff: boolean;
    link: string;
  };
  
  // Licensing and legal
  license: {
    identifier: string;
  };
  attribution: string | null;
  constraints_other: string | null;
  raw_constraints_other: string;
  
  // Spatial information
  extent: {
    coords: [number, number, number, number]; // [minx, miny, maxx, maxy]
    srid: string;
  };
  ll_bbox_polygon: {
    type: 'Polygon';
    coordinates: number[][][];
  };
  bbox_polygon: {
    type: 'Polygon';
    coordinates: number[][][];
  };
  srid: string;
  
  // Temporal information
  has_time: boolean;
  has_elevation: boolean;
  temporal_extent_start: string | null;
  temporal_extent_end: string | null;
  date: string;
  created: string;
  last_updated: string;
  
  // Publishing and status
  is_published: boolean;
  is_approved: boolean;
  advertised: boolean;
  featured: boolean;
  favorite: boolean;
  processed: boolean;
  is_copyable: boolean;
  metadata_only: boolean;
  state: string;
  
  // Categories and classification
  category: any | null;
  group: any | null;
  subtype: string;
  resource_type: string;
  ptype: string;
  
  // Data characteristics
  charset: string;
  language: string;
  sourcetype: string;
  store: string;
  is_mosaic: boolean;
  
  // Usage statistics
  popular_count: string;
  share_count: string;
  rating: string;
  
  // URLs and links
  link: string;
  detail_url: string;
  embed_url: string;
  download_url: string;
  thumbnail_url: string;
  links: DatasetLink[];
  download_urls: {
    url: string;
    ajax_safe: boolean;
    default: boolean;
  }[];
  
  // Styling
  default_style: {
    pk: number;
    name: string;
    workspace: string;
    sld_title: string;
    sld_url: string;
  };
  styles: {
    pk: number;
    name: string;
    workspace: string;
    sld_title: string;
    sld_url: string;
  }[];
  
  // Custom template for feature info
  featureinfo_custom_template: string;
  
  // Additional metadata
  metadata_author: {
    pk: number;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    avatar: string;
    is_superuser: boolean;
    is_staff: boolean;
    link: string;
  }[];
  poc: any[];
  metadata_uploaded_preserve: boolean;
  
  // Related data
  regions: any[];
  distributor: any[];
  processor: any[];
  originator: any[];
  publisher: any[];
  resource_provider: any[];
  resource_user: any[];
  principal_investigator: any[];
  custodian: any[];
  
  // Technical metadata
  polymorphic_ctype_id: string;
  perms: any[];
  
  // Optional fields
  doi: string | null;
  edition: string | null;
  maintenance_frequency: string | null;
  spatial_representation_type: string | null;
  data_quality_statement: string | null;
  raw_data_quality_statement: string;
  restriction_code_type: string | null;
  date_type: string;
  raw_purpose: string;
  purpose: string | null;
  elevation_regex: string | null;
  time_regex: string | null;
}

export interface DatasetLink {
  extension: string;
  link_type: string;
  name: string;
  mime: string;
  url: string;
  extras?: {
    type: string;
    content: {
      title: string;
      description: string | null;
      type: string;
      download_url: string;
    };
  };
}

// Simplified dataset for listing views
export interface DatasetSummary {
  pk: string;
  uuid: string;
  name: string;
  title: string;
  abstract: string;
  thumbnail_url: string;
  owner: {
    username: string;
    first_name: string;
    last_name: string;
  };
  created: string;
  last_updated: string;
  popular_count: string;
  keywords: string[];
  category: any;
  subtype: string;
  extent: {
    coords: [number, number, number, number];
    srid: string;
  };
  download_urls: {
    url: string;
    ajax_safe: boolean;
    default: boolean;
  }[];
}

// Search and filter parameters
export interface DatasetSearchParams {
  q?: string;                    // Search query
  keywords?: string[];           // Filter by keywords
  owner?: string;               // Filter by owner username
  category?: string;            // Filter by category
  subtype?: string;             // Filter by subtype (vector, raster, etc.)
  date_from?: string;           // Filter by creation date
  date_to?: string;             // Filter by creation date
  bbox?: [number, number, number, number]; // Spatial filter
  limit?: number;               // Results per page
  offset?: number;              // Pagination offset
  sort?: 'title' | 'created' | 'updated' | 'popular' | 'rating';
  order?: 'asc' | 'desc';
}

// Export format options
export interface ExportOptions {
  format: 'csv' | 'excel' | 'geojson' | 'shapefile' | 'gml' | 'pdf' | 'tiff' | 'png' | 'kml';
  bbox?: [number, number, number, number];
  srid?: string;
  attributes?: string[];
  geometry?: string;
  includeMetadata?: boolean;
  includePreview?: boolean;
}
