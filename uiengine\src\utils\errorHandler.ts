/**
 * Comprehensive error handling utility for GeoServer and service errors
 */

export interface ServiceError {
  success: false;
  error: string;
  code?: string;
  details?: {
    message: string;
    url?: string;
    status?: number;
    timestamp: string;
    suggestion?: string;
    context?: Record<string, any>;
  };
}

export interface ServiceSuccess<T = any> {
  success: true;
  data: T;
  metadata?: {
    timestamp: string;
    source: string;
    cached?: boolean;
  };
}

export type ServiceResponse<T = any> = ServiceSuccess<T> | ServiceError;

/**
 * Enhanced error logging with context
 */
export function logServiceError(
  service: string,
  operation: string,
  error: any,
  context?: Record<string, any>
): void {
  const errorInfo = {
    service,
    operation,
    error: error.message || String(error),
    code: error.code,
    status: error.response?.status,
    url: error.config?.url || context?.url,
    timestamp: new Date().toISOString(),
    context
  };

  console.error(`❌ ${service} ${operation} failed:`, errorInfo);
}

/**
 * Create standardized error response
 */
export function createErrorResponse(
  error: any,
  context: {
    service: string;
    operation: string;
    url?: string;
    suggestion?: string;
  }
): ServiceError {
  return {
    success: false,
    error: `${context.service} ${context.operation} failed`,
    code: error.code,
    details: {
      message: error.message || String(error),
      url: context.url,
      status: error.response?.status,
      timestamp: new Date().toISOString(),
      suggestion: context.suggestion || getErrorSuggestion(error),
      context: {
        service: context.service,
        operation: context.operation
      }
    }
  };
}

/**
 * Create standardized success response
 */
export function createSuccessResponse<T>(
  data: T,
  metadata?: {
    source?: string;
    cached?: boolean;
  }
): ServiceSuccess<T> {
  return {
    success: true,
    data,
    metadata: {
      timestamp: new Date().toISOString(),
      source: metadata?.source || 'unknown',
      cached: metadata?.cached || false
    }
  };
}

/**
 * Get helpful error suggestions based on error type
 */
function getErrorSuggestion(error: any): string {
  if (error.code === 'ENOTFOUND' || error.code === 'EAI_AGAIN') {
    return 'Check if the server is running and network connectivity is available';
  }
  
  if (error.code === 'DEPTH_ZERO_SELF_SIGNED_CERT') {
    return 'SSL certificate issue - using secure request utility to bypass';
  }
  
  if (error.response?.status === 404) {
    return 'Service endpoint not found - check GeoServer configuration';
  }
  
  if (error.response?.status === 500) {
    return 'Server error - check GeoServer logs for details';
  }
  
  if (error.response?.status === 503) {
    return 'Service unavailable - GeoServer may be starting up or overloaded';
  }
  
  if (error.message?.includes('timeout')) {
    return 'Request timed out - server may be slow or unresponsive';
  }
  
  if (error.message?.includes('WMS_Capabilities')) {
    return 'Invalid WMS response - check if layers are published in GeoServer';
  }
  
  return 'Check server logs and configuration for more details';
}

/**
 * Validate and enhance layer data with error context
 */
export function validateLayerData(layers: any[], source: string): {
  validLayers: any[];
  errors: string[];
} {
  const validLayers: any[] = [];
  const errors: string[] = [];

  if (!Array.isArray(layers)) {
    errors.push(`Expected array of layers from ${source}, got ${typeof layers}`);
    return { validLayers: [], errors };
  }

  layers.forEach((layer, index) => {
    if (!layer) {
      errors.push(`Layer ${index} is null/undefined from ${source}`);
      return;
    }

    if (!layer.name) {
      errors.push(`Layer ${index} missing required 'name' property from ${source}`);
      return;
    }

    // Add type safety
    const safeLayer = {
      ...layer,
      type: layer.type || 'unknown',
      title: layer.title || layer.name,
      abstract: layer.abstract || 'No description available'
    };

    validLayers.push(safeLayer);
  });

  if (errors.length > 0) {
    console.warn(`⚠️ Layer validation issues from ${source}:`, errors);
  }

  return { validLayers, errors };
}

/**
 * Check if error indicates GeoServer configuration issues
 */
export function isGeoServerConfigError(error: any): boolean {
  const message = error.message?.toLowerCase() || '';
  const status = error.response?.status;

  return (
    message.includes('wms_capabilities') ||
    message.includes('missing capability') ||
    message.includes('no layers found') ||
    status === 404 ||
    (status === 500 && message.includes('geoserver'))
  );
}

/**
 * Check if error indicates network/connectivity issues
 */
export function isNetworkError(error: any): boolean {
  const code = error.code;
  const message = error.message?.toLowerCase() || '';

  return (
    code === 'ENOTFOUND' ||
    code === 'EAI_AGAIN' ||
    code === 'ECONNREFUSED' ||
    code === 'ETIMEDOUT' ||
    message.includes('timeout') ||
    message.includes('network')
  );
}
