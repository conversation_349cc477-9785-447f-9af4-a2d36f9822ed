/**
 * GeoServer connection validator
 * 
 * This module validates the connection to GeoServer on server startup
 * and logs appropriate warnings if the connection fails.
 */
import { secureGet } from './secureRequest';

/**
 * Validates the connection to GeoServer
 * @param geoserverUrl - URL of the GeoServer instance
 * @returns True if connection is successful, false otherwise
 */
export async function validateGeoServerConnection(geoserverUrl: string): Promise<boolean> {
  if (!geoserverUrl) {
    console.warn('No GeoServer URL provided for validation');
    return false;
  }

  try {
    console.log(`[Startup] Validating connection to GeoServer: ${geoserverUrl}`);
    const wmsUrl = `${geoserverUrl}/wms?service=WMS&version=1.3.0&request=GetCapabilities`;
    
    const response = await secureGet(wmsUrl, { timeout: 5000 });
    
    if (response.status === 200) {
      console.log('[Startup] ✅ Successfully connected to GeoServer with certificate validation bypass');
      return true;
    } else {
      console.warn(`[Startup] ⚠️ GeoServer connection validation returned status: ${response.status}`);
      return false;
    }
  } catch (error: any) {
    console.warn(`[Startup] ❌ Failed to connect to GeoServer: ${error.message}`);
    console.warn('[Startup] This may affect layer discovery and map functionality');
    return false;
  }
}

/**
 * Validates GeoServer capabilities and returns basic info
 * @param geoserverUrl - URL of the GeoServer instance
 * @returns Basic GeoServer information or null if validation fails
 */
export async function getGeoServerInfo(geoserverUrl: string): Promise<{ version?: string; layerCount?: number } | null> {
  try {
    const wmsUrl = `${geoserverUrl}/wms?service=WMS&version=1.3.0&request=GetCapabilities`;
    const response = await secureGet(wmsUrl, { timeout: 10000 });
    
    if (response.status === 200 && response.data) {
      // Basic parsing to extract version info
      const xmlData = response.data.toString();
      const versionMatch = xmlData.match(/version="([^"]+)"/);
      const layerMatches = xmlData.match(/<Layer[^>]*>/g);
      
      return {
        version: versionMatch ? versionMatch[1] : undefined,
        layerCount: layerMatches ? layerMatches.length : 0
      };
    }
    
    return null;
  } catch (error: any) {
    console.warn(`Failed to get GeoServer info: ${error.message}`);
    return null;
  }
}
