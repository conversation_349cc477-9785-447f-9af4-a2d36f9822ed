import { AxiosInstance, AxiosResponse, AxiosRequestConfig } from 'axios';

/**
 * Custom Axios instance with SSL certificate validation bypassed
 */
export const secureAxios: AxiosInstance;

/**
 * Make a secure GET request with certificate validation bypassed
 * @param url - The URL to request
 * @param options - Request options (headers, params, etc.)
 * @returns Axios response promise
 */
export function secureGet<T = any>(url: string, options?: AxiosRequestConfig): Promise<AxiosResponse<T>>;

/**
 * Make a secure POST request with certificate validation bypassed
 * @param url - The URL to request
 * @param data - POST data
 * @param options - Request options (headers, etc.)
 * @returns Axios response promise
 */
export function securePost<T = any>(url: string, data?: any, options?: AxiosRequestConfig): Promise<AxiosResponse<T>>;
