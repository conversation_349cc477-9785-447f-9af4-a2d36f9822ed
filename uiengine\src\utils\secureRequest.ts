/**
 * Server-side secure request utility that bypasses SSL certificate validation
 *
 * IMPORTANT: This is for development purposes only and should not be used in production
 * without proper certificate validation.
 */
import axios, { AxiosInstance, AxiosResponse, AxiosRequestConfig } from 'axios';
import https from 'https';

// Create a custom HTTPS agent that ignores certificate validation errors
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

// Create a secure axios instance with certificate validation bypassed
export const secureAxios: AxiosInstance = axios.create({
  httpsAgent,
  timeout: 30000
});

// Add response interceptor for consistent error handling
secureAxios.interceptors.response.use(
  response => response,
  error => {
    console.error('[Secure Request Error]', error.message);
    return Promise.reject(error);
  }
);

/**
 * Make a secure GET request with certificate validation bypassed
 *
 * @param url - The URL to request
 * @param options - Request options (headers, params, etc.)
 * @returns Axios response promise
 */
export const secureGet = async <T = any>(url: string, options: AxiosRequestConfig = {}): Promise<AxiosResponse<T>> => {
  console.log(`🔒 Secure GET request to: ${url}`);
  console.log(`🔒 Options:`, JSON.stringify(options, null, 2));
  try {
    const response = await secureAxios.get<T>(url, options);
    console.log(`🔒 Response received: ${response.status}`);
    return response;
  } catch (error: any) {
    console.error(`[Secure GET Error] ${url}: ${error.message}`);
    throw error;
  }
};

/**
 * Make a secure POST request with certificate validation bypassed
 *
 * @param url - The URL to request
 * @param data - POST data
 * @param options - Request options (headers, etc.)
 * @returns Axios response promise
 */
export const securePost = async <T = any>(url: string, data: any = {}, options: AxiosRequestConfig = {}): Promise<AxiosResponse<T>> => {
  try {
    return await secureAxios.post<T>(url, data, options);
  } catch (error: any) {
    console.error(`[Secure POST Error] ${url}: ${error.message}`);
    throw error;
  }
};
