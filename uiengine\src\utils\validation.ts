// Simple validation utilities for ROI operations
// This module provides basic validation without external dependencies

export interface ValidationResult {
  error?: {
    details: Array<{ message: string }>;
  };
  value?: any;
}

export class SimpleValidator {
  static validateROI(data: any): ValidationResult {
    const errors: string[] = [];

    // Name validation
    if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
      errors.push('Name is required and must be a non-empty string');
    } else if (data.name.length > 255) {
      errors.push('Name must be less than 255 characters');
    }

    // Geometry validation
    if (!data.geometry || typeof data.geometry !== 'object') {
      errors.push('Geometry is required and must be an object');
    } else {
      // Basic GeoJSON structure validation
      if (!data.geometry.type || !data.geometry.coordinates) {
        errors.push('Geometry must have type and coordinates properties');
      }
      if (data.geometry.type !== 'Polygon' && data.geometry.type !== 'MultiPolygon') {
        errors.push('Geometry type must be Polygon or MultiPolygon');
      }
    }

    // Optional field validation
    if (data.description && (typeof data.description !== 'string' || data.description.length > 1000)) {
      errors.push('Description must be a string with maximum 1000 characters');
    }

    if (data.created_by && (typeof data.created_by !== 'string' || data.created_by.length > 100)) {
      errors.push('Created by must be a string with maximum 100 characters');
    }

    if (data.status && !['active', 'archived', 'processing'].includes(data.status)) {
      errors.push('Status must be one of: active, archived, processing');
    }

    if (data.metadata && typeof data.metadata !== 'object') {
      errors.push('Metadata must be an object');
    }

    return errors.length > 0
      ? { error: { details: errors.map(e => ({ message: e })) } }
      : { value: data };
  }

  static validateSpatialQuery(data: any): ValidationResult {
    const errors: string[] = [];

    // ROI ID validation
    if (data.roi_id !== undefined) {
      if (!Number.isInteger(data.roi_id) || data.roi_id <= 0) {
        errors.push('ROI ID must be a positive integer');
      }
    }

    // Geometry validation (optional)
    if (data.geometry && typeof data.geometry !== 'object') {
      errors.push('Geometry must be an object');
    }

    // Buffer meters validation
    if (data.buffer_meters !== undefined) {
      if (typeof data.buffer_meters !== 'number' || data.buffer_meters < 0 || data.buffer_meters > 50000) {
        errors.push('Buffer meters must be a number between 0 and 50000');
      }
    }

    // Analysis type validation
    if (data.analysis_type && !['flood_extent', 'water_level', 'damage_assessment', 'temporal_change'].includes(data.analysis_type)) {
      errors.push('Analysis type must be one of: flood_extent, water_level, damage_assessment, temporal_change');
    }

    // Date validation
    if (data.start_date && !this.isValidDate(data.start_date)) {
      errors.push('Start date must be a valid ISO date string');
    }

    if (data.end_date && !this.isValidDate(data.end_date)) {
      errors.push('End date must be a valid ISO date string');
    }

    return errors.length > 0
      ? { error: { details: errors.map(e => ({ message: e })) } }
      : { value: data };
  }

  private static isValidDate(dateString: string): boolean {
    try {
      const date = new Date(dateString);
      return date instanceof Date && !isNaN(date.getTime()) && dateString.includes('T');
    } catch {
      return false;
    }
  }

  static validateNumericId(id: string | number): { valid: boolean; id?: number; error?: string } {
    const numId = typeof id === 'string' ? parseInt(id, 10) : id;
    
    if (isNaN(numId) || numId <= 0 || !Number.isInteger(numId)) {
      return { valid: false, error: 'ID must be a positive integer' };
    }
    
    return { valid: true, id: numId };
  }
}
