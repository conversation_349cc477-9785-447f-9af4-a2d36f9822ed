/**
 * TypeScript declaration file for wmsProxy module
 */

import { Readable } from 'stream';

/**
 * Response object from streamSecureWmsRequest
 */
export interface WmsStreamResponse {
  status: number;
  headers: any;
  data: Readable;
}

/**
 * Stream a WMS response from GeoServer with certificate validation bypassed
 * @param url - The GeoServer URL
 * @param params - The WMS parameters
 * @returns Response object with headers and a readable stream
 */
export function streamSecureWmsRequest(url: string, params: Record<string, string>): Promise<WmsStreamResponse>;
