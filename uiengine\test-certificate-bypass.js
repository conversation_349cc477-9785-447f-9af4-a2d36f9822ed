/**
 * Verify GeoServer connections with certificate validation bypass
 * 
 * This script tests the secure request utility with the GeoServer URL
 */
const { secureGet } = require('./src/utils/secureRequest');
const dotenv = require('dotenv');

dotenv.config();

async function testConnection() {
  try {
    console.log('Testing GeoServer connection with certificate bypass...');
    
    const geoserverUrl = process.env.GEOSERVER_URL || 'https://*************/geoserver';
    const wmsUrl = `${geoserverUrl}/wms?service=WMS&version=1.3.0&request=GetCapabilities`;
    
    console.log(`Connecting to: ${wmsUrl}`);
    
    const response = await secureGet(wmsUrl);
    console.log('Connection successful!');
    console.log(`Response status: ${response.status} ${response.statusText}`);
    console.log(`Content-Type: ${response.headers['content-type']}`);
    console.log(`Response size: ${response.data.length} bytes`);
    
    // Output the first 200 characters to verify it's valid XML
    if (typeof response.data === 'string') {
      console.log('\nResponse preview:');
      console.log(response.data.substring(0, 200) + '...');
    }
    
    console.log('\nCertificate validation bypass is working correctly.');
  } catch (error) {
    console.error('Connection test failed:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Headers: ${JSON.stringify(error.response.headers, null, 2)}`);
      console.error(`Data: ${error.response.data}`);
    } else {
      console.error(error.message);
    }
  }
}

testConnection();
