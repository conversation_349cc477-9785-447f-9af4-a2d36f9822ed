/**
 * Test the getFullDiscovery function directly
 */
const dotenv = require('dotenv');
dotenv.config();

// Set up the require path for TypeScript modules
require('ts-node/register');

async function testGetFullDiscovery() {
  try {
    console.log('Testing getFullDiscovery function...');
    
    // Import the TypeScript module
    const { getFullDiscovery } = require('./src/services/geoServerService.ts');
    
    const layers = await getFullDiscovery();
    
    console.log('✅ getFullDiscovery test successful!');
    console.log(`Found ${layers.length} layers`);
    
    if (layers.length > 0) {
      console.log(`First layer: ${layers[0].name} - ${layers[0].title}`);
    }
    
  } catch (error) {
    console.error('❌ getFullDiscovery test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testGetFullDiscovery();
