$baseUrl = "http://localhost:3001/api/ows"

Write-Host "`nTesting /capabilities endpoint..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/capabilities" -Method Get
    Write-Host "Success! Found" $response.Count "layers" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 5 | Out-File "capabilities_response.json"
    Write-Host "Response saved to capabilities_response.json" -ForegroundColor Gray
} catch {
    Write-Host "Error:" $_.Exception.Message -ForegroundColor Red
}

Write-Host "`nTesting /features endpoint with flood risk data..." -ForegroundColor Cyan
try {
    $params = @{
        typeName = "flood:risk_areas"
        bbox = "-26.195,28.034,-26.105,28.124"
        startDate = "2025-01-01"
        endDate = "2025-12-31"
    }
    $queryString = [System.Web.HttpUtility]::ParseQueryString([string]::Empty)
    foreach ($param in $params.GetEnumerator()) {
        $queryString.Add($param.Key, $param.Value)
    }
    $uri = "$baseUrl/features?" + $queryString.ToString()
    $response = Invoke-RestMethod -Uri $uri -Method Get
    Write-Host "Success! Found" $response.features.Count "features" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 5 | Out-File "features_response.json"
    Write-Host "Response saved to features_response.json" -ForegroundColor Gray
} catch {
    Write-Host "Error:" $_.Exception.Message -ForegroundColor Red
}

Write-Host "`nTesting /legend endpoint..." -ForegroundColor Cyan
try {
    $params = @{
        layer = "flood:risk_areas"
        format = "image/png"
    }
    $queryString = [System.Web.HttpUtility]::ParseQueryString([string]::Empty)
    foreach ($param in $params.GetEnumerator()) {
        $queryString.Add($param.Key, $param.Value)
    }
    $uri = "$baseUrl/legend?" + $queryString.ToString()
    $response = Invoke-RestMethod -Uri $uri -Method Get -OutFile "legend.png"
    Write-Host "Success! Legend image saved as legend.png" -ForegroundColor Green
} catch {
    Write-Host "Error:" $_.Exception.Message -ForegroundColor Red
}

Write-Host "`nTesting /search endpoint..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/search?query=Johannesburg" -Method Get
    Write-Host "Success! Found" $response.features.Count "locations" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 5 | Out-File "search_response.json"
    Write-Host "Response saved to search_response.json" -ForegroundColor Gray
} catch {
    Write-Host "Error:" $_.Exception.Message -ForegroundColor Red
}
