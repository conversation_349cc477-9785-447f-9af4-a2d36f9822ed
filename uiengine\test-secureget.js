/**
 * Quick test to verify secureGet function works properly
 */
const { secureGet } = require('./src/utils/secureRequest');
const dotenv = require('dotenv');

dotenv.config();

async function testSecureGet() {
  try {
    console.log('Testing secureGet function...');
    
    const geoserverUrl = process.env.GEOSERVER_URL || 'https://10.150.16.184/geoserver';
    const testUrl = `${geoserverUrl}/wms?service=WMS&version=1.3.0&request=GetCapabilities`;
    
    console.log(`Making request to: ${testUrl}`);
    
    const response = await secureGet(testUrl);
    
    console.log('✅ secureGet test successful!');
    console.log(`Status: ${response.status}`);
    console.log(`Content-Type: ${response.headers['content-type']}`);
    console.log(`Data length: ${response.data ? response.data.length : 'undefined'}`);
    
    if (typeof response.data === 'string' && response.data.includes('WMS_Capabilities')) {
      console.log('✅ Response contains valid WMS capabilities data');
    } else {
      console.log('⚠️ Response data format unexpected');
    }
    
  } catch (error) {
    console.error('❌ secureGet test failed:', error.message);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data: ${error.response.data}`);
    }
  }
}

testSecureGet();
