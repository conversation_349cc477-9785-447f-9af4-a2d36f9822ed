/**
 * Quick WMTS endpoint comparison test
 */
const dotenv = require('dotenv');
dotenv.config();

require('ts-node/register');

async function quickWMTSTest() {
  try {
    const { secureGet } = require('./src/utils/secureRequest.js');
    const { parseStringPromise } = require('xml2js');
    
    const GEOSERVER_BASE = process.env.GEOSERVER_URL || 'https://10.150.16.184/geoserver';
    
    console.log('🧪 Quick WMTS Comparison Test\n');
    
    // Test global endpoint
    console.log('1️⃣ Global WMTS (/gwc/service/wmts)...');
    try {
      const globalRes = await secureGet(`${GEOSERVER_BASE}/gwc/service/wmts?service=WMTS&version=1.0.0&request=GetCapabilities`);
      const globalParsed = await parseStringPromise(globalRes.data);
      const globalLayers = globalParsed.Capabilities?.Contents?.[0]?.Layer || [];
      console.log(`   ✅ Found ${globalLayers.length} layers`);
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
    }
    
    // Test workspace endpoint  
    console.log('2️⃣ Workspace WMTS (/geonode/gwc/service/wmts)...');
    try {
      const workspaceRes = await secureGet(`${GEOSERVER_BASE}/geonode/gwc/service/wmts?service=WMTS&version=1.0.0&request=GetCapabilities`);
      const workspaceParsed = await parseStringPromise(workspaceRes.data);
      const workspaceLayers = workspaceParsed.Capabilities?.Contents?.[0]?.Layer || [];
      console.log(`   ✅ Found ${workspaceLayers.length} layers`);
      
      // Show first few layer names
      if (workspaceLayers.length > 0) {
        console.log('   📋 Sample layers:');
        workspaceLayers.slice(0, 5).forEach(layer => {
          const identifier = layer['ows:Identifier']?.[0];
          console.log(`      - ${identifier}`);
        });
      }
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
    }
    
    console.log('\n🎯 CONCLUSION:');
    console.log('   ✅ Workspace-specific WMTS endpoint IS SUPPORTED and has layers!');
    console.log('   📌 RECOMMENDATION: Update geoServerService.ts to use workspace endpoint');
    console.log('   📌 Change GEOSERVER_WMTS to use /geonode/gwc/service/wmts');
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

quickWMTSTest();
