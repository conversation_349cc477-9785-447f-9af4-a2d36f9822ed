/**
 * Test workspace-specific WMTS endpoint support
 * This test verifies if GeoServer supports workspace-specific WMTS/GWC endpoints
 * and compares the results with the global WMTS endpoint
 */
const dotenv = require('dotenv');
dotenv.config();

// Set up the require path for TypeScript modules
require('ts-node/register');

const { parseStringPromise } = require('xml2js');

async function testWMTSEndpoints() {
  try {
    console.log('🧪 Testing WMTS endpoint workspace support...\n');
    
    // Import the secure request utility
    const { secureGet } = require('./src/utils/secureRequest.js');
    
    const GEOSERVER_BASE = process.env.GEOSERVER_URL || 'https://*************/geoserver';
    
    // Define both endpoints
    const globalWMTS = `${GEOSERVER_BASE}/gwc/service/wmts?service=WMTS&version=1.0.0&request=GetCapabilities`;
    const workspaceWMTS = `${GEOSERVER_BASE}/geonode/gwc/service/wmts?service=WMTS&version=1.0.0&request=GetCapabilities`;
    
    console.log('📡 Testing endpoints:');
    console.log(`   Global WMTS: ${globalWMTS}`);
    console.log(`   Workspace WMTS: ${workspaceWMTS}\n`);
    
    // Test global WMTS endpoint
    console.log('1️⃣ Testing Global WMTS endpoint...');
    let globalLayers = [];
    let globalSuccess = false;
    
    try {
      const globalRes = await secureGet(globalWMTS);
      const globalParsed = await parseStringPromise(globalRes.data);
      const globalContents = globalParsed.Capabilities?.Contents?.[0]?.Layer || [];
      
      globalLayers = globalContents.map(layer => {
        const identifier = layer['ows:Identifier']?.[0];
        const title = layer['ows:Title']?.[0] || identifier;
        return { identifier, title };
      }).filter(layer => layer.identifier);
      
      globalSuccess = true;
      console.log(`   ✅ Global WMTS: Found ${globalLayers.length} layers`);
      
      // Show some example layers
      if (globalLayers.length > 0) {
        console.log('   📋 Sample layers from global endpoint:');
        globalLayers.slice(0, 5).forEach(layer => {
          console.log(`      - ${layer.identifier} (${layer.title})`);
        });
        if (globalLayers.length > 5) {
          console.log(`      ... and ${globalLayers.length - 5} more layers`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ Global WMTS failed: ${error.message}`);
    }
    
    console.log();
    
    // Test workspace-specific WMTS endpoint
    console.log('2️⃣ Testing Workspace-specific WMTS endpoint...');
    let workspaceLayers = [];
    let workspaceSuccess = false;
    
    try {
      const workspaceRes = await secureGet(workspaceWMTS);
      const workspaceParsed = await parseStringPromise(workspaceRes.data);
      const workspaceContents = workspaceParsed.Capabilities?.Contents?.[0]?.Layer || [];
      
      workspaceLayers = workspaceContents.map(layer => {
        const identifier = layer['ows:Identifier']?.[0];
        const title = layer['ows:Title']?.[0] || identifier;
        return { identifier, title };
      }).filter(layer => layer.identifier);
      
      workspaceSuccess = true;
      console.log(`   ✅ Workspace WMTS: Found ${workspaceLayers.length} layers`);
      
      // Show some example layers
      if (workspaceLayers.length > 0) {
        console.log('   📋 Sample layers from workspace endpoint:');
        workspaceLayers.slice(0, 5).forEach(layer => {
          console.log(`      - ${layer.identifier} (${layer.title})`);
        });
        if (workspaceLayers.length > 5) {
          console.log(`      ... and ${workspaceLayers.length - 5} more layers`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ Workspace WMTS failed: ${error.message}`);
      console.log(`   🔍 Error details: ${error.response?.status} ${error.response?.statusText || ''}`);
    }
    
    console.log();
    
    // Analysis and recommendations
    console.log('📊 ANALYSIS & RECOMMENDATIONS:\n');
    
    if (!globalSuccess && !workspaceSuccess) {
      console.log('❌ Both WMTS endpoints failed. Check GeoServer configuration.');
      return;
    }
    
    if (!globalSuccess && workspaceSuccess) {
      console.log('⚠️  Only workspace WMTS works. This is unusual but workspace endpoint should be used.');
    }
    
    if (globalSuccess && !workspaceSuccess) {
      console.log('❌ Workspace-specific WMTS endpoint is NOT supported by this GeoServer version.');
      console.log('   📌 RECOMMENDATION: Keep using global WMTS endpoint');
      console.log('   📌 Current configuration is correct: /gwc/service/wmts');
      return;
    }
    
    if (globalSuccess && workspaceSuccess) {
      console.log('✅ Both WMTS endpoints work! Analyzing layer differences...\n');
      
      // Compare layer counts
      console.log(`📈 Layer counts:`);
      console.log(`   Global endpoint: ${globalLayers.length} layers`);
      console.log(`   Workspace endpoint: ${workspaceLayers.length} layers`);
      
      // Check for geonode workspace layers
      const globalGeonodeLayers = globalLayers.filter(layer => 
        layer.identifier.startsWith('geonode:') || layer.identifier.includes('geonode')
      );
      
      const workspaceGeonodeLayers = workspaceLayers.filter(layer => 
        layer.identifier.includes('mtata_river_flood') || 
        layer.identifier.includes('geonode') ||
        !layer.identifier.includes(':') // workspace endpoints often return layer names without prefix
      );
      
      console.log(`\n🎯 Geonode workspace analysis:`);
      console.log(`   Global endpoint geonode layers: ${globalGeonodeLayers.length}`);
      console.log(`   Workspace endpoint relevant layers: ${workspaceGeonodeLayers.length}`);
      
      if (globalGeonodeLayers.length > 0) {
        console.log(`\n   📋 Geonode layers from global endpoint:`);
        globalGeonodeLayers.slice(0, 3).forEach(layer => {
          console.log(`      - ${layer.identifier}`);
        });
      }
      
      if (workspaceGeonodeLayers.length > 0) {
        console.log(`\n   📋 Relevant layers from workspace endpoint:`);
        workspaceGeonodeLayers.slice(0, 3).forEach(layer => {
          console.log(`      - ${layer.identifier}`);
        });
      }
      
      // Make recommendation
      console.log(`\n🎯 RECOMMENDATION:`);
      
      if (workspaceLayers.length > 0 && workspaceLayers.length <= globalLayers.length) {
        console.log('✅ CHANGE TO WORKSPACE ENDPOINT');
        console.log('   📌 Workspace-specific WMTS is supported');
        console.log('   📌 Update geoServerService.ts to use:');
        console.log(`   📌 const GEOSERVER_WMTS = \`\${GEOSERVER_BASE}/geonode/gwc/service/wmts?service=WMTS&version=1.0.0&request=GetCapabilities\`;`);
        console.log('   📌 This will provide consistency with WMS and WFS endpoints');
        console.log('   📌 Layer names will be consistent (without workspace prefixes)');
      } else {
        console.log('⚠️  KEEP GLOBAL ENDPOINT');
        console.log('   📌 Workspace endpoint returns unexpected results');
        console.log('   📌 Current configuration should remain unchanged');
      }
    }
    
    console.log('\n🔍 TEST DETAILS:');
    console.log(`   Global WMTS Status: ${globalSuccess ? '✅ Working' : '❌ Failed'}`);
    console.log(`   Workspace WMTS Status: ${workspaceSuccess ? '✅ Working' : '❌ Failed'}`);
    console.log(`   GeoServer Version: Check admin panel for version info`);
    console.log(`   Test completed at: ${new Date().toISOString()}`);
    
  } catch (error) {
    console.error('💥 Test script failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
console.log('🚀 Starting WMTS Workspace Support Test\n');
testWMTSEndpoints().then(() => {
  console.log('\n✅ Test completed');
}).catch(error => {
  console.error('\n❌ Test failed:', error);
});
