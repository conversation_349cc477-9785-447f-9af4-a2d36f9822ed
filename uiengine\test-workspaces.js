/**
 * Test different GeoServer workspace endpoints
 */
const { secureGet } = require('./src/utils/secureRequest');
const { parseStringPromise } = require('xml2js');
const dotenv = require('dotenv');

dotenv.config();

async function testWorkspaces() {
  try {
    const geoserverUrl = process.env.GEOSERVER_URL || 'https://10.150.16.184/geoserver';
    
    // Test 1: Try geonode workspace specifically
    console.log('Testing geonode workspace...');
    try {
      const geonodeUrl = `${geoserverUrl}/geonode/wms?service=WMS&version=1.3.0&request=GetCapabilities`;
      const response = await secureGet(geonodeUrl);
      const parsed = await parseStringPromise(response.data);
      
      const layers = parsed['WMS_Capabilities']?.['Capability']?.[0]?.['Layer']?.[0]?.['Layer'] || [];
      console.log(`✅ Geonode workspace found ${layers.length} layers`);
      
      if (layers.length > 0) {
        layers.slice(0, 3).forEach((layer, index) => {
          console.log(`  Layer ${index}: ${layer.Name?.[0]} - ${layer.Title?.[0]}`);
        });
      }
    } catch (error) {
      console.log('❌ Geonode workspace failed:', error.message);
    }
    
    // Test 2: Try REST API to list workspaces
    console.log('\nTesting REST API for workspaces...');
    try {
      const restUrl = `${geoserverUrl}/rest/workspaces`;
      const response = await secureGet(restUrl, {
        headers: {
          'Accept': 'application/json'
        }
      });
      
      console.log('Available workspaces:', response.data);
    } catch (error) {
      console.log('❌ REST API failed:', error.message);
    }
    
    // Test 3: Try OWS endpoint with specific service
    console.log('\nTesting OWS endpoint...');
    try {
      const owsUrl = `${geoserverUrl}/ows?service=WMS&version=1.3.0&request=GetCapabilities`;
      const response = await secureGet(owsUrl);
      const parsed = await parseStringPromise(response.data);
      
      const layers = parsed['WMS_Capabilities']?.['Capability']?.[0]?.['Layer']?.[0]?.['Layer'] || [];
      console.log(`✅ OWS endpoint found ${layers.length} layers`);
      
      if (layers.length > 0) {
        layers.slice(0, 3).forEach((layer, index) => {
          console.log(`  Layer ${index}: ${layer.Name?.[0]} - ${layer.Title?.[0]}`);
        });
      }
    } catch (error) {
      console.log('❌ OWS endpoint failed:', error.message);
    }
    
  } catch (error) {
    console.error('Error testing workspaces:', error.message);
  }
}

testWorkspaces();
