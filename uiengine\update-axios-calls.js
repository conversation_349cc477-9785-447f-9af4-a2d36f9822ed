/**
 * Update helper for replacing axios calls with secureGet/securePost
 * 
 * This script searches through the code in the specified file paths
 * and replaces all axios.get calls to the GeoServer URL with secureGet.
 */
const fs = require('fs');
const path = require('path');

// File paths to update
const filesToUpdate = [
  path.join(__dirname, 'src', 'routes', 'ows.ts'),
  path.join(__dirname, 'src', 'services', 'geoServerService.ts'),
  path.join(__dirname, 'src', 'services', 'datasetsService.ts')
];

// Regex pattern to match and replacement function
const httpsAgentPattern = /httpsAgent:\s*new\s*\(require\('https'\)\.Agent\)\(\{\s*rejectUnauthorized:\s*false\s*\}\)/g;

// Process each file
filesToUpdate.forEach(filePath => {
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return;
  }
  
  try {
    // Read file content
    let content = fs.readFileSync(filePath, 'utf8');
    const originalLength = content.length;
    
    // Count occurrences
    const occurrences = (content.match(httpsAgentPattern) || []).length;
    
    if (occurrences === 0) {
      console.log(`No httpsAgent patterns found in ${filePath}`);
      return;
    }
    
    // Replace all axios.get with httpsAgent with secureGet
    content = content.replace(/axios\.get\(`\${[^`]+`\s*,\s*\{\s*([^}]*)\s*httpsAgent:[^}]+\}\s*\}\)/g, 
      (match, paramsSection) => {
        // Extract the URL and parameters
        const urlMatch = match.match(/axios\.get\(`(\${[^`]+})`/);
        const url = urlMatch ? urlMatch[1] : '${url}';
        
        // Create the new secureGet call
        return `secureGet(\`${url}\`, {\n    ${paramsSection}})`;
      });
    
    // Write updated content back to file
    fs.writeFileSync(filePath, content, 'utf8');
    
    console.log(`Updated ${filePath}: ${occurrences} occurrences of httpsAgent pattern found.`);
    console.log(`File size changed from ${originalLength} to ${content.length} bytes.`);
  } catch (error) {
    console.error(`Error updating ${filePath}:`, error);
  }
});

console.log('Update process completed.');
